// const { BabelCatchPlugin as AbcBabelCatchPlugin } = require('@abc/tools');

module.exports = (api) => {
    api.cache(true);
    return {
        presets: [
            [
                '@babel/preset-env',
                {
                    'modules': false, // 对ES6的模块文件不做转化，以便使用tree shaking、sideEffects等
                    'useBuiltIns': 'entry', // browserslist环境不支持的所有垫片都导入
                    // https://babeljs.io/docs/en/babel-preset-env#usebuiltins
                    // https://github.com/zloirock/core-js/blob/master/docs/2019-03-19-core-js-3-babel-and-a-look-into-the-future.md
                    'corejs': {
                        'version': 3, // 使用core-js@3
                    },
                },
            ],
            '@vue/babel-preset-jsx',
        ],
        plugins: [
            [
                '@babel/plugin-transform-runtime',
                {
                    'corejs': false, // 解决 helper 函数重复引入
                },
            ],
            '@babel/plugin-proposal-object-rest-spread',
            '@babel/plugin-proposal-optional-chaining',
            '@babel/plugin-proposal-nullish-coalescing-operator',
            [
                'component',
                {
                    'libraryName': 'element-ui',
                    'styleLibraryName': 'theme-chalk',
                },
            ],
            // [
            //     AbcBabelCatchPlugin,
            //     {
            //         global: 'window',
            //     },
            // ],
            ['@babel/plugin-proposal-decorators', { 'legacy': true }],
            ['@babel/plugin-proposal-class-properties'],
        ],
    };
};

<template>
    <div class="skeleton-wrapper" :style="skeletonStyle"></div>
</template>

<script>
    import { defineComponent } from 'vue';
    export default defineComponent({
        name: 'Skeleton',
        props: {
            width: {
                type: Number,
                default: 50,
            },
            height: {
                type: Number,
                default: 16,
            },
        },
        computed: {
            skeletonStyle() {
                return {
                    width: `${this.width}px`,
                    height: `${this.height}px`,
                };
            },
        },
    });
</script>

<style lang="scss" scoped>
.skeleton-wrapper {
    background: linear-gradient(90deg, #eaedf1 0%, rgba(234, 237, 241, 0.3) 100%);
    border-radius: 6px;
}
</style>

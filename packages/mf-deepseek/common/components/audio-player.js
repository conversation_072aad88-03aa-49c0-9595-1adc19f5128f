// packages/mf-deepseek/common/audio-player.js
// 简单的 HTMLAudioElement 封装，支持基本播放控制、倍速、跳转、事件监听等

export default class AudioPlayer {
    constructor() {
        this.audio = new Audio();
        this.audio.preload = 'auto';
    }

    setSrc(src) {
        this.audio.src = src;
    }

    play() {
        return this.audio.play();
    }

    pause() {
        this.audio.pause();
    }

    setCurrentTime(time) {
        this.audio.currentTime = time;
    }

    setPlaybackRate(rate) {
        this.audio.playbackRate = rate;
    }

    getCurrentTime() {
        return this.audio.currentTime;
    }

    getDuration() {
        return this.audio.duration;
    }

    isPaused() {
        return this.audio.paused;
    }

    on(event, handler) {
        this.audio.addEventListener(event, handler);
    }

    off(event, handler) {
        this.audio.removeEventListener(event, handler);
    }

    destroy() {
        this.pause();
        this.audio.src = '';
        this.audio = null;
    }
}

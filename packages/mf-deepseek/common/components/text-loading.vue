<template>
    <abc-flex class="text-loading-wrapper" align="center" gap="6">
        <abc-flex class="spinner">
            <abc-loading-spinner size="tiny" theme="green"></abc-loading-spinner>
        </abc-flex>
        <abc-text theme="success-light" :class="{ 'dot-loading': dot }">
            {{ text }}
        </abc-text>
    </abc-flex>
</template>

<script>
    import { LoadingSpinner as AbcLoadingSpinner } from '@abc/ui-pc';
    import { defineComponent } from 'vue';
    export default defineComponent({
        name: 'TextLoading',
        components: {
            AbcLoadingSpinner,
        },
        props: {
            text: {
                type: String,
                default: '加载中',
            },
            dot: {
                type: Boolean,
                default: false,
            },
        },
    });
</script>

<style scoped>
.dot-loading::after {
    display: inline-block;
    width: 1.2em;
    color: currentColor;
    text-align: left;
    vertical-align: baseline;
    content: '';
    animation: dot-after 1s steps(3, end) infinite;
}

@keyframes dot-after {
    0% { content: ''; }
    33% { content: '.'; }
    66% { content: '..'; }
    100% { content: '...'; }
}
</style>

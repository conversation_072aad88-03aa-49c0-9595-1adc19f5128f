<template>
    <div class="ai-loading-spinner">
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20.25"
            height="20.25"
            viewBox="0 0 20.25 20.25"
            fill="none"
        >
            <path
                d="M3.47375 0.18617C3.57427 -0.0620567 3.92572 -0.0620567 4.02625 0.18617C4.63193 1.68186 5.81814 2.86807 7.31383 3.47375C7.56206 3.57428 7.56206 3.92573 7.31383 4.02625C5.81814 4.63193 4.63193 5.81814 4.02625 7.31383C3.92572 7.56206 3.57427 7.56206 3.47375 7.31383C2.86807 5.81814 1.68186 4.63193 0.18617 4.02625C-0.0620566 3.92573 -0.0620567 3.57428 0.18617 3.47375C1.68186 2.86807 2.86807 1.68186 3.47375 0.18617Z"
                fill="#5ACAFD"
            />
            <defs>
                <linearGradient
                    id="paint0_linear_1266_2978"
                    x1="19.5"
                    y1="18.75"
                    x2="11.25"
                    y2="9"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stop-color="#634DFC" />
                    <stop offset="1" stop-color="#4E99FF" />
                </linearGradient>
            </defs>
        </svg>

        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20.25"
            height="20.25"
            viewBox="0 0 20.25 20.25"
            fill="none"
            class="ai-loading-spinner_icon"
            :class="{ 'loading': loading }"
        >
            <path d="M10.5713 19.5386C10.4389 20.1538 9.56109 20.1538 9.42866 19.5386C8.46184 15.0468 4.95317 11.5382 0.461447 10.5713C-0.153816 10.4389 -0.153816 9.56109 0.461447 9.42866C4.95317 8.46184 8.46184 4.95317 9.42866 0.461448C9.56109 -0.153814 10.4389 -0.153814 10.5713 0.461448C11.5382 4.95317 15.0468 8.46184 19.5386 9.42866C20.1538 9.56109 20.1538 10.4389 19.5386 10.5713C15.0468 11.5382 11.5382 15.0468 10.5713 19.5386Z" fill="#487BFB" />
        </svg>
    </div>
</template>

<script>
    export default {
        name: 'AiLoadingSpinner',
        props: {
            loading: {
                type: Boolean,
                default: false,
            },
        },
    };
</script>

<style lang="scss">
.ai-loading-spinner {
    position: relative;
    display: inline-block;
    font-size: 0;
    zoom: 0.65;

    .ai-loading-spinner_icon {
        position: absolute;
        top: 1px;
        left: 2px;

        &.loading {
            transform-origin: center;
            animation: ai-loading-anim 1.2s linear infinite;
        }
    }

    @keyframes ai-loading-anim {
        0% {
            transform: rotate(0);
        }

        16.67% {
            transform: rotate(90deg);
        }

        100% {
            transform: rotate(90deg);
        }
    }
}
</style>

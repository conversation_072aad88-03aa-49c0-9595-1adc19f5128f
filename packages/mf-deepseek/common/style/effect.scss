/* 主容器 */
.ai-effect-gradient-border {
    overflow: hidden;
    border-radius: 16px;
    // background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);

    --ai-effect-fill-color: #ffffff;

    &::before {
        position: absolute;
        top: -50%;
        left: -50%;
        z-index: -2;
        width: 200%;
        height: 200%;
        content: '';
        background:
            conic-gradient(
                hsl(344, 100%, 93%),
                hsl(217, 100%, 76%),
                hsl(260, 100%, 84%),
                hsl(173, 86%, 84%),
                hsl(344, 100%, 93%)
            );
    }

    &.animation::before {
        animation: ai-effect-rotate 4s linear infinite;
    }

    &::after {
        position: absolute;
        top: 2px;
        left: 2px;
        z-index: -1;
        width: calc(100% - 4px);
        height: calc(100% - 4px);
        content: '';
        background: var(--ai-effect-fill-color);
        border-radius: 14px;
    }

    @keyframes ai-effect-rotate {
        100% {
            transform: rotate(1turn);
        }
    }
}

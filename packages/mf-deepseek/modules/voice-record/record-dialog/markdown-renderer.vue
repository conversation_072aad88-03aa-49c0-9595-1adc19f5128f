<template>
    <div>
        <template v-for="(block, index) in blocks">
            <!-- Markdown 内容 -->
            <div
                :key="index"
                class="voice-record-markdown-renderer"
                v-html="block.content"
            ></div>
        </template>
    </div>
</template>

<script>
    import {
        defineComponent,
    } from 'vue';
    import {
        StreamParser,
    } from '@/core/index';
    import {
        NODE_TYPE,
    } from '@/common/utils/constant';
    import { marked } from 'marked';

    export default defineComponent({
        name: 'MarkdownRenderer',
        props: {
            content: {
                type: String,
                required: true,
            },
            loading: {
                type: Boolean,
                default: false,
            },
        },

        data() {
            return {
                blocks: [],
                pendingContent: '',

                NODE_TYPE,

                parser: null,
            };
        },
        watch: {
            content: {
                immediate: true,
                handler(newContent) {
                    this.processContent(newContent);
                },
            },

            loading: {
                handler(val) {
                    if (!val) {
                        this.blocks.forEach((block) => {
                            block.loaded = true;
                        });
                    }
                },
                immediate: true,
            },
        },

        methods: {
            createParser() {
                this.parser = new StreamParser({
                    rules: [],
                    defaultProcessor: (data) => {
                        const { content } = data;

                        this.blocks.push({
                            type: NODE_TYPE.MARKDOWN,
                            content: this.render(content),
                        });
                    },
                });
            },

            processContent(newContent) {
                if (!newContent) {
                    return;
                }
                newContent = newContent.replace(/(\n)(?!\n)/g, '\n\n');
                newContent = newContent.replace(/\*\*(.*?)：\s*\*\*/g, '**$1**');
                // 避免重复处理相同内容
                if (newContent === this.pendingContent) {
                    return;
                }

                if (!this.parser) {
                    this.createParser();
                }

                this.blocks = [];
                this.parser.parse(newContent);
                this.pendingContent = newContent;
            },

            render(content) {
                try {
                    return marked.parse(content);
                } catch (e) {
                    console.error('Error processing content:', e);
                    return content;
                }
            },
        },
    });
</script>
<style lang="scss">
.voice-record-markdown-renderer {
    font-size: 14px;
    hyphens: auto;
    line-height: 1.5;
    color: #000000;
    word-break: break-word;
    word-wrap: break-word;
    overflow-wrap: break-word;

    p:first-child {
        margin-top: 0;
        line-height: 22px;
    }

    p {
        display: flex;
        margin: 16px 0;
        line-height: 22px;
    }

    p > strong:first-child {
        position: relative;
        flex-shrink: 0;
        width: 86px;
        padding-right: 14px;
        text-align-last: justify;

        &::after {
            position: absolute;
            content: '：';
        }
    }

    strong {
        font-weight: bold;
    }
}
</style>

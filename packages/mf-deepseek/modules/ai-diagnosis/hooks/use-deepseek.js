/* eslint-disable abc/no-timer-id */
import { defineStore } from 'MfBase/pinia';
import { pick1 } from 'MfBase/lodash';
import {
    MATADATA_RENDER_STATUS, DIAGNOSIS_MODEL_TYPE,
} from '../utils/constant';
import { sleep } from '@/common/utils/index';
import deepseekService from '../services/deepseek-service';
import { useDeepseekDataStore } from './use-deepseek-data';
import {
    STREAM_EVENT_TYPE,
    CUSTOM_STREAM_EVENT_TYPES,
} from '../utils/constant';
import StreamController from '../utils/stream-controller';
import {
    isCustomEqual,
    removeObjectEmptyPropertyShallow,
    isArrayContentsEqual,
    filterMedicalRecordEmptyItem,
} from '../utils/helper';
import cloneDeep from 'lodash.clonedeep';

/**
 * Pinia store for DeepSeek state management
 */
export const useDeepseekStore = defineStore('deepSeek', {
    state: () => {
        return {
            // 主要状态
            showDeepseekPage: false,
            isCache: false,
            isReadTips: false,
            currentAbort: null,
            sleepTimer: null,

            // 使用对象代替 reactive
            deepseekResult: {
                thinking: '',
                content: '',
                metadata: {
                    patient: 0,
                    medicalRecord: 0,
                    examResults: 0,
                    outpatientSheetHistories: 0,
                    treatmentHistories: 0,
                    suggestionPromptNames: [],
                },
                loading: false,
                isPreparing: false, // 准备用于基本信息 - thinking 之前
                error: null,
                r1IsCompleted: false,
                v3Loading: false,
            },

            metadataRenderStatus: MATADATA_RENDER_STATUS.NOT_STARTED,
        };
    },

    getters: {
        // 计算属性
        isDeepseekDataValid() {
            const deepseekDataStore = useDeepseekDataStore();
            // 确保正确追踪依赖
            return Boolean(deepseekDataStore.medicalRecord?.chiefComplaint);
        },

        deepseekParamsChangeInfo() {
            const deepseekDataStore = useDeepseekDataStore();

            const {
                lastReq,
            } = deepseekDataStore;

            if (!lastReq) {
                return {
                    changed: false,
                    info: null,
                    isExtraLongInfo: false,
                };
            }

            const changeInfoList = [];


            // 处理患者信息
            if (
                !isCustomEqual(
                    lastReq.patient.id,
                    deepseekDataStore.patientInfo?.id,
                ) ||
                !isCustomEqual(
                    lastReq.patient.sex,
                    deepseekDataStore.patientInfo?.sex,
                ) ||
                !isCustomEqual(
                    removeObjectEmptyPropertyShallow(lastReq.patient.age),
                    removeObjectEmptyPropertyShallow(
                        deepseekDataStore.patientInfo?.age,
                    ),
                ) ||
                !isCustomEqual(
                    lastReq.patient.name,
                    deepseekDataStore.patientInfo?.name,
                ) ||
                !isCustomEqual(
                    removeObjectEmptyPropertyShallow(lastReq.patient.address),
                    removeObjectEmptyPropertyShallow(
                        deepseekDataStore.patientInfo?.address,
                    ),
                )
            ) {
                changeInfoList.push('患者');
            }

            // 处理病历信息
            const {
                chiefComplaint = '',
                presentHistory = '', // 现病史
                pastHistory = '', // 既往史
                familyHistory = '', // 家庭史
                allergicHistory = '', // 过敏史
                personalHistory = '', // 个人史
                obstetricalHistory = '', // 月经婚育史
                physicalExamination = '', // 体格检查
                chineseExamination = '', // 望闻问切
                oralExamination = '',
                dentistryExaminations = null,
                auxiliaryExamination = '',
                auxiliaryExaminations = null,
                pulse = '',
                tongue = '',
                type,
            } = deepseekDataStore.medicalRecord || {};
            const medicalRecordSwitch = deepseekDataStore.medicalRecordSwitch || {};
            const extendInfo = deepseekDataStore.extendInfo || {};

            if (lastReq.medicalRecord.type !== type) {
                changeInfoList.push('病历类型');
            }

            if (
                medicalRecordSwitch.chiefComplaint &&
                !isCustomEqual(
                    lastReq.medicalRecord.chiefComplaint,
                    chiefComplaint,
                )
            ) {
                changeInfoList.push('主诉');
            }

            if (
                medicalRecordSwitch.presentHistory &&
                !isCustomEqual(
                    lastReq.medicalRecord.presentHistory,
                    presentHistory,
                )
            ) {
                changeInfoList.push('现病史');
            }

            if (
                medicalRecordSwitch.pastHistory &&
                !isCustomEqual(lastReq.medicalRecord.pastHistory, pastHistory)
            ) {
                changeInfoList.push('既往史');
            }

            if (
                medicalRecordSwitch.familyHistory &&
                !isCustomEqual(
                    lastReq.medicalRecord.familyHistory,
                    familyHistory,
                )
            ) {
                changeInfoList.push('家族史');
            }

            if (
                medicalRecordSwitch.allergicHistory &&
                !isCustomEqual(
                    lastReq.medicalRecord.allergicHistory,
                    allergicHistory,
                )
            ) {
                changeInfoList.push('过敏史');
            }

            if (
                medicalRecordSwitch.personalHistory &&
                !isCustomEqual(
                    lastReq.medicalRecord.personalHistory,
                    personalHistory,
                )
            ) {
                changeInfoList.push('个人史');
            }

            if (
                medicalRecordSwitch.obstetricalHistory &&
                !isCustomEqual(
                    lastReq.medicalRecord.obstetricalHistory,
                    obstetricalHistory,
                )
            ) {
                changeInfoList.push('月经婚育史');
            }

            if (
                medicalRecordSwitch.physicalExamination &&
                !isCustomEqual(
                    lastReq.medicalRecord.physicalExamination,
                    physicalExamination,
                )
            ) {
                changeInfoList.push('体格检查');
            }

            if (
                medicalRecordSwitch.chineseExamination &&
                !isCustomEqual(
                    lastReq.medicalRecord.chineseExamination,
                    chineseExamination,
                )
            ) {
                changeInfoList.push('望闻切诊');
            }

            if (
                medicalRecordSwitch.pulse &&
                !isCustomEqual(lastReq.medicalRecord.pulse, pulse)
            ) {
                changeInfoList.push('脉像');
            }

            if (
                medicalRecordSwitch.tongue &&
                !isCustomEqual(lastReq.medicalRecord.tongue, tongue)
            ) {
                changeInfoList.push('舌像');
            }

            if (
                medicalRecordSwitch.oralExamination &&
                !isCustomEqual(
                    lastReq.medicalRecord.oralExamination,
                    oralExamination,
                )
            ) {
                changeInfoList.push('口腔检查');
            }

            if (
                medicalRecordSwitch.dentistryExaminations &&
                !isCustomEqual(
                    filterMedicalRecordEmptyItem(lastReq.medicalRecord.dentistryExaminations),
                    filterMedicalRecordEmptyItem(dentistryExaminations),
                )
            ) {
                changeInfoList.push('口腔检查');
            }

            if (
                medicalRecordSwitch.auxiliaryExamination &&
                !isCustomEqual(
                    lastReq.medicalRecord.auxiliaryExamination,
                    auxiliaryExamination,
                )
            ) {
                changeInfoList.push('辅助检查');
            }

            if (
                medicalRecordSwitch.auxiliaryExaminations &&
                !isCustomEqual(
                    filterMedicalRecordEmptyItem(lastReq.medicalRecord.auxiliaryExaminations),
                    filterMedicalRecordEmptyItem(auxiliaryExaminations),
                )
            ) {
                changeInfoList.push('辅助检查');
            }

            // 处理配置信息
            const _rule = pick1(lastReq, [
                'dialecticalSchool',
                'treatmentMethods',
                'chineseMedicineFlavors',
                'diagnosisModel',
            ]);

            if (_rule.diagnosisModel === DIAGNOSIS_MODEL_TYPE.WESTERN) {
                if (_rule.diagnosisModel !== deepseekDataStore.rule.diagnosisModel ||
                    _rule.treatmentMethods !== deepseekDataStore.rule.treatmentMethods) {
                    changeInfoList.push('指令配置');
                }
            } else {
                if (!isCustomEqual(_rule, deepseekDataStore.rule)) {
                    changeInfoList.push('指令配置');
                }
            }

            // 处理检查检验报告
            const {
                examReports = [], examResults = [],
            } = lastReq;
            const _ids = [...examReports, ...examResults].map((item) => item.examinationSheetId);
            if (!isArrayContentsEqual(_ids, extendInfo.examReportFinishedIds || [])) {
                changeInfoList.push('检查检验报告');
            }

            if (changeInfoList.length === 0) {
                return {
                    changed: false,
                    info: null,
                    isExtraLongInfo: false,
                };
            }

            return {
                changed: true,
                info: changeInfoList.join('、'),
                isExtraLongInfo: changeInfoList.length > 2,
            };
        },
    },

    actions: {
        // 初始化读取提示状态
        async initReadTipsStatus() {
            this.isReadTips = await deepseekService.fetchIsReadTips();
        },

        // 标记已读提示
        async markReadTips() {
            this.isReadTips = await deepseekService.markReadTips();
            return this.isReadTips;
        },

        // 获取 Deepseek 参数
        getDeepseekParams() {
            const deepseekDataStore = useDeepseekDataStore();
            const {
                id: medicalRecordId,
                type: medicalRecordType,
                chiefComplaint = '',
                presentHistory = '', // 现病史
                pastHistory = '', // 既往史
                familyHistory = '', // 家庭史
                allergicHistory = '', // 过敏史
                personalHistory = '', // 个人史
                obstetricalHistory = '', // 月经婚育史
                physicalExamination = '', // 体格检查
                chineseExamination = '', // 望闻问切
                oralExamination = '',
                dentistryExaminations = null,
                auxiliaryExamination = '',
                auxiliaryExaminations = null,
                outpatientSheetId = '',
                patientOrderId = '',
                pulse = '',
                tongue = '',
            } = deepseekDataStore.medicalRecord || {};

            const postMedicalRecord = {
                chiefComplaint,
                presentHistory,
                pastHistory,
                familyHistory,
                allergicHistory,
                personalHistory,
                obstetricalHistory,
                physicalExamination,
                chineseExamination,
                oralExamination, // 口腔检查
                dentistryExaminations, // 口腔检查
                auxiliaryExamination, // 辅助检查
                auxiliaryExaminations, // 辅助检查
                pulse,
                tongue,
            };

            Object.keys(postMedicalRecord).forEach((key) => {
                if (!deepseekDataStore.medicalRecordSwitch[key]) {
                    delete postMedicalRecord[key];
                }
            });

            const {
                id: patientId,
                sex = '',
                age = '',
                name = '',
                address = null,
            } = deepseekDataStore.patientInfo || {};

            const {
                dialecticalSchool,
                treatmentMethods,
                chineseMedicineFlavors,
                diagnosisModel,
            } = deepseekDataStore.rule;

            return {
                medicalRecord: {
                    id: medicalRecordId,
                    type: medicalRecordType,
                    outpatientSheetId,
                    patientOrderId,
                    ...postMedicalRecord,
                },
                patient: {
                    id: patientId,
                    name,
                    sex,
                    age,
                    address: address || null,
                },
                diagnosedDate: deepseekDataStore.extendInfo?.diagnosedDate,
                version: 3,
                dialecticalSchool,
                treatmentMethods,
                chineseMedicineFlavors,
                diagnosisModel,
            };
        },

        // 设置错误状态
        setError(error) {
            this.clear();

            this.deepseekResult.error = error;
            this.deepseekResult.loading = false;
            this.deepseekResult.isPreparing = false;
            this.deepseekResult.r1IsCompleted = false;
            this.deepseekResult.v3Loading = false;
            this.metadataRenderStatus = MATADATA_RENDER_STATUS.NOT_STARTED;
        },

        // 初始化 Deepseek 状态
        initDeepseekState() {
            this.showDeepseekPage = true;

            // 初始化状态
            this.deepseekResult.thinking = '';
            this.deepseekResult.content = '';
            this.deepseekResult.loading = true;
            this.deepseekResult.isPreparing = true;
            this.deepseekResult.r1IsCompleted = false;
            this.deepseekResult.v3Loading = false;
            this.deepseekResult.error = null;

            this.metadataRenderStatus = MATADATA_RENDER_STATUS.NOT_STARTED;

            // 再次确保业务数据被清理
            const deepseekDataStore = useDeepseekDataStore();
            const {
                setResultId, setLastReq,
            } = deepseekDataStore;
            setResultId('');
            setLastReq(null);
        },

        changeMetadataRenderStatus(status) {
            this.metadataRenderStatus = status;
        },

        // 处理缓存结果的通用函数
        handleCachedResult(cachedResult) {
            console.log('使用缓存数据', cachedResult);
            const deepseekDataStore = useDeepseekDataStore();
            const {
                setResultId, setLastReq,
            } = deepseekDataStore;
            if (cachedResult && cachedResult.id) {
                setResultId(cachedResult.id);
            }

            if (cachedResult && cachedResult.request) {
                setLastReq(cachedResult.request);
            }

            this.metadataRenderStatus = MATADATA_RENDER_STATUS.COMPLETED;
            this.deepseekResult.thinking = cachedResult.thinking;
            this.deepseekResult.content = cachedResult.content;
            this.deepseekResult.metadata = cachedResult.metadata;
            this.deepseekResult.loading = false;
            this.deepseekResult.isPreparing = false;
            this.deepseekResult.r1IsCompleted = true;
            this.deepseekResult.v3Loading = false;
        },

        // 处理流式请求的通用函数
        async handleFetchDeepseekStream(params) {
            console.log('开始调用流式推理接口');

            const currentFetchParams = cloneDeep(params);

            try {
                const {
                    promise, abort,
                } = deepseekService.getDeepseekStream(
                    params,
                    (data) => {
                        if (!data || data.code !== 200) {
                            console.error('接口错误:', data);
                            return;
                        }

                        const {
                            type, metadata, thinking, answer, done,
                        } =
                            data.data;

                        if (type === STREAM_EVENT_TYPE.METADATA) {
                            this.deepseekResult.metadata = metadata;
                            StreamController.initQueue(
                                metadata.suggestionPromptNames,
                            );
                        }

                        if (type === STREAM_EVENT_TYPE.THINKING && thinking) {
                            this.deepseekResult.thinking += thinking;
                            this.deepseekResult.isPreparing = false;
                        }

                        if (type === STREAM_EVENT_TYPE.THINKING && done) {
                            this.deepseekResult.r1IsCompleted = true;

                            StreamController.start(
                                () => {
                                    // 屏蔽 loading 组件
                                    // this.deepseekResult.v3Loading = true;
                                },
                                (key, chunk) => {
                                    // this.deepseekResult.v3Loading = false;
                                    this.deepseekResult.content += chunk;
                                },
                                () => {
                                    // this.deepseekResult.v3Loading = false;
                                    this.deepseekResult.loading = false;
                                    this.handleSaveDeepseekResult(currentFetchParams);
                                },
                            );
                        }

                        if (CUSTOM_STREAM_EVENT_TYPES.includes(type)) {
                            StreamController.push({
                                key: type,
                                content: answer,
                                done,
                            });
                        }
                    },
                    () => {
                        // 在关闭回调中存入缓存
                        console.log('Stream closed, saving to cache');
                        // 清空当前终止函数
                        this.currentAbort = null;
                    },
                    (error) => {
                        console.error('Stream error:', error);
                        this.setError(error?.message || '请求异常，请稍后重试');
                    },
                );

                // 保存当前终止函数
                this.currentAbort = abort;

                // 等待请求完成
                await promise;
            } catch (error) {
                console.error('Stream error:', error);
                StreamController.destroy();
                this.setError(error?.message || '请求异常，请稍后重试');
            }
        },

        async handleSaveDeepseekResult(params) {
            const res = await deepseekService.saveDeepseekResult({
                medicalRecordDiagnosisReq: {
                    ...params,
                },
                content: this.deepseekResult.content,
                reasoningContent: this.deepseekResult.thinking,
                metadata: this.deepseekResult.metadata,
            });

            const deepseekDataStore = useDeepseekDataStore();
            const {
                setResultId, setLastReq,
            } = deepseekDataStore;

            setResultId(res?.data?.data?.id || '');
            setLastReq(res?.data?.data?.request || null);
        },

        // 开始 Deepseek
        async startDeepseek() {
            this.clear();

            const params = this.getDeepseekParams();
            // 检查缓存
            const cachedResult = await deepseekService.checkDeepseekResult(
                params,
            );
            if (cachedResult) {
                this.isCache = true;
                this.metadataRenderStatus = MATADATA_RENDER_STATUS.IN_PROGRESS;
                this.handleCachedResult(cachedResult);
                return;
            }

            this.isCache = false;
            this.metadataRenderStatus = MATADATA_RENDER_STATUS.IN_PROGRESS;
            await this.handleFetchDeepseekStream(params);
        },

        // 处理 Deepseek 点击
        async handleDeepseekEnter() {
            if (!this.isDeepseekDataValid) {
                return;
            }

            this.initDeepseekState();
            await this.startDeepseek();
        },

        // 重试 Deepseek
        retryDeepseek() {
            this.handleDeepseekEnter();
        },

        // 清理资源
        clear() {
            if (this.currentAbort) {
                console.log('cancel stream');
                this.currentAbort();
                this.currentAbort = null;
            }

            if (this.sleepTimer) {
                this.sleepTimer.cancel();
                this.sleepTimer = null;
            }

            if (StreamController) {
                StreamController.destroy();
            }
        },

        // 取消获取 Deepseek 流
        handleDeepseekDestroy() {
            this.clear();

            this.deepseekResult.error = null;
            this.deepseekResult.loading = false;
            this.deepseekResult.isPreparing = false;
            this.deepseekResult.r1IsCompleted = false;
            this.deepseekResult.v3Loading = false;

            this.metadataRenderStatus = MATADATA_RENDER_STATUS.NOT_STARTED;

            const deepseekDataStore = useDeepseekDataStore();
            const {
                setResultId, setLastReq,
            } = deepseekDataStore;
            setResultId('');
            setLastReq(null);
        },

        // 处理返回
        handleDeepseekBack() {
            // 如果有正在进行的请求，先终止
            this.handleDeepseekDestroy();

            this.showDeepseekPage = false;
        },

        // 处理 Deepseek 缓存
        async handleDeepseekCache() {
            if (!this.isDeepseekDataValid) {
                this.handleDeepseekBack();
                return;
            }

            const deepseekDataStore = useDeepseekDataStore();
            const businessId =
                deepseekDataStore.medicalRecord?.outpatientSheetId;
            if (!businessId) {
                this.handleDeepseekBack();
                return;
            }

            const res = await deepseekService.getDeepseekReqAndResult(
                businessId,
            );
            const lastReqAndResult = res?.data?.data?.rows?.[0];
            if (!lastReqAndResult) {
                this.handleDeepseekBack();
                return;
            }

            this.initDeepseekState();

            this.clear();

            this.sleepTimer = sleep(200);
            await this.sleepTimer.promise;

            this.isCache = true;
            this.metadataRenderStatus = MATADATA_RENDER_STATUS.IN_PROGRESS;
            this.handleCachedResult(lastReqAndResult);
        },

        // 获取 Deepseek 提示和结果
        async getDeepseekPromptAndResult() {
            const params = this.getDeepseekParams();
            const { data } = await deepseekService.fetchPrompt(params);
            console.log(
                '%c deepseek prompt------------',
                'background-color: yellow; color: black; padding: 5px;',
            );
            Object.keys(data).forEach((key) => {
                console.log(
                    `%c ${key}:`,
                    'background-color: #0090ff; color: ffff; padding: 5px;',
                );
                console.log(data[key]);
            });
            console.log(
                '%c deepseek result------------',
                'background-color: yellow; color: black; padding: 5px;',
            );
            console.log(this.deepseekResult.content);
        },

        // 重置 store 状态到初始值
        resetState() {
            this.$reset();
        },
    },
});

import {
    isEqual,
} from 'MfBase/lodash';

export const isCustomEqual = (a, b) => {
    if (!a && !b) return true; // 忽略undefined, null, ''等情况

    return isEqual(a, b);
};

export const removeObjectEmptyPropertyShallow = (obj) => {
    if (!obj) return null;

    const newObj = { ...obj };

    Object.keys(newObj).forEach((key) => {
        if (newObj[key] === null || newObj[key] === undefined || newObj[key] === '') {
            delete newObj[key];
        }
    });

    return newObj;
};

export function isArrayContentsEqual(arr1, arr2) {
    if (!Array.isArray(arr1) || !Array.isArray(arr2)) {
        return false;
    }

    if (arr1.length !== arr2.length) {
        return false;
    }

    // 创建一个副本，避免修改原数组
    const arr2Copy = [...arr2];

    for (const item of arr1) {
        const index = arr2Copy.indexOf(item);
        if (index === -1) {
            return false;
        }
        // 移除已匹配的元素，处理重复值的情况
        arr2Copy.splice(index, 1);
    }

    return true;
}

export const filterMedicalRecordEmptyItem = (arr) => {
    return arr.reduce((acc, curr) => {
        if (curr.toothNos && curr.toothNos.length > 0) {
            acc.push(curr);
        } else if (curr.value) {
            acc.push(curr);
        }

        return acc;
    }, []);
};

<template>
    <div class="deepseek-pre-render-container">
        <nested-stream-renderer
            :rows="rows"
            :enable-stream="!isCache"
            @render-complete="handleRenderComplete"
        >
            <template
                #default="{
                    data
                }"
            >
                <template
                    v-if="data.type === ELEMENT_TYPE.TEXT"
                >
                    <span style="color: var(--abc-color-T2);">{{ data.value }}</span>
                </template>

                <template
                    v-if="data.type === ELEMENT_TYPE.ICON"
                >
                    <abc-icon
                        :icon="data.value"
                        style="margin-left: auto; color: var(--abc-color-G2);"
                    ></abc-icon>
                </template>

                <template
                    v-if="data.type === ELEMENT_TYPE.EMPTY"
                >
                    <span style=" margin-left: auto; color: var(--abc-color-T3);">{{ data.value }}</span>
                </template>
            </template>
        </nested-stream-renderer>
    </div>
</template>

<script>
    import { useDeepseekStore } from '../../hooks/use-deepseek';
    import {
        defineComponent,
    } from 'vue';
    import NestedStreamRenderer from '@/common/components/nested-stream-renderer.vue';
    import { MATADATA_RENDER_STATUS } from '../../utils/constant';
    import { storeToRefs } from 'MfBase/pinia';


    const ELEMENT_TYPE = {
        TEXT: 'text',
        ICON: 'icon',
        EMPTY: 'empty',
    };

    export default defineComponent({
        name: 'PreRender',

        components: {
            NestedStreamRenderer,
        },

        props: {
            metadata: {
                type: Object,
                required: true,
            },
        },

        setup() {
            const store = useDeepseekStore();
            const { changeMetadataRenderStatus } = store;
            const {
                isCache,
            } = storeToRefs(store);

            return {
                changeMetadataRenderStatus,
                isCache,
            };
        },

        data() {
            return {
                ELEMENT_TYPE,
            };
        },

        computed: {
            rows() {
                const list = [
                    {
                        text: '读取患者性别、年龄、地域',
                        value: this.metadata.patient,
                    },
                    {
                        text: '读取患者本次病历',
                        value: this.metadata.medicalRecord,
                    },
                    {
                        text: '读取患者本次检验、检查结果',
                        value: this.metadata.examResults,
                    },
                    {
                        text: '读取患者历史相关病历',
                        value: this.metadata.outpatientSheetHistories,
                    },
                    {
                        text: '读取患者历史处方、治疗方案',
                        value: this.metadata.treatmentHistories,
                    },
                ].filter((o) => o.value >= 0); // 协议定义 0 无，1 有，-1 不展示

                return list.map((item) => {
                    return {
                        elements: item.text
                            .split('')
                            .map((o) => {
                                return {
                                    type: ELEMENT_TYPE.TEXT,
                                    value: o,
                                };
                            })
                            .concat([
                                {
                                    type: item.value ? ELEMENT_TYPE.ICON : ELEMENT_TYPE.EMPTY,
                                    value: item.value ? 's-check-1-line' : '',
                                },
                            ]),
                        customClass: 'pre-render-row',
                    };
                });
            },
        },

        methods: {
            handleRenderComplete() {
                this.changeMetadataRenderStatus(MATADATA_RENDER_STATUS.COMPLETED);
            },
        },
    });
</script>

<style lang="scss">
.deepseek-pre-render-container {
    .pre-render-row {
        align-items: center !important;
    }

    .pre-render-row + .pre-render-row {
        margin-top: 4px;
    }
}
</style>

<template>
    <div
        class="deepseek-markdown-renderer chinese-add-sub-suggestion-stream-renderer"
        v-html="content"
    ></div>
</template>

<script>
    import { defineComponent } from 'vue';
    import { marked } from 'marked';

    export default defineComponent({
        name: 'ChineseAddSubSuggestion',

        props: {
            data: {
                type: Object,
                default: () => ({}),
            },
        },

        computed: {
            content() {
                return this.render(this.data?.content || '');
            },
        },
        
        methods: {
            render(text) {
                try {
                    const cleanText = text.replace(/```/g, '');
                    return marked.parse(cleanText);
                } catch (e) {
                    console.error('Error processing content:', e);
                    return text;
                }
            },
        },
    });
</script>

<style lang="scss">
.chinese-add-sub-suggestion-stream-renderer {
    li::marker {
        display: none;
        content: "";
    }
}

.chinese-add-sub-suggestion-stream-renderer > *:first-child {
    margin-top: 0 !important;
}

.chinese-add-sub-suggestion-stream-renderer > *:last-child {
    margin-bottom: 0 !important;
}
</style>

/**
 * 生成guid
 * @returns {string}
 */
export function createGUID() {
    return 'xxxxxxxx'.replace(/[xy]/g, () => {
        const r = Math.random() * 16 | 0;
        return r.toString(16);
    });
}


class Logger {
    // 全局 traceId 管理
    static globalTraceId = null;

    constructor(prefix = 'DEFAULT', logLevel = 'INFO', options = {}) {
        this.prefix = prefix;
        this.logLevel = logLevel.toUpperCase();

        // 使用传入的 traceId 或生成新的 traceId
        this.traceId = options.traceId || Logger.getTraceId();

        // 定义颜色和样式
        this.styles = {
            TRACE: 'color: #888; font-weight: normal;',
            DEBUG: 'color: gray; font-weight: normal;',
            INFO: 'color: blue; font-weight: normal;',
            WARN: 'color: orange; font-weight: bold;',
            ERROR: 'color: red; font-weight: bold;',
            PREFIX: 'color: green; font-weight: bold;',
            TIMESTAMP: 'color: #888; font-style: italic;',
            TRACE_ID: 'color: purple; font-weight: bold;',
        };

        // 允许自定义样式
        if (options.styles) {
            this.styles = {
                ...this.styles, ...options.styles,
            };
        }

        // 日志级别层级
        this.logLevels = {
            TRACE: -1,
            DEBUG: 0,
            INFO: 1,
            WARN: 2,
            ERROR: 3,
        };
    }

    // 获取 traceId 的静态方法
    static getTraceId() {
        // 优先使用全局 traceId，否则生成新的
        return this.globalTraceId || createGUID();
    }

    // 设置全局 traceId
    static setGlobalTraceId(traceId) {
        this.globalTraceId = traceId;
    }

    // 清除全局 traceId
    static clearGlobalTraceId() {
        this.globalTraceId = null;
    }

    _shouldLog(level) {
        return this.logLevels[level.toUpperCase()] >= this.logLevels[this.logLevel];
    }

    _formatMessage(level, ...args) {
        const timestamp = new Date().toISOString();
        const timestampStyle = this.styles.TIMESTAMP;
        const levelStyle = this.styles[level];
        const prefixStyle = this.styles.PREFIX;
        const traceIdStyle = this.styles.TRACE_ID;

        const formattedMessage = [
            `%c${timestamp}%c %c[${level}]%c %c[${this.prefix}]%c %c[TraceID: ${this.traceId}]%c`,
            timestampStyle, 'color: inherit;',
            levelStyle, 'color: inherit;',
            prefixStyle, 'color: inherit;',
            traceIdStyle, 'color: inherit;',
        ];

        return [...formattedMessage, ...args];
    }

    // 创建子 Logger，继承 traceId
    createChildLogger(prefix, logLevel) {
        return new Logger(prefix, logLevel, {
            traceId: this.traceId,
        });
    }

    trace(...args) {
        if (this._shouldLog('TRACE')) {
            console.trace(...this._formatMessage('TRACE', ...args));
        }
    }

    debug(...args) {
        if (this._shouldLog('DEBUG')) {
            console.log(...this._formatMessage('DEBUG', ...args));
        }
    }

    info(...args) {
        if (this._shouldLog('INFO')) {
            console.log(...this._formatMessage('INFO', ...args));
        }
    }

    warn(...args) {
        if (this._shouldLog('WARN')) {
            console.warn(...this._formatMessage('WARN', ...args));
        }
    }

    error(...args) {
        if (this._shouldLog('ERROR')) {
            console.error(...this._formatMessage('ERROR', ...args));
        }
    }

    // 静态方法创建日志实例
    static create(prefix, logLevel = 'INFO', options = {}) {
        return new Logger(prefix, logLevel, options);
    }
}

export default Logger;

<template>
    <abc-card class="content-card-wrapper" :border="false">
        <!-- <abc-tabs-v2
            v-model="currentTab"
            :option="tabOptions"
            size="large"
            type="border-card"
            @change="onChangeTab"
        ></abc-tabs-v2> -->
        <abc-layout>
            <abc-layout-content>
                <abc-flex>
                    <abc-space :size="6">
                        <abc-date-picker-bar
                            v-model="currentBasicInfoFilter"
                            :options="dateOptions"
                            value-format="YYYY-MM-DD"
                            :picker-options="pickerOptions"
                            :clearable="false"
                            @change="handleDateChange"
                        >
                        </abc-date-picker-bar>
                        <filter-select
                            v-model="params.ecTypeId"
                            placeholder="平台"
                            :width="240"
                            :inner-width="240"
                            :options="ecTypeList"
                            @change="handleEcTypeChange"
                        ></filter-select>
                        <filter-select
                            v-model="params.mallId"
                            placeholder="网店"
                            :width="240"
                            :inner-width="240"
                            :options="mallList"
                            @change="handleMallChange"
                        ></filter-select>
                    </abc-space>
                </abc-flex>

                <abc-row v-abc-loading="summaryLoading" style="margin-top: 16px;" :gutter="[16]">
                    <abc-col
                        v-for="(item, index) in summeryData"
                        :key="index"
                        :span="6"
                    >
                        <abc-statistic
                            variant="outline"
                            :top-title="item.text"
                            :theme="item.theme"
                            :top-content="displayDateRange"
                            :title="`${item.price }`"
                            :content="`日均 ${item.avg}`"
                        >
                        </abc-statistic>
                    </abc-col>
                </abc-row>

                <abc-row :gutter="[16, 16]" :custom-style="{ marginTop: '16px' }">
                    <abc-col :span="12">
                        <abc-card padding-size="large" radius-size="small" style="height: 322px;">
                            <abc-flex>
                                <abc-text bold>
                                    商品销售额 Top 10
                                </abc-text>
                            </abc-flex>

                            <abc-row style="margin-top: 24px;">
                                <abc-col :span="11">
                                    <abc-list
                                        :create-key="() => `${Math.random() }`"
                                        readonly
                                        :data-list="beforeFiveTop"
                                        size="large"
                                        show-divider
                                        :divider-config="{
                                            variant: 'dashed',
                                        }"
                                    >
                                        <template #prepend="{ index }">
                                            <img
                                                v-if="index < 3"
                                                :src="getColorImg(index)"
                                                alt=""
                                                width="16px"
                                                height="“16px”"
                                            />
                                            <abc-text v-else style="width: 16px; text-align: center;">
                                                {{ index + 1 }}
                                            </abc-text>
                                        </template>
                                        <template
                                            #default="{ item }"
                                        >
                                            <abc-flex v-abc-title.ellipsis="item.productName" flex="1" style=" display: inline-block; max-width: 300px;">
                                                {{ item.productName }}
                                            </abc-flex>
                                        </template>
                                        <template
                                            #append="{
                                                item
                                            }"
                                        >
                                            <abc-text theme="gray">
                                                {{ item.amount }}
                                            </abc-text>
                                        </template>
                                    </abc-list>
                                </abc-col>

                                <abc-col :span="2" style="display: flex; justify-content: center;">
                                    <div style="display: block; width: 1px; height: 168px; background: var(--abc-color-P8);"></div>
                                </abc-col>
                                <abc-col :span="11">
                                    <abc-list
                                        :create-key="() => `${Math.random() }`"
                                        readonly
                                        :data-list="afterFiveTop"
                                        size="large"
                                        show-divider
                                        :divider-config="{
                                            variant: 'dashed',
                                        }"
                                    >
                                        <template #prepend="{ index }">
                                            <abc-text style="width: 16px; text-align: center;">
                                                {{ index + 6 }}
                                            </abc-text>
                                        </template>
                                        <template
                                            #default="{ item }"
                                        >
                                            <abc-flex v-abc-title.ellipsis="item.productName" flex="1" style=" display: inline-block; max-width: 300px;">
                                                {{ item.productName }}
                                            </abc-flex>
                                        </template>
                                        <template
                                            #append="{
                                                item
                                            }"
                                        >
                                            <abc-text theme="gray">
                                                {{ item.amount }}
                                            </abc-text>
                                        </template>
                                    </abc-list>
                                </abc-col>
                            </abc-row>
                        </abc-card>
                    </abc-col>
                    <abc-col :span="12">
                        <abc-card
                            padding-size="large"
                            radius-size="small"
                        >
                            <abc-flex justify="space-between" align="center" style="margin-bottom: 10px;">
                                <abc-space
                                    direction="vertical"
                                    align="flex-start"
                                    :size="4"
                                >
                                    <abc-text bold>
                                        实收金额趋势
                                        <abc-tooltip-info :visible-arrow="false" placement="top" content="实收金额=订单金额-退款金额"></abc-tooltip-info>
                                    </abc-text>

                                    <abc-text size="mini" theme="gray">
                                        {{ trendDisplayDateRange }}
                                    </abc-text>
                                </abc-space>
                                <abc-date-picker-bar
                                    v-model="currentAmountTrendFilter"
                                    :options="trendDateOptions"
                                    :picker-options="pickerOptions"
                                    value-format="YYYY-MM-DD"
                                    class="date-picker-bar-amount"
                                    :clearable="false"
                                    @change="handleAmountTrendChange"
                                >
                                </abc-date-picker-bar>
                            </abc-flex>
                            <abc-line-chart
                                id="amount-trend-chart"
                                :date="amountDates"
                                :data="amountData"
                                :legend="{
                                    data: ['金额'],
                                    bottom: -3,
                                    left: '45%',
                                }"
                                series-name="金额"
                                :grid="{
                                    show: true,
                                    borderWidth: 0,
                                    borderColor: '#E6E9EC',
                                    containLabel: true,
                                    top: '3%',
                                    left: '2%',
                                    bottom: '5%',
                                    right: '2%',
                                }"
                                height="221px"
                                :data-zoom-conf="null"
                                line-color="#2680F7"
                                item-color="#2680F7"
                                x-axis-label-count="10"
                                :x-axis-label-rotate="40"
                                width="100%"
                            >
                            </abc-line-chart>
                        </abc-card>
                    </abc-col>
                </abc-row>
            </abc-layout-content>
        </abc-layout>
    </abc-card>
</template>
<script>
    import FilterSelect from 'MfBase/filter-select';
    import { AbcDatePickerBar } from '@abc/ui-pc';
    const { DatePickerBarOptions } = AbcDatePickerBar;
    import ECStatisticsAPI from '@/api/statistics';
    import no1Img from '@/assets/images/statistics/no1-color.png';
    import no2Img from '@/assets/images/statistics/no2-color.png';
    import no3Img from '@/assets/images/statistics/no3-color.png';
    import { formatDate } from '@abc/utils-date';
    import { formatMoney } from '@abc/utils';
    import ECAuthAPI from '@/api/auth';
    import { EcShopTypeEnum } from '@/utils/constants';

    export default {
        components: {
            FilterSelect,
        },
        data() {
            return {
                currentTab: 0,
                tabOptions: [
                    {
                        label: '电商统计',
                        value: 0,
                    },
                ],
                params: {
                    beginDate: '',
                    endDate: '',
                    amountBeginDate: '',
                    amountEndDate: '',
                    ecTypeId: '',
                    mallId: '',
                },
                summaryConfig: {},
                currentBasicInfoFilter: DatePickerBarOptions.DAY.label,
                pickerOptions: {
                    disabledDate(date) {
                        return date > new Date();
                    },
                },
                mallList: [],
                ecTypeList: [],
                dateOptions: [
                    {
                        label: DatePickerBarOptions.DAY.label,
                        name: DatePickerBarOptions.DAY.name,
                        getValue() {
                            return [new Date(), new Date()];
                        },
                    },
                    DatePickerBarOptions.WEEK,
                    DatePickerBarOptions.MONTH,
                    DatePickerBarOptions.YEAR,
                ],
                currentAmountTrendFilter: DatePickerBarOptions.LATEST_MONTH.label,
                trendDateOptions: [
                    DatePickerBarOptions.LATEST_MONTH,
                    DatePickerBarOptions.LATEST_THREE_MONTH,
                    DatePickerBarOptions.LATEST_HALF_YEAR,
                    DatePickerBarOptions.MONTHLY,
                ],
                amountDates: [],
                amountData: [],
                summaryLoading: false,
                dataList: [],
            };
        },
        computed: {
            beforeFiveTop() {
                return this.dataList?.slice(0, 5);
            },
            afterFiveTop() {
                return this.dataList?.slice(5);
            },
            displayDateRange() {
                const {
                    beginDate, endDate,
                } = this.params;
                if (beginDate === endDate) {
                    return beginDate;
                }
                return `${beginDate} ~ ${endDate}`;
            },
            trendDisplayDateRange() {
                const {
                    amountBeginDate, amountEndDate,
                } = this.params;
                if (amountBeginDate === amountEndDate) {
                    return amountBeginDate;
                }
                return `${amountBeginDate} ~ ${amountEndDate}`;
            },
            summeryData() {
                const {
                    orderNumber = 0,
                    averagePerDayOrderNumber = 0,
                    orderAmount = 0,
                    averagePerDayOrderAmount = 0,
                    refundOrderNumber = 0,
                    averagePerDayRefundOrderNumber = 0,
                    refundOrderAmount = 0,
                    averagePerDayRefundOrderAmount = 0,
                } = this.summaryConfig || {};
                return [
                    {
                        text: '订单数',
                        dateRange: this.displayDateRange,
                        price: orderNumber,
                        avg: formatMoney(averagePerDayOrderNumber),
                        theme: 'default',
                    },
                    {
                        text: '订单金额',
                        dateRange: this.displayDateRange,
                        price: formatMoney(orderAmount),
                        avg: formatMoney(averagePerDayOrderAmount),
                        theme: 'warning',
                    },
                    {
                        text: '退款单数',
                        dateRange: this.displayDateRange,
                        price: refundOrderNumber,
                        avg: formatMoney(averagePerDayRefundOrderNumber),
                        theme: 'default',
                    },
                    {
                        text: '退款金额',
                        dateRange: this.displayDateRange,
                        price: formatMoney(refundOrderAmount),
                        avg: formatMoney(averagePerDayRefundOrderAmount),
                        theme: 'warning',
                    },

                ];
            },
        },
        created() {
            const [start, end] = DatePickerBarOptions.LATEST_MONTH.getValue();
            const monthStart = formatDate(start);
            const monthEnd = formatDate(end);
            this.params.amountBeginDate = monthStart;
            this.params.amountEndDate = monthEnd;
            this.params.beginDate = formatDate(new Date());
            this.params.endDate = formatDate(new Date());
            this.getMallList();
            this.getOrderSelectionData();
            this.getAmountTrend();
            this.getOrderSummaryData();
            this.getSaleTopData();
        },
        methods: {
            onChangeTab() { },
            handleDateChange(date) {
                this.params.beginDate = date[ 0 ];
                this.params.endDate = date[ 1 ];
                this.getOrderSelectionData();
                this.getOrderSummaryData();
                this.getSaleTopData();
            },
            handleAmountTrendChange(date) {
                this.params.amountBeginDate = date[ 0 ];
                this.params.amountEndDate = date[ 1 ];
                this.getAmountTrend();
            },
            handleEcTypeChange() {
                this.getOrderSelectionData();
                this.getAmountTrend();
                this.getOrderSummaryData();
                this.getSaleTopData();
            },
            handleMallChange() {
                this.getOrderSelectionData();
                this.getOrderSummaryData();
                this.getAmountTrend();
                this.getSaleTopData();
            },

            getColorImg(index) {
                switch (index) {
                    case 0:
                        return no1Img;
                    case 1:
                        return no2Img;
                    case 2:
                        return no3Img;
                    default:
                        return '';
                }
            },

            async getAmountTrend() {
                try {
                    let groupBy = 'day';
                    if (this.currentAmountTrendFilter === DatePickerBarOptions.MONTHLY.label) {
                        groupBy = 'month';
                    }
                    const data = await ECStatisticsAPI.getPriceTrend({
                        beginDate: this.params.amountBeginDate,
                        endDate: this.params.amountEndDate,
                        ecTypeId: this.params.ecTypeId,
                        mallId: this.params.mallId,
                        groupBy,
                    });
                    if (data.length) {
                        const list = [
                            {
                                name: '金额',
                                type: 'line',
                                data: data.map((item) => item.amount) || [],
                                itemStyle: {
                                    normal: {
                                        color: '#55BFC0',
                                    },
                                },
                                lineStyle: {
                                    normal: {
                                        color: '#55BFC0',
                                        width: 1,
                                    },
                                },
                            },
                        ];
                        this.amountData = list || [];
                        this.amountDates = data.map((item) => item.date) || [];
                    }
                } catch (e) {
                    console.log(e);
                    this.amountData = [];
                    this.amountDates = [];
                }
            },
            async getSaleTopData() {
                try {
                    const res = await ECStatisticsAPI.getSaleTop({
                        beginDate: this.params.beginDate,
                        endDate: this.params.endDate,
                        ecTypeId: this.params.ecTypeId,
                        mallId: this.params.mallId,
                    });
                    this.dataList = res;
                } catch (e) {
                    console.log(e);
                }
            },

            async getOrderSelectionData() {
                try {
                    const res = await ECStatisticsAPI.getOrderSelection({
                        beginDate: this.params.beginDate,
                        endDate: this.params.endDate,
                        ecTypeId: this.params.ecTypeId,
                        mallId: this.params.mallId,
                    });
                    this.ecTypeList = res.ecTypeList.map((item) => {
                        const key = Object.keys(item)[0];
                        const name = item[key];
                        return {
                            name,
                            id: key,
                        };
                    });
                } catch (e) {
                    console.log(e);
                }
            },
            async getMallList() {
                try {
                    const res = await ECAuthAPI.fetchBindAuthList({
                        includeHistory: 1,
                    });
                    this.mallList = res.rows.reduce((pre, cur) => {
                        if (pre.find((item) => item.id === cur.ecMallId)) return pre;
                        pre.push({
                            name: cur.mallName,
                            id: cur.ecMallId,
                            shopType: cur.shopType,
                        });
                        return pre;
                    }, []).filter((item) => item.shopType === EcShopTypeEnum.B2C);
                } catch (e) {
                    console.log(e);
                }
            },
            async getOrderSummaryData() {
                try {
                    this.summaryLoading = true;
                    const res = await ECStatisticsAPI.getOrderSummary({
                        beginDate: this.params.beginDate,
                        endDate: this.params.endDate,
                        ecTypeId: this.params.ecTypeId,
                        mallId: this.params.mallId,
                    });
                    this.summaryConfig = res;
                } catch (e) {
                    console.log(e);
                } finally {
                    this.summaryLoading = false;
                }
            },
        },
    };
</script>

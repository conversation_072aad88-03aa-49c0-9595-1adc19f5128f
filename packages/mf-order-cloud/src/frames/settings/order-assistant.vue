<template>
    <abc-layout class="setting-desktop-assistant-wrapper">
        <abc-layout-content style="overflow-y: auto;">
            <abc-section>
                <abc-manage-page :label-width="132" style="padding: 0;">
                    <abc-form ref="deliverSetting">
                        <abc-manage-group>
                            <abc-manage-item
                                label="桌面助手"
                                :label-style="{
                                    'display': 'inline',
                                }"
                                :align="'center'"
                            >
                                <abc-manage-layout>
                                    <abc-manage-tip tip="开启后，桌面助手（如下图所示）将常驻本台电脑桌面最前端，可实时查看待处理订单及异常预警">
                                        <abc-checkbox
                                            v-model="params.isEnableDesktopAssistant"
                                            type="number"
                                            control
                                            @click="onClick('isEnableDesktopAssistant')"
                                        >
                                            开启
                                        </abc-checkbox>
                                    </abc-manage-tip>
                                </abc-manage-layout>
                            </abc-manage-item>

                            <abc-manage-item>
                                <abc-image :width="705" :height="115" :src="BarImg"></abc-image>
                            </abc-manage-item>
                        </abc-manage-group>
                    </abc-form>
                    <template slot="footer">
                        <abc-button :disabled="!isUpdated" @click="onClickSave">
                            保存
                        </abc-button>
                    </template>
                </abc-manage-page>
            </abc-section>

            <!--            <abc-section>-->
            <!--                <abc-button-->
            <!--                    variant="ghost"-->
            <!--                    type="text"-->
            <!--                    @click="toggleDesktopAssistant"-->
            <!--                >-->
            <!--                    {{ isEnableDesktopAssistant ? '关闭桌面助手' : '打开桌面助手' }}-->
            <!--                </abc-button>-->

            <!--                <abc-flex vertical gap="large">-->
            <!--                    <abc-title>授权相关</abc-title>-->
            <!--                    <abc-flex align="center">-->
            <!--                        <abc-space size="large">-->
            <!--                            <div>-->
            <!--                                <abc-button variant="ghost" theme="success" @click="requestAuth">-->
            <!--                                    请求授权-->
            <!--                                </abc-button>-->
            <!--                                <abc-button variant="ghost" theme="success" @click="cancelAuth">-->
            <!--                                    取消授权-->
            <!--                                </abc-button>-->
            <!--                                <abc-button variant="ghost" theme="success" @click="fetchAuthStatus">-->
            <!--                                    获取授权状态-->
            <!--                                </abc-button>-->
            <!--                            </div>-->
            <!--                            <div>PDD 授权状态: {{ pddData.authStatus }}</div>-->
            <!--                        </abc-space>-->
            <!--                    </abc-flex>-->
            <!--                    <abc-title>业务接口</abc-title>-->
            <!--                    <abc-flex>-->
            <!--                        <abc-button @click="fetchUserInfo">-->
            <!--                            获取用户信息-->
            <!--                        </abc-button>-->
            <!--                        <abc-button @click="fetchUserSetting">-->
            <!--                            获取用户设置-->
            <!--                        </abc-button>-->
            <!--                        <abc-button @click="fetchExceptionList">-->
            <!--                            获取异常列表-->
            <!--                        </abc-button>-->
            <!--                        <abc-button @click="fetchOrderTrace">-->
            <!--                            获取物流预警-->
            <!--                        </abc-button>-->
            <!--                        <abc-button @click="fetchAfterSalesOrderList">-->
            <!--                            获取售后列表-->
            <!--                        </abc-button>-->
            <!--                    </abc-flex>-->
            <!--                    <abc-tips-card>-->
            <!--                        用户信息{{ pddData.userInfo }}-->
            <!--                    </abc-tips-card>-->
            <!--                    <abc-tips-card>-->
            <!--                        用户设置{{ pddData.userSetting }}-->
            <!--                    </abc-tips-card>-->
            <!--                    <abc-tips-card>-->
            <!--                        异常列表{{ pddData.exceptionList }}-->
            <!--                    </abc-tips-card>-->
            <!--                    <abc-tips-card>-->
            <!--                        物流预警{{ pddData.orderTrace }}-->
            <!--                    </abc-tips-card>-->
            <!--                    <abc-tips-card>-->
            <!--                        售后列表{{ pddData.afterSalesOrderList }}-->
            <!--                    </abc-tips-card>-->

            <!--                    <abc-title>-->
            <!--                        打开页面-->
            <!--                    </abc-title>-->

            <!--                    <abc-flex v-for="page in pageGroups" :key="page[0].url">-->
            <!--                        <abc-tabs-v2-->
            <!--                            v-model="curPage"-->
            <!--                            :option="page"-->
            <!--                            size="small"-->
            <!--                            type="outline"-->
            <!--                            @change="handleOpenPage"-->
            <!--                        ></abc-tabs-v2>-->
            <!--                    </abc-flex>-->
            <!--                </abc-flex>-->
            <!--            </abc-section>-->
        </abc-layout-content>
    </abc-layout>
</template>

<script type="text/ecmascript-6">
    import { OrderCloudDaemonService } from '../../daemon';
    import { PddPage } from '../../daemon/crawler/common/constants';
    import {
        AbcManageGroup,
        AbcManageItem,
        AbcManageLayout,
        AbcManagePage,
        AbcManageTip,
    } from 'MfBase/abc-manage';
    import { isEqual } from 'MfBase/lodash';
    import AbcPlayer from 'MfBase/abc-player';
    import BarImg from '@/assets/images/setting/bar.png';
    import { clone } from '@abc/utils';
    import DialogDownloadClient from '../../components/dialog-download-client/index.js';
    import { isClientSupportPharmacy } from '../../utils/electron';
    export default {
        name: 'DesktopAssistantSetting',
        components: {
            AbcManageGroup,
            AbcManageItem,
            AbcManageLayout,
            AbcManagePage,
            AbcManageTip,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        data() {
            return {
                params: {
                    isEnableDesktopAssistant: 0,
                },
                cacheParams: {
                    isEnableDesktopAssistant: 0,
                },
                BarImg,
                isOrderNewPlaying: false,
                isOrderAfterSalePlaying: false,

                isEnableDesktopAssistant: false,
                pddData: {
                    authStatus: -1,
                    exceptionList: {},
                    orderTrace: {},
                    userInfo: {},
                    userSetting: {},
                    afterSalesOrderList: {},
                },
                curPage: '',
            };
        },
        computed: {
            pageGroups() {
                const map = new Map();
                Object.keys(PddPage)
                    .map((key) => {
                        return {
                            label: PddPage[key].name,
                            value: key,
                            url: PddPage[key].url,
                        };
                    }).forEach((it) => {
                        let arr = map.get(it.url);
                        if (!arr) {
                            arr = [];
                            map.set(it.url, arr);
                        }
                        arr.push(it);
                    });
                return [...map.values()];
            },
            // 页面发生变化
            isUpdated() {
                return !isEqual(this.params, this.cacheParams);
            },
            orderCloudConfigInit() {
                return this.$abcPage.$store.state.orderCloudConfigInit;
            },
        },
        watch: {
            orderCloudConfigInit: {
                handler(val) {
                    if (val) {
                        if (isClientSupportPharmacy()) {
                            this.cacheParams = clone(this.$abcPage.$store.orderCloudConfig.orderAssistantSetting);
                            this.params = clone(this.$abcPage.$store.orderCloudConfig.orderAssistantSetting);
                        }
                    }
                },
                immediate: true,
            },
        },
        async created() {
            this._waitOrderNewVoicePlay = new AbcPlayer(require('@/assets/audio/order-new.mp3'));
            this._waitOrderAfterSaleVoicePlay = new AbcPlayer(require('@/assets/audio/after-sale.mp3'));

            this.desktopAssistantManager = OrderCloudDaemonService.getInstance().getDesktopAssistManager();
            this.crawlerManager = OrderCloudDaemonService.getInstance().getCrawlerManager();
            if (isClientSupportPharmacy()) {
                this.isEnableDesktopAssistant = this.desktopAssistantManager.isDesktopAssistantEnable();
            }

            // this.fetchAuthStatus();
        },
        destroyed() {
            this.destroyElement();
        },
        methods: {
            async toggleDesktopAssistant() {
                if (this.isEnableDesktopAssistant) {
                    this.desktopAssistantManager.disableDesktopAssistant();
                } else {
                    this.desktopAssistantManager.enableDesktopAssistant();
                }
                this.isEnableDesktopAssistant = this.desktopAssistantManager.isDesktopAssistantEnable();
            },
            async requestAuth() {
                await this.crawlerManager.requestAuth();
            },
            async cancelAuth() {
                await this.crawlerManager.cancelAuth();
            },
            async fetchAuthStatus() {
                const status = await this.crawlerManager.getAuthStatus();
                this.pddData.authStatus = status;
            },
            async fetchExceptionList() {
                const response = await this.crawlerManager.getExceptionList();
                if (response.status === false) {
                    this.$Toast({
                        message: response.message,
                    });
                } else {
                    this.pddData.exceptionList = response.data;
                }
            },
            async fetchOrderTrace() {
                const response = await this.crawlerManager.getExpressWarn();
                if (response.status === false) {
                    this.$Toast({
                        message: response.message,
                    });
                } else {
                    this.pddData.orderTrace = response.data;
                }
            },
            async fetchUserInfo() {
                const response = await this.crawlerManager.getUserInfo();
                if (response.status === false) {
                    this.$Toast({
                        message: response.message,
                    });
                } else {
                    this.pddData.userInfo = response.data;
                }
            },
            async fetchUserSetting() {
                const response = await this.crawlerManager.getUserSetting();
                if (response.status === false) {
                    this.$Toast({
                        message: response.message,
                    });
                } else {
                    this.pddData.userSetting = response.data;
                }
            },

            async fetchAfterSalesOrderList() {
                const response = await this.crawlerManager.getAfterSalesOrderList();
                if (response.status === false) {
                    this.$Toast({
                        message: response.message,
                    });
                } else {
                    this.pddData.afterSalesOrderList = response.data;
                }
            },

            openAuth() {
                this.crawlerManager.requestAuth();
            },

            handleOpenPage() {
                this.crawlerManager.openPage(PddPage[this.curPage]);
            },

            async onClickSave() {
                this.$refs.deliverSetting.validate(async (valid) => {
                    if (valid) {
                        await this.$abcPage.$store.updateOrderAssistantSetting({
                            orderAssistantSetting: this.params,
                        });
                        this.$Toast({
                            type: 'success',
                            message: '保存成功',
                        });
                        this.cacheParams = clone(this.params);
                        if (this.params.isEnableDesktopAssistant) {
                            this.desktopAssistantManager.enableDesktopAssistant();
                        } else {
                            this.desktopAssistantManager.disableDesktopAssistant();
                        }
                    }
                });
            },

            destroyElement() {
                this._waitOrderNewVoicePlay && this._waitOrderNewVoicePlay.destroy();
                this._waitOrderNewVoicePlay = null;
                this._waitOrderAfterSaleVoicePlay && this._waitOrderAfterSaleVoicePlay.destroy();
                this._waitOrderAfterSaleVoicePlay = null;
            },

            onClick(type) {
                // 浏览器关闭
                if (!isClientSupportPharmacy()) {
                    new DialogDownloadClient().generateDialogAsync();
                    return;
                }
                if (this.params.hasOwnProperty(type)) {
                    this.params[type] = !this.params[type] ? 1 : 0;
                }
            },
        },
    };
</script>


<style lang="scss">
    .setting-desktop-assistant-wrapper {
        .template-card-wrapper {
            display: inline-flex;
            flex-direction: column;
            align-items: flex-start;
            width: 470px;
            height: 262px;
            margin-right: 20px;
            margin-bottom: 20px;
            border: 1px solid var(--abc-color-P7);
            border-radius: 6px;

            .card-title {
                padding: 16px 16px 0;

                .wp-logo {
                    display: flex;
                    align-items: center;
                    line-height: 24px;
                }
            }

            .card-body {
                flex: 1;
                width: 100%;
                height: 0;
            }

            .card-footer {
                width: 100%;
                padding: 12px 16px;
                background: var(--abc-color-AbcDivGrey);
            }
        }

        .trumpet-img {
            width: 18px;
            height: 16px;
            margin-left: 8px;
            cursor: pointer;
            background-image: url("~assets/images/voice/trumpet-grey.png");
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;

            &:hover {
                background-image: url("~assets/images/voice/trumpet-blue-0.png");
            }

            &.playing-trumpet {
                animation: auto-play 0.8s infinite;
            }
        }

        @keyframes auto-play {
            0% {
                background-image: url("~assets/images/voice/trumpet-blue-1.png");
            }

            50% {
                background-image: url("~assets/images/voice/trumpet-blue-2.png");
            }

            100% {
                background-image: url("~assets/images/voice/trumpet-blue-3.png");
            }
        }
    }
</style>



<template>
    <abc-card class="content-card-wrapper" :border="false">
        <abc-layout has-sidebar class="abc-order-cloud-settings-container">
            <abc-layout-sidebar class="setting-sidebar" :width="241">
                <abc-menu
                    v-model="menuValue"
                    badge-variant="dot"
                    @click="handleTabChange"
                >
                    <div v-for="item in settingMenuList" :key="item.path">
                        <template v-if="!item.hide">
                            <abc-text theme="gray" tag="div" style="height: 34px; padding: 8px 0 4px 12px;">
                                {{ item.label }}
                            </abc-text>
                            <template v-for="child in item.children">
                                <abc-tooltip
                                    v-if="!child.hide"
                                    :key="child.name"
                                    placement="bottom"
                                    :open-delay="500"
                                    :visible-arrow="false"
                                    custom-popper-class="abc-order-cloud-custom-menu-tooltip-content"
                                    content="请先开通和安装订单云"
                                    :disabled="!(menuDisabled || child.disabled) || child.path === 'setting/service-intro'"
                                >
                                    <abc-menu-item
                                        :icon="child.icon"
                                        :index="child.name"
                                        :disabled-item="(menuDisabled || child.disabled) && child.path !== 'setting/service-intro'"
                                    >
                                        {{ child.label }}
                                    </abc-menu-item>
                                </abc-tooltip>
                            </template>
                        </template>
                    </div>
                </abc-menu>
            </abc-layout-sidebar>
            <abc-layout-content style="padding: 0; overflow: auto;">
                <router-view></router-view>
            </abc-layout-content>
        </abc-layout>
    </abc-card>
</template>

<script type="text/ecmascript-6">
    // 自定义组件
    import {
        OrderCloudModuleId,PharmacyOrderCloudRouterNameKeys,
    } from '../../core/routes';
    import { EditionKeyEnum } from 'MfBase/access-constant';
    import { mapGetters } from 'vuex';
    export default {
        name: 'OrderCloudSetting',
        inject: {
            $abcPage: {
                default: {},
            },
        },
        data() {
            return {
                iconSize: 16,
                parentPath: 'order-cloud',
                menuValue: PharmacyOrderCloudRouterNameKeys.ecSettingsServiceIntro,
            };
        },
        computed: {
            ...mapGetters([

                'settingTodo',
                'currentEdition',
                'isChainAdmin',
                'isChainSubStore',
                'userInfo',
            ]),
            installChromeStatus() {
                return this.$abcPage?.$store?.state?.installChromeStatus;
            },
            // 基础版
            isBasic() {
                return this.currentEdition.key === EditionKeyEnum.BASIC;
            },
            // menu 禁用判断
            menuDisabled() {
                return this.isBasic;
            },
            moduleIds() {
                return (this.userInfo && this.userInfo.moduleIds) || '';
            },
            moduleArr() {
                if (!this.moduleIds) {
                    return [];
                }
                return this.moduleIds.split(',');
            },
            hasSettingAllModule() {
                if (!this.moduleIds) {
                    return false;
                }
                return this.moduleIds === '0' || this.moduleArr.includes(OrderCloudModuleId.main) || this.moduleArr.includes(OrderCloudModuleId.setting);
            },
            settingMenuList() {
                if (this.menuDisabled || this.hasSettingAllModule) {
                    return [
                        {
                            label: '初始化设置',
                            showRedDot: false,
                            children: [
                                {
                                    label: '开通订单云',
                                    name: PharmacyOrderCloudRouterNameKeys.ecSettingsServiceIntro,
                                    path: 'setting/service-intro',
                                    icon: 's-cloud-line',
                                    showRedDot: false,
                                },
                            ],
                        },
                        {
                            label: '外卖设置',
                            showRedDot: false,
                            children: [
                                {
                                    label: '接入外卖网店',
                                    name: PharmacyOrderCloudRouterNameKeys.ecSettingsTakeawayOnlineStore,
                                    path: 'setting/takeaway-online-store',
                                    icon: 's-commodity-line',
                                    showRedDot: false,
                                },
                                {
                                    label: '外卖出库设置',
                                    name: PharmacyOrderCloudRouterNameKeys.ecSettingsTakeawayOut,
                                    path: 'setting/takeaway-out',
                                    icon: 's-out-line',
                                    hide: this.isChainAdmin,
                                    disabled: !this.installChromeStatus,
                                    showRedDot: false,
                                },
                            ],
                        },
                        {
                            label: '电商设置',
                            showRedDot: false,
                            children: [
                                {
                                    label: '绑定电商网店',
                                    name: PharmacyOrderCloudRouterNameKeys.ecSettingsEcommerceOnlineStore,
                                    path: 'setting/ecommerce-online-store',
                                    icon: 's-store-line',
                                    hide: this.isChainSubStore,
                                    showRedDot: false,
                                },
                                {
                                    label: '订单提醒',
                                    name: PharmacyOrderCloudRouterNameKeys.ecSettingOrderReminder,
                                    path: 'setting/order-reminder',
                                    icon: 's-notice-line',
                                    hide: this.isChainAdmin,
                                    showRedDot: false,
                                },
                                {
                                    label: '打单发货',
                                    name: PharmacyOrderCloudRouterNameKeys.ecSettingsDeliver,
                                    path: 'setting/deliver',
                                    icon: 's-truck-line',
                                    hide: this.isChainAdmin,
                                    showRedDot: false,
                                },
                                {
                                    label: '快递面单',
                                    name: PharmacyOrderCloudRouterNameKeys.ecSettingsPrint,
                                    path: 'setting/waybill-print-setting',
                                    icon: 'n-preferential-line',
                                    hide: this.isChainAdmin,
                                    showRedDot: false,
                                },
                                {
                                    label: '发货单',
                                    name: PharmacyOrderCloudRouterNameKeys.ecSettingsShipPrint,
                                    path: 'setting/ship-print-setting',
                                    icon: 'n-print-line',
                                    hide: this.isChainAdmin,
                                    showRedDot: false,
                                },
                                {
                                    label: '电商出库',
                                    name: PharmacyOrderCloudRouterNameKeys.ecSettingsEcommerceOut,
                                    path: 'setting/ecommerce-out',
                                    icon: 's-out-line',
                                    hide: this.isChainAdmin,
                                    showRedDot: false,
                                },
                            ],
                        },
                        {
                            label: '通用设置',
                            showRedDot: false,
                            hide: this.isChainAdmin,
                            children: [
                                {
                                    label: '订单助手',
                                    name: PharmacyOrderCloudRouterNameKeys.ecSettingDesktopAssistant,
                                    path: 'setting/order-assistant',
                                    icon: 's-plugiline-line',
                                    hide: this.isChainAdmin,
                                    showRedDot: false,
                                },
                            ],
                        },
                    ];
                }
                return [
                    {
                        label: '初始化设置',
                        showRedDot: false,
                        children: [
                            {
                                label: '开通订单云',
                                name: PharmacyOrderCloudRouterNameKeys.ecSettingsServiceIntro,
                                path: 'setting/service-intro',
                                icon: 's-cloud-line',
                                showRedDot: false,
                            },
                        ],
                    },
                ];
            },
        },
        created() {
            this.initRouteAndTab();
        },
        methods: {
            handleClickBack() {
                this.$router.go(-1);
            },
            redirectTo(name) {
                this.$router.push({
                    name,
                });
            },
            comparePath(val) {
                const { path } = this.$route;
                return path.includes(`/${this.parentPath}/${val.path}`);
            },
            handleTabChange(value) {
                this.$router.push({
                    name: value,
                });
            },
            initRouteAndTab() {
                // 获取所有可用的子路由选项
                const allMenuItems = [];
                this.settingMenuList.forEach((menuGroup) => {
                    if (menuGroup.children && menuGroup.children.length > 0) {
                        menuGroup.children.forEach((item) => {
                            allMenuItems.push(item);
                        });
                    }
                });

                // 检查当前路由是否是设置页面的根路由
                if (this.$route.name === PharmacyOrderCloudRouterNameKeys.ecSettings) {
                    // 如果是根路由，则跳转到第一个可用的子路由
                    const firstMenuItem = allMenuItems[0];
                    if (firstMenuItem) {
                        this.$router.push({
                            name: firstMenuItem.name,
                        });
                        this.menuValue = firstMenuItem.name;
                    }
                } else {
                    // 检查当前路由是否在菜单项中
                    const currentRouteInMenu = allMenuItems.some((item) => item.name === this.$route.name);

                    if (!currentRouteInMenu) {
                        // 如果当前路由不在菜单项中，则跳转到第一个可用的子路由
                        const firstMenuItem = allMenuItems[0];
                        if (firstMenuItem) {
                            this.$router.push({
                                name: firstMenuItem.name,
                            });
                            this.menuValue = firstMenuItem.name;
                        }
                    } else {
                        // 如果当前路由在菜单项中，则更新当前选中的菜单项
                        this.menuValue = this.$route.name;
                    }
                }
            },
        },
    };
</script>

<style lang="scss">
    .abc-order-cloud-settings-container {
        height: 100%;

        .setting-sidebar {
            padding: 12px 10px;
            background-color: #f9fafc;
            border-right: 1px solid var(--abc-color-P7);
            border-radius: 8px 0 0 8px;

            .back-title {
                padding-left: 10px;
                margin-bottom: 8px;
            }

            & ul li {
                position: relative;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                height: 40px;
                font-size: 0;
                line-height: 40px;
                cursor: pointer;
                border-radius: 6px;

                & + li {
                    margin-top: 4px;
                }

                &:hover {
                    background: var(--abc-color-P8);
                }

                span {
                    font-size: 14px;
                }

                .icon {
                    margin: 0 12px;
                }

                .red-dot {
                    box-sizing: border-box;
                    width: 6px;
                    height: 6px;
                    margin-left: 6px;
                    background-color: var(--abc-color-R2);
                    border-radius: var(--abc-border-radius-small);
                }

                .icon i,
                .abc-icon {
                    font-size: 14px;
                    color: var(--abc-color-T2);
                }

                > img {
                    width: 14px;
                    height: 14px;
                    margin: 0 16px 0 24px;
                }
            }

            & ul {
                height: 100%;
                padding: 8px 10px;
                overflow-x: hidden;
                overflow-y: auto;
                overflow-y: overlay;
                border-radius: 4px 0 0 4px;

                .line {
                    height: 0;
                    margin: 8px 0;
                    border-bottom: 1px solid var(--abc-color-P8);
                }

                .active {
                    position: relative;
                    color: #ffffff;
                    background: var(--abc-color-theme3);

                    .icon i,
                    .abc-icon {
                        color: #ffffff;
                    }

                    &:hover {
                        background: var(--abc-color-theme3);
                    }

                    & > .icon {
                        font-weight: normal;
                    }
                }
            }
        }

        .order-cloud-back-btn {
            position: absolute;
            top: 8px;
            right: 20px;
        }

        .setting-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px var(--abc-paddingLR-xll, 20px);
            line-height: 24px; /* 150% */
            color: var(--abc-color-T1, #000000);
            background: var(--abc-color-cp-white);
            border-bottom: 1px solid var(--abc-color-P8);
            border-radius: 0 var(--abc-border-radius-medium) 0 0;

            h2 {
                font-size: 16px;
                font-weight: bold;
            }
        }
    }

    .abc-order-cloud-custom-menu-tooltip-content {
        padding: 4px 10px;
        font-size: 12px;
        line-height: 1.5;
        color: white;
        text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.2);
        white-space: nowrap;
        background-color: rgba(#000000, 0.9);
        border: 0;
        border-radius: var(--abc-border-radius-small);
        box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
    }
</style>

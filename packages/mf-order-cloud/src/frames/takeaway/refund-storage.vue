<template>
    <abc-layout
        v-abc-loading="loading"
        preset="page-table"
        style="height: calc(100% - 48px);"
        class="order-cloud-refund-storage"
    >
        <abc-layout-header>
            <abc-flex vertical gap="large">
                <abc-tips-card-v2 v-if="!isChainAdmin && !loading && currentMall.status !== 1" align="center">
                    <abc-space>
                        {{ currentMall.status === 2 ? '当前网店授权已失效，无法同步数据，请重新授权' : '当前未绑定网店，无法同步数据，请重新绑定' }}
                        <abc-link @click="handleEcAuthorized(currentMall.status === 2 ? '2' : '1')">
                            {{ currentMall.status === 2 ? '立即授权' : '绑定网店' }}
                        </abc-link>
                    </abc-space>
                </abc-tips-card-v2>
                <abc-flex align="center">
                    <abc-space>
                        <abc-space is-compact>
                            <abc-select
                                v-model="params.ecType"
                                :width="92"
                                trigger-icon="s-triangle-select-color"
                                :input-style="{ fontWeight: 600 }"
                            >
                                <abc-image
                                    slot="prepend"
                                    :src="WpCodeLogo.MT"
                                    :width="16"
                                ></abc-image>
                                <abc-option
                                    v-for="(op) in ecTypeOptions"
                                    :key="op.value"
                                    :value="op.value"
                                    :label="op.label"
                                ></abc-option>
                            </abc-select>
                            <biz-select-tabs
                                v-model="params.ecMallId"
                                :options="mallList"
                                :width="240"
                                :max-width="240"
                            >
                            </biz-select-tabs>
                        </abc-space>
                        <abc-date-picker
                            v-model="datePickerValue"
                            :picker-options="pickerOptions"
                            type="daterange"
                            :width="256"
                            :clearable="false"
                            placeholder="选择日期范围"
                            @change="handleChangeDate"
                        >
                        </abc-date-picker>

                        <clinic-select
                            v-if="isChainAdmin"
                            v-model="params.selectedClinicId"
                            :show-all-clinic="false"
                            :exclude-chain-clinics="true"
                            clearable
                            placeholder="总部/门店"
                            @change="fetchData"
                        ></clinic-select>

                        <abc-input
                            v-model="params.keyword"
                            placeholder="联系人/手机号/订单号"
                            clearable
                            :width="220"
                            @input="handleSearchInput"
                        >
                            <abc-search-icon slot="prepend"></abc-search-icon>
                        </abc-input>

                        <abc-select
                            v-model="params.stockDealStatus"
                            :width="120"
                            placeholder="库存处理状态"
                            clearable
                            @change="fetchData"
                        >
                            <abc-option
                                v-for="item in stockDealStatusOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></abc-option>
                        </abc-select>
                    </abc-space>
                    <div style="flex: 1;"></div>
                    <sync-order-button :disabled="!isValidMall"></sync-order-button>
                </abc-flex>
            </abc-flex>
        </abc-layout-header>

        <abc-layout-content>
            <abc-table
                :loading="contentLoading"
                :empty-content="'暂无数据'"
                :render-config="tableConfig"
                :data-list="tableData"
                cell-size="xlarge"
                class="out-storage-table"
                @handleClickTr="handleOperation"
            >
                <template #ecType="{ trData }">
                    <abc-table-cell>
                        {{ ECTypeText[trData.ecType] }}
                    </abc-table-cell>
                </template>

                <template #receiverInfo="{ trData }">
                    <abc-table-cell>
                        <abc-flex vertical>
                            <abc-text class="ellipsis">
                                #{{ trData.orderAdditionalInfo?.orderDaySeq || '' }}
                                {{ trData.orderReceiverNameMask || '-' }}
                            </abc-text>
                            <abc-text size="mini" theme="gray">
                                {{ trData.orderReceiverPhoneMask || '-' }}
                            </abc-text>
                        </abc-flex>
                    </abc-table-cell>
                </template>

                <template #refundReason="{ trData }">
                    <abc-table-cell>
                        <abc-flex vertical>
                            <abc-text>{{ trData.reason || '-' }}</abc-text>
                            <abc-text size="mini" theme="gray">
                                {{ trData.orderExtCreated ? `${formatDate(trData.orderExtCreated, 'MM-DD HH:mm')}` : '-' }}
                                {{ trData.additionalInfo?.afterSaleApplyFromShowInfo || '' }}
                            </abc-text>
                        </abc-flex>
                    </abc-table-cell>
                </template>

                <template #stockDealStatus="{ trData }">
                    <abc-table-cell>
                        <abc-flex vertical>
                            <abc-text :theme="trData.stockDealStatus === 0 ? 'warning-light' : 'black'">
                                {{ stockDealStatusOptions.find((item) => item.value === trData.stockDealStatus)?.label || '' }}
                            </abc-text>
                        </abc-flex>
                    </abc-table-cell>
                </template>

                <template #goodsList="{ trData }">
                    <abc-popover
                        :value="showGoodsDetailPopover && hoverRowId === trData.id && currentRow.id === trData.id"
                        theme="yellow"
                        trigger="manual"
                        placement="bottom-start"
                        :visible-arrow="false"
                        style="width: 100%;"
                        :offset="-5"
                        :popper-style="{
                            padding: 0,
                            margin: 0,
                        }"
                    >
                        <abc-table-cell slot="reference" style="cursor: pointer;">
                            <div
                                class="ellipsis"
                                @mouseenter="handleGoodsMouseEnter(trData)"
                                @mouseleave="handleReferenceMouseLeave"
                            >
                                <abc-text bold>
                                    {{ (trData.items || [])
                                        .map(item => `${item.extGoodsName} x ${item.extGoodsCount}`)
                                        .join(' + ')
                                    }}
                                </abc-text>
                            </div>
                        </abc-table-cell>
                        <div
                            @mouseenter="handlePopoverMouseEnter"
                            @mouseleave="handlePopoverMouseLeave"
                        >
                            <abc-table
                                :data-list="currentRow.items"
                                :render-config="goodsTableConfig"
                                style="border: none;"
                                cell-size="xxxlarge"
                                class="order-cloud-goods-list-table"
                            >
                                <template #goodsInfo="{ trData: row }">
                                    <abc-table-cell align="start" style="padding-top: 8px;">
                                        <abc-flex style="width: 100%;" justify="space-between" gap="large">
                                            <abc-flex gap="middle" style="flex: 1;">
                                                <abc-image
                                                    :src="row.extGoodsImg || ''"
                                                    :width="56"
                                                    :height="56"
                                                ></abc-image>
                                                <abc-flex vertical :gap="2" style="flex: 1;">
                                                    <abc-text>
                                                        {{ row.extGoodsName }}
                                                    </abc-text>
                                                    <abc-space>
                                                        <abc-text size="mini" theme="gray">
                                                            SKUID: {{ row.extGoodsId }}
                                                        </abc-text>
                                                        <abc-text size="mini" theme="gray">
                                                            店内码/货号: {{ row.ecGoodsSku?.extSourceFoodCode || '-' }}
                                                        </abc-text>
                                                    </abc-space>
                                                </abc-flex>
                                            </abc-flex>
                                            <abc-text>
                                                x {{ row.extGoodsCount }}
                                            </abc-text>
                                        </abc-flex>
                                    </abc-table-cell>
                                </template>
                                <template #bindInfo="{ trData: row }">
                                    <abc-table-cell align="start" style="padding-top: 8px;">
                                        <abc-flex v-if="row.selectedStockRecord && row.selectedStockRecord.hisGoodsInfo" justify="space-between" style="width: 100%;">
                                            <abc-flex vertical :gap="2" style="flex: 1;">
                                                <abc-text>
                                                    {{ row.selectedStockRecord.hisGoodsInfo.displayName }}
                                                </abc-text>
                                                <abc-space>
                                                    <abc-text size="mini" theme="gray">
                                                        {{ row.selectedStockRecord.hisGoodsInfo.displaySpec }}
                                                    </abc-text>
                                                    <abc-text size="mini" theme="gray">
                                                        {{ row.selectedStockRecord.hisGoodsInfo.manufacturer }}
                                                    </abc-text>
                                                    <abc-text size="mini" theme="gray">
                                                        {{ row.selectedStockRecord.hisGoodsInfo.shortId }}
                                                    </abc-text>
                                                </abc-space>
                                            </abc-flex>
                                            <abc-text>
                                                x {{ row.extGoodsCount }} {{ row.selectedStockRecord.hisGoodsInfo.packageUnit }}
                                            </abc-text>
                                        </abc-flex>
                                        <abc-flex v-else-if="row.enableOutStockRecord && row.enableOutStockRecord.hisGoodsInfo" justify="space-between" style="width: 100%;">
                                            <abc-flex vertical :gap="2" style="flex: 1;">
                                                <abc-text>
                                                    {{ row.enableOutStockRecord.hisGoodsInfo.displayName }}
                                                </abc-text>
                                                <abc-space>
                                                    <abc-text size="mini" theme="gray">
                                                        {{ row.enableOutStockRecord.hisGoodsInfo.displaySpec }}
                                                    </abc-text>
                                                    <abc-text size="mini" theme="gray">
                                                        {{ row.enableOutStockRecord.hisGoodsInfo.manufacturer }}
                                                    </abc-text>
                                                    <abc-text size="mini" theme="gray">
                                                        {{ row.enableOutStockRecord.hisGoodsInfo.shortId }}
                                                    </abc-text>
                                                </abc-space>
                                            </abc-flex>
                                            <abc-text>
                                                x {{ row.extGoodsCount }} {{ row.enableOutStockRecord.hisGoodsInfo.packageUnit }}
                                            </abc-text>
                                        </abc-flex>
                                        <abc-text v-else theme="gray">
                                            未绑定ABC商品
                                        </abc-text>
                                    </abc-table-cell>
                                </template>
                            </abc-table>
                        </div>
                    </abc-popover>
                </template>

                <template #orderNo="{ trData }">
                    <abc-table-cell>
                        <abc-flex vertical>
                            <abc-space>
                                <abc-text>{{ trData.orderNo || '-' }}</abc-text>
                                <abc-tag-v2
                                    v-if="trData.orderTypeFlag & 8"
                                    variant="outline"
                                    shape="square"
                                    size="small"
                                    theme="danger"
                                >
                                    处方
                                </abc-tag-v2>
                            </abc-space>
                            <abc-text size="mini" theme="gray">
                                {{ trData.extCreated ? `${formatDate(trData.extCreated, 'MM-DD HH:mm')}下单` : '-' }}
                            </abc-text>
                        </abc-flex>
                    </abc-table-cell>
                </template>

                <template #mallName="{ trData }">
                    <abc-table-cell>
                        <abc-flex vertical>
                            <abc-text>{{ trData.mallName || '-' }}</abc-text>
                            <abc-text size="mini" theme="gray">
                                {{ trData.clinicName || '-' }}
                            </abc-text>
                        </abc-flex>
                    </abc-table-cell>
                </template>

                <template #operation="{ trData }">
                    <abc-table-cell>
                        <abc-button
                            type="text"
                            @click.stop="handleOperation(trData)"
                        >
                            {{ !isChainAdmin && trData.stockDealStatus === 0 && trData.bindStatus === 1 ? '处理' : '查看' }}
                        </abc-button>
                    </abc-table-cell>
                </template>
            </abc-table>
        </abc-layout-content>

        <abc-layout-footer>
            <abc-pagination
                :pagination-params="paginationParams"
                :count="totalCount"
                :show-total-page="true"
                :page-sizes="[15, 30, 50, 100]"
                show-size
                @current-change="handleCurrentChange"
                @size-change="handleSizeChange"
            ></abc-pagination>
        </abc-layout-footer>
    </abc-layout>
</template>

<script>
    import { formatDate } from '@abc/utils-date';
    import {
        ECTypeEnum, ECTypeText, WpCodeLogo,
    } from '@/utils/constants';
    import { debounce } from '@abc/utils';
    import ECOrderAPI from '@/api/order';
    import DialogOrderAfterSale from '@/components/dialog-order-after-sale/index.js';
    import SyncOrderButton from '@/components/sync-order-button.vue';
    import { mapGetters } from 'vuex';
    import ClinicSelect from 'MfBase/clinic-select';
    import ECAuthAPI from '@/api/auth';
    import BizSelectTabs from 'MfBase/biz-select-tabs';
    import { PharmacyOrderCloudRouterNameKeys } from '@/core/routes';
    import DialogEcAuth from '@/components/dialog-ec-auth';

    export default {
        name: 'OutStorage',
        components: {
            ClinicSelect,
            SyncOrderButton,
            BizSelectTabs,
        },
        data() {
            const now = new Date();
            const today = formatDate(now);
            const nowDayOfWeek = now.getDay();
            const nowDay = now.getDate();
            const nowMonth = now.getMonth();
            const nowYear = now.getFullYear();

            return {
                // 搜索条件相关
                WpCodeLogo,
                ECTypeText,
                params: {
                    limit: 15,
                    offset: 0,
                    selectedClinicId: '',
                    shopType: 1,
                    ecType: 4,
                    keyword: '',
                    stockDealStatus: '',
                    extCreatedBeginDate: today,
                    extCreatedEndDate: today,
                    ecMallId: '',
                },
                totalCount: 0,
                loading: false,
                contentLoading: false,

                stockDealStatusOptions: [
                    {
                        label: '待处理', value: 0,
                    },
                    {
                        label: '已处理', value: 10,
                    },
                ],

                datePickerValue: [today, today],
                pickerOptions: {
                    disabledDate(date) {
                        return date > new Date() || date < new Date('2001-11-01');
                    },
                    shortcuts: [{
                        text: '今天',
                        onClick(cb) {
                            const start = new Date();
                            const end = new Date();
                            cb([start, end]);
                        },
                    }, {
                        text: '昨天',
                        onClick(cb) {
                            const end = new Date(nowYear, nowMonth, nowDay - 1);
                            const start = new Date(nowYear, nowMonth, nowDay - 1);
                            cb([start, end]);
                        },
                    }, {
                        text: '本周',
                        onClick(cb) {
                            let start = new Date(nowYear, nowMonth, nowDay - 6);
                            if (nowDayOfWeek) {
                                start = new Date(nowYear, nowMonth, nowDay - nowDayOfWeek + 1);
                            }
                            const end = new Date();
                            cb([start, end]);
                        },
                    }, {
                        text: '本月',
                        onClick(cb) {
                            const start = new Date(nowYear, nowMonth, 1);
                            const end = new Date();
                            cb([start, end]);
                        },
                    }],
                },
                tableData: [],
                showGoodsDetailPopover: false,
                isMouseInReference: false,
                isMouseInPopover: false,
                hoverRowId: null,
                closeTimer: null,
                currentRow: {},
                ecTypes: [],
                mallList: [],
                ecTypeOptions: [
                    {
                        label: '美团',
                        value: ECTypeEnum.MT,
                    },
                ],
            };
        },
        computed: {
            ...mapGetters([
                'isChainAdmin',
                'currentClinic',
            ]),
            paginationParams() {
                const {
                    limit, offset,
                } = this.params;
                const pageIndex = Math.ceil(offset / limit);
                return {
                    pageIndex,
                    pageSize: limit,
                };
            },

            // 表格相关
            tableConfig() {
                return {
                    hasInnerBorder: false,
                    list: [
                        {
                            key: 'ecType',
                            label: '平台',
                            style: {
                                flex: 'none',
                                width: '80px',
                            },
                        },
                        {
                            key: 'receiverInfo',
                            label: '联系人',
                            style: {
                                flex: 'none',
                                width: '150px',
                            },

                        },
                        {
                            key: 'refundReason',
                            label: '退货原因',
                            style: {
                                flex: 'none',
                                width: '190px',
                            },
                        },
                        {
                            key: 'stockDealStatus',
                            label: '库存处理状态',
                            style: {
                                flex: 'none',
                                width: '120px',
                                textAlign: 'center',
                            },
                        },
                        {
                            key: 'goodsList',
                            label: '退货商品',
                            style: {
                                minWidth: '240px',
                            },
                        },
                        {
                            key: 'refundAmount',
                            label: '退款金额',
                            style: {
                                flex: 'none',
                                width: '100px',
                                textAlign: 'right',
                            },
                        },
                        {
                            key: 'goodsAfter',
                            label: '',
                            style: {
                                flex: 'none',
                                width: '20px',
                            },
                        },
                        {
                            key: 'orderNo',
                            label: '订单号',
                            style: {
                                flex: 'none',
                                width: '216px',
                            },
                        },
                        {
                            key: 'amountAfter',
                            label: '',
                            style: {
                                flex: 'none',
                                width: '20px',
                            },
                        },
                        {
                            key: 'mallName',
                            label: '网店/线下门店',
                            style: {
                                flex: 'none',
                                width: '240px',
                            },
                        },
                        {
                            key: 'operation',
                            label: '操作',
                            pinned: 'right',
                            style: {
                                flex: 'none',
                                width: '80px',
                                textAlign: 'center',
                            },
                        },
                    ],
                };
            },
            goodsTableConfig() {
                return {
                    hasHeaderBorder: true,
                    hasInnerBorder: true,
                    list: [
                        {
                            key: 'goodsInfo',
                            label: '退货商品',
                            style: {
                                flex: 'none',
                                width: '400px',
                                backgroundColor: 'var(--abc-color-LY4)',
                                borderColor: 'var(--abc-color-LY1)',
                            },
                            headerStyle: {
                                backgroundColor: 'var(--abc-color-LY4)',
                                borderColor: 'var(--abc-color-LY1)',
                            },
                        },
                        {
                            key: 'bindInfo',
                            label: '绑定的ABC系统商品',
                            style: {
                                flex: 'none',
                                width: '300px',
                                backgroundColor: 'var(--abc-color-LY4)',
                                borderColor: 'var(--abc-color-LY1)',
                            },
                            headerStyle: {
                                backgroundColor: 'var(--abc-color-LY4)',
                                borderColor: 'var(--abc-color-LY1)',
                            },
                        },
                    ],
                };
            },
            currentMall() {
                return this.mallList.find((item) => item.ecMallId === this.params.ecMallId) || {};
            },
            isValidMall() {
                return this.currentMall.status === 1;
            },
        },
        async created() {
            await this.getEcTypes();
            await this.getMallList();
            this.params.ecMallId = this.mallList?.[0]?.ecMallId || '';
            this.fetchData();
            this._debounceFetchData = debounce(this.fetchData, 800, true);
            this._debounceFetchOrderDetail = debounce(this.fetchOrderDetail, 300, true);
        },
        methods: {
            formatDate,
            handleGoodsMouseEnter(trData) {
                this.clearCloseTimer();

                this.hoverRowId = trData.id;
                this.isMouseInReference = true;
                this.isMouseInPopover = false;

                if (this.currentRow.id === trData.id) {
                    this.showGoodsDetailPopover = true;
                } else {
                    this._debounceFetchOrderDetail(trData);
                }
            },
            setCloseTimer() {
                // 清除之前的定时器
                if (this.closeTimer) {
                    clearTimeout(this.closeTimer);
                }

                // 设置新的定时器
                this.closeTimer = setTimeout(() => {
                    if (!this.isMouseInReference && !this.isMouseInPopover) {
                        this.showGoodsDetailPopover = false;
                        this.hoverRowId = null;
                    }
                    this.closeTimer = null;
                }, 300);
            },
            clearCloseTimer() {
                if (this.closeTimer) {
                    clearTimeout(this.closeTimer);
                    this.closeTimer = null;
                }
            },
            handleReferenceMouseLeave() {
                this.isMouseInReference = false;
                this.setCloseTimer();
            },
            handlePopoverMouseEnter() {
                this.isMouseInPopover = true;
                this.clearCloseTimer();
            },
            handlePopoverMouseLeave() {
                this.isMouseInPopover = false;
                this.setCloseTimer();
            },

            // 获取订单详情 - 使用防抖函数调用
            async fetchOrderDetail(trData) {
                try {
                    const data = await ECOrderAPI.getAfterSale(trData.id);
                    if (this.hoverRowId === trData.id) {
                        this.currentRow = data;
                    }
                    this.showGoodsDetailPopover = true;
                } catch (e) {
                    console.error(e);
                }
            },

            // 处理操作按钮点击
            handleOperation(row) {
                const _dialog = new DialogOrderAfterSale({
                    afterSaleId: row.id,
                    orderNo: row.orderNo,
                    stockDealStatus: row.stockDealStatus,
                    onRefresh: this.fetchData,
                    isValidMall: this.isValidMall,
                });
                _dialog.generateDialogAsync({
                    parent: this,
                });
            },
            handleChangeDate() {
                this.params.extCreatedBeginDate = this.datePickerValue[0];
                this.params.extCreatedEndDate = this.datePickerValue[1];
                this.fetchData();
            },
            // 处理每页显示数量变化
            handleSizeChange(pageSize) {
                this.params.limit = pageSize;
                this.fetchData();
            },
            // 处理页码变化
            handleCurrentChange(pageIndex) {
                this.params.offset = (pageIndex - 1) * this.params.limit;
                this.fetchData(false);
            },
            // 获取数据
            async fetchData(needResetOffset = true) {
                try {
                    this.contentLoading = true;
                    if (needResetOffset) {
                        this.params.offset = 0;
                    }
                    const {
                        rows, total = 0,
                    } = await ECOrderAPI.fetchEcRefundOrderList(this.params);
                    this.tableData = rows ?? [];
                    this.totalCount = total;
                } catch (e) {
                    console.error(e);
                } finally {
                    this.contentLoading = false;
                }
            },
            async getEcTypes() {
                try {
                    this.loading = true;
                    const { rows } = await ECAuthAPI.fetchEcTypes({
                        ...this.params,
                    });
                    this.ecTypes = rows || [];
                } catch (err) {
                    console.error(err);
                } finally {
                    this.loading = true;
                }
            },
            async getMallList() {
                try {
                    this.loading = true;
                    const { rows = [] } = await ECAuthAPI.fetchBindAuthList({
                        offset: 0,
                        limit: 1000,
                        onlyUnexpired: 0, // 在授权有效期内
                        includeHistory: 1, // 是否包含历史绑定的电商店铺
                        includeDeleted: 1, // 是否包含已删除的绑定数据
                    });

                    this.mallList = rows
                        .filter((item) => item.ecType === ECTypeEnum.MT)
                        .sort((a, b) => {
                            const getOrder = (order) => (order === 1 ? 1 : 0);
                            const orderA = getOrder(a.status);
                            const orderB = getOrder(b.status);
                            return orderB - orderA;
                        })
                        .reduce((pre, cur) => {
                            if (pre.find((item) => item.ecMallId === cur.ecMallId)) return pre;
                            pre.push(cur);
                            return pre;
                        }, [])
                        .map((item) => {
                            return {
                                ...item,
                                key: item.ecMallId,
                                label: `${item.mallName}（${item.bindClinicName}）`,
                                value: item.ecMallId,
                                icon: 's-s-drugstore-color',
                            };
                        });
                } catch (err) {
                    console.error(err);
                } finally {
                    this.loading = false;
                }
            },
            handleSearchInput() {
                this._debounceFetchData();
            },
            async handleEcAuthorized(controlType) {
                if (controlType === '2') {
                    await new DialogEcAuth({
                        authCode: this.authCode,
                        extMallId: this.params.extMallId,
                        mallId: this.params.mallId,
                        finishFunc: () => {
                            this.getMallList();
                        },
                    }).generateDialogAsync({ parent: this });
                } else {
                    this.$router.push({
                        name: PharmacyOrderCloudRouterNameKeys.ecSettingsTakeawayOnlineStore,
                    });
                }
            },
        },
    };
</script>

<style lang="scss">
.order-cloud-goods-list-table .abc-table-header {
    border-color: var(--abc-color-LY1) !important;
}
</style>

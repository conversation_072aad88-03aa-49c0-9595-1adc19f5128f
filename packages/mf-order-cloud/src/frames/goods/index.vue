<template>
    <abc-card class="content-card-wrapper pharmacy__ec-goods-table-section" :border="false">
        <abc-tabs-v2
            v-model="currentTab"
            :option="tabOptions"
            size="large"
            :item-min-width="64"
            :border-style="{ borderBottom: 'none' }"
            @change="handleTabChange"
        ></abc-tabs-v2>
        <abc-divider margin="none" style="width: auto; min-width: auto; margin: 0 20px;"></abc-divider>
        <router-view></router-view>
    </abc-card>
</template>

<script>
    import { PharmacyOrderCloudRouterNameKeys } from '../../core/routes';

    export default {
        name: 'OrderCloudGoodsTable',
        data() {
            return {
                currentTab: 0,
                tabOptions: [
                    {
                        label: '美团',
                        value: 0,
                    },
                    {
                        label: '拼多多',
                        value: 1,
                    },
                ],
            };
        },
        methods: {
            handleTabChange(tab) {
                if (tab === 0) {
                    this.$router.push({
                        name: PharmacyOrderCloudRouterNameKeys.mtGoods,
                    });
                } else {
                    this.$router.push({
                        name: PharmacyOrderCloudRouterNameKeys.ecGoods,
                    });
                }
            },
        },
    };
</script>

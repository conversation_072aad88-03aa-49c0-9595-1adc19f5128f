<template>
    <div
        v-abc-loading.small="loading"
        class="popper-wrap"
    >
        <abc-flex
            v-if="detail"
            vertical
        >
            <abc-flex :gap="8" vertical>
                <abc-flex
                    justify="space-between"
                    style="width: 100%;"
                >
                    <abc-text theme="gray">
                        可售数量
                    </abc-text>
                    <abc-text bold>
                        {{ detail.dispStockGoodsCount }}
                    </abc-text>
                </abc-flex>
                
                <abc-flex justify="space-between" style="width: 100%;">
                    <abc-text theme="gray">
                        当前库存
                    </abc-text>
                    <abc-text>
                        {{ detail.dispGoodsCount }}
                    </abc-text>
                </abc-flex>

                <abc-flex justify="space-between" style="width: 100%;">
                    <abc-text theme="gray">
                        锁定数量
                    </abc-text>

                    <abc-link @click="handleClickViewLockDetail">
                        {{ detail.dispLockingGoodsCount }}
                    </abc-link>
                </abc-flex>

                <abc-flex justify="space-between" style="width: 100%;">
                    <abc-text theme="gray">
                        禁售数量
                    </abc-text>
                    <abc-flex vertical>
                        <abc-text style="text-align: right;">
                            {{ detail.dispProhibitGoodsCount }}
                        </abc-text>
                        <div v-for="(item, index) in (detail.prohibitStockList || [])" :key="index">
                            {{ `${item.excelExportStock} 批次：${item.batchNo}` }}
                        </div>
                    </abc-flex>
                </abc-flex>
            </abc-flex>
            <abc-divider margin="small"></abc-divider>
            <abc-flex vertical class="footer">
                <abc-text size="mini" theme="gray">
                    可售数量=当前库存-锁定/禁售数量
                </abc-text>
                <abc-text
                    v-if="isNegative(detail.dispStockGoodsCount)"
                    theme="warning-light"
                    size="mini"
                >
                    因已锁定的待出库存超出现有可用库存，可售数量为负
                </abc-text>
            </abc-flex>
        </abc-flex>
        <abc-content-empty
            v-else
            top="50%"
            :show-icon="false"
            value="暂无数据"
        ></abc-content-empty>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';
    import ApiV3 from '../../../api/goods';
    import GoodsLockDetailDialog from 'MfBase/goods-lock-detail-dialog';

    export default {
        name: 'PopoverStockCard',
        props: {
            row: {
                type: Object,
                required: true,
            },
        },
        data() {
            return {
                loading: false,
                detail: {},
                fetchParams: {
                    goodsId: this.row?.hisGoodsId || '',
                },
            };
        },
        computed: {
            ...mapGetters(['isChainAdmin']),
        },
        created() {
            this.fetchData();  
        },
        methods: {
            isNegative(value) {
                return (`${value}`).startsWith('-');
            },
            async fetchData() {
                try {
                    this.loading = true;
                    const params = {
                        ...this.fetchParams,
                    };
                    if (this.isChainAdmin) {
                        delete params.pharmacyNo;
                    }
                    if (!this.fetchParams.goodsId) {
                        return;
                    }
                    const data = await ApiV3.getGoodsStockDetail(params);
                    this.detail = data || {};
                } catch (e) {
                    console.log(e);
                } finally {
                    this.loading = false;
                }
            },
            handleClickViewLockDetail() {
                new GoodsLockDetailDialog({
                    goodsId: this.fetchParams.goodsId,
                }).generateDialogAsync({
                    parent: this,
                });
            },
        },
    };
</script>

<style lang="scss" scoped>
.popper-wrap {
    .footer {
        order: 2;
        padding: 4px 0;
    }
}
</style>

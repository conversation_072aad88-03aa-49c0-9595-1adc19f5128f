import { ToastFunc as Toast } from '@abc/ui-pc';
import { isDev } from '../../utils/build-env.js';
import { PaperOrientation } from '../config';
import { AbcPrintOrientation } from '../constants';

const printDPI = 600;

export const DuplexMode = Object.freeze({
    // 单面打印
    Simplex: 'simplex',
    // 短边翻转
    ShortEdge: 'shortEdge',
    // 长边翻转
    LongEdge: 'longEdge',
});

export const DuplexModeOptions = Object.freeze([
    {
        label: '单面打印',
        value: DuplexMode.Simplex,
        desc: '仅单面打印',
    },
    {
        label: '双面打印',
        value: DuplexMode.LongEdge,
        desc: '长边翻转',
    },
    {
        label: '双面打印',
        value: DuplexMode.ShortEdge,
        desc: '短边翻转',
    },
]);

export function execSync(cmd) {
    return new Promise((resolve, reject) => {
        try {
            window.require('child_process').execSync(cmd);
            resolve();
        } catch (e) {
            reject(e);
        }
    });
}

export function exec(cmd) {
    return new Promise((resolve, reject) => {
        try {
            window.require('child_process').exec(cmd);
            resolve();
        } catch (e) {
            reject(e);
        }
    });
}

export function electronPageToAbcPrintPage({
    height = 0,
    name = '',
    width = 0,
}) {
    return {
        defaultHeightLevel: null,
        defaultOrientation: 1,
        isRecommend: false,
        isElectronPage: true,
        name,
        paper: {
            width: `${width / 10}mm`,
            height: `${height / 10}mm`,
            heightLevels: null,
            key: name,
            name,
            orientations: [0, 1],
        },
    };
}

export function injectContentToHTML(html, content) {
    return html.replace('</head>', `${content}</head>`);
}

export const ABC_ELECTION = {
    preview(printConfig, template, extra) {
        console.log(printConfig, template, extra);
        Toast.error('客户端不支持预览');
    },
    async print(printConfig, template, extra = {}) {
        console.warn('ABC_ELECTION.print');

        let duplexMode = DuplexMode.Simplex;

        if (printConfig.advance && printConfig.advance.duplexMode) {
            duplexMode = printConfig.advance.duplexMode;
        }

        const regex = /<body>(\s|\n)*<\/body>/i;
        if (regex.test(template)) {
            console.error('打印内容为空白!');
            return;
        }

        const {
            splitAbcPageHTML,
            addWrapperToAbcPageHTML,
            AbcPageSizeMap: {
                A5,
                A4,
            },
        } = window.AbcPackages.AbcPrint;

        const {
            deviceName,
            pageSize,
            orient,
            printCopies,
            pageWidth,
            pageSizeReduce,
        } = printConfig;
        let { pageHeight } = printConfig;
        const MM2PX = 3.7795275591;
        // 是否横向打印
        // 这个是指布局，不是纸张方向
        const isLandScape = orient !== AbcPrintOrientation.portrait;
        // 1.选择横向进纸
        // 2.选择A5纸张
        const isLandscapePapper = printConfig.advance?.paperOrientation === PaperOrientation.Landscape && pageSize === A5.name;

        let currentPrintCopies = parseInt(printCopies);

        if (isNaN(currentPrintCopies)) {
            currentPrintCopies = 1;
        }

        // 为小票设置一个极大的高度
        // 不用计算小票的实际内容高度
        // 打印机会自动在空白处结束
        if (pageHeight === 'auto') {
            pageHeight = '6000mm';
        }

        // 横向进纸强制使用A4纸张
        const pageSizeOptions = isLandscapePapper ? {
            name: A4.name,
            width: parseFloat(A4.width) * 1000,
            height: parseFloat(A4.height) * 1000,
        } : {
            name: pageSize,
            width: parseFloat(pageWidth) * 1000,
            height: parseFloat(pageHeight) * 1000,
        };

        const printOptions = {
            deviceName,
            margins: {
                /**
                 * 可选值 `default`, `none`, `printableArea`, or `custom`. 如果选择 `custom`，
                 * 需要自己指定 `top`, `bottom`, `left`, and `right`
                 */
                marginType: 'custom',
                /**
                 * 顶 margin，单位像素
                 */
                top: pageSizeReduce.top * MM2PX,
                /**
                 * 底 margin
                 */
                bottom: pageSizeReduce.bottom * MM2PX,
                /**
                 * 左margin
                 */
                left: pageSizeReduce.left * MM2PX,
                /**
                 * 右margin
                 */
                right: pageSizeReduce.right * MM2PX,
            },
            /**
             * 是否横向打印，默认false
             */
            landscape: isLandscapePapper ? false : isLandScape,
            /**
             * 页面缩放系数, 100表示原始大小
             */
            scaleFactor: extra?.scaleFactor || 100,
            /**
             * The number of pages to print per page sheet.
             */
            pagesPerSheet: 1,
            /**
             * Whether the web page should be collated.
             */
            collate: false,
            /**
             * 打印多少份
             */
            copies: currentPrintCopies,
            /**
             * 设置duplex mode 可取值 be `simplex`, `shortEdge`, or
             * `longEdge`.
             */
            duplexMode,
            dpi: {
                horizontal: printDPI,
                vertical: printDPI,
            },
            /**
             * 作为header打印
             */
            header: '',
            /**
             * 作为footer打印
             */
            footer: '',
            /**
             * 页大小，可取值`A3`, `A4`, `A5`, `Legal`, `Letter`, `Tabloid` 或是包含`height` 和`width`和对象类型
             */
            pageSize: pageSizeOptions,
            /**
             * 打印背景图片
             */
            printBackground: true,
        };

        const printHTML = async (html) => {
            try {
                html = html.replace(/[\u200B-\u200D\uFEFF]/gi, '');
                if (typeof extra.onHandleElectronOutputHTML === 'function') {
                    html = extra.onHandleElectronOutputHTML(html);
                }

                // 重置样式
                html = injectContentToHTML(html, `
                     <style type="text/css">
                        .abc-page {
                            box-shadow: none!important;
                            zoom: 1!important;
                        }
                     </style>
                `);

                // 方便调试
                console.debug('print-task-detail', {
                    html,
                    printOptions,
                });


                console.warn('use native print');
                const response = await window.electron.printer.print(html, printOptions);
                console.log(JSON.stringify(response, null, 2));
            } catch (e) {
                Toast.error('打印失败');
                console.error(e);
            }
        };

        const abcPageHTMLList = splitAbcPageHTML(template);
        // 旧版打印继续支持多个
        for (let i = 0; i < abcPageHTMLList.length; i++) {
            if (isLandscapePapper) {
                const html = addWrapperToAbcPageHTML(abcPageHTMLList[i]);
                await printHTML(injectContentToHTML(html, `
                        <style type="text/css">
                            .abc-page-wrapper {
                               page-break-after: always;
                            }
                            .abc-page {
                               ${!isLandScape ? `transform: rotate(90deg);transform-origin: ${parseFloat(A5.height) / 2}mm;` : ''}
                               margin: initial;
                            }
                        </style>
                    `));
            } else {
                await printHTML(abcPageHTMLList[i]);
            }
        }
    },

    validatePrinter() {
        return true;
    },

    isCustomPrinter({ name }) {
        if (isDev) {
            return true;
        }
        return ![
            'Fax',
            'Microsoft Print to PDF',
            'Microsoft XPS Document Writer',
            'OneNote for Windows 10',
            '导出为WPS PDF',
            'Microsoft Print to OneNote',
            'OneNote',
            '发送至 OneNote',
        ].includes(name);
    },
};

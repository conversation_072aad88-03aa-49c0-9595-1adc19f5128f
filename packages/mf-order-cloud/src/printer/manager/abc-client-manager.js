import {
    ABC_ELECTION, electronPageToAbcPrintPage,
} from '../utils/electron.js';

export default class ABCClientManager {
    static instance = null;
    static getInstance() {
        if (!ABCClientManager.instance) {
            ABCClientManager.instance = new ABCClientManager();
        }
        return ABCClientManager.instance;
    }
    printerList = [];
    constructor() {
        this.init();
    }

    async init() {
        const handlePrinterList = (printerList) => {
            return printerList.map((printer, index) => {
                const {
                    offsetXInMm, offsetYInMm,
                } = printer;
                return {
                    name: printer.name,
                    deviceName: printer.name,
                    deviceIndex: index,
                    driveName: 'default',
                    pageList: printer.pages.map(electronPageToAbcPrintPage),
                    offset: {
                        offsetX: offsetXInMm !== undefined ? offsetXInMm / 10 : undefined,
                        offsetY: offsetXInMm !== undefined ? offsetYInMm / 10 : undefined,
                    },
                };
            }).filter((printer) => ABC_ELECTION.isCustomPrinter(printer));
        };

        let _printerList = [];
        if (typeof window.getPrinterList === 'function') {
            _printerList = await window.getPrinterList();
        }
        this.printerList = handlePrinterList(_printerList);
    }

    getPrinterList() {
        return this.printerList;
    }

    getPageListByDeviceName(deviceName) {
        const printer = this.printerList.find((p) => p.deviceName === deviceName);
        if (printer) {
            return printer.pageList;
        }
        return [];
    }

    debug(printConfig, template, extra) {
        ABC_ELECTION.preview(printConfig, template, extra);
    }

    getOffsetByDeviceName(deviceName) {
        const printer = this.printerList.find((p) => p.deviceName === deviceName);
        if (printer) {
            const {
                offsetXInMm, offsetYInMm,
            } = printer;
            return {
                offsetX: offsetXInMm !== undefined ? offsetXInMm / 10 : undefined,
                offsetY: offsetXInMm !== undefined ? offsetYInMm / 10 : undefined,
            };
        }
        return {};
    }

    isElectronPageSize(deviceName, pageSize) {
        const pageList = this.getPageListByDeviceName(deviceName);
        return pageList.some((page) => page.paper.name === pageSize);
    }

    getPageByDeviceAndPageSize(deviceName, pageSize) {
        const pageList = this.getPageListByDeviceName(deviceName);
        return pageList.find((page) => page.paper.name === pageSize);
    }

    supportTinyPrinter(deviceIndex) {
        const printer = this.printerList[deviceIndex];
        if (printer) {
            return printer.pageList.every((page) => parseFloat(page.paper.width) * 10 < 900);
        }
        return false;
    }

    getPrinterOffset(deviceName) {
        return this.printerList.find((p) => p.deviceName === deviceName)?.offset ?? {};
    }
}

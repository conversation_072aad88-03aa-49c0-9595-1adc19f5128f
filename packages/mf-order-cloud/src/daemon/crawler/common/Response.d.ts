export default class Response<T = any> {
    status: boolean;
    message: string;
    data: T | null;

    constructor();

    base(status: boolean, message: string, data: T | null): this;
    success(data: T): this;
    error(message: string, data?: T): this;

    static success<S>(data: S): Response<S>;
    static error<E = any>(message: string, data?: E): Response<E>;
}

export class UnAuthResponse extends Response {
    status: false;
    data: {
        code: 401,
    };
}

export type ApiResponse<T> = Promise<Response<T> | UnAuthResponse>;

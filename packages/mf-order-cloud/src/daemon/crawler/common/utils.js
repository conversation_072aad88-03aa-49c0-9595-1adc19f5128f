export function openWindow(url, options, callbacks = {}) {
    if (!window.electron) {
        return;
    }
    const { remote } = window.electron;
    const { BrowserWindow } = remote;
    options = options || {};
    const win = new BrowserWindow(options);
    win.on('close', () => callbacks.close && callbacks.close());
    if (callbacks) {
        const events = Object.keys(callbacks);
        for (let i = 0; i < events.length; i++) {
            win.webContents.on(events[i], callbacks[events[i]]);
        }
    }

    win.loadURL(url);
    // remote.app.registerJSAPForBrowserWindow(win);

    if (options && options.openDevTools) {
        win.webContents.openDevTools();
    }

    // win.setVisibleOnAllWorkspaces(false);
    return win;
}


export async function sleep(time) {
    return new Promise((resolve) => {
        let timer = setTimeout(() => {
            clearTimeout(timer);
            timer = null;
            resolve();
        }, time);
    });
}

import ECAuthAPI from '../../api/auth.js';
/**
 * @desc 订单云数据store
 * <AUTHOR>
 * @date 2024-04-18 15:05:34
 */
export default class OrderCloudStore {
    constructor() {
        this.state = {
            authMallList: [],
        };
    }

    async init() {
        await this.fetchBindAuthList();
    }

    get authMallList() {
        return this.state.authMallList || [];
    }

    // 只是授权在期的门店列表
    get onAuthMallList() {
        return this.state.authMallList?.filter((it) => it.status === 1) || [];
    }

    assignFeInfo(obj, val) {
        obj.FEInfo = obj.FEInfo || {};
        Object.assign(obj.FEInfo, val);
    }

    setEcMallFEInfo(extMallId, data) {
        const res = this.state.authMallList.find((it) => it.extMallId === extMallId);
        this.assignFeInfo(res, data);
    }

    async fetchBindAuthList() {
        const res = await ECAuthAPI.fetchBindAuthList({
            offset: 0,
            limit: 999,
        });
        this.state.authMallList = res.rows || [];
    }
}

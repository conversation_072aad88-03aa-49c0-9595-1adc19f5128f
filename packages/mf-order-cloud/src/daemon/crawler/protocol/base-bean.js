export default class BaseBean {
    params = null;
    baseUrl = '';
    path = '';
    method = 'GET';


    constructor(params) {
        this.params = params;
    }

    /**
     * 创建请求 url
     * @return {string}
     */
    createUrl() {
        return this.baseUrl + this.path;
    }

    /**
     * 创建请求体
     * @return {{}}
     */
    createRequestBody() {
        return {};
    }

    /**
     * 解析响应
     * @param response
     * @return {*}
     */
    parseResponse(response) {
        return response;
    }
}

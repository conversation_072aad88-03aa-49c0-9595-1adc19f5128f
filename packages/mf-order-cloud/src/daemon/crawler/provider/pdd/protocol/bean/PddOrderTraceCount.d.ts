export interface PddOrderTraceCount {
    // 揽件即将超时
    shippedUnAcceptWarnCount: number;
    // 揽件超时
    shippedUnAcceptErrorCount: number;
    // 揽件后更新即将超时
    acceptUnTraceWarnCount: number;
    // 揽件后更新超时
    acceptUnTraceErrorCount: number;
    // 分拨中心停留即将超时
    stationWarnCount: number | null;
    // 分拨中心超时
    stationErrorCount: number | null;
    // 物流更新即将超时
    traceWarnCount: number | null;
    // 物流更新超时
    traceErrorCount: number | null;
    // 包裹被原路退回
    packageReturnCount: number;
}

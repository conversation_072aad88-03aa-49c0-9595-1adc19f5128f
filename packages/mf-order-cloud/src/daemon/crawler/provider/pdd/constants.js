export const PddPage = Object.freeze({
    LOGIN: {
        name: '登录',
        url: 'https://mms.pinduoduo.com/login',
    },

    // 异常订单-打印后改地址
    EXCEPTION_ORDER_MODIFY_RECEIVER_AFTER_PRINT: {
        name: '异常订单-打印后改地址',
        locator: '打印后改地址',
        url: 'https://mms.pinduoduo.com/print/exception-order',
    },

    // 异常订单-打印后改备注
    EXCEPTION_ORDER_MODIFY_REMARK_AFTER_PRINT: {
        name: '异常订单-打印后改备注',
        locator: '打印后改备注',
        url: 'https://mms.pinduoduo.com/print/exception-order',
    },

    // 异常订单-打印后发生售后
    EXCEPTION_ORDER_AFTER_SALE_AFTER_PRINT: {
        name: '异常订单-打印后发生售后',
        locator: '打印后发生售后',
        url: 'https://mms.pinduoduo.com/print/exception-order',
    },

    // 异常订单-揽件前改备注
    EXCEPTION_ORDER_MODIFY_REMARK_BEFORE_TMS: {
        name: '异常订单-揽件前改备注',
        locator: '揽件前改备注',
        url: 'https://mms.pinduoduo.com/print/exception-order',
    },

    // 异常订单-揽件前发生售后
    EXCEPTION_ORDER_AFTER_SALE_BEFORE_TMS: {
        name: '异常订单-揽件前发生售后',
        locator: '揽件前发生售后',
        url: 'https://mms.pinduoduo.com/print/exception-order',
    },

    // 物流预警-揽件即将超时
    LOGISTIC_WARN_SHIPPED_UN_ACCEPT_WARN: {
        name: '物流预警-揽件即将超时',
        // locator: '揽件即将超时', 不需要 locator，默认进去就是
        url: 'https://mms.pinduoduo.com/print/logistic-warning',
    },
    // 物流预警-揽件后更新即将超时
    LOGISTIC_WARN_ACCEPT_UN_TRACE_WARN: {
        name: '物流预警-揽件后更新即将超时',
        locator: '揽件后更新即将超时',
        url: 'https://mms.pinduoduo.com/print/logistic-warning',
    },
    // 物流预警-揽件超时
    LOGISTIC_WARN_SHIPPED_UN_ACCEPT_ERROR: {
        name: '物流预警-揽件超时',
        locator: '揽件超时',
        url: 'https://mms.pinduoduo.com/print/logistic-warning',
    },
    // 物流预警-揽件后更新超时
    LOGISTIC_WARN_ACCEPT_UN_TRACE_ERROR: {
        name: '物流预警-揽件后更新超时',
        locator: '揽件后更新超时',
        url: 'https://mms.pinduoduo.com/print/logistic-warning',
    },
    // 物流预警-分拨中心停留即将超时
    LOGISTIC_WARN_STATION_WARN: {
        name: '物流预警-分拨中心停留即将超时',
        locator: '分拨中心停留即将超时',
        url: 'https://mms.pinduoduo.com/print/logistic-warning',
    },
    // 物流预警-分拨中心超时
    LOGISTIC_WARN_STATION_ERROR: {
        name: '物流预警-分拨中心超时',
        locator: '分拨中心超时',
        url: 'https://mms.pinduoduo.com/print/logistic-warning',
    },
    // 物流预警-物流更新即将超时
    LOGISTIC_WARN_TRACE_WARN: {
        name: '物流预警-物流更新即将超时',
        locator: '物流更新即将超时',
        url: 'https://mms.pinduoduo.com/print/logistic-warning',
    },
    // 物流预警-物流更新超时
    LOGISTIC_WARN_TRACE_ERROR: {
        name: '物流预警-物流更新超时',
        locator: '物流更新超时',
        url: 'https://mms.pinduoduo.com/print/logistic-warning',
    },
    // 物流预警-包裹被原路退回
    LOGISTIC_WARN_PACKAGE_RETURN: {
        name: '物流预警-包裹被原路退回',
        locator: '包裹被原路退回',
        url: 'https://mms.pinduoduo.com/print/logistic-warning',
    },

    // 售后管理
    AFTER_SALES: {
        name: '售后管理',
        url: 'https://mms.pinduoduo.com/print/after-sale-query',
    },
});

export const PDD_EVENT_AUTH_STATUS_CHANGED = 'pdd-event-auth-status-changed';
export const PDD_EVENT_AUTH_EXPIRED = 'pdd-event-auth-expired';
export const PDD_EVENT_EXCEPTION_LIST_CHANGED = 'pdd-event-exception-list-changed';
export const PDD_EVENT_EXPRESS_WARN_CHANGED = 'pdd-event-express-warn-changed';

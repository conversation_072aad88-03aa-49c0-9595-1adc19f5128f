import BaseService from '../../base/base-service';
import MeituanCrawler from './meituan-crawler';
import MTOrderPrescriptionCrawler from './order-prescription-crawler.js';
import Meitu<PERSON><PERSON>uth<PERSON>anager from './auth-manager';
import CrawlerTask from '../../base/crawler-task';
import Logger from '@/utils/logger';
import {
    AuthStatus,
} from '../../common/constants';
import ECA<PERSON><PERSON><PERSON> from '@/api/auth';
import {
    MEITUAN_EVENT_AUTH_STATUS_CHANGED,
    MEITUAN_EVENT_SYNC_PRODUCT_COMPOSE_LIST,
    MEITUAN_EVENT_ORDER_LIST_CHANGED,
    MEITUAN_EVENT_ORDER_SUMMARY_CHANGED,
    MEITUAN_EVENT_ORDER_LIST_NEW,
    MEITUAN_EVENT_PRESCRIPTION_LIST_CHANGED,
    MEITUAN_EVENT_SYNC_IS_DONE,
    MEITUAN_EVENT_SYNC_IS_START,
    MEITUAN_EVENT_SYNC_PRODUCT_IS_DONE,
    MEITUAN_EVENT_SYNC_PRODUCT_IS_FAILED,
    MEITUAN_EVENT_SYNC_PRODUCT_LIST,
    MEITUAN_EVENT_SYNC_PRODUCT_INFO,
    MEITUAN_EVENT_SYNC_PRODUCT_SUMMARY_IS_DONE,
    MEITUAN_EVENT_SYNC_PRODUCT_SUMMARY_IS_FAILED,
} from './constants';
import { MeituanCombinationLabel } from '../../common/constants';
import { createGUID } from '@/utils/index';
import BaseLogger from 'MfBase/logger';
import TAGoodsAPI from '@/api/ta-goods';

export default class MeituanService extends BaseService {
    /**
     * @type {MeituanAuthManager}
     */
    authManager;
    /**
     * @type {MeituanCrawler}
     */
    crawler;

    /**
     * @type {MTOrderPrescriptionCrawler}
     */
    MTOrderPrescriptionCrawler;

    /**
     * @type {Logger}
     */
    logger;
    ecMallId;
    authMallCookies;
    unregisterTaskListener;
    scrapeProductsOnceTask;
    scrapeProductsSummaryTask;

    isSyncingProduct = false;
    isSyncingProductSummary = false;
    uuid;
    syncFlag = false; // 美团RPA同步标识

    productIdList = [];
    packageList = [];
    needSyncExtGoodsSkuStocks = [];
    failedProductList = [];

    lastExecutionTimeKey = 'meituan_scrape_products_last_execution';

    syncInfo = {
        total: 0,
        synced: 0,
        progress: 0,
        binded: 0,
    };
    timer = null;
    isOnlyOnSale = false;

    constructor(account, scheduler, bindMallInfo) {
        super(account, scheduler);
        this.platform = 'meituan';
        this.ecMallId = bindMallInfo.ecMallId;
        this.authMallCookies = bindMallInfo.clientCookie ? JSON.parse(bindMallInfo.clientCookie) : [];

        this.handleTaskEvent = this.handleTaskEvent.bind(this);
        this.authManager = MeituanAuthManager.getInstance();
        this.crawler = new MeituanCrawler(account, this.authMallCookies, this.ecMallId);
        this.MTOrderPrescriptionCrawler = new MTOrderPrescriptionCrawler(account, this.authMallCookies);
        this.scheduler.addCrawler(this.platform, this.account, this.crawler);

        this.uuid = localStorage.getItem('OrderCloudUUID');
        this.logger = Logger.create(`MeituanService[${this.account}]`);
        this.logger.info(`MeituanService constructor, account: ${this.account}`, this.authStatus);
    }

    async init() {
        super.init();
        this.logger.info(`MeituanService init, account: ${this.account}`);
        // 初始化时从本地缓存中获取授权状态
        this.authStatus = this.authMallCookies?.length ? AuthStatus.AUTHED : AuthStatus.INIT;
        this._emitEvent(MEITUAN_EVENT_AUTH_STATUS_CHANGED, {
            authStatus: this.authStatus,
        });
    }

    async start() {
        // 定时同步美团RPA标识
        const disabledOrderCloud = localStorage.getItem('disabledOrderCloud');
        if (disabledOrderCloud) return;
        this.syncFlagTimer = setInterval(async () => {
            const flag = await this.syncMTFlag();
            if (flag.isSuccess) {
                !this.syncFlag && await this.startService();
            } else {
                this.destroy();
            }
        }, 1000 * 60 * 1);

        const res = await this.syncMTFlag();
        if (!res.isSuccess) {
            this.logger.info('美团RPA同步标识失败，不开启RPA任务');
            clearInterval(this.syncFlagTimer);
            return;
        }
        await this.startService();
    }

    async startService() {
        this.syncFlag = true;
        this.logger.info(`MeituanService start, account: ${this.account}`);
        this.unregisterTaskListener = this.scheduler.registerTaskListener(this.platform, this.account, this.handleTaskEvent);
        // 检查授权状态，授权状态正常，才会启动其他任务
        const checkAuthResponse = await this.checkAuth();
        if (checkAuthResponse.status === false) {
            this.syncFlag = false;
            // 授权失败关闭任务
            ECAuthAPI.syncMTFlag({
                mallId: this.ecMallId,
                clientId: this.uuid,
                opType: 10,
            });
            clearInterval(this.syncFlagTimer);
            this.logger.info(`start checkAuth failed, ${JSON.stringify(checkAuthResponse)}`);
            return checkAuthResponse;
        }
        this.logger.info('start checkAuth success, start tasks');

        this.scheduleTasks();
    }

    async syncMTFlag() {
        if (!this.uuid) {
            this.uuid = createGUID();
            localStorage.setItem('OrderCloudUUID', this.uuid);
        }
        this.isAuthOrderCloud = localStorage.getItem('isAuthOrderCloud');
        // 美团同步前端同步标识
        const res = await ECAuthAPI.syncMTFlag({
            mallId: this.ecMallId,
            clientId: this.uuid,
            opType: 0,
            isForced: this.isAuthOrderCloud ? 1 : undefined,
        });
        return res;
    }

    async checkAuth() {
        const task = new CrawlerTask(this.platform, this.account, 'checkAuth');
        const response = await this.scheduler.execTaskSync(task);
        if (response.status === false) {
            await this._clearAuth();
        }
        return response;
    }

    async requestAuth() {
        const task = new CrawlerTask(this.platform, this.account, 'requestAuth');
        const response = await this.scheduler.execTaskSync(task);
        if (response.status === true) {
            const result = response.data;
            await this.authManager.addAccount(this.account, result);
            this.authStatus = AuthStatus.AUTHED;
            this._emitEvent(MEITUAN_EVENT_AUTH_STATUS_CHANGED, {
                authStatus: this.authStatus,
            });
            // 授权成功，重新调度任务
            this.scheduleTasks();
            return Response.success(result);
        }
        await this._clearAuth();
        return response;
    }

    getAccount() {
        return {
            extMallId: this.account,
            ecMallId: this.ecMallId,
        };
    }

    async _clearAuth() {
        const accountInfo = this.authManager.getAccount(this.account);
        this.authStatus = accountInfo ? AuthStatus.EXPIRED : AuthStatus.INIT;
        await this.authManager.clearToken(this.account);
        this._emitEvent(MEITUAN_EVENT_AUTH_STATUS_CHANGED, {
            authStatus: this.authStatus,
        });
    }

    scheduleTasks() {
        // 同步订单 & 处方(当待审核订单被审核后，就会产生新订单)
        this.startSyncOrderAndPrescription();

        // 3. 同步商品库存 3分钟后再同步
        // eslint-disable-next-line abc/no-timer-id
        setTimeout(() => {
            this.startSyncProductStock();
        }, 1000 * 60 * 3);
    }

    async productCallbackHandler(data, eventResHandler) {
        const {
            productList, tagList, total,
        } = data;

        // 第一次爬取获取 total 值，后续不获取为 null
        if (total) {
            this.syncInfo.total = total;
            this.syncInfo.progress = 0; // 两个场景，其中一个场景需要读取后重置进度才是对的
            this._emitEvent(MEITUAN_EVENT_SYNC_PRODUCT_SUMMARY_IS_DONE);
        }

        // 当前页的结果
        const packageList = productList.filter((item) => item.combinationLabel === MeituanCombinationLabel.PACKAGE);

        // 同步商品-发送请求 单品组包都一起同步，只是组包的详细信息后面再同步
        const batch = 25;
        for (let i = 0; i < productList.length; i += batch) {
            const list = productList.slice(i, i + batch);
            this.syncInfo.synced += list.length;
            const progress = (this.syncInfo.synced / this.syncInfo.total).toFixed(3);
            if (progress >= 20) {
                this.syncInfo.progress = progress < 100 ? progress : 99;
                this._emitEvent(MEITUAN_EVENT_SYNC_PRODUCT_INFO);
            }

            this._emitEvent(MEITUAN_EVENT_SYNC_PRODUCT_LIST, {
                singleList: list,
                tagList,
            }, eventResHandler);
        }

        productList.forEach((item) => {
            this.productIdList.push(item.id);
        });


        // 同步组包
        if (packageList?.length) {
            this.packageList.push(...packageList);
        }
    }

    async handleProductListChanged() {
        this.logger.info('handleMeituanEvent handleProductListChanged');
        const currentTime = Date.now();
        // 失败商品同步-发送请求
        if (this.failedProductList?.length) {
            const batch = 25;
            for (let i = 0; i < this.failedProductList.length; i += batch) {
                const failedList = this.failedProductList.slice(i, i + batch);
                this._emitEvent(MEITUAN_EVENT_SYNC_PRODUCT_LIST, {
                    singleList: failedList.map((item) => item.singleList),
                    tagList: failedList[0].tagList,
                });
            }
            this.logger.info('handleMeituanEvent syncTaGoodsList success');
        }

        if (!this.isOnlyOnSale) {
            await TAGoodsAPI.deleteMtGoods({
                mallId: this.ecMallId,
                allMtGoodsIds: this.productIdList,
            });
        }

        this.isSyncingProduct = false;
        this.syncInfo.progress = 100;
        this._emitEvent(MEITUAN_EVENT_SYNC_PRODUCT_IS_DONE);

        // 同步组包-发送请求
        for (const item of this.packageList) {
            try {
                const productDetailRes = await this.scrapeProductDetail(item.skuId);
                this._emitEvent(MEITUAN_EVENT_SYNC_PRODUCT_COMPOSE_LIST, productDetailRes);
                this.logger.info('handleMeituanEvent scrapeProductDetail packageList', productDetailRes);
            } catch (error) {
                this.logger.error('handleMeituanEvent scrapeProductDetail packageList error', error);
            }
        }

        // 同步库存
        if (this.needSyncExtGoodsSkuStocks?.length) {
            for (const item of this.needSyncExtGoodsSkuStocks) {
                await this.syncProductStockBySkuId(item.extSkuId, item.quantity);
            }
        }

        // 更新执行时间
        localStorage.setItem(this.lastExecutionTimeKey, currentTime.toString());
        this.needSyncExtGoodsSkuStocks = [];
        this.packageList = [];
        this.productIdList = [];
        this.failedProductList = [];
    }

    // 订单同步比较特殊，除开第一次同步历史订单，剩下都有美团自己定时推送请求，所以只需监听该定时请求
    // service 需要定时给his服务器上报爬虫获取的orderList
    startSyncOrderAndPrescription() {
        // 同步订单
        this.MTOrderPrescriptionCrawler.scrapeOrderAndPrescription();
        // 监听订单变化
        this.MTOrderPrescriptionCrawler.addOrderListListener((orderList) => {
            this._emitEvent(MEITUAN_EVENT_ORDER_LIST_CHANGED, orderList);
        });
        this.MTOrderPrescriptionCrawler.addOrderNewListener((order) => {
            this._emitEvent(MEITUAN_EVENT_ORDER_LIST_NEW, order);
            BaseLogger.report({
                scene: 'meituan-new-order',
                data: {
                    poiName: order?.orderInfo?.unifiedBasicInfo?.poiName,
                    daySeq: order?.orderInfo?.unifiedBasicInfo?.daySeq,
                    orderStatusDesc: order?.orderInfo?.unifiedBasicInfo?.orderStatusDesc,
                    businessType: order?.orderInfo?.businessType,
                },
            });
        });
        this.MTOrderPrescriptionCrawler.addOrderSummaryListener((orderSummary) => {
            this._emitEvent(MEITUAN_EVENT_ORDER_SUMMARY_CHANGED, orderSummary);
        });
        this.MTOrderPrescriptionCrawler.addPrescriptionListener((prescriptionList) => {
            this._emitEvent(MEITUAN_EVENT_PRESCRIPTION_LIST_CHANGED, prescriptionList);
        });
        this.MTOrderPrescriptionCrawler.addSyncIsStartListener(() => {
            this._emitEvent(MEITUAN_EVENT_SYNC_IS_START);
        });
        this.MTOrderPrescriptionCrawler.addSyncIsDoneListener(() => {
            this._emitEvent(MEITUAN_EVENT_SYNC_IS_DONE);
        });
    }

    async startSyncProductStock() {
        const task = new CrawlerTask(this.platform, this.account, 'syncProductStockBySkuId');
        await this.scheduler.execTaskSync(task);
    }

    // 根据skuid同步库存
    async syncProductStockBySkuId() {
        const task = new CrawlerTask(this.platform, this.account, 'syncProductStockBySkuId');
        await this.scheduler.execTaskSync(task);
    }

    // 同步商品总信息
    async scrapeProductsSummaryInfo() {
        const scrapeProductsSummaryTask = new CrawlerTask(this.platform, this.account, 'scrapeProductsSummary', {
            type: 'once',
            immediate: true,
            callback: (total) => {
                this.summaryCallbackHandler(total);
            },
        });
        this.isSyncingProductSummary = true;
        this.scrapeProductsSummaryTask = this.scheduler.scheduleTask(scrapeProductsSummaryTask);
    }

    summaryCallbackHandler(total) {
        this.syncInfo.total = total;
        this.isSyncingProductSummary = false;
        this._emitEvent(MEITUAN_EVENT_SYNC_PRODUCT_SUMMARY_IS_DONE);
    }

    // 同步商品
    async scrapeProductListOnce(onlyOnSale = false, isNeedScrapeTotal = false) {
        if (isNeedScrapeTotal) {
            this.syncInfo.total = 0;
        }
        this.syncInfo.binded = 0;
        this.syncInfo.synced = 0;
        this.syncInfo.progress = 0;
        this.isOnlyOnSale = onlyOnSale;
        this.timer = setInterval(() => {
            if (this.syncInfo.progress < 20) {
                this.syncInfo.progress += 1;
                this._emitEvent(MEITUAN_EVENT_SYNC_PRODUCT_INFO);
            } else {
                clearInterval(this.timer);
                this.timer = null;
            }
        }, 3000);
        const scrapeProductsOnceTask = new CrawlerTask(this.platform, this.account, 'scrapeProducts', {
            type: 'once',
            immediate: true,
            callback: (productList, tagList, total) => {
                this.productCallbackHandler({
                    productList,
                    tagList,
                    total,
                });
            },
        }, -1, {
            onlyOnSale,
            isNeedScrapeTotal,
        });
        this.isSyncingProduct = true;
        this.scrapeProductsOnceTask = this.scheduler.scheduleTask(scrapeProductsOnceTask);
    }

    // 同步指定 skuIds 的商品
    async scrapeProductListBySkuIds(skuIds, eventResHandler) {
        const task = new CrawlerTask(this.platform, this.account, 'scrapeProductsBySkuIds', {
            type: 'once',
            immediate: true,
            callback: (productList) => {
                this.productCallbackHandler({
                    productList,
                }, eventResHandler);
            },
        });
        task.data = {
            skuIds,
        };
        return this.scheduler.execTaskSync(task);
    }

    // 获取商品详情
    scrapeProductDetail(skuId) {
        const task = new CrawlerTask(this.platform, this.account, 'scrapeProductDetail', {
            type: 'once',
        });
        task.data = {
            skuId,
        };
        return this.scheduler.execTaskSync(task);
    }

    async handleTaskEvent(event) {
        this.logger.info(`MeituanService handleTaskEvent, event: ${event}`);
        const SCRAPE_PRODUCTS_TYPE = 'scrapeProducts';
        const SCRAPE_PRODUCTS_SUMMARY_TYPE = 'scrapeProductsSummary';
        const {
            taskId,
        } = event;
        if (taskId !== this.scrapeProductsSummaryTask && taskId !== this.scrapeProductsOnceTask) {
            return;
        }
        switch (event.eventType) {
            case 'success': {
                const {
                    response, task,
                } = event.data;

                if (response.status === false) {
                    // 任务执行失败
                    this.logger.error(`task faile, ${task.type}`, response);
                    if (task.type === SCRAPE_PRODUCTS_TYPE) {
                        this.isSyncingProduct = false;
                        this._emitEvent(MEITUAN_EVENT_SYNC_PRODUCT_IS_FAILED);
                    }
                    if (task.type === SCRAPE_PRODUCTS_SUMMARY_TYPE) {
                        this.isSyncingProductSummary = false;
                        this._emitEvent(MEITUAN_EVENT_SYNC_PRODUCT_SUMMARY_IS_FAILED);
                    }
                } else {
                    this.logger.info(`task success, ${task.type}`, response);
                    if (task.type === SCRAPE_PRODUCTS_TYPE) {
                        this.handleProductListChanged(response);
                    }
                }
                break;
            }
            case 'cancelled': {
                const {
                    task,
                } = event.data;
                if (task.type === SCRAPE_PRODUCTS_TYPE) {
                    this.isSyncingProduct = false;
                    this._emitEvent(MEITUAN_EVENT_SYNC_PRODUCT_IS_FAILED);
                }
                if (task.type === SCRAPE_PRODUCTS_SUMMARY_TYPE) {
                    this.isSyncingProductSummary = false;
                    this._emitEvent(MEITUAN_EVENT_SYNC_PRODUCT_SUMMARY_IS_FAILED);
                }
                this.logger.info(`task cancelled, ${task.type}`);
                break;
            }
            default:
                break;
        }
    }

    _emitEvent(type, data, eventResHandler) {
        const eventData = {
            type,
            data,
            service: this,
            eventResHandler,
        };

        this.emit('event', eventData);
    }

    async destroy() {
        this.logger.info(`MeituanService destroy, account: ${this.account}`);
        super.destroy();
        this.unregisterTaskListener?.();
        // 美团同步前端同步标识
        ECAuthAPI.syncMTFlag({
            mallId: this.ecMallId,
            clientId: this.uuid,
            opType: 10,
        });
        clearInterval(this.syncFlagTimer); // 停止同步标识
        this.scheduler.removeCrawler(this.platform, this.account);
        this.MTOrderPrescriptionCrawler?.stopSync();
        this.syncFlag = false;
    }
}

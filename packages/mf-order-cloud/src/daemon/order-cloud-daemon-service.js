import DesktopAssistManager from './desktop-assitant/desktop-assist-manager';
import CrawlerManager from './crawler/crawler-manager';
import EcOrderWatcherManager from './ec-order-watcher/ec-order-watcher-manager';
import EcGoodsWatcherManager from './ec-goods-watcher/ec-goods-watcher-manager';
import { isClientSupportPharmacy } from '../utils/electron';
import { ChromePuppeteerManager } from '@/daemon/chrome-puppeteer/chrome-puppeteer-manager';

/**
 * 订单云守护进程服务，挂在 window 共享，保证全局唯一
 * 通过 mf 暴露给其他工程使用
 * 内部有三个主要的 Manager:
 * 1. DesktopAssistManager {@link DesktopAssistManager}
 * 用于根据条件决定是否启动桌面助手，管理桌面助手的窗口，以及和桌面助手窗口的双向通信
 * 2. CrawlerManager {@link CrawlerManager}
 * 用于管理电商爬虫，维护多平台，多账号的爬虫调度，并维护爬取的数据，为桌面助手和订单云提供数据
 * 3. EcOrderWatcherManager {@link EcOrderWatcherManager}
 * 用于管理电商订单监听，并维护订单数据，为桌面助手和订单云提供数据
 */
export default class OrderCloudDaemonService {
    // 是否是支持药店管家的客户端，支持的客户端才会实例化桌面助手和电商爬虫
    isClientSupportPharmacy = false;

    isRunning = false;
    /**
     * 不支持药店管家的客户端，为 null
     * @type {DesktopAssistManager | null}
     */
    desktopAssistantManager = null;
    /**
     *
     * 不支持药店管家的客户端，为 null
     * @type {CrawlerManager | null}
     */
    crawlerManager = null;
    /**
     *
     * @type {EcOrderWatcherManager}
     */
    ecOrderWatcherManager = null;

    chromePuppeteerManager = null;
    /**
     *
     * @return {OrderCloudDaemonService}
     */
    static getInstance() {
        if (!window.orderCloudDaemonService) {
            window.orderCloudDaemonService = new OrderCloudDaemonService();
        }
        return window.orderCloudDaemonService;
    }

    constructor() {
        this.isClientSupportPharmacy = isClientSupportPharmacy();
        this._init();
    }

    start() {
        if (this.isRunning) {
            return;
        }
        this.isRunning = true;

        // 启动订单数据监听
        this.ecOrderWatcherManager.start();

        // 启动商品数据监听
        this.ecGoodsWatcherManager?.start();

        // 启动电商爬虫
        this.crawlerManager?.start();

        // 启动桌面助手
        this.desktopAssistantManager?.start();
    }

    stop() {
        this.isRunning = false;

        this.ecOrderWatcherManager?.stop();
        this.ecGoodsWatcherManager?.stop();
        this.crawlerManager?.stop();
        this.desktopAssistantManager?.stop();
    }

    getDesktopAssistManager() {
        return this.desktopAssistantManager;
    }

    getCrawlerManager() {
        return this.crawlerManager;
    }

    getEcOrderWatcherManager() {
        return this.ecOrderWatcherManager;
    }

    getEcGoodsWatcherManager() {
        return this.ecGoodsWatcherManager;
    }

    _init() {
        this.ecOrderWatcherManager = new EcOrderWatcherManager();
        if (this.isClientSupportPharmacy) {
            this.ecGoodsWatcherManager = new EcGoodsWatcherManager();
            this.crawlerManager = new CrawlerManager(this.ecGoodsWatcherManager);
            this.desktopAssistantManager = new DesktopAssistManager(this.ecOrderWatcherManager, this.crawlerManager);
            this.chromePuppeteerManager = new ChromePuppeteerManager();
        }
    }
}

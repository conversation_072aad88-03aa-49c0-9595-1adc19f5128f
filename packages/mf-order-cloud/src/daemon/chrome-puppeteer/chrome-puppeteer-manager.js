const os = window.remote?.require?.('os');
const fs = window.remote?.require?.('fs');
const path = window.remote?.require?.('path');
import Logger from '@/utils/logger';

const logger = Logger.create('Puppeteer Init');

export class ChromePuppeteerManager {

    logger = null;

    constructor() {
        this.logger = logger;
    }

    // 检查 puppeteer-core 是否已安装指定版本
    async isPuppeteerInstalled(requiredVersion) {
        try {
            const puppeteerPath = path.join(window.remote.app.getPath('appData'), 'node_modules', 'puppeteer-core', 'package.json');
            if (fs.existsSync(puppeteerPath)) {
                const packageJson = JSON.parse(fs.readFileSync(puppeteerPath, 'utf-8'));
                return packageJson.version === requiredVersion;
            }
            return false;
        } catch (error) {
            logger.error('Error checking puppeteer-core:', error);
            return false;
        }
    }

    // 检查 Chromium 是否已安装（Windows）
    isChromiumInstalledWin() {
        const { username } = os.userInfo();
        const chromePath = `C:\\Users\\<USER>\\AppData\\Local\\Chromium\\Application\\chrome.exe`;
        const installed = fs.existsSync(chromePath);
        if (installed) {
            window.localStorage.setItem('chromePath', chromePath);
        }
        return installed;
    }

    // 下载文件函数
    async downloadFile(url, outputPath) {
        const { electron } = window;
        if (electron && electron.downloadManager && electron.downloadManager.downloadFile) {
            const downloader = await electron.downloadManager.downloadFile({
                url,
                file: outputPath,
                onComplete: async () => {
                    logger.info('\nDownload complete');
                },
            });
            // 下载完成,开始安装
            const downloadRsp = await downloader.waitComplete();
            return downloadRsp;
        }
    }

    // Windows平台，运行安装程序
    async installChromium(downloadRsp,filePath) {
        return new Promise((resolve, reject) => {
            let installer = null;
            if (downloadRsp?.fullPath) {
                installer = window.require('child_process').exec(`${downloadRsp.fullPath} --silent --install --do-not-launch-chrome`, { detached: true });
            } else {
                installer = window.require('child_process').exec(`${filePath} --silent --install --do-not-launch-chrome`, { detached: true });
            }
            installer.on('close', () => {
                logger.info('Chromium installed');
                resolve();
            });
            installer.stderr.on('error', (data) => {
                console.error('Chromium error\n', data);
                reject(data);
            });

        });
    }

    // Puppeteer
    async executChromePath() {
        const { platform } = process;
        let executablePath;

        if (platform === 'win32') {
            const { username } = os.userInfo();
            executablePath = `C:\\Users\\<USER>\\AppData\\Local\\Chromium\\Application\\chrome.exe`;
        } else if (platform === 'darwin') {
            executablePath = path.join(window.remote.app.getPath('appData'), 'browser', 'chrome-mac', 'chrome-mac' ,'Chromium.app', 'Contents', 'MacOS', 'Chromium');
        } else {
            throw new Error('Unsupported platform for launching Puppeteer');
        }

        if (!fs.existsSync(executablePath)) {
            throw new Error(`Chromium not found at ${executablePath}`);
        }
        return executablePath;
    }

    async launchPuppeteer() {
        try {
            const packageInfo = window.remote.require('puppeteer-core/package.json');
            logger.info(`puppeteer version: ${packageInfo.version}`);

            // 获取平台信息
            const { platform } = process;
            let url, outputDir, filePath;

            if (platform === 'win32') {
                url = 'https://cis-static-dev.oss-cn-shanghai.aliyuncs.com/mf-order-cloud-rpa/chromium_109.0.5414.120-1.1_installer_x86.exe';
                outputDir = path.join(window.remote.app.getPath('appData'), 'browser');
                filePath = path.join(outputDir, 'chromium-installer.exe');
            } else {
                throw new Error('Unsupported platform');
            }

            // 创建浏览器目录
            if (!fs.existsSync(outputDir)) fs.mkdirSync(outputDir, { recursive: true });

            // 下载文件（带存在性检查）
            let downloadRsp = null;
            if (fs.existsSync(filePath)) {
                logger.info('Install file already exists');
            } else {
                logger.info('Downloading Chromium...');
                downloadRsp = await this.downloadFile(url, filePath);
            }

            // 安装/解压处理（带存在性检查）
            if (platform === 'win32') {
                if (this.isChromiumInstalledWin()) {
                    logger.info('Chromium already installed');
                } else {
                    logger.info('Running Chromium installer...');
                    await this.installChromium(downloadRsp,filePath);
                }
            } else if (platform === 'darwin') {
                logger.info('Mac is not supported yet!!');
            }

            logger.info('Chromium ready!');
            const chromePath = await this.executChromePath();
            window.localStorage.setItem('chromePath', chromePath);
            return chromePath;
        } catch (error) {
            console.error('Process failed:', error);
            return null;
        }
    }
}

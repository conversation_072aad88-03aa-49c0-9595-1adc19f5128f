<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        append-to-body
        class="batches-selector-dialog"
        :auto-focus="false"
        size="huge"
        :title="title"
        :show-header-border-bottom="false"
        @close="$emit('close')"
    >
        <abc-layout v-abc-loading="contentLoading" class="dialog-content clearfix">
            <abc-form ref="abcForm" item-no-margin>
                <abc-table
                    :render-config="renderConfig"
                    :data-list="dataList"
                    style="height: 440px;"
                    empty-content="暂无库存"
                    class="batches-selector-table"
                    :custom-tr-class="customTrClass"
                    @sortChange="sortChange"
                >
                    <template #topHeader>
                        <abc-flex justify="space-between" style="width: 100%;">
                            <goods-filed :vertical="false" :goods="productInfo"></goods-filed>
                            <abc-flex
                                v-if="customTopHeadRightHtml"
                                justify="right"
                                align="center"
                                style="min-width: 146px;"
                            >
                                <div style="padding-right: 23px;" v-html="customTopHeadRightHtml"></div>
                            </abc-flex>
                            <abc-flex v-else style="min-width: 231px;" align="center">
                                <span style="min-width: 34px; margin-right: 8px;">总量</span>
                                <abc-form-item
                                    v-if="disabledUnitCount && fixedCount"
                                    required
                                    trigger="change"
                                    :validate-event="validateTotalCount"
                                >
                                    <abc-input-number
                                        v-model="fixedCount"
                                        disabled
                                        button-placement="left"
                                        fixed-button
                                        :input-custom-style="{ 'text-align': 'center' }"
                                        :width="54"
                                    >
                                    </abc-input-number>
                                </abc-form-item>
                                <abc-form-item
                                    v-else
                                    required
                                    trigger="change"
                                    :validate-event="validateTotalCount"
                                >
                                    <abc-input-number
                                        v-model="curUnitCount"
                                        v-abc-focus-selected
                                        fixed-button
                                        :disabled="disabledUnitCount"
                                        button-placement="left"
                                        :width="54"
                                        :config="{
                                            supportZero: false,
                                            max: fixedCount || 100000,
                                            formatLength: supportDecimal ? 2 : undefined
                                        }"
                                        :auto-step="supportDecimal"
                                        @change="handleInput"
                                    >
                                    </abc-input-number>
                                </abc-form-item>
                                <div style="width: 66px; text-align: right;">
                                    <div style="padding-right: 23px;">
                                        {{ curUnit }}
                                    </div>
                                </div>
                            </abc-flex>
                        </abc-flex>
                    </template>
                    <template #batchNo="{ trData }">
                        <abc-table-cell :theme="getBatchNoTheme(trData)">
                            <abc-flex gap="4" justify="space-between" style="width: 100%;">
                                <div class="ellipsis" :title="trData.batchNo">
                                    {{ trData.batchNo }}
                                </div>
                                <abc-tag-v2
                                    v-if="isExpired(trData)"
                                    variant="outline"
                                    theme="danger"
                                    size="small"
                                >
                                    过期
                                </abc-tag-v2>
                            </abc-flex>
                        </abc-table-cell>
                    </template>
                    <template #expiryDate="{ trData }">
                        <abc-table-cell :theme="getBatchNoTheme(trData)">
                            {{ trData.expiryDate }}
                        </abc-table-cell>
                    </template>
                    <template #productionDate="{ trData }">
                        <abc-table-cell>
                            <abc-text :theme="isExpired(trData) ? 'gray-light' : ''">
                                {{ trData.productionDate }}
                            </abc-text>
                        </abc-table-cell>
                    </template>
                    <template #unitCount="{ trData }">
                        <abc-table-cell>
                            <abc-form-item trigger="change" :validate-event="validateCount(trData)">
                                <abc-input-number
                                    v-model="trData.unitCount"
                                    v-abc-focus-selected
                                    fixed-button
                                    button-placement="left"
                                    :width="54"
                                    :config="{
                                        supportZero: true,
                                        formatLength: supportDecimal ? 2 : undefined
                                    }"
                                    :auto-step="supportDecimal"
                                    :disabled="isExpired(trData)"
                                    @change="handleInputBatchCount(trData)"
                                >
                                </abc-input-number>
                            </abc-form-item>
                        </abc-table-cell>
                    </template>
                    <template #unit>
                        <abc-table-cell>
                            {{ curUnit }}
                        </abc-table-cell>
                    </template>
                </abc-table>
            </abc-form>
        </abc-layout>

        <div slot="footer" class="dialog-footer">
            <abc-button @click="handleClickConfirm">
                确定
            </abc-button>
            <abc-button type="blank" @click="closed = true">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script type="text/babel">
    import GoodsFiled from 'MfBase/goods-filed';
    import GoodsAPIV3 from 'MfBase/goods-api-v3';
    import { isShortage } from 'MfBase/validate';
    import {
        isChineseMedicine, isGoods,
    } from 'MfBase/filters';

    import {
        createGUID, clone, 
    } from '@/utils/index.js';
    import Big from 'big.js';

    export default {
        name: 'BatchesSelector',
        components: {
            GoodsFiled,
        },
        props: {
            value: Boolean,
            title: {
                type: String,
                default: '选择批次',
            },
            chargeItem: {
                type: Object,
                required: true,
            },
            onConfirm: {
                type: Function,
                required: true,
            },
            disabledUnitCount: {
                type: Boolean,
                default: true,
            },
            // table topHead 区域允许自定义
            customTopHeadRightHtml: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                visible: false,
                closed: false,
                contentLoading: false,

                renderConfig: {
                    hasInnerBorder: false,
                    list: [
                        {
                            'key': 'batchNo',
                            'label': '生产批号',
                            'style': {
                                'flex': '3',
                                'min-width': '130px',
                            },
                        },
                        {
                            'key': 'expiryDate',
                            'label': '效期',
                            sortable: true,
                            'style': {
                                'flex': 'none',
                                'min-width': '90px',
                                'max-width': '90px',
                            },
                        },
                        {
                            'key': 'productionDate',
                            'label': '生产日期',
                            sortable: true,
                            'style': {
                                'flex': 'none',
                                'min-width': '90px',
                                'max-width': '90px',
                            },
                        },
                        {
                            'key': 'packageCostPrice',
                            'label': '进价',
                            'colType': 'money4',
                            sortable: true,
                            'style': {
                                'flex': '2',
                                'textAlign': 'right',
                            },
                        },
                        {
                            'key': 'inDate',
                            'label': '入库日期',
                            'colType': 'date',
                            sortable: true,
                            'style': {
                                'flex': 'none',
                                'min-width': '90px',
                                'max-width': '90px',
                            },
                        },
                        {
                            'key': 'dispStockGoodsCount',
                            'label': '库存',
                            sortable: true,
                            'style': {
                                'flex': '2',
                            },
                        },{
                            'key': 'unitCount',
                            'label': '数量',
                            'style': {
                                'flex': '2',
                                'textAlign': 'center',
                            },
                        },{
                            'key': 'unit',
                            'label': '单位',
                            'style': {
                                'width': '80px',
                                'maxWidth': '80px',
                                'textAlign': 'center',
                            },
                        },
                    ],
                },
                dataList: [],

                curUnitCount: this.chargeItem.unitCount,
                lastUnitCount: this.chargeItem.unitCount,
                curUnit: this.chargeItem.unit,
                isExpectedBatch: this.chargeItem.isExpectedBatch,
            };
        },
        computed: {
            productInfo() {
                return this.chargeItem.productInfo;
            },
            isChineseMedicine() {
                const {
                    type,
                    subType,
                } = this.productInfo;
                return isChineseMedicine({
                    type,
                    subType,
                });
            },
            goodsSupportDecimal() {
                const {
                    type,
                    subType,
                    pieceNum,
                } = this.productInfo;
                return pieceNum === 1 && isGoods({
                    type,
                    subType,
                });
            },
            supportDecimal() {
                return this.isChineseMedicine || this.goodsSupportDecimal;
            },
            batchInfoDetails() {
                const {
                    batchInfos,
                } = this.chargeItem;
                return batchInfos || [];
            },
            useDismounting() {
                return this.chargeItem.useDismounting;
            },

            totalCountLimit() {
                const {
                    stockPackageCount,
                    stockPieceCount,
                } = this.chargeItem;
                let totalStock = stockPackageCount;
                if (this.useDismounting) {
                    const {
                        pieceNum,
                    } = this.productInfo;
                    totalStock = pieceNum * stockPackageCount + stockPieceCount;
                }
                return totalStock;
            },
            errorInfo() {
                console.log(this.curUnitCount, this.totalCountLimit);
                return {
                    error: this.curUnitCount > this.totalCountLimit,
                    message: `库存不足（${this.totalCountLimit}${this.curUnit}）`,
                };
            },
            fixedCount() {
                // 禁止改变数量，批次和需要等于 this.chargeItem.unitCount
                return this.disabledUnitCount ? this.chargeItem.unitCount : undefined;
            },
        },
        watch: {
            closed(newVal) {
                if (newVal) {
                    this.visible = false;
                    this.destroyElement();
                }
            },
        },
        created() {
            console.log('created');
            this.initHandler();
        },
        methods: {
            customTrClass(trData) {
                if (this.isExpired(trData)) return 'batch-is-expired';
                return '';
            },
            async initHandler() {
                this.contentLoading = true;
                await this.fetchBatchesByGoods();
            },
            getBatchNoTheme(trData) {
                const {
                    expiredWarnFlag,
                } = trData;
                if (expiredWarnFlag === 1) return 'warning';
                if (expiredWarnFlag === 2) return 'danger';
                return undefined;
            },
            // 判断是否过期
            isExpired(trData) {
                const {
                    expiredWarnFlag,
                } = trData;
                return expiredWarnFlag === 2;
            },
            getStockCount(item) {
                const {
                    stockPackageCount = 0,
                    stockPieceCount = 0,
                } = item;
                let totalStock = stockPackageCount;
                if (this.useDismounting) {
                    const {
                        pieceNum,
                    } = this.productInfo;
                    totalStock = pieceNum * stockPackageCount + stockPieceCount;
                }
                return totalStock;
            },
            validateTotalCount(_, callback) {
                if (this.fixedCount && this.curUnitCount !== this.fixedCount) {
                    return callback({
                        validate: false,
                        message: `应出${this.fixedCount}${this.curUnit}，已选数量${this.curUnitCount < this.fixedCount ? '不足' : '过多'}`,
                    });
                }
                return callback({
                    validate: true,
                });
            },
            validateCount(item) {
                const res = isShortage({
                    ...item,
                    unit: this.curUnit,
                    productInfo: this.productInfo,
                });
                if (res.flag) {
                    return (_, callback) => {
                        callback({
                            validate: false,
                            message: res.tips || '无库存',
                        });
                    };
                }
                return (_, callback) => {
                    callback({
                        validate: true,
                    });
                };
            },
            handleInput() {
                const diffCount = Big(this.curUnitCount).minus(this.lastUnitCount).toNumber();
                if (diffCount > 0) {
                    // 增加数量，从最优批次开始加，需要判断库存限制
                    this.addUnitCount(this.dataList, diffCount, 0);
                }
                if (diffCount < 0) {
                    // 减少数量，需要从最差匹配批次倒序减少
                    const reduceSort = this.dataList.length - 1;
                    this.reduceUnitCount(this.dataList, diffCount, reduceSort);
                }

                this.lastUnitCount = this.curUnitCount;
                this.refreshSort();
            },
            addUnitCount(dataList, diffCount, addSort) {
                // 增加数量，直接加到最优解批次（批次填了数量sort越小）上
                dataList.forEach((it, index) => {
                    if (it.addSort === addSort) {
                        const nextItem = dataList[index + 1];
                        if (nextItem && this.isExpired(it) && !this.isExpired(nextItem)) {
                            // 过期的批次不需要增加，直接跳下一个
                            this.addUnitCount(dataList, diffCount, nextItem.addSort);
                        } else {
                            const diff = Big(+it.unitCount).plus(+diffCount).toNumber();
                            const stockCount = this.getStockCount(it);
                            const overLimitCount = Big(diff).minus(stockCount).toNumber();
                            if (overLimitCount > 0) {
                                it.unitCount = stockCount;
                                // 下一批次存在且未过期
                                if (nextItem && dataList.find((item) => item.addSort === addSort + 1) && !this.isExpired(nextItem)) {
                                    this.addUnitCount(dataList, overLimitCount, nextItem.addSort);
                                } else {
                                    it.unitCount = diff;
                                }
                            } else {
                                it.unitCount = diff;
                            }
                        }
                    }
                });
            },
            reduceUnitCount(dataList, diffCount, reduceSort) {
                dataList.forEach((it, index) => {
                    if (it.reduceSort === reduceSort) {
                        const prevItem = dataList[index - 1];
                        if (prevItem && this.isExpired(it) && !this.isExpired(prevItem)) {
                            // 过期的批次不需要减少，直接跳下一个
                            this.reduceUnitCount(dataList, diffCount, prevItem.reduceSort);
                        } else {
                            const diff = Big(+it.unitCount).plus(+diffCount).toNumber();
                            if (diff < 0) {
                                it.unitCount = 0;
                                this.reduceUnitCount(dataList, diff, prevItem.reduceSort);
                            } else {
                                it.unitCount = diff;
                            }
                        }
                    }
                });
            },

            async fetchBatchesByGoods() {
                let packageCount, pieceCount;
                const {
                    useDismounting,
                } = this.chargeItem;

                if (useDismounting) {
                    pieceCount = this.curUnitCount;
                } else {
                    packageCount = this.curUnitCount;
                }
                try {
                    const { data } = await GoodsAPIV3.fetchBatchesByGoods({
                        goodsList: [
                            {
                                goodsId: this.chargeItem.productId,
                                pieceCount,
                                packageCount,
                            },
                        ],
                        pharmacyNo: 0,
                    });
                    if (pieceCount !== this.curUnitCount && packageCount !== this.curUnitCount) return;
                    const batchList = data.rows[0]?.batchList || [];
                    this.batchList = clone(batchList);
                    this.dataList = batchList.map((it, index) => {
                        it.keyId = it.keyId || createGUID();
                        it.addSort = index;
                        it.reduceSort = index;
                        it.unitCount = this.getUnitCount(it, useDismounting);
                        return it;
                    });
                    this.refreshSort();
                } catch (e) {
                    console.error(e);
                } finally {
                    this.contentLoading = false;
                }
            },

            // 有数量的批次sort越靠前
            refreshSort() {
                this.dataList.slice().sort((a,b) => {
                    return new Date(a.expiryDate) - new Date(b.expiryDate);
                }).sort((a,b) => {
                    const aVal = a.unitCount > 0 ? 1 : 0;
                    const bVal = b.unitCount > 0 ? 1 : 0;
                    return bVal - aVal;
                }).forEach((it, index) => {
                    it.addSort = index;
                });
            },
            getUnitCount(batch, useDismounting) {
                if (this.isExpectedBatch) {
                    const res = this.batchInfoDetails.find((detail) => {
                        return `${detail.batchId}` === `${batch.batchId}`;
                    });
                    if (!res) return 0;
                    const {
                        packageCount,
                        pieceCount,
                    } = res;
                    return useDismounting ? pieceCount : packageCount;
                }
                const {
                    cutPieceCount = 0,
                    cutPackageCount = 0,
                } = batch;
                return useDismounting ? cutPieceCount : cutPackageCount;
            },

            handleInputBatchCount() {
                const _curUnitCount = this.dataList.reduce((sum, cur) => {
                    sum = Big(+sum).plus(+cur.unitCount || 0).toNumber();
                    return sum;
                }, 0);
                //
                // if (this.fixedCount && _curUnitCount !== this.fixedCount) {
                //     const diffCount = this.fixedCount - _curUnitCount;
                //
                //     // 需要排除当前操作的item
                //     const _arr = this.dataList.filter((it) => it.keyId !== item.keyId).map((it, index) => {
                //         it.addSort = index;
                //         it.reduceSort = index;
                //         return it;
                //     });
                //
                //     if (diffCount > 0) {
                //         this.addUnitCount(_arr, diffCount, 0);
                //     }
                //     if (diffCount < 0) {
                //         const lastItem = _arr[_arr.length - 1];
                //         this.reduceUnitCount(_arr, diffCount, lastItem?.reduceSort);
                //     }
                //     _curUnitCount = this.fixedCount;
                //
                //     const res = _arr.reduce((sum, cur) => {
                //         sum = Big(+sum).plus(+cur.unitCount || 0).toNumber();
                //         return sum;
                //     }, 0);
                //     // 如果可自动分配的数量不够，就还原到操作的这个批次上
                //     if (res + +item.unitCount !== this.fixedCount) {
                //         item.unitCount += this.fixedCount - +item.unitCount;
                //     }
                // }
                //
                this.lastUnitCount = this.curUnitCount = _curUnitCount;
                this.isExpectedBatch = 1;
                this.refreshSort();
            },

            sortChange(res) {
                const {
                    orderBy,
                    orderType,
                } = res;
                this.dataList.sort((a,b) => {
                    let aVal = a[orderBy];
                    let bVal = b[orderBy];
                    if ([
                        'productionDate',
                        'expiryDate',
                        'inDate',
                    ].indexOf(orderBy) > -1) {
                        aVal = new Date(aVal);
                        bVal = new Date(bVal);
                    } else if (orderBy === 'dispStockGoodsCount') {
                        aVal = this.getStockCount(a);
                        bVal = this.getStockCount(b);
                    }
                    return orderType === 'asc' ? aVal - bVal : bVal - aVal;
                });
            },


            handleClickConfirm() {
                if (!this.curUnitCount) {
                    return this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '数量不能为0',
                    });
                }
                this.$refs.abcForm.validate((val) => {
                    if (val) {
                        this.onConfirm && this.onConfirm({
                            ...this.chargeItem,
                            isExpectedBatch: this.isExpectedBatch,
                            batchInfos: this.dataList.filter((it) => it.unitCount).map((it) => {
                                return {
                                    batchId: it.batchId,
                                    batchNo: it.batchNo,
                                    expiredWarnFlag: it.expiredWarnFlag,
                                    expiryDate: it.expiryDate,
                                    packageCount: this.useDismounting ? 0 : it.unitCount,
                                    pieceCount: this.useDismounting ? it.unitCount : 0,
                                };
                            }),
                        });
                        this.closed = true;
                    }
                });
            },
            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
        },
    };
</script>
<style lang="scss">
    .batches-selector-dialog {
        .batches-selector-table {
            .batch-is-expired {
                color: var(--abc-color-T3);
            }
        }

        .price {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            min-width: 100px;
            margin-right: 10px;
        }
    }
</style>

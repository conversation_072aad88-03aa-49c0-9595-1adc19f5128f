<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        append-to-body
        class="prescription-registration-dialog"
        :auto-focus="false"
        content-styles="max-height: 708px"
        size="huge"
        title="处方登记"
        disabled-keyboard
    >
        <abc-form ref="form" v-abc-loading:page="pageLoading">
            <section>
                <h3 class="group-title">
                    患者信息
                </h3>
                <abc-row :gutter="[24]" wrap="wrap">
                    <abc-col :span="8">
                        <abc-form-item label="姓名">
                            <abc-input
                                v-model="patientData.name"
                                :width="288"
                                clearable
                                disabled
                            ></abc-input>
                        </abc-form-item>
                    </abc-col>
                    <abc-col :span="8">
                        <abc-space
                            is-compact
                            style="align-items: flex-end; margin-bottom: 24px;"
                        >
                            <abc-form-item label="年龄">
                                <abc-input
                                    v-model.number="patientData.age.year"
                                    :width="96"
                                    type="number"
                                    disabled
                                >
                                    <span slot="appendInner">岁</span>
                                </abc-input>
                            </abc-form-item>
                            <abc-form-item>
                                <abc-input
                                    v-model.number="patientData.age.month"
                                    :width="96"
                                    type="number"
                                    disabled
                                >
                                    <span slot="appendInner">月</span>
                                </abc-input>
                            </abc-form-item>
                            <abc-form-item>
                                <abc-input
                                    v-model.number="patientData.age.day"
                                    :width="96"
                                    type="number"
                                    disabled
                                >
                                    <span slot="appendInner">天</span>
                                </abc-input>
                            </abc-form-item>
                        </abc-space>
                    </abc-col>
                    <abc-col :span="8">
                        <abc-form-item label="性别">
                            <abc-radio-group
                                v-model="patientData.sex"
                            >
                                <abc-radio-button label="男" disabled>
                                    男
                                </abc-radio-button>
                                <abc-radio-button label="女" disabled>
                                    女
                                </abc-radio-button>
                            </abc-radio-group>
                        </abc-form-item>
                    </abc-col>
                    <abc-col :span="8">
                        <abc-form-item label="证件">
                            <abc-certificates-type
                                ref="crm-id-card"
                                v-model.trim="patientData.idCard"
                                :cert-type.sync="patientData.idCardType"
                                :cert-type-width="78"
                                :width="288"
                                disabled
                                is-disabled-cert-type
                                clearable
                            ></abc-certificates-type>
                        </abc-form-item>
                    </abc-col>
                    <abc-col :span="8">
                        <abc-form-item label="手机号">
                            <abc-input
                                v-model="patientData.mobile"
                                :width="288"
                                disabled
                                clearable
                            ></abc-input>
                        </abc-form-item>
                    </abc-col>
                </abc-row>
            </section>
            <section>
                <h3 class="group-title">
                    处方信息
                </h3>
                <abc-row :gutter="[24]" wrap="wrap">
                    <abc-col :span="8">
                        <abc-form-item label="处方编号">
                            <abc-input
                                v-model="prescriptionData.no"
                                :width="288"
                                clearable
                                disabled
                                :max-length="64"
                            ></abc-input>
                        </abc-form-item>
                    </abc-col>
                    <abc-col :span="8">
                        <abc-form-item label="诊断">
                            <diagnosis-info
                                v-model="postData.diagnosis"
                                :only-diagnosis-name="true"
                                class="diagnosis-input"
                                :disabled="disabled"
                            ></diagnosis-info>
                        </abc-form-item>
                    </abc-col>
                    <abc-col :span="8">
                        <abc-form-item label="处方医师">
                            <abc-input
                                v-model="prescriptionData.prescriptionPhysician"
                                :width="288"
                                clearable
                                disabled
                                :max-length="32"
                            ></abc-input>
                        </abc-form-item>
                    </abc-col>
                    <abc-col :span="8">
                        <abc-form-item label="审方药师">
                            <abc-input
                                v-model="prescriptionData.reviewerPhysician"
                                :width="288"
                                disabled
                            >
                            </abc-input>
                        </abc-form-item>
                    </abc-col>
                    <abc-col :span="8">
                        <abc-form-item label="复核人" required>
                            <abc-select
                                v-model="postData.reviewer"
                                :width="288"
                                with-search
                                clearable
                                :fetch-suggestions="filterEmployees"
                                :disabled="disabled"
                            >
                                <abc-option
                                    v-for="it in employees"
                                    :key="it.employeeId"
                                    :value="it.employeeId"
                                    :label="it.employeeName"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                    </abc-col>
                    <abc-col :span="8">
                        <abc-form-item label="调配人" required>
                            <abc-select
                                v-model="postData.dispatcher"
                                :width="288"
                                with-search
                                clearable
                                :fetch-suggestions="filterEmployees"
                                :disabled="disabled"
                            >
                                <abc-option
                                    v-for="it in employees"
                                    :key="it.employeeId"
                                    :value="it.employeeId"
                                    :label="it.employeeName"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                    </abc-col>
                    <abc-col :span="8">
                        <abc-form-item label="备注">
                            <abc-input
                                v-model="postData.remark"
                                :width="288"
                                :max-length="128"
                                clearable
                                :disabled="disabled"
                            ></abc-input>
                        </abc-form-item>
                    </abc-col>
                </abc-row>
            </section>
            <section v-if="!disabled">
                <h3 class="group-title">
                    上传处方
                </h3>
                <abc-row :gutter="[24]" wrap="wrap">
                    <abc-col :span="24">
                        <abc-form-item>
                            <external-file
                                v-model="attachments"
                                :business-type="14"
                                oss-filepath="register-prescription"
                                :max-upload-count="20"
                                :patient-info="patientInfo"
                                business-desc="处方登记"
                                width="96px"
                                height="96px"
                                :accept="['.jpg', '.JPG', '.jpeg', '.JPEG', '.png', '.PNG', '.pdf', '.PDF']"
                                upload-description="处方附件持图片、PDF格式"
                            ></external-file>
                        </abc-form-item>
                    </abc-col>
                </abc-row>
            </section>
        </abc-form>
        <div slot="footer" class="dialog-footer">
            <abc-button :disabled="disabled" :loading="confirmLoading" @click="onConfirm">
                保存
            </abc-button>
            <abc-button type="blank" @click="showDialog = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import { mapGetters } from 'vuex';
    import ECOrderAPI from '@/api/order';
    import ExternalFile from 'MfBase/external-file';
    import AbcSocket from 'MfBase/single-socket';
    import { ROLE_PHARMACY_DOCTOR } from 'MfBase/constants';
    import LocalStore from 'MfBase/utils/localStorage-handler';
    import DiagnosisInfo from 'MfBase/diagnosis-info';

    const _CONFIG_KEY = '_prescription_registration_';

    export default {
        name: 'PrescriptionRegistrationDialog',
        components: {
            ExternalFile,
            DiagnosisInfo,
        },
        props: {
            value: Boolean,
            orderId: {
                type: String,
                required: true,
            },
            extOrderId: {
                type: String,
                required: true,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                prescriptionData: {
                    no: '', // 处方编号
                    prescriptionPhysician: '', // 处方医师
                    reviewerPhysician: '', // 审方医师
                },
                postData: {
                    reviewer: '', // 复核人
                    dispatcher: '', // 调配人
                    remark: '',
                    prescriptionUrls: [],
                    diagnosis: [], // 诊断信息
                },
                patientData: {
                    name: '',
                    sex: '',
                    age: {
                        day: null,
                        month: null,
                        year: null,
                    },
                },
                employees: [],
                attachments: [],
                pageLoading: false,
                confirmLoading: false,
            };
        },
        computed: {
            ...mapGetters([
                'employeeList',
                'currentClinic',
            ]),
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            patientInfo() {
                const {
                    name, sex, age,
                } = this.patientData;
                return {
                    name,
                    sex,
                    age,
                };
            },
        },
        created() {
            this.initHandler();
            const { socket } = AbcSocket.getSocket();
            this._socket = socket;
            this._socket.on('short-url.upload_attachment', this.handleImages);
        },
        beforeDestroy() {
            this._socket?.off('short-url.upload_attachment', this.handleImages);
        },
        methods: {
            async initHandler() {
                this.employees = this.employeeList?.filter((it) => it.roleIds?.includes(ROLE_PHARMACY_DOCTOR)) || [];
                try {
                    this.pageLoading = true;
                    await this.fetchOrderPrescription();
                } catch (e) {
                    console.error(e);
                } finally {
                    this.pageLoading = false;
                }
            },
            filterEmployees(key = '') {
                const filterKey = key.trim().toLocaleLowerCase();
                this.employees = this.employeeList
                    .filter((it) => it.roleIds?.includes(ROLE_PHARMACY_DOCTOR))
                    .filter((item) => {
                        return item.employeeName.includes(filterKey) ||
                            item.employeeNamePy.toLocaleLowerCase().includes(filterKey) ||
                            item.employeeNamePyFirst.toLocaleLowerCase().includes(filterKey);
                    });
            },
            async fetchOrderPrescription() {
                if (!this.orderId || !this.extOrderId) return;

                const res = await ECOrderAPI.fetchOrderPrescription({
                    orderId: this.orderId,
                    extOrderId: this.extOrderId,
                });
                if (!res) return;
                const {
                    identityView,
                    prescriptionView,
                } = res;
                Object.assign(this.patientData, identityView);
                const {
                    no,
                    prescriptionPhysician,
                    reviewerPhysician,
                    dispatcher,
                    reviewer,
                    prescriptionUrls,
                    diagnosis = [],
                    remark,
                } = prescriptionView || {};
                Object.assign(this.prescriptionData, {
                    no,
                    prescriptionPhysician,
                    reviewerPhysician: reviewerPhysician?.name,
                });
                Object.assign(this.postData, {
                    dispatcher: dispatcher?.id,
                    reviewer: reviewer?.id,
                    diagnosis: diagnosis[0]?.value || '',
                    remark: remark || '',
                });
                this.attachments = (prescriptionUrls || []).map((x) => ({
                    url: x,
                }));
            },
            onConfirm() {
                this.$refs.form.validate(async (valid) => {
                    if (valid) {
                        try {
                            this.confirmLoading = true;
                            this.postData.prescriptionUrls = this.attachments.map((x) => x.url);
                            await ECOrderAPI.updateOrderPrescription(this.orderId, this.extOrderId, {
                                ...this.postData,
                                diagnosis: [
                                    {
                                        toothNos: null,
                                        value: this.postData.diagnosis,
                                    },
                                ],
                            });
                            const {
                                reviewer,
                                dispatcher,
                            } = this.postData;
                            LocalStore.setObj(_CONFIG_KEY, this._currentClinicId,{
                                reviewer,
                                dispatcher,
                            });
                            this.$Toast({
                                message: '保存成功',
                                type: 'success',
                            });
                            this.showDialog = false;
                        } catch (e) {
                            console.error(e);
                            this.$Toast({
                                message: e.message,
                                type: 'error',
                            });
                        } finally {
                            this.confirmLoading = false;
                        }
                    }
                });
            },
            handleImages(data) {
                const {
                    attachments = [],
                    businessType,
                } = data;
                if (+businessType === 14 && attachments?.length) {
                    attachments.forEach((item) => {
                        const isExist = this.attachments.find((it) => it.id === item.id);
                        if (!isExist) {
                            this.attachments.push(item);
                        }
                    });
                }
            },
        },
    };
</script>
<style lang="scss">
    .prescription-registration-dialog {
        .abc-form {
            .group-title {
                margin-bottom: 12px;
                font-size: 16px;
                line-height: 24px;
            }

            .diagnosis-input {
                width: 288px;
            }
        }
    }
</style>

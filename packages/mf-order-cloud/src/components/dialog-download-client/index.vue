<template>
    <abc-modal v-model="visible" :show-footer="false" style="text-align: center;">
        <abc-text size="large" bold>
            需在ABC客户端使用
        </abc-text>

        <div style="width: 372px; padding: 8px 24px 16px 24px; text-align: center;">
            <div>
                <abc-text theme="gray-light">
                    当前为网页浏览器，无法使用此功能，
                </abc-text>
            </div>
            <div>
                <abc-text theme="gray-light">
                    请在ABC客户端中使用。
                </abc-text>
            </div>

            <div style="padding: 32px 0 24px 0;">
                <abc-flex vertical gap="8" align="center">
                    <abc-image
                        width="80"
                        height="80"
                        :src="IconAbc"
                    ></abc-image>
                    <div style="height: 14px;">
                        <abc-text theme="gray-light">
                            {{ desktopVersion }}
                        </abc-text>
                    </div>
                </abc-flex>
            </div>

            <abc-flex vertical align="center" style="margin-top: 16px;">
                <abc-space size="middle">
                    <abc-button
                        :width="120"
                        variant="ghost"
                        size="large"
                        @click="visible = false"
                    >
                        {{ canOpenClient ? '取消' : '暂不下载' }}
                    </abc-button>
                    <a v-if="!canOpenClient" href="//static-common-cdn.abcyun.cn/apks/abc_pc/abcyun-desktop-win-latest.exe">
                        <abc-button :width="120" size="large" @click="downloadAbcClient">
                            下载客户端
                        </abc-button>
                    </a>
                    <abc-button
                        v-else
                        :width="120"
                        size="large"
                        @click="openAbcClient"
                    >
                        打开客户端
                    </abc-button>
                </abc-space>
            </abc-flex>
        </div>
    </abc-modal>
</template>

<script>
    import IconAbc from '../../assets/images/setting/icon-abc.png';
    import DashboardAPI from 'MfBase/dashboard';
    import ECDesktopAssistantStatAPI from '../../api/desktop-assistant';
    export default {
        name: 'DialogDownloadClient',
        data() {
            return {
                IconAbc,
                visible: false,
                desktopVersion: '',
                canOpenClient: false,
            };
        },
        async created() {
            const res = await ECDesktopAssistantStatAPI.getIsInstallClient();
            this.canOpenClient = res?.ok;
            const list = await DashboardAPI.getAppVersion();
            this.desktopVersion = list?.find((item) => item.appId === 'abcyun-desktop-pc')?.version;
        },
        methods: {
            downloadAbcClient() {
                this.visible = false;
            },
            openAbcClient() {
                ECDesktopAssistantStatAPI.openClientByGet();
                this.visible = false;
            },
        },
    };
</script>

<template>
    <abc-popover
        ref="popover"
        placement="bottom-start"
        trigger="click"
        :visible-arrow="false"
        theme="yellow"
        padding-size="large"
        style="height: 100%;"
        :popper-style="{
            padding: 0
        }"
        @show="handleOpen"
    >
        <abc-table-cell
            slot="reference"
            class="ecommerce-table-cell"
            vertical
            align="flex-start"
            justify="center"
            clickable
        >
            <abc-space>
                <abc-tag-v2
                    v-if="orderInfo.refundStatus === ECOrderRefundStatusEnum.HANDING"
                    shape="square"
                    size="mini"
                    theme="danger"
                    variant="outline"
                >
                    售后中
                </abc-tag-v2>
                <abc-tag-v2
                    v-if="orderInfo.refundStatus === ECOrderRefundStatusEnum.REFUND_SUCCESS"
                    shape="square"
                    size="mini"
                    theme="danger"
                    variant="outline"
                >
                    售后成功
                </abc-tag-v2>
                <abc-tag-v2
                    v-if="orderInfo.mergeStatus === MergeStatusEnum.WAIT"
                    shape="square"
                    size="mini"
                    theme="warning"
                    variant="outline"
                >
                    待合单
                </abc-tag-v2>
                <abc-tag-v2
                    v-if="orderInfo.receiverChanged"
                    shape="square"
                    size="mini"
                    theme="warning"
                    variant="outline"
                >
                    地址变更
                </abc-tag-v2>
                <abc-tag-v2
                    v-if="hasMergedOrder"
                    shape="square"
                    size="mini"
                    theme="success"
                    variant="outline"
                >
                    合单·{{ mergedOrders.length }}
                </abc-tag-v2>
                <div class="ecommerce-table-cell__row">
                    {{ curReceiverInfo.receiverNameMask }} {{ curReceiverInfo.receiverPhoneMask }}
                </div>
            </abc-space>
            <div class="ecommerce-table-cell__row">
                <div class="ellipsis">
                    {{ curReceiverInfo.receiverAddressMask }}
                </div>
            </div>
        </abc-table-cell>

        <order-popover
            :order-info="orderInfo"
            :disabled="disabled"
            :can-merge-order-list="canMergeOrderList"
            @refresh="handleRefresh"
            @close="handleClose"
        ></order-popover>
    </abc-popover>
</template>

<script>
    import {
        ECOrderRefundStatusEnum, MergeStatusEnum,
    } from '../utils/constants.js';
    import ECOrderAPI from '../api/order.js';
    import OrderPopover from './table-cell-order-popover.vue';

    export default {
        components: {
            OrderPopover,
        },
        props: {
            orderInfo: {
                required: true,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                MergeStatusEnum,
                ECOrderRefundStatusEnum,
                canMergeOrderList: [],
                btnLoading: false,
            };
        },
        computed: {
            curReceiverInfo() {
                return this.orderInfo.receiverInfo || {};
            },
            mergedOrders() {
                return this.orderInfo.detailOrders || [];
            },
            hasMergedOrder() {
                return this.orderInfo.mergeStatus === MergeStatusEnum.MERGE_ED && this.mergedOrders.length > 0;
            },
        },
        methods: {
            handleRefresh() {
                this.canMergeOrderList = [];
                this.handleOpen();
                this.$emit('refresh');
            },
            handleClose() {
                this.$refs.popover.doClose();
            },
            async handleOpen() {
                if (this.orderInfo.mergeStatus !== MergeStatusEnum.WAIT) return;
                try {
                    const res = await ECOrderAPI.getCanMergeOrders({
                        orderId: this.orderInfo.id,
                    });
                    this.canMergeOrderList = res?.orderList || [];
                } catch (e) {
                    console.error(e);
                }
            },
        },
    };
</script>

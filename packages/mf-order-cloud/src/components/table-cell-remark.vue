<template>
    <abc-popover
        ref="popover"
        placement="bottom-start"
        trigger="click"
        :visible-arrow="false"
        theme="yellow"
        padding-size="large"
        size="large"
        :disabled="disabled"
        style="height: 100%;"
        @show="handleShow"
    >
        <abc-table-cell
            slot="reference"
            class="ecommerce-table-cell"
            vertical
            align="flex-start"
            justify="center"
            clickable
        >
            <div v-if="buyerMemo" class="ecommerce-table-cell__remark" :title="buyerMemo">
                <span class="warning">买家</span>{{ buyerMemo }}
            </div>
            <div v-if="remark" class="ecommerce-table-cell__remark" :title="remark">
                <span class="success">商家</span>{{ remark || '' }}
            </div>
            <template v-if="!buyerMemo && !remark">
                -
            </template>
        </abc-table-cell>

        <div id="table-cell-remark-popover">
            <h5>买家留言</h5>
            <p>{{ buyerMemo || '-' }}</p>
            <abc-divider
                theme="dark"
                margin="mini"
                variant="dashed"
                style="margin: 8px 0;"
            ></abc-divider>
            <h5>商家留言</h5>
            <abc-edit-div v-model="curRemark" :width="334" :maxlength="100"></abc-edit-div>
            <abc-flex align="center" justify="end" style="margin-top: 16px;">
                <abc-button :loading="btnLoading" @click="handleClickConfirm">
                    确定
                </abc-button>
                <abc-button type="blank" @click="handleClose">
                    取消
                </abc-button>
            </abc-flex>
        </div>
    </abc-popover>
</template>

<script>
    import ECOrderAPI from '../api/order';

    export default {
        name: 'TableCellRemark',
        props: {
            order: {
                type: Object,
                required: true,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                btnLoading: false,
                curRemark: '',
            };
        },
        computed: {
            remark() {
                return this.order.remark || '';
            },
            buyerMemo() {
                return this.order.buyerMemo || '';
            },
        },
        methods: {
            handleShow() {
                this.curRemark = this.order.remark || '';
            },
            handleClose() {
                this.$refs.popover.doClose();
            },
            async handleClickConfirm() {
                try {
                    this.btnLoading = true;
                    await ECOrderAPI.updateOrderRemark({
                        orderIds: [this.order.id],
                        note: this.curRemark,
                    });
                    this.$set(this.order, 'remark', this.curRemark);
                    this.handleClose();
                } catch (e) {
                    console.error(e);
                } finally {
                    this.btnLoading = false;
                }
            },
        },
    };
</script>

<style lang="scss">
@import "../styles/theme.scss";
@import "../styles/mixin.scss";

#table-cell-remark-popover {
    width: 332px;
    max-height: 280px;

    h5 {
        margin-bottom: 8px;
        font-size: 14px;
        font-weight: bold;
        line-height: 22px; /* 157.143% */
        color: var(--abc-color-T1, #000000);
    }
}
</style>

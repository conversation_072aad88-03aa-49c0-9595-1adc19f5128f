<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        append-to-body
        size="small"
        title="修改收件信息"
        @close="$emit('close')"
    >
        <abc-layout class="dialog-content clearfix">
            <abc-form
                ref="abcForm"
                label-width="72"
                item-block
                label-position="left"
            >
                <abc-form-item label="收件人">
                    <abc-input
                        v-model="postData.name"
                        :width="240"
                        placeholder="收件人"
                    ></abc-input>
                </abc-form-item>
                <abc-form-item label="手机号" :validate-event="validateMobile">
                    <abc-input
                        v-model="postData.mobile"
                        :width="240"
                        :max-length="11"
                        type="phone"
                        placeholder="手机号"
                    ></abc-input>
                </abc-form-item>
                <abc-form-item label="收件地址">
                    <abc-address-selector v-model="postData" :width="240"></abc-address-selector>
                </abc-form-item>
                <abc-form-item label=" ">
                    <abc-input
                        v-model.trim="postData.addressDetail"
                        :width="240"
                        trim
                        placeholder="详细地址"
                    ></abc-input>
                </abc-form-item>
            </abc-form>
        </abc-layout>

        <div slot="footer" class="dialog-footer">
            <abc-button :loading="btnLoading" @click="handleClickConfirm">
                确定
            </abc-button>
            <abc-button type="blank" @click="closed = true">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script type="text/babel">
    import ECOrderAPI from '@/api/order.js';
    import { validateMobile } from 'MfBase/validate';

    export default {
        name: 'DialogReceiverForm',
        components: {
        },
        props: {
            defaultData: {
                type: Object,
                required: true,
            },
            ecType: {
                type: Number,
                required: true,
            },
            orderId: {
                type: String,
                required: true,
            },
            onConfirm: {
                type: Function,
                required: true,
            },
        },
        data() {
            return {
                visible: false,
                closed: false,
                contentLoading: false,
                btnLoading: false,

                postData: {
                    name: '',
                    mobile: '',
                    addressCityId: '',
                    addressCityName: '',
                    addressProvinceId: '',
                    addressProvinceName: '',
                    addressDistrictId: '',
                    addressDistrictName: '',
                    addressDetail: '',
                },
            };
        },
        watch: {
            closed(newVal) {
                if (newVal) {
                    this.visible = false;
                    this.destroyElement();
                }
            },
        },
        created() {
            if (this.defaultData) {
                Object.assign(this.postData, {
                    name: this.defaultData.receiverNameMask,
                    mobile: this.defaultData.receiverPhoneMask,
                });
            }
        },
        methods: {
            validateMobile,
            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
            handleClickConfirm() {
                this.submitHandler();
            },
            async submitHandler() {
                try {
                    this.btnLoading = true;
                    await ECOrderAPI.updateOrderAddress({
                        ecType: this.ecType,
                        orderId: this.orderId,
                        receiverName: this.postData.name,
                        receiverPhone: this.postData.mobile,
                        address: this.postData.addressDetail,
                        city: this.postData.addressCityName,
                        cityId: this.postData.addressCityId,
                        province: this.postData.addressProvinceName,
                        provinceId: this.postData.addressProvinceId,
                        town: this.postData.addressDistrictName,
                        townId: this.postData.addressDistrictId,
                    });
                } catch (e) {
                    console.error(e);
                } finally {
                    this.btnLoading = false;
                }
            },

        },
    };
</script>
<style lang="scss">
    .batches-selector-dialog {
        .price {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            min-width: 100px;
            margin-right: 10px;
        }
    }
</style>

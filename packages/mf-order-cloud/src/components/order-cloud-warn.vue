<template>
    <div class="order-cloud-warn-wrapper">
        <abc-space>
            <abc-popover
                ref="orderPopover"
                placement="bottom-end"
                theme="white"
                @show="handleOrderShow"
            >
                <abc-button
                    id="order-cloud-warn-exception-btn"
                    slot="reference"
                    theme="default"
                    shape="square"
                    variant="text"
                    size="normal"
                    icon="s-b-report-line"
                >
                    订单异常
                    <span v-if="hasAuth" :class="exceptionOrderCount ? 'warn' : 'gray'">
                        {{ exceptionOrderCount }}
                    </span>
                    <span v-else class="warn" style="margin-left: 4px;">未开启</span>
                </abc-button>

                <div id="order-cloud-warn-popover">
                    <div v-for="(mall, index) in authMallList" :key="mall.extMallId" class="mall-group">
                        <div class="mall-name">
                            {{ mall.mallName }}
                        </div>

                        <template v-if="mall.FEInfo && mall.FEInfo.authStatus === AuthStatus.AUTHED">
                            <template v-if="getExceptionInfos(mall).length">
                                <abc-flex
                                    v-for="item in getExceptionInfos(mall)"
                                    :key="item.label"
                                    class="mall-cell"
                                    justify="space-between"
                                    @click="handleOpenEcPage(mall, item.page)"
                                >
                                    {{ item.label }}
                                    <span class="warn">{{ item.count }}</span>
                                </abc-flex>
                            </template>

                            <div v-else class="mall-cell is-disabled">
                                暂无订单异常
                            </div>
                        </template>

                        <abc-flex
                            v-else
                            class="mall-cell"
                            justify="space-between"
                            @click.native="handleClickAuth(mall)"
                        >
                            <div v-if="mall.FEInfo && mall.FEInfo.authStatus === AuthStatus.EXPIRED">
                                授权失效，请登录网店重新授权
                            </div>
                            <div v-else>
                                未开启，可登录网店授权开启
                            </div>
                            <abc-button type="text">
                                去授权
                            </abc-button>
                        </abc-flex>
                        <abc-divider
                            v-if="index !== authMallList.length - 1"
                            size="normal"
                            theme="dark"
                            margin="mini"
                            variant="dashed"
                        ></abc-divider>
                    </div>
                </div>
            </abc-popover>

            <abc-popover
                ref="tracePopover"
                placement="bottom-end"
                theme="white"
                @show="handleTraceShow"
            >
                <abc-button
                    id="order-cloud-warn-trace-btn"
                    slot="reference"
                    theme="default"
                    shape="square"
                    variant="text"
                    size="normal"
                    icon="s-b-report-line"
                >
                    物流预警
                    <span v-if="hasAuth" :class="orderTraceCount ? 'warn' : 'gray'">
                        {{ orderTraceCount }}
                    </span>
                    <span v-else class="warn" style="margin-left: 4px;">未开启</span>
                </abc-button>

                <div id="order-cloud-warn-popover">
                    <div v-for="(mall, index) in authMallList" :key="mall.extMallId" class="mall-group">
                        <div class="mall-name">
                            {{ mall.mallName }}
                        </div>

                        <template v-if="mall.FEInfo && mall.FEInfo.authStatus === AuthStatus.AUTHED">
                            <template v-if="getTraceInfos(mall).length">
                                <abc-flex
                                    v-for="item in getTraceInfos(mall)"
                                    :key="item.label"
                                    class="mall-cell"
                                    justify="space-between"
                                    @click="handleOpenEcPage(mall, item.page)"
                                >
                                    {{ item.label }}
                                    <span class="warn">{{ item.count }}</span>
                                </abc-flex>
                            </template>

                            <div v-else class="mall-cell is-disabled">
                                暂无物流预警
                            </div>
                        </template>

                        <abc-flex
                            v-else
                            class="mall-cell"
                            justify="space-between"
                            @click.native="handleClickAuth(mall)"
                        >
                            <div v-if="mall.FEInfo && mall.FEInfo.authStatus === AuthStatus.EXPIRED">
                                授权失效，请登录网店重新授权
                            </div>
                            <div v-else>
                                未开启，可登录网店授权开启
                            </div>
                            <abc-button type="text">
                                去授权
                            </abc-button>
                        </abc-flex>
                        <abc-divider
                            v-if="index !== authMallList.length - 1"
                            size="normal"
                            theme="dark"
                            margin="mini"
                            variant="dashed"
                        ></abc-divider>
                    </div>
                </div>
            </abc-popover>
        </abc-space>
    </div>
</template>

<script type="text/ecmascript-6">
    import { OrderCloudDaemonService } from '../daemon/index.js';
    import {
        AuthStatus, PddPage, ResponseErrorCode,
    } from '../daemon/crawler/common/constants.js';
    import { EcShopTypeEnum } from '@/utils/constants';

    export default {
        name: 'OrderCloudWarn',
        components: {

        },
        data() {
            return {
                AuthStatus,
                authMallList: [], // 授权门店列表
            };
        },
        computed: {
            hasAuth() {
                return this.authMallList.some((mall) => {
                    return mall.FEInfo?.authStatus === AuthStatus.AUTHED;
                });
            },
            exceptionOrderCount() {
                return this.authMallList.reduce((acc, cur) => {
                    const {
                        exceptionList,
                    } = cur.FEInfo || {};
                    acc += exceptionList?.orderCount || 0;
                    return acc;
                }, 0);
            },
            orderTraceCount() {
                return this.authMallList.reduce((acc, cur) => {
                    const {
                        orderTraceCount = {},
                    } = cur.FEInfo || {};
                    acc += orderTraceCount?.acceptUnTraceErrorCount || 0;
                    acc += orderTraceCount?.acceptUnTraceWarnCount || 0;
                    acc += orderTraceCount?.packageReturnCount || 0;
                    acc += orderTraceCount?.shippedUnAcceptErrorCount || 0;
                    acc += orderTraceCount?.shippedUnAcceptWarnCount || 0;
                    acc += orderTraceCount?.stationErrorCount || 0;
                    acc += orderTraceCount?.stationWarnCount || 0;
                    acc += orderTraceCount?.traceErrorCount || 0;
                    acc += orderTraceCount?.traceWarnCount || 0;
                    return acc;
                }, 0);
            },
        },
        created() {
            this.crawlerManager = OrderCloudDaemonService.getInstance().getCrawlerManager();
            if (this.crawlerManager) {
                this.refreshAuthStatus();
                this.crawlerManager.addListener(this.refreshAuthStatus);
                this.$on('hook:beforeDestroy', () => {
                    this.crawlerManager.removeListener(this.refreshAuthStatus);
                });
            }
        },
        methods: {
            async refreshAuthMallList() {
                if (!this.crawlerManager) return;
                const authMallList = await this.crawlerManager.getAuthMallList();
                this.authMallList = authMallList.filter((mall) => mall.shopType === EcShopTypeEnum.B2C);
            },
            handleClickAuth(mall) {
                this.requestAuth(mall.extMallId);
            },
            getExceptionInfos(mall) {
                const {
                    exceptionList = {},
                } = mall.FEInfo || {};
                const _arr = [];

                if (exceptionList.modifyReceiverAfterPrint) {
                    _arr.push({
                        label: '打印后改地址',
                        count: exceptionList.modifyReceiverAfterPrint,
                        page: PddPage.EXCEPTION_ORDER_MODIFY_RECEIVER_AFTER_PRINT,
                    });
                }
                if (exceptionList.modifyRemarkAfterPrint) {
                    _arr.push({
                        label: '打印后改备注',
                        count: exceptionList.modifyRemarkAfterPrint,
                        page: PddPage.EXCEPTION_ORDER_MODIFY_REMARK_AFTER_PRINT,
                    });
                }
                if (exceptionList.afterSaleAfterPrint) {
                    _arr.push({
                        label: '打印后发生售后',
                        count: exceptionList.afterSaleAfterPrint,
                        page: PddPage.EXCEPTION_ORDER_AFTER_SALE_AFTER_PRINT,
                    });
                }
                if (exceptionList.modifyRemarkBeforeTms) {
                    _arr.push({
                        label: '揽件前改备注',
                        count: exceptionList.modifyRemarkBeforeTms,
                        page: PddPage.EXCEPTION_ORDER_MODIFY_REMARK_BEFORE_TMS,
                    });
                }
                if (exceptionList.afterSaleBeforeTms) {
                    _arr.push({
                        label: '揽件前发生售后',
                        count: exceptionList.afterSaleBeforeTms,
                        page: PddPage.EXCEPTION_ORDER_AFTER_SALE_BEFORE_TMS,
                    });
                }

                return _arr;
            },
            getTraceInfos(mall) {
                const {
                    orderTraceCount = {},
                } = mall.FEInfo || {};
                const _arr = [];

                if (orderTraceCount.shippedUnAcceptWarnCount) {
                    _arr.push({
                        label: '揽件即将超时',
                        count: orderTraceCount.shippedUnAcceptWarnCount,
                        page: PddPage.LOGISTIC_WARN_SHIPPED_UN_ACCEPT_WARN,
                    });
                }
                if (orderTraceCount.shippedUnAcceptErrorCount) {
                    _arr.push({
                        label: '揽件超时',
                        count: orderTraceCount.shippedUnAcceptErrorCount,
                        page: PddPage.LOGISTIC_WARN_SHIPPED_UN_ACCEPT_ERROR,
                    });
                }
                if (orderTraceCount.acceptUnTraceWarnCount) {
                    _arr.push({
                        label: '揽件后更新即将超时',
                        count: orderTraceCount.acceptUnTraceWarnCount,
                        page: PddPage.LOGISTIC_WARN_ACCEPT_UN_TRACE_WARN,
                    });
                }
                if (orderTraceCount.acceptUnTraceErrorCount) {
                    _arr.push({
                        label: '揽件后更新超时',
                        count: orderTraceCount.acceptUnTraceErrorCount,
                        page: PddPage.LOGISTIC_WARN_ACCEPT_UN_TRACE_ERROR,
                    });
                }
                if (orderTraceCount.stationWarnCount) {
                    _arr.push({
                        label: '分拨中心停留即将超时',
                        count: orderTraceCount.stationWarnCount,
                        page: PddPage.LOGISTIC_WARN_STATION_WARN,
                    });
                }
                if (orderTraceCount.stationErrorCount) {
                    _arr.push({
                        label: '分拨中心超时',
                        count: orderTraceCount.stationErrorCount,
                        page: PddPage.LOGISTIC_WARN_STATION_ERROR,
                    });
                }
                if (orderTraceCount.traceWarnCount) {
                    _arr.push({
                        label: '物流更新即将超时',
                        count: orderTraceCount.traceWarnCount,
                        page: PddPage.LOGISTIC_WARN_TRACE_WARN,
                    });
                }
                if (orderTraceCount.traceErrorCount) {
                    _arr.push({
                        label: '物流更新超时',
                        count: orderTraceCount.traceErrorCount,
                        page: PddPage.LOGISTIC_WARN_TRACE_ERROR,
                    });
                }

                if (orderTraceCount.packageReturnCount) {
                    _arr.push({
                        label: '包裹被原路退回',
                        count: orderTraceCount.packageReturnCount,
                        page: PddPage.LOGISTIC_WARN_PACKAGE_RETURN,
                    });
                }

                return _arr;
            },
            handleOrderShow() {
                this.refreshAuthStatus();
            },
            handleTraceShow() {
                this.refreshAuthStatus();
            },
            async requestAuth(extMallId) {
                if (!this.crawlerManager) return;
                const response = await this.crawlerManager.requestAuth(extMallId);
                // 授权失败 && 非用户取消授权，则提示信息
                console.log('response',response);
                if (response.status === false) {
                    if (response.data?.code !== ResponseErrorCode.REQUEST_AUTH_CANCEL) {
                        this.$alert({
                            type: 'warn',
                            title: '授权失败',
                            content: response.message,
                        });
                    }
                    return;
                }
                this.$Toast({
                    type: 'success',
                    message: '授权成功，正在同步异常信息',
                });
                this.refreshAuthStatus();
            },

            async refreshAuthStatus() {
                this.refreshAuthMallList();
            },
            handleOpenEcPage(mall, page) {
                this.crawlerManager?.openPage(mall.extMallId, page);
            },
        },
    };
</script>

<style lang="scss">
.order-cloud-warn-wrapper {
    .warn {
        color: var(--abc-color-Y2);
    }

    .gray {
        color: var(--abc-color-T3);
    }
}

#order-cloud-warn-popover {
    width: 300px;

    .mall-group {
        .mall-name {
            padding: 5px 10px;
            font-size: 14px;
            font-style: normal;
            font-weight: bold;
            line-height: 22px;

            /* 157.143% */
            color: var(--abc-color-T1);
        }

        .mall-cell {
            padding: 5px 10px;
            color: var(--abc-color-T2);
            cursor: pointer;
            border-radius: var(--abc-border-radius-small);

            .warn {
                font-weight: bold;
                color: var(--abc-color-Y2);
            }

            &:not(.is-disabled):hover {
                background: var(--abc-color-cp-grey4);
            }
        }
    }
}
</style>


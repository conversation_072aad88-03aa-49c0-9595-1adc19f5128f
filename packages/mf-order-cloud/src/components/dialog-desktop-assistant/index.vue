<template>
    <abc-modal
        v-if="visible"
        v-model="visible"
        :show-footer="false"
        append-to-body
        content-styles="padding: 0px;"
        size="medium"
        class="desktop-assistant-dialog-wrapper"
    >
        <abc-image :src="DesktopAssistantImage" style="border-radius: 8px;"></abc-image>

        <abc-flex
            align="center"
            justify="center"
            :vertical="true"
            style="padding: 40px;"
        >
            <abc-space direction="vertical">
                <abc-text size="xlarge" bold>
                    开启桌面助手
                </abc-text>
                <abc-text theme="gray-light">
                    常驻桌面最前端，实时查看待处理订单及异常预警
                </abc-text>
            </abc-space>
            <abc-flex vertical style="margin-top: 40px;">
                <abc-space direction="vertical">
                    <abc-button style="width: 216px;" @click="enableDesktopAssistant">
                        立即开启
                    </abc-button>
                    <abc-button variant="text" @click="visible = false">
                        暂不开启
                    </abc-button>
                </abc-space>
            </abc-flex>
        </abc-flex>
    </abc-modal>
</template>

<script>
    import DesktopAssistantImage from '../../assets/images/setting/desktop-assistant.png';
    import { OrderCloudDaemonService } from '../../daemon';
    import { isClientSupportPharmacy } from '../../utils/electron';
    import DialogDownloadClient from '../dialog-download-client';
    export default {
        name: 'DialogDesktopAssistant',
        props: {
            $store: {
                type: Object,
                default: () => {},
            },
        },
        data() {
            return {
                DesktopAssistantImage,
                visible: false,
                desktopAssistantManager: null,
            };
        },
        created() {
            this.desktopAssistantManager = OrderCloudDaemonService.getInstance().getDesktopAssistManager();
        },
        methods: {
            enableDesktopAssistant() {
                if (!isClientSupportPharmacy()) {
                    new DialogDownloadClient().generateDialogAsync();
                    return;
                }
                this.desktopAssistantManager.enableDesktopAssistant();
                this.$store.updateOrderAssistantEnabledStatus(1);
                this.$Toast({
                    type: 'success',
                    message: '已开启桌面助手',
                });
                this.visible = false;
            },
        },
    };
</script>

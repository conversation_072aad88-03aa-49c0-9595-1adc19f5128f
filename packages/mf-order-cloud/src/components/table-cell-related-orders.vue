<template>
    <abc-popover
        ref="popover"
        placement="bottom-start"
        trigger="click"
        :visible-arrow="false"
        theme="yellow"
        padding-size="large"
        style="height: 100%;"
        :popper-style="{
            width: '365px',
            padding: 0
        }"
        @show="handleOpen"
    >
        <abc-table-cell
            slot="reference"
            class="ecommerce-table-cell"
            vertical
            align="flex-start"
            justify="center"
            clickable
        >
            <abc-space>
                <abc-tag-v2
                    v-if="hasRefundingOrders"
                    shape="square"
                    size="mini"
                    theme="danger"
                    variant="outline"
                >
                    售后中
                </abc-tag-v2>
                <abc-tag-v2
                    v-if="hasRefundedOrders"
                    shape="square"
                    size="mini"
                    theme="danger"
                    variant="outline"
                >
                    售后成功
                </abc-tag-v2>
                <abc-tag-v2
                    v-if="orderInfo?.detailOrders && orderInfo?.detailOrders?.length > 1"
                    shape="square"
                    size="mini"
                    theme="success"
                    variant="outline"
                >
                    合单·{{ orderInfo?.detailOrders?.length }}
                </abc-tag-v2>
                <div class="ecommerce-table-cell__row">
                    {{ orderInfo?.extOrderNo }}
                </div>
            </abc-space>
            <div v-if="orderInfo?.shippingTime" class="ecommerce-table-cell__row">
                <abc-text theme="gray">
                    {{ orderInfo?.shippingTime | formatDate('YYYY-MM-DD HH:mm') }} 发货
                </abc-text>
            </div>
        </abc-table-cell>

        <div id="table-cell-related-orders-popover" v-abc-loading="contentLoading">
            <abc-scrollbar :padding="16">
                <abc-text bold>
                    订单信息 <template v-if="detailOrders.length > 1">
                        合单发货{{ detailOrders.length }}
                    </template>
                </abc-text>
                <div v-for="(order, index) in detailOrders" :key="order.id" class="merged-order-item">
                    <abc-flex vertical gap="4">
                        <abc-flex
                            class="order-no"
                            align="center"
                            justify="space-between"
                        >
                            <div>
                                <abc-text size="mini">
                                    {{ order.orderNo }}
                                </abc-text>
                                <abc-button
                                    size="small"
                                    variant="text"
                                    icon="copy"
                                    @click.stop="copyOrderNo($event, order.orderNo)"
                                ></abc-button>
                            </div>
                            <abc-tag-v2
                                v-if="order.refundStatus === ECOrderRefundStatusEnum.HANDING"
                                shape="square"
                                size="mini"
                                theme="danger"
                                variant="outline"
                            >
                                售后中
                            </abc-tag-v2>
                            <abc-tag-v2
                                v-if="order.refundStatus === ECOrderRefundStatusEnum.REFUND_SUCCESS"
                                shape="square"
                                size="mini"
                                theme="danger"
                                variant="outline"
                            >
                                售后成功
                            </abc-tag-v2>
                        </abc-flex>
                        <abc-text size="mini">
                            支付时间：{{ order.payTime | formatDate('YYYY-MM-DD HH:mm') }}
                        </abc-text>
                        <abc-text size="mini">
                            发货时间：{{ order.shippingTime | formatDate('YYYY-MM-DD HH:mm') }}
                        </abc-text>
                        <abc-text size="mini">
                            来源网店：{{ order.mallName }}
                        </abc-text>
                        <abc-text size="mini">
                            接单门店：{{ order.clinicName }}
                        </abc-text>
                    </abc-flex>
                    <abc-flex v-for="item in order.goodsList" :key="item.skuId" style="margin-top: 8px;">
                        <div class="order-count">
                            ×{{ item.goodsCount }}
                        </div>
                        <div>
                            <span v-if="item.isGift" class="danger">[赠品]</span> {{ item.goodsName }} {{ item.goodsSpec || '' }}
                        </div>
                    </abc-flex>
                    <abc-flex class="total-info" style="margin-top: 8px;">
                        <abc-text size="mini">
                            {{ getOrderTotalInfo(order) }}
                        </abc-text>
                    </abc-flex>
                    <abc-divider
                        v-if="index !== detailOrders.length - 1"
                        class="cut-line"
                        theme="dark"
                        margin="mini"
                        variant="dashed"
                    ></abc-divider>
                </div>

                <template v-if="orderDetail.logisticsTraceList">
                    <abc-divider
                        class="cut-line"
                        theme="dark"
                        variant="dashed"
                    ></abc-divider>
                    <abc-text bold>
                        快递信息
                    </abc-text>
                    <div v-for="trace in orderDetail.logisticsTraceList" :key="trace.waybillCode">
                        <abc-flex class="order-no" align="center">
                            <abc-text size="mini">
                                {{ trace.wpName }}  {{ trace.waybillCode }}
                            </abc-text>
                            <abc-link style="margin-left: 4px;" @click.stop="copyOrderNo($event, trace.waybillCode)">
                                <abc-icon slot="prepend" size="14" icon="s-b-copy-line"></abc-icon>
                            </abc-link>
                        </abc-flex>
                        <abc-text v-if="trace.traceList && trace.traceList[0]" size="mini">
                            {{ trace.traceList[0].statusTime | formatDate('YYYY-MM-DD HH:mm') }}  {{ trace.traceList[0].desc }}
                        </abc-text>
                    </div>
                </template>
            </abc-scrollbar>
        </div>
    </abc-popover>
</template>

<script>
    import {
        ECOrderRefundStatusEnum, MergeStatusEnum,
    } from '../utils/constants.js';
    import ECOrderAPI from '../api/order.js';
    import { copy } from '@abc/utils-dom';
    import {
        formatDate,
    } from '@abc/utils-date';
    import { formatMoney } from '@abc/utils';
    export default {
        filters: {
            formatDate,
        },
        props: {
            orderInfo: {
                required: true,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                MergeStatusEnum,
                ECOrderRefundStatusEnum,
                canMergeOrderList: [],
                orderDetail: {},
                contentLoading: false,
            };
        },
        computed: {
            detailOrdersAbs() {
                return this.orderInfo?.detailOrders || [];
            },

            detailOrders() {
                return this.orderDetail?.detailOrders || [];
            },
            hasRefundingOrders() {
                return this.detailOrdersAbs.some((it) => it.refundStatus === ECOrderRefundStatusEnum.HANDING);
            },
            hasRefundedOrders() {
                return this.detailOrdersAbs.some((it) => it.refundStatus === ECOrderRefundStatusEnum.REFUND_SUCCESS);
            },
        },
        methods: {
            getOrderTotalInfo(item) {
                const {
                    actualAmount, totalPrice, goodsList,
                } = item;
                const totalCount = goodsList?.reduce((total, it) => {
                    return total + +it.goodsCount;
                }, 0) || 0;
                return `总数: ${totalCount}，总价: ${formatMoney(totalPrice)}，实收: ${formatMoney(actualAmount)}`;
            },
            copyOrderNo(event, text) {
                copy(`${text}`);
                this.$Toast({
                    message: '复制成功',
                    type: 'info',
                    referenceEl: event.target,
                });
            },
            handleClose() {
                this.$refs.popover.doClose();
            },
            async handleOpen() {
                try {
                    this.contentLoading = true;
                    const res = await ECOrderAPI.fetchOrderDetail({
                        orderId: this.orderInfo?.ecOrderId,
                        customErrorTips: true,
                    });
                    this.orderDetail = res;
                } catch (e) {
                    console.error(e);
                } finally {
                    this.contentLoading = false;
                }
            },
        },
    };
</script>
<style lang="scss">
#table-cell-related-orders-popover {
    min-height: 200px;

    .merged-order-item {
        margin-top: 8px;
        font-size: 14px;
        line-height: 22px; /* 157.143% */
        color: var(--abc-color-T1, #000000);
    }

    .order-count {
        min-width: 28px;
    }

    .danger {
        color: var(--abc-color-R6);
    }

    .cut-line {
        margin: 10px 0;
    }
}
</style>

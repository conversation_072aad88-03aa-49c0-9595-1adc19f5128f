<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        append-to-body
        content-styles="width:480px;"
        title="打印发货单"
        class="express-print-dialog-wrapper"
        @close="$emit('close')"
    >
        <abc-layout class="dialog-content clearfix">
            <abc-form ref="abcForm" label-position="left" :label-width="114">
                <abc-form-item label="已选订单" class="express-print-form-item">
                    <div style="font-size: 14px;">
                        共{{ orderList.length }}个
                    </div>
                </abc-form-item>
                <abc-form-item label="发货单打印机" class="express-print-form-item" required>
                    <abc-space is-compact compact-block>
                        <abc-select v-model="shipmentTemplate" :width="120" placeholder="请选择">
                            <abc-option label="A4横版" value="A4"></abc-option>
                            <abc-option label="A5横版" value="A5"></abc-option>
                            <abc-option label="热敏(76*130)" value="76"></abc-option>
                        </abc-select>
                        <abc-select
                            v-model="shipmentPrinter"
                            :width="198"
                            placeholder="请选择"
                        >
                            <abc-option
                                v-for="(it) in printerList"
                                :key="it.deviceIndex"
                                :value="it.deviceName"
                                :label="it.deviceName"
                            >
                                <span>{{ it.deviceName }}</span>
                            </abc-option>
                        </abc-select>
                    </abc-space>
                </abc-form-item>
            </abc-form>
        </abc-layout>

        <div slot="footer" class="dialog-footer">
            <abc-button @click="handleClickPrint">
                开始打印
            </abc-button>
            <abc-button type="blank" @click="closed = true">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script type="text/babel">
    import { ECTypeText } from '../../utils/constants';
    import PrintManager from '@/printer/manager/print-manager';
    import DialogPrintManager from '../dialog-print-manager';

    export default {
        name: 'DialogExpressPrint',
        components: {
        },
        props: {
            value: Boolean,
            orderList: Array,
            onFinish: Function,
            shipPrintConfig: {
                type: Object,
                default: () => {},
            },
        },
        data() {
            return {
                ECTypeText,
                visible: false,
                closed: false,
                contentLoading: false,
                alternatelyPrint: false,
                shipmentPrinter: '',
                shipmentTemplate: 'A4',
                printerList: [],
            };
        },
        watch: {
            closed(newVal) {
                if (newVal) {
                    this.visible = false;
                    this.destroyElement();
                }
            },
        },
        created() {
            this.getPrinters();

            let _printConfig = localStorage.getItem('last_selected_shipment_print_config');
            if (_printConfig) {
                _printConfig = JSON.parse(_printConfig);
                this.shipmentPrinter = _printConfig.shipmentPrinter;
                this.shipmentTemplate = _printConfig.shipmentTemplate;
            }

        },
        methods: {
            getPrinters() {
                this.printerList = PrintManager.getInstance().getPrinterList() || [];
                console.log(this.printerList);
            },

            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
            handleClickPrint() {
                this.$refs.abcForm.validate((valid) => {
                    if (valid) {
                        new DialogPrintManager({
                            printTitle: '打印发货单',
                            shipPrintConfig: this.shipPrintConfig,
                            printTask: this.orderList.map((order) => {
                                return {
                                    orderInfo: order,
                                    printConfig: {
                                        printShipment: true,
                                        shipmentPrinter: this.shipmentPrinter,
                                        shipmentTemplate: this.shipmentTemplate,
                                    },
                                };
                            }),
                            onFinish: () => {
                                this.onFinish && this.onFinish();
                                this.closed = true;
                            },
                        }).generateDialogAsync();
                        localStorage.setItem('last_selected_shipment_print_config', JSON.stringify({
                            shipmentPrinter: this.shipmentPrinter,
                            shipmentTemplate: this.shipmentTemplate,
                        }));
                    } else {
                        console.warn('打印发货单校验失败');
                    }
                });
            },

        },
    };
</script>
<style lang="scss">
.express-print-dialog-wrapper {
    .dialog-content {
        h5 {
            margin-bottom: 16px;
            font-size: 14px;
            font-weight: bold;
            line-height: 22px; /* 157.143% */
            color: var(--abc-color-T1, #000000);
        }

        .express-print-form-item {
            margin-right: 0;
        }
    }
}
</style>

<template>
    <abc-modal
        v-model="visible"
        size="large"
        content-styles="text-align: center;padding: 24px 0 60px 0;"
        class="express-print-manager-dialog-wrapper"
        :show-footer="false"
        :show-close="false"
    >
        <h2>
            {{ printTitle }}
        </h2>
        <abc-progress
            :percentage="doneCount"
            :circle-total="printTask.length"
            theme="green"
            :status="progressStatus"
            variant="circle"
            style="display: flex; align-items: center; justify-content: center;"
        >
        </abc-progress>
        <div class="print-tips">
            <h5 v-if="isDone">
                打印完成
            </h5>
            <h5
                v-else
                :class="{
                    'warn-infos': errorList.length
                }"
            >
                打印中
            </h5>
            <div v-if="errorList.length" class="warn-infos">
                {{ errorList.length }}条打印失败
            </div>
        </div>
        <abc-button v-if="isDone" type="blank" @click="handleClose">
            关闭窗口
        </abc-button>
        <abc-button v-else type="blank" @click="handleClose">
            终止打印
        </abc-button>
    </abc-modal>
</template>

<script type="text/babel">
    import ExpressPrintManage from '../../utils/print-manager/manage';

    export default {
        name: 'DialogPrintManager',
        components: {},
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            value: Boolean,
            printTitle: {
                type: String,
                default: '打印快递单',
            },
            printTask: {
                type: Array,
                required: true,
            },
            shipPrintConfig: {
                type: Object,
                default: () => {},
            },
            onProcess: Function,
            onFinish: Function,
        },
        data() {
            return {
                visible: false,
                closed: false,
                doneCount: 0,
                errorList: [],
            };
        },
        computed: {
            isDone() {
                return this.printTask.length > 0 && this.doneCount === this.printTask.length;
            },
            progressStatus() {
                if (this.errorList.length) return 'warning';
                if (this.isDone) return 'success';
                return 'progressing';
            },
        },
        watch: {
            closed(newVal) {
                if (newVal) {
                    this.visible = false;
                    this.destroyElement();
                }
            },
        },
        created() {
            this._manager = new ExpressPrintManage({
                printTask: this.printTask,
                shipPrintConfig: this.shipPrintConfig,
                onProcess: this.curOnProcess,
                onError: this.onError,
            });
            this.handleStart();
            this.errorList = [];
        },
        beforeDestroy() {
            this.handleStop();
        },
        methods: {
            async curOnProcess(res) {
                console.log(res);
                this.doneCount = this.printTask.length - res.leftCount;
                this.onProcess && this.onProcess(res.curPrint);

                if (this.isDone) {
                    this._timer = setTimeout(() => {
                        this.handleStop();
                    }, 500);
                }
            },
            async onError(err) {
                this.errorList.push(err);
            },
            handleStart() {
                this._manager && this._manager.startPrint();
            },
            handleStop() {
                this._manager && this._manager.stopPrint();
                this._manager = null;
                this.closed = true;
                this.onFinish && this.onFinish();
            },
            handleClose() {
                this.closed = true;
                this.onClose && this.onClose();
            },
            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode.removeChild(this.$el);
            },
        },
    };
</script>
<style lang="scss">
.express-print-manager-dialog-wrapper {
    h2 {
        margin-bottom: 14px;
        font-size: 16px;
        font-weight: bold;
    }

    .print-tips {
        height: 46px;
        margin: 18px 0;

        h5 {
            font-size: 14px;
            font-weight: bold;
            line-height: 22px; /* 157.143% */
            color: var(--abc-color-G1);
            text-align: center;
        }

        .warn-infos {
            color: var(--abc-color-Y2);
        }
    }
}
</style>

<template>
    <abc-table-cell
        class="o2o-goods-out-table-cell-order"
        :class="{
            'is-disabled': item.isSkuNotSync
        }"
        style="height: 100%;"
    >
        <abc-flex gap="10" align="center" style="flex: 1;">
            <abc-flex>
                <abc-flex v-if="item.isSkuNotSync" class="not-sync-goods-loading">
                    <abc-loading small blue></abc-loading>
                </abc-flex>
                <abc-image
                    v-else
                    :src="goodsImage"
                    alt=""
                    width="56"
                    height="56"
                ></abc-image>
            </abc-flex>
            <abc-flex vertical gap="2" style="flex: 1; width: 0;">
                <abc-flex align="center">
                    <abc-tag-v2
                        v-if="item.isCancelled"
                        variant="outline"
                        theme="danger"
                        shape="square"
                        size="small"
                        style="flex-shrink: 0; margin-right: 8px;"
                    >
                        已取消
                    </abc-tag-v2>
                    <abc-text class="ellipsis" :title="goodsName">
                        {{ goodsName }}
                    </abc-text>
                    <abc-flex style="flex-shrink: 0; margin-left: auto;">
                        × {{ goodsCount }}
                    </abc-flex>
                </abc-flex>
                <abc-flex align="center" gap="8">
                    <abc-text :theme="item.isCancelled ? 'gray-light' : 'gray'" size="mini" :title="goodsSkuId">
                        SKUID：{{ goodsSkuId }}
                    </abc-text>
                    <abc-text
                        v-if="extSourceFoodCode"
                        :theme="item.isCancelled ? 'gray-light' : 'gray'"
                        size="mini"
                        :title="extSourceFoodCode"
                    >
                        店内码/货号：{{ extSourceFoodCode }}
                    </abc-text>
                </abc-flex>
            </abc-flex>
        </abc-flex>
    </abc-table-cell>
</template>

<script>
    export default {
        name: 'TableCellOrder',
        props: {
            item: {
                type: Object,
                required: true,
            },
        },
        computed: {
            goodsImage() {
                return this.item.extGoodsImg || '';
            },
            goodsName() {
                return this.item.extGoodsName || '';
            },
            goodsCount() {
                return this.item.extGoodsCount || '';
            },
            goodsSkuId() {
                return this.item.extSkuId || '';
            },
            extSourceFoodCode() {
                const {
                    ecGoodsSku,
                } = this.item;
                return ecGoodsSku?.extSourceFoodCode || '';
            },
        },
        methods: {

        },
    };
</script>

<style lang="scss">
.o2o-goods-out-table-cell-order {
    .is-disabled {
        color: var(--abc-color-T3);
    }

    .not-sync-goods-loading {
        display: flex;
        flex-direction: column;
        flex-shrink: 0;
        align-items: center;
        justify-content: center;
        width: 56px;
        height: 56px;
        padding: 28px 14px;
        background: var(--abc-color-cp-grey2);
        border: 0.5px solid var(--abc-color-P8);
        border-radius: 2px;
    }
}
</style>





<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        title="绑定ABC系统商品"
        append-to-body
        :size="isPackage ? 'hugely' : 'huge'"
        content-styles="height: 608px; overflow-y: auto;"
        custom-class="pharmacy__bind-goods-dialog-o2o"
    >
        <template #top-extend>
            <abc-tips-card-v2 theme="primary">
                <div style="width: 972px;">
                    <abc-flex justify="space-between" align="center">
                        <p>绑定ABC系统内的商品后，库存数量将从ABC系统实时同步到网店，网店订单发货时会自动在ABC系统下账、扣库</p>
                    </abc-flex>
                </div>
            </abc-tips-card-v2>
        </template>
        <abc-section>
            <abc-text bold size="normal">
                网店商品
            </abc-text>
            <abc-card padding-size="small" background="gray" style="margin-top: 16px;">
                <abc-flex justify="space-between" align="center" style="height: 64px;">
                    <abc-space>
                        <div class="goods-item-left">
                            <abc-image
                                :src="imageUrl"
                                width="56"
                                height="56"
                                style="border-radius: 6px;"
                            ></abc-image>
                            <abc-tag-v2
                                v-if="skuList[0]?.composeType === MeituanComposeType.COMPOSE_PACKAGE"
                                class="goods-item-tag"
                                variant="dark"
                                theme="success"
                                size="mini"
                                use-first-letter
                            >
                                组
                            </abc-tag-v2>
                        </div>

                        <abc-flex vertical>
                            <abc-space>
                                <abc-tag-v2
                                    v-if="getGoodsStatusText(status)"
                                    variant="outline"
                                    min-width="54"
                                    size="mini"
                                    :theme="getGoodsStatusTheme(status)"
                                >
                                    {{ getGoodsStatusText(status) }}
                                </abc-tag-v2>
                                <abc-text bold size="large">
                                    {{ goodsName }}
                                </abc-text>
                            </abc-space>
                            <abc-space>
                                <abc-text size="normal" theme="gray-light">
                                    SKUID: {{ desc }}
                                </abc-text>
                                <abc-text size="normal" theme="gray-light">
                                    店内码/货号: {{ extSourceFoodCode }}
                                </abc-text>
                            </abc-space>
                        </abc-flex>
                    </abc-space>
                    <abc-text size="xlarge">
                        <abc-money :value="price"></abc-money>
                    </abc-text>
                </abc-flex>
            </abc-card>
        </abc-section>

        <abc-space
            direction="vertical"
            align="left"
            :custom-style="{
                marginTop: '24px',
            }"
        >
            <abc-text bold size="normal">
                ABC系统商品
            </abc-text>
        </abc-space>

        <abc-section>
            <abc-form ref="bindGoodsFormRef" class="table-wrapper">
                <abc-table
                    custom
                    type="excel"
                    :show-hover-tr-bg="false"
                    :data-list="dataList"
                    cell-size="xxxlarge"
                >
                    <abc-table-header>
                        <abc-table-td v-if="isPackage" :style="tableTdStyle.o2oGoodsName">
                            网店单品
                        </abc-table-td>
                        <abc-table-td v-if="isPackage" :style="tableTdStyle.o2oGoodsNum">
                            数量
                        </abc-table-td>
                        <abc-table-td :style="tableTdStyle.name">
                            商品名称
                        </abc-table-td>
                        <abc-table-td :style="tableTdStyle.unit" align="center">
                            绑定单位
                        </abc-table-td>
                        <abc-table-td :style="tableTdStyle.dispGoodsCount" align="right">
                            ABC可售库存
                        </abc-table-td>
                        <abc-table-td :style="tableTdStyle.allocationStock">
                            分配的ABC库存
                        </abc-table-td>
                    </abc-table-header>
                    <abc-table-body>
                        <abc-table-tr v-for="(item, index) in dataList" :key="index" @delete-tr="handleDelete(index)">
                            <abc-table-td v-if="isPackage" class="ellipsis" :style="tableTdStyle.o2oGoodsName">
                                <abc-flex align="center">
                                    <abc-flex gap="middle" align="center">
                                        <abc-image :src="item.meituanGoodsImg || ''" width="56" height="56"></abc-image>
                                        <abc-flex vertical>
                                            <abc-space>
                                                <abc-tag-v2
                                                    variant="outline"
                                                    min-width="54"
                                                    size="mini"
                                                    theme="success"
                                                >
                                                    售卖中
                                                </abc-tag-v2>
                                                <abc-text
                                                    :title="item.meituanName"
                                                    tag="div"
                                                    style="width: 236px;"
                                                    class="ellipsis"
                                                >
                                                    {{ item.meituanName }}
                                                </abc-text>
                                            </abc-space>
                                            <abc-text :title="item.externalSkuId" theme="gray-light">
                                                SKUID: {{ item.externalSkuId }}
                                            </abc-text>
                                        </abc-flex>
                                    </abc-flex>
                                </abc-flex>
                            </abc-table-td>
                            <abc-table-td v-if="isPackage" :style="tableTdStyle.o2oGoodsNum">
                                <abc-text>{{ item.count }}</abc-text>
                            </abc-table-td>
                            <abc-table-td
                                :style="tableTdStyle.name"
                                custom-td
                            >
                                <goods-autocomplete
                                    v-if="item.showAutocomplete"
                                    ref="goodsAutocompleteRef"
                                    :key="index"
                                    placeholder="商品名称/条码"
                                    :search="keyword"
                                    :focus-show="true"
                                    size="medium"
                                    class="goods-autocomplete-wrapper"
                                    need-filter-disable
                                    is-type-arr
                                    style="width: 100%;"
                                    format-count-key="stock"
                                    :disabled="isChainAdmin"
                                    :is-sell="1"
                                    :inorder-config="0"
                                    :clinic-id="clinicId"
                                    :show-suggestion-header="true"
                                    @selectGoods="onSelectGoods($event,item, index)"
                                    @blur="handleGoodsNameBlur(item)"
                                >
                                </goods-autocomplete>
                                <abc-flex
                                    v-else-if="item.name"
                                    justify="center"
                                    vertical
                                    style="height: 100%; padding: 0 12px;"
                                    @click="handleGoodsNameClick(item)"
                                >
                                    <abc-popover
                                        trigger="hover"
                                        append-to-body
                                        theme="yellow"
                                        placement="top"
                                        :disabled="!isMeituanUnbindWarn(item.warnFlag)"
                                    >
                                        <template slot="reference">
                                            <abc-text :theme="isMeituanUnbindWarn(item.warnFlag) ? 'warning' : ''">
                                                {{ item.name }}
                                            </abc-text>
                                        </template>
                                        <abc-text>
                                            商品已停用
                                        </abc-text>
                                    </abc-popover>
                                    <abc-text
                                        size="mini"
                                        theme="gray"
                                    >
                                        {{ item.displaySpec }}   {{ item.manufacturer }}
                                    </abc-text>
                                </abc-flex>
                                <abc-flex
                                    v-else
                                    align="center"
                                    style="height: 100%; padding: 0 11px;"
                                    @click="handleGoodsNameClick(item)"
                                >
                                    <abc-text theme="gray-light">
                                        商品名称/条码
                                    </abc-text>
                                </abc-flex>
                            </abc-table-td>

                            <abc-table-td :style="tableTdStyle.unit" align="center" custom-td>
                                <abc-form-item v-if="item.hisGoodsId" required>
                                    <abc-select
                                        v-model="item.unit"
                                        :title="item.unit"
                                        size="small"
                                        :width="100"
                                        :tabindex="-1"
                                        placeholder="选择单位"
                                        :disabled="isChainAdmin || item.unitOptions.length === 1"
                                        :no-icon="item.unitOptions.length === 1"
                                        @change="handleChangeUnit(item)"
                                    >
                                        <abc-option
                                            v-for="it in item.unitOptions"
                                            :key="it.name"
                                            :label="it.name"
                                            :value="it.name"
                                        >
                                        </abc-option>
                                    </abc-select>
                                </abc-form-item>
                            </abc-table-td>
                            <abc-table-td
                                custom-td
                                :style="tableTdStyle.dispGoodsCount"
                                align="right"
                            >
                                <abc-table-cell>
                                    <abc-text v-if="item.hisGoodsId" :theme="item && item.shortageWarnFlag ? 'warning-light' : 'black'">
                                        {{ item.dispGoodsCount || '-' }}
                                    </abc-text>
                                </abc-table-cell>
                            </abc-table-td>
                            <abc-table-td
                                custom-td
                                :style="tableTdStyle.allocationStock"
                            >
                                <table-cell-allocation-stock
                                    v-if="item.hisGoodsId"
                                    :ec-type="ECTypeEnum.MT"
                                    :item="item"
                                    :clinic-id="clinicId"
                                    @change="handleChangeAllocationStock"
                                ></table-cell-allocation-stock>
                            </abc-table-td>
                        </abc-table-tr>
                    </abc-table-body>
                </abc-table>
            </abc-form>
        </abc-section>
        <div slot="footer" class="dialog-footer">
            <abc-button
                v-if="isChainSubStore || isSingleStore"
                type="primary"
                :loading="btnLoading"
                @click="handleBindHisGoods"
            >
                确定
            </abc-button>
            <abc-button type="blank" @click="visible = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import GoodsAutocomplete from 'MfBase/goods-autocomplete';
    import TAGoodsAPI from '@/api/ta-goods';
    import {
        GoodsTypeEnum, PharmacyTypeEnum,
    } from '@abc/constants';
    import { mapGetters } from 'vuex';
    import {
        isMeituanUnbindWarn, MeituanComposeType,
    } from '../../frames/goods/ta-goods/constants';
    import TableCellAllocationStock from '@/components/table-cell-allocation-stock.vue';
    import {
        AllocationStockType, ECTypeEnum,
    } from '@/utils/constants';
    import ECGoodsAPI from '@/api/goods';

    export default {
        name: 'DialogBindGoods',
        components: {
            TableCellAllocationStock,
            GoodsAutocomplete,
        },
        props: {
            value: Boolean,
            clinicId: String,
            goodsId: String,
            goodsSkuId: String,
            ecMallId: String,
            mallName: String,
            goodsName: String,
            imageUrl: String,
            hisGoodsList: Array,
            desc: String,
            extSourceFoodCode: String,
            status: Number,
            price: {
                type: Number,
                default: 0,
            },
            composeItems: {
                type: Array,
                default: () => [],
            },
            skuList: {
                type: Array,
                default: () => [],
            },
            onOpen: Function,
            onClose: Function,
        },
        data() {
            return {
                ECTypeEnum,
                MeituanComposeType,
                visible: false,
                loading: false,
                btnLoading: false,
                keyword: '',
                dataList: [],
                unitArray: [],
                blurTimeout: null,
                tableTdStyle: {
                    o2oGoodsName: {
                        flex: 'none',
                        textAlign: 'left',
                        cursor: 'pointer',
                        paddingLeft: '',
                        width: '376px',
                    },
                    o2oGoodsNum: {
                        flex: 'none',
                        textAlign: 'left',
                        cursor: 'pointer',
                        width: '100px',
                        paddingLeft: '',
                    },
                    name: {
                        flex: '1',
                        textAlign: 'left',
                        cursor: 'pointer',
                        paddingLeft: '',
                    },
                    unitCount: {
                        flex: 'none',
                        textAlign: 'right',
                        width: '100px',
                        cursor: 'pointer',
                        paddingLeft: '',
                    },
                    unit: {
                        flex: 'none',
                        textAlign: 'center',
                        width: '100px',
                        cursor: 'pointer',
                        paddingLeft: '',
                    },
                    dispGoodsCount: {
                        flex: 'none',
                        textAlign: 'right',
                        width: '110px',
                        paddingLeft: '',
                    },
                    allocationStock: {
                        flex: 'none',
                        width: '130px',
                    },
                },
            };
        },
        computed: {
            ...mapGetters([
                'isChainAdmin',
                'isChainSubStore',
                'isSingleStore',
            ]),
            isPackage() {
                return this.composeItems.length > 0;
            },
        },
        watch: {
            visible(val) {
                if (val) {
                    this.onOpen && this.onOpen();
                } else {
                    this.onClose && this.onClose();
                    this.destroyElement();
                }
            },
            hisGoodsList: {
                handler(list) {
                    if (this.isPackage) return;
                    const goods = list[0] || {};
                    const hisGoodsInfo = goods?.hisGoodsInfo || {};
                    this.dataList = [{
                        ecType: ECTypeEnum.MT,
                        id: goods.id,
                        unit: goods.unit,
                        unitCount: goods.useDismounting ? goods.bindPieceCount : goods.bindPackageCount,
                        hisGoodsId: goods.hisGoodsId,
                        hisGoodsInfo,
                        dismounting: hisGoodsInfo.dismounting,
                        pieceUnit: hisGoodsInfo.pieceUnit,
                        packageUnit: hisGoodsInfo.packageUnit,
                        unitOptions: this.getUnitOptions(goods.hisGoodsInfo),
                        name: hisGoodsInfo.displayName,
                        manufacturer: hisGoodsInfo.manufacturer,
                        displaySpec: hisGoodsInfo.displaySpec,
                        dispGoodsCount: goods.dispStockGoodsCount,
                        showAutocomplete: hisGoodsInfo.displayName ? false : true,
                        goodsSkuId: this.goodsSkuId,
                        warnFlag: goods.warnFlag,
                        goodsName: this.goodsName,
                        goodsSkuName: this.goodsName,
                        assignStockType: goods.assignStockType || null,
                        assignStockRatio: goods.assignStockRatio || null,
                        assignedStockPieceCount: goods.assignedStockPieceCount || null,
                        assignedStockPackageCount: goods.assignedStockPackageCount || null,
                        mallName: this.mallName,
                    }];
                },
                immediate: true,
                deep: true,
            },
            composeItems: {
                handler(list) {
                    if (!this.isPackage) return;
                    if (list?.length) {
                        this.dataList = [];
                        list.forEach((composeItem) => {
                            const hisGoodsList = composeItem?.relHisGoodsList || {};
                            const goods = hisGoodsList[0] || {};
                            const hisGoodsInfo = goods?.hisGoodsInfo || {};
                            this.dataList.push({
                                ecType: ECTypeEnum.MT,
                                id: goods.id,
                                goodsSkuId: composeItem.id,
                                meituanName: composeItem.name,
                                externalSkuId: composeItem.externalSkuId,
                                meituanGoodsImg: composeItem?.imageUrl || '',
                                count: composeItem.count,
                                spec: composeItem.spec[0].specName,
                                unit: goods.unit,
                                unitCount: goods.useDismounting ? goods.bindPieceCount : goods.bindPackageCount,
                                unitOptions: this.getUnitOptions(goods.hisGoodsInfo),
                                hisGoodsId: goods.hisGoodsId,
                                dismounting: hisGoodsInfo.dismounting,
                                pieceUnit: hisGoodsInfo.pieceUnit,
                                packageUnit: hisGoodsInfo.packageUnit,
                                useDismounting: goods.useDismounting,
                                name: hisGoodsInfo.displayName,
                                manufacturer: hisGoodsInfo.manufacturer,
                                displaySpec: hisGoodsInfo.displaySpec,
                                dispGoodsCount: goods.dispStockGoodsCount,
                                showAutocomplete: hisGoodsInfo.displayName ? false : true,
                                warnFlag: composeItem.warnFlag,
                                goodsName: this.goodsName,
                                goodsSkuName: this.goodsName,
                                assignStockType: composeItem.assignStockType || null,
                                assignStockRatio: composeItem.assignStockRatio || null,
                                assignedStockPieceCount: composeItem.assignedStockPieceCount || null,
                                assignedStockPackageCount: composeItem.assignedStockPackageCount || null,
                                mallName: this.mallName,
                            });
                        });
                    }
                },
                immediate: true,
                deep: true,
            },
        },
        beforeDestroy() {
            if (this.blurTimeout) {
                clearTimeout(this.blurTimeout);
                this.blurTimeout = null;
            }
        },
        methods: {
            isMeituanUnbindWarn,
            handleChangeAllocationStock(relatedGoodsSkuReqs) {
                this.$abcEventBus.$emit('refresh-ec-goods');
                this.dataList.map((item) => {
                    const _obj = relatedGoodsSkuReqs?.find((it) => it.id === item.id);
                    if (_obj) {
                        Object.assign(item, _obj);
                    }
                });
            },
            isChineseMedicine(goods) {
                return goods.type === 1 && goods.subType === 2;
            },
            handleChangeUnit(item) {
                item.useDismounting = +(
                    item.dismounting &&
                    item.unit === item.pieceUnit &&
                    item.unit !== item.packageUnit
                );
            },
            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
            validateCount(value, callback) {
                if (!+value) {
                    callback({
                        validate: false,
                        message: '不能为空',
                    });
                }
            },
            handleDelete(index) {
                this.dataList.splice(index, 1);
            },
            isStockGoods(goodsType, pharmacyType) {
                // 虚拟药房不判断库存逻辑
                if (pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY) return false;
                return (
                    goodsType === GoodsTypeEnum.MEDICINE ||
                    goodsType === GoodsTypeEnum.MATERIAL ||
                    goodsType === GoodsTypeEnum.GOODS
                );
            },
            async onSelectGoods(goods,item,index) {
                if (this.blurTimeout) {
                    clearTimeout(this.blurTimeout);
                    this.blurTimeout = null;
                }

                this.keyword = '';
                if (!goods) return false;
                if (goods.disabled) return false;

                const {
                    type,
                    displayName,
                    medicineCadn,
                    packageUnit,
                    pieceUnit,
                    stockPieceCount,
                    stockPackageCount,
                    shortageWarnFlag,
                    displaySpec,
                    manufacturer,
                    dismounting,
                } = goods || {};

                const defaultUnit = this.isStockGoods(type) ? '' : '次';
                const selectGoods = {
                    ecType: ECTypeEnum.MT,
                    meituanName: item.meituanName,
                    externalSkuId: item.externalSkuId,
                    meituanGoodsImg: item.meituanGoodsImg || '',
                    unit: packageUnit || pieceUnit || defaultUnit,
                    name: displayName || medicineCadn,
                    unitCount: 1, // 单位数量
                    unitOptions: this.getUnitOptions(goods),
                    type,
                    hisGoodsId: goods.id,
                    hisGoodsInfo: goods,
                    dispGoodsCount: this.isChineseMedicine(goods) ? `${stockPieceCount}${pieceUnit}` : stockPieceCount ? `${stockPackageCount}${packageUnit}${stockPieceCount}${pieceUnit}` : `${stockPackageCount}${packageUnit}`,
                    shortageWarnFlag,
                    displaySpec,
                    pieceUnit,
                    dismounting,
                    packageUnit,
                    manufacturer,
                    goodsSkuId: item.goodsSkuId,
                    useDismounting: packageUnit ? 0 : dismounting,
                    goodsName: this.goodsName,
                    goodsSkuName: this.goodsName,
                    assignStockType: null,
                    assignStockRatio: null,
                    assignedStockPieceCount: null,
                    assignedStockPackageCount: null,
                    mallName: this.mallName,
                };
                this.dataList.splice(index, 1, selectGoods);
                const { data } = await ECGoodsAPI.fetchRelatedSku(goods.id, {
                    clinicId: this.clinicId,
                });
                if (data.relatedGoodsSku?.length) {
                    const {
                        assignStockType,
                    } = data.relatedGoodsSku[0];
                    selectGoods.assignStockType = assignStockType;
                    if (assignStockType === AllocationStockType.SHARE) {
                        selectGoods.assignedStockPieceCount = stockPieceCount;
                        selectGoods.assignedStockPackageCount = stockPackageCount;
                    }
                }
            },

            getUnitOptions(item) {
                const res = [];
                if (!item) return res;
                const {
                    dismounting,
                    pieceUnit,
                    packageUnit,
                } = item;

                if (dismounting) {
                    if (pieceUnit) {
                        res.push({ 'name': pieceUnit });
                    }
                    if (packageUnit && packageUnit !== pieceUnit) {
                        res.push({ 'name': packageUnit });
                    }
                } else {
                    res.push({ 'name': packageUnit });
                }
                return res;
            },
            async handleBindHisGoods() {
                this.$refs.bindGoodsFormRef.validate(async (valid) => {
                    if (valid) {
                        // const flag = this.dataList.some((item) => item.assignedStockPackageCount === null && item.assignedStockPieceCount === null);
                        // if (flag) {
                        //     this.$Toast.error('未分配库存');
                        //     return;
                        // }
                        this.btnLoading = true;
                        const arr = this.dataList;
                        const params = {
                            ecMallId: this.ecMallId,
                            hisGoodsList: arr.map((item) => {
                                const {
                                    unit,
                                    hisGoodsId,
                                    name,
                                    useDismounting,
                                    goodsSkuId,
                                    assignStockType,
                                    assignStockRatio,
                                    assignedStockPieceCount,
                                    assignedStockPackageCount,
                                } = item;

                                return {
                                    name,
                                    hisGoodsId,
                                    unit,
                                    goodsSkuId,
                                    useDismounting,
                                    bindCount: 1,
                                    assignStockType,
                                    assignStockRatio,
                                    assignedStockPieceCount,
                                    assignedStockPackageCount,
                                };
                            }),
                        };

                        try {
                            const res = await TAGoodsAPI.bindHisGoods(this.goodsId, this.goodsSkuId, params);
                            if (res) {
                                this.$Toast.success('绑定成功');
                                this.$abcEventBus.$emit('refresh-ec-goods');
                                this.visible = false;
                            }
                        } catch (err) {
                            this.$Toast.error(err.message || '绑定失败');
                            console.error(err);
                        } finally {
                            this.btnLoading = false;
                        }
                    }
                });

            },
            getGoodsStatusText(status) {
                if (status === 0) return '售卖中';
                if (status === 10) return '已下架';
                if (status === 20) return '在售中';
                if (status === 30) return '已售罄';
                if (status === 99) return '已删除';
            },
            getGoodsStatusTheme(status) {
                if (status === 0) return 'success';
                if (status === 20) return 'success';
                if (status === 10) return 'danger';
                if (status === 30) return 'warning';
                if (status === 99) return 'warning';
            },
            handleGoodsNameClick(item) {
                this.$set(item, 'showAutocomplete', true);
                this.$nextTick(() => {
                    this.$refs.goodsAutocompleteRef[0].$el.querySelector('input').focus();
                });
            },
            handleGoodsNameBlur(item) {
                this.blurTimeout = setTimeout(() => {
                    this.$set(item, 'showAutocomplete', false);
                    this.blurTimeout = null;
                }, 200);
            },
        },
    };
</script>
<style lang="scss">
.pharmacy__bind-goods-dialog-o2o {
    .goods-autocomplete-wrapper {
        height: 72px;

        .abc-input__inner {
            height: 72px;
            border-radius: 0 0 0 var(--abc-border-radius-medium) !important;
        }
    }

    .table-wrapper {
        margin-top: 10px;
    }

    .goods-item-left {
        position: relative;
        height: 56px;

        .goods-item-tag {
            position: absolute;
            top: 0;
            left: 0;
        }
    }
}
</style>

<template>
    <div id="table-cell-receiver-popover">
        <div class="receiver-info">
            <abc-flex class="title" align="center" justify="space-between">
                <h5>收件信息</h5>

                <abc-tooltip v-if="!disabled" placement="top">
                    <template #content>
                        <div>由于拼多多限制，卖家无法修改收件信息，</div>
                        <div>请引导消费者至订单详情发起修改申请</div>
                    </template>
                    <div>
                        <abc-button
                            variant="ghost"
                            size="small"
                            disabled
                        >
                            修改
                        </abc-button>
                    </div>
                </abc-tooltip>
            </abc-flex>
            <div class="content">
                <p>{{ curReceiverInfo.receiverNameMask }} {{ curReceiverInfo.receiverPhoneMask }}</p>
                <p :title="curReceiverInfo.receiverAddressMask">
                    {{ curReceiverInfo.receiverAddressMask }}
                </p>
            </div>
        </div>
        <div v-if="afterSalesDetail" class="after-sale-info">
            <abc-divider
                class="cut-line"
                theme="dark"
                margin="none"
                variant="dashed"
            ></abc-divider>
            <abc-flex align="center" style="margin-top: 16px;">
                <abc-tag-v2
                    v-if="refundStatus === ECOrderRefundStatusEnum.HANDING"
                    shape="square"
                    size="mini"
                    theme="danger"
                    variant="outline"
                >
                    售后中
                </abc-tag-v2>
                <abc-tag-v2
                    v-if="refundStatus === ECOrderRefundStatusEnum.REFUND_SUCCESS"
                    shape="square"
                    size="mini"
                    theme="danger"
                    variant="outline"
                >
                    售后成功
                </abc-tag-v2>
                <div class="content">
                    {{ afterSalesDetail.desc }}
                </div>
                <abc-flex
                    v-if="afterSalesDetail.afterSalesId"
                    align="center"
                    class="after-sales-no"
                >
                    {{ afterSalesDetail.afterSalesId }}
                    <abc-link style="margin-left: 4px;" @click.stop="copyOrderNo($event, afterSalesDetail.afterSalesId)">
                        <abc-icon slot="prepend" size="14" icon="s-b-copy-line"></abc-icon>
                    </abc-link>
                </abc-flex>
            </abc-flex>
        </div>
        <template v-if="hasMergedOrder">
            <abc-divider
                class="cut-line"
                theme="dark"
                margin="none"
            ></abc-divider>
            <abc-scrollbar padding-size="small" class="merge-order-info">
                <abc-flex class="title" align="center" justify="space-between">
                    <h5>已合并订单</h5>
                    <abc-button
                        v-if="!disabled"
                        variant="ghost"
                        :loading="btnLoading"
                        size="small"
                        @click="handleClickSplit"
                    >
                        拆分
                    </abc-button>
                </abc-flex>
                <div v-for="(order, index) in mergedOrders" :key="order.id" class="merged-order-item">
                    <abc-flex class="order-no">
                        {{ order.orderNo }}
                        <abc-link style="margin-left: 4px;" @click.stop="copyOrderNo($event, order.orderNo)">
                            <abc-icon slot="prepend" size="14" icon="s-b-copy-line"></abc-icon>
                        </abc-link>
                    </abc-flex>
                    <abc-flex class="total-info">
                        {{ getOrderTotalInfo(order) }}
                    </abc-flex>
                    <abc-flex v-for="item in order.goodsList" :key="item.skuId">
                        <div class="order-count">
                            ×{{ item.goodsCount }}
                        </div>
                        <div><span v-if="item.isGift" class="danger">[赠品]</span> {{ item.goodsName }} {{ item.goodsSpec || '' }}</div>
                    </abc-flex>

                    <template v-if="order.receiverChanged">
                        <abc-space>
                            <abc-tag-v2
                                shape="square"
                                size="mini"
                                theme="success"
                                variant="outline"
                            >
                                新地址
                            </abc-tag-v2>
                            <div>
                                {{ order.receiverNameMask }} {{ order.receiverPhoneMask }}
                            </div>
                        </abc-space>
                        <div>
                            {{ order.receiverAddressMask }}
                        </div>
                    </template>

                    <abc-divider
                        v-if="index !== mergedOrders.length - 1"
                        class="cut-line"
                        theme="dark"
                        margin="mini"
                        variant="dashed"
                    ></abc-divider>
                </div>
            </abc-scrollbar>
        </template>

        <template v-if="canMergeOrderList.length">
            <abc-divider
                class="cut-line"
                theme="dark"
                margin="none"
                variant="dashed"
            ></abc-divider>
            <abc-scrollbar padding-size="small" class="merge-order-info">
                <abc-flex class="title" align="center" justify="space-between">
                    <h5>可合并订单</h5>
                    <abc-button
                        v-if="!disabled"
                        variant="ghost"
                        :loading="btnLoading"
                        size="small"
                        :disabled="unLockMergeOrders.length < 2"
                        @click="handleClickMerge"
                    >
                        合并
                    </abc-button>
                </abc-flex>
                <div v-for="(order, index) in canMergeOrderList" :key="order.id" class="merged-order-item">
                    <abc-flex class="order-no" align="center">
                        {{ order.orderNo }}
                        <abc-link style="margin-left: 4px;" @click.stop="copyOrderNo($event, order.orderNo)">
                            <abc-icon slot="prepend" size="14" icon="s-b-copy-line"></abc-icon>
                        </abc-link>
                        <div v-if="order.orderLock" class="danger">
                            已锁定，不可合并
                        </div>
                    </abc-flex>
                    <abc-flex class="total-info">
                        {{ getOrderTotalInfo(order) }}
                    </abc-flex>
                    <abc-flex v-for="item in order.goodsList" :key="item.skuId">
                        <div class="order-count">
                            ×{{ item.goodsCount }}
                        </div>
                        <div><span v-if="item.isGift" class="danger">[赠品]</span> {{ item.goodsName }} {{ item.goodsSpec || '' }}</div>
                    </abc-flex>
                    <abc-divider
                        v-if="index !== mergedOrders.length - 1"
                        class="cut-line"
                        theme="dark"
                        margin="mini"
                        variant="dashed"
                    ></abc-divider>
                </div>
            </abc-scrollbar>
        </template>
    </div>
</template>

<script>
    import DialogReceiverForm from '@/components/dialog-receiver-form';
    import {
        ECOrderRefundStatusEnum, MergeStatusEnum,
    } from '../utils/constants.js';
    import { formatMoney } from '@abc/utils';
    import ECOrderAPI from '../api/order.js';
    import { copy } from '@abc/utils-dom';

    export default {
        props: {
            orderInfo: {
                required: true,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            canMergeOrderList: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                ECOrderRefundStatusEnum,
                MergeStatusEnum,
                btnLoading: false,
            };
        },
        computed: {
            curReceiverInfo() {
                return this.orderInfo.receiverInfo || {};
            },
            refundStatus() {
                return this.orderInfo.refundStatus;
            },
            afterSalesDetail() {
                return this.orderInfo.afterSalesDetail;
            },
            mergedOrders() {
                return this.orderInfo.detailOrders || [];
            },
            hasMergedOrder() {
                return this.orderInfo.mergeStatus === MergeStatusEnum.MERGE_ED && this.mergedOrders.length > 0;
            },
            unLockMergeOrders() {
                return this.canMergeOrderList.filter((it) => !it.orderLock);
            },
        },
        methods: {
            handleClose() {
                this.$emit('close');
            },
            copyOrderNo(event, text) {
                copy(`${text}`);
                this.$Toast({
                    message: '复制成功',
                    type: 'info',
                    referenceEl: event.target,
                });
            },
            handleUpdate() {
                this._dialogReceiverForm = new DialogReceiverForm({
                    ecType: this.orderInfo.ecType,
                    orderId: this.orderInfo.id,
                    defaultData: this.curReceiverInfo,
                    onConfirm: (data) => {
                        this.$emit('update', data);
                    },
                    onClosed: () => {
                        this._dialogReceiverForm = null;
                    },
                });
                this._dialogReceiverForm.generateDialogAsync();
            },
            getOrderTotalInfo(item) {
                const {
                    actualAmount, totalPrice, goodsList,
                } = item;
                const totalCount = goodsList.reduce((total, it) => {
                    return total + +it.goodsCount;
                }, 0);
                return `总数: ${totalCount}，总价: ${formatMoney(totalPrice)}，实收: ${formatMoney(actualAmount)}`;
            },
            async handleClickMerge() {
                const hasPrintWaybill = this.unLockMergeOrders.some((order) => order.waybillPrintStatus);
                if (this.orderInfo.waybillPrintStatus || hasPrintWaybill) {
                    this.$confirm({
                        type: 'warn',
                        title: '合并订单确认',
                        content: [
                            '合并订单后，原订单的快递单号将回收作废，新合并订单将',
                            '变为 [未打印] 状态。是否确认合并？',
                        ],
                        onConfirm: this.mergeSubmit,
                    });
                    return;
                }
                this.mergeSubmit();
            },
            async mergeSubmit() {
                try {
                    this.btnLoading = true;
                    await ECOrderAPI.mergeOrders({
                        ecType: this.orderInfo.ecType,
                        orderIds: this.unLockMergeOrders.map((it) => it.id),
                    });
                    this.$Toast({
                        type: 'success',
                        message: '合单成功',
                    });
                    this.$emit('refresh');
                    this.handleClose();
                } catch (e) {
                    console.error(e);
                } finally {
                    this.btnLoading = false;
                }
            },
            async handleClickSplit() {
                try {
                    this.btnLoading = true;
                    await ECOrderAPI.splitOrders({
                        orderIds: [this.orderInfo.id],
                    });
                    this.$Toast({
                        type: 'success',
                        message: '拆单成功',
                    });
                    this.$emit('refresh');
                    this.handleClose();
                } catch (e) {
                    console.error(e);
                } finally {
                    this.btnLoading = false;
                }
            },
        },
    };
</script>

<style lang="scss">
    #table-cell-receiver-popover {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        width: 332px;

        .receiver-info,
        .merge-order-info {
            width: 100%;
            max-height: 350px;

            .title {
                display: flex;
                margin-bottom: 8px;

                h5 {
                    font-size: 14px;
                    font-weight: bold;
                    line-height: 22px; /* 157.143% */
                    color: var(--abc-color-T1, #000000);
                }
            }

            .merged-order-item {
                margin-top: 8px;
                font-size: 14px;
                line-height: 22px; /* 157.143% */
                color: var(--abc-color-T1, #000000);
            }

            .order-count {
                min-width: 28px;
            }

            .danger {
                color: var(--abc-color-R6);
            }
        }

        .after-sale-info {
            width: 100%;
            padding: 0 16px 16px;

            > div {
                line-height: 22px;

                & + div {
                    margin-top: 2px;
                }
            }

            .content {
                margin-left: 12px;
                font-size: 14px;
                line-height: 22px; /* 157.143% */
                color: var(--abc-color-T1, #000000);
            }

            .after-sales-no {
                margin-left: 12px;
            }
        }

        .receiver-info {
            padding: 16px;
        }

        .order-no {
            line-height: 20px; /* 133.333% */
            color: var(--abc-color-T1);
            cursor: pointer;

            .danger {
                margin-left: auto;
                font-size: 14px;
                color: var(--abc-color-R2);
            }
        }

        .total-info {
            margin: 0 0 6px;
            font-size: 12px;
            line-height: 16px; /* 133.333% */
            color: var(--abc-color-T1, #000000);
        }
    }
</style>

<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        append-to-body
        content-styles="width:480px;min-height: 320px"
        title="打印快递单"
        class="express-print-dialog-wrapper"
        @close="$emit('close')"
    >
        <abc-layout class="dialog-content clearfix">
            <abc-form label-position="left" :label-width="114">
                <div v-for="item in waybillTask" :key="item.ecType">
                    <h5>{{ ECTypeText[item.ecType] || '' }} （共{{ item.orderList.length }}订单）</h5>
                    <abc-form-item
                        label="快递面单模版"
                        class="express-print-form-item"
                        :custom-label-style="{
                            paddingTop: '10px', alignSelf: 'flex-start'
                        }"
                    >
                        <div style="font-size: 14px; line-height: 32px;">
                            按原单号补打，使用原模板
                        </div>
                    </abc-form-item>
                    <abc-form-item
                        label="快递单打印机"
                        class="express-print-form-item"
                    >
                        <abc-select
                            v-model="item.printConfig.expressPrinter"
                            :width="318"
                            placeholder="请选择"
                        >
                            <abc-option
                                v-for="(it) in printerList"
                                :key="it.name"
                                :value="it.name"
                                :label="it.name"
                            >
                                <span>{{ it.name }}</span>
                            </abc-option>
                        </abc-select>
                    </abc-form-item>
                    <abc-form-item label="同时打印发货单" class="express-print-form-item" :custom-label-style="{ alignSelf: 'flex-start' }">
                        <abc-flex vertical>
                            <abc-checkbox v-model="item.printConfig.samePrint" @change="handleChangeSamePrint(item)">
                                开启
                            </abc-checkbox>
                            <abc-checkbox v-if="item.printConfig.samePrint" v-model="item.printConfig.alternatelyPrint" style="margin-top: 8px;">
                                用快递面单打印机，交替打印快递面单、发货单
                            </abc-checkbox>
                        </abc-flex>
                    </abc-form-item>
                    <abc-form-item v-if="item.printConfig.samePrint && !item.printConfig.alternatelyPrint" label="发货单打印机" class="express-print-form-item">
                        <abc-space is-compact compact-block>
                            <abc-select v-model="item.printConfig.shipmentTemplate" :width="120" placeholder="请选择">
                                <abc-option label="A4横版" value="A4"></abc-option>
                                <abc-option label="A5横版" value="A5"></abc-option>
                                <abc-option label="热敏(76*130)" value="76"></abc-option>
                            </abc-select>
                            <abc-select
                                v-model="item.printConfig.shipmentPrinter"
                                :width="198"
                                placeholder="请选择"
                            >
                                <abc-option
                                    v-for="(it) in printerList"
                                    :key="it.name"
                                    :value="it.name"
                                    :label="it.name"
                                >
                                    <span>{{ it.name }}</span>
                                </abc-option>
                            </abc-select>
                        </abc-space>
                    </abc-form-item>
                </div>
            </abc-form>
        </abc-layout>

        <div slot="footer" class="dialog-footer">
            <abc-button @click="handleClickConfirm">
                开始打印
            </abc-button>
            <abc-button type="blank" @click="closed = true">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script type="text/babel">
    import { ECTypeText } from '../../utils/constants';
    import DialogPrintManager from '../dialog-print-manager';
    import ECOrderAPI from '@/api/order';
    import ExpressPrintManage from '../../utils/print-manager/manage.js';

    export default {
        name: 'DialogExpressRePrint',
        props: {
            value: Boolean,
            orderList: Array,
            defaultWaybillList: Array,
            onFinish: Function,
            shipPrintConfig: {
                type: Object,
                default: () => {},
            },
        },
        data() {
            return {
                ECTypeText,
                visible: false,
                closed: false,
                contentLoading: false,
                waybillTask: [],
                printerList: [],
            };
        },
        computed: {
            printTask() {
                const printTask = [];
                this.waybillTask.forEach((item) => {
                    item.orderList.forEach((order) => {
                        order.waybillInfoList.forEach((it) => {
                            printTask.push({
                                ecType: item.ecType,
                                printConfig: item.printConfig,
                                waybillInfo: {
                                    objectId: it.objectId,
                                    printData: it.printData,
                                    waybillCode: it.waybillCode,
                                    shipper: it.waybillTemplate.shipper,
                                },
                                templateInfo: {
                                    id: it.waybillTemplate.id,
                                    customAreaSwitch: it.waybillTemplate.customAreaSwitch,
                                },
                                orderInfo: order.orderInfo,
                            });
                        });
                    });
                });
                return printTask;
            },
        },
        watch: {
            closed(newVal) {
                if (newVal) {
                    this.visible = false;
                    this.destroyElement();
                }
            },
        },
        created() {
            this.fetchOrderWaybill();
            this.getPrinters();
        },
        methods: {
            async getPrinters() {
                this.printerList = await ExpressPrintManage.getPrinterList() || [];
            },
            handleChangeSamePrint(item) {
                if (!item.samePrint) {
                    item.printConfig.alternatelyPrint = false;
                }
            },
            async fetchOrderWaybill() {
                const res = await ECOrderAPI.fetchOrderWaybill({
                    orderIds: this.orderList.map((it) => it.id),
                });

                let _printConfig = localStorage.getItem('last_selected_waybill_print_config');
                if (_printConfig) {
                    _printConfig = JSON.parse(_printConfig);
                }

                this.waybillTask = [];
                res?.rows?.forEach((item) => {
                    const _obj = this.waybillTask.find((it) => item.orderInfo.ecType === it.ecType);
                    if (_obj) {
                        _obj.orderList.push({
                            orderInfo: item.orderInfo,
                            waybillInfoList: item.waybillInfoList,
                        });
                    } else {
                        const ecType = item.orderInfo?.ecType;
                        this.waybillTask.push({
                            ecType,
                            orderList: [{
                                orderInfo: item.orderInfo,
                                waybillInfoList: item.waybillInfoList,
                            }],
                            printConfig: _printConfig && _printConfig[ecType] ? _printConfig[ecType] : {
                                expressPrinter: '',
                                samePrint: false,
                                alternatelyPrint: false,
                                shipmentPrinter: '',
                                shipmentTemplate: 'A4',
                            },
                        });
                    }
                });
            },

            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
            async handleClickConfirm() {
                const status = await this.checkPrintStatus();
                if (!status) return;
                await new DialogPrintManager({
                    printTask: this.printTask,
                    shipPrintConfig: this.shipPrintConfig,
                    onFinish: () => {
                        this.closed = true;
                        this.onFinish && this.onFinish();
                    },
                }).generateDialogAsync();

                const _printConfig = {};
                this.printTask.forEach((it) => {
                    _printConfig[it.ecType] = it.printConfig;
                });
                localStorage.setItem('last_selected_waybill_print_config', JSON.stringify(_printConfig));
            },
            async checkPrintStatus() {
                try {
                    await ExpressPrintManage.checkPrintStatus(this.printTask);
                    return true;
                } catch (e) {
                    console.error(e);
                    this.$alert({
                        type: 'warn',
                        title: '未连接到打印组件',
                        content: [
                            '请检查是否启动拼多多打印组件',
                            '或者没有下载？<a style="color: #005ed9;text-decoration: underline" href="http://meta.pinduoduo.com/api/one/app/v1/lateststable?appId=com.xunmeng.pddprint&platform=windows&subType=main">点击下载</a>',
                        ],
                    });
                    return false;
                }
            },
        },
    };
</script>
<style lang="scss">
    .express-print-dialog-wrapper {
        .dialog-content {
            h5 {
                margin-bottom: 16px;
                font-size: 14px;
                font-weight: bold;
                line-height: 22px; /* 157.143% */
                color: var(--abc-color-T1, #000000);
            }

            .express-print-form-item {
                margin-right: 0;
            }
        }
    }
</style>

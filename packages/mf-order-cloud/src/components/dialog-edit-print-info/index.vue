<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        append-to-body
        title="修改打印信息"
        size="large"
        :show-header-border-bottom="true"
        :fullscreen="false"
        :responsive="false"
        :header-style="{
            'padding': '0 8px 0 12px',
        }"
        @close="$emit('close')"
    >
        <template #top-extend>
            <abc-tips-card-v2 theme="primary" :border-radius="true">
                {{ selectedTab ? '设置商品、规格简称可以节省打印空间，让面单更简洁，修改后对所有订单生效' : '修改后的商品信息仅会出现在快递单、发货单上，不会替代订单列表的商品信息' }}
            </abc-tips-card-v2>
        </template>

        <template #title>
            <abc-tabs-v2
                v-model="selectedTab"
                :border="false"
                :option="tabsOptions"
                size="huge"
            >
            </abc-tabs-v2>
        </template>

        <div style="height: 245px;">
            <abc-flex
                v-if="selectedTab"
                vertical
                align="flex-start"
                :gap="24"
            >
                <abc-card
                    radius-size="mini"
                    padding-size="medium"
                    background="gray"
                    style="width: 100%;"
                >
                    <abc-flex
                        justify="flex-start"
                        align="center"
                        gap="12"
                    >
                        <abc-image :src="detail.goodsImg" :width="48" :height="48"></abc-image>
                        <abc-flex
                            vertical
                            justify="flex-start"
                            align="flex-start"
                            gap="2"
                        >
                            <abc-text size="large" theme="black" :bold="true">
                                {{ detail.goodsName || '-' }}
                            </abc-text>
                            <abc-flex
                                justify="flex-start"
                                align="center"
                                gap="large"
                                style="width: 100%;"
                            >
                                <abc-text size="normal" theme="gray">
                                    规格: {{ detail.goodsSpec || '-' }}
                                </abc-text>
                                <abc-text size="normal" theme="gray">
                                    商品ID: {{ detail.id || detail.goodsId || '-' }}
                                </abc-text>
                                <abc-text size="normal" theme="gray">
                                    规格编码: {{ detail.outerId || '-' }}
                                </abc-text>
                            </abc-flex>
                        </abc-flex>
                    </abc-flex>
                </abc-card>

                <abc-form
                    label-position="left"
                    :label-width="72"
                    item-no-margin
                >
                    <abc-flex vertical gap="large">
                        <abc-form-item label="商品简称">
                            <abc-input
                                v-model="formData.goodsShortName"
                                placeholder="请输入"
                                :clearable="true"
                                :width="300"
                            ></abc-input>
                        </abc-form-item>
                        <abc-form-item label="规格简称">
                            <abc-input
                                v-model="formData.specShortName"
                                placeholder="请输入"
                                :clearable="true"
                                :width="300"
                            ></abc-input>
                        </abc-form-item>
                    </abc-flex>
                </abc-form>
            </abc-flex>
            <abc-form
                v-else
                label-position="left-top"
                item-no-margin
                :label-width="142"
            >
                <abc-flex vertical gap="large">
                    <abc-form-item label="当前商品信息">
                        <abc-text>{{ detail.goodsName }}</abc-text>
                    </abc-form-item>
                    <abc-form-item label="修改后商品打印信息">
                        <abc-textarea
                            v-model="formData.goodsPrintInfo"
                            placeholder="请输入"
                            :width="450"
                            :rows="2"
                        ></abc-textarea>
                    </abc-form-item>
                </abc-flex>

                <abc-divider></abc-divider>

                <abc-flex vertical gap="large">
                    <abc-form-item label="当前规格信息">
                        <abc-text>{{ detail.goodsSpec }}</abc-text>
                    </abc-form-item>
                    <abc-form-item label="修改后规格打印信息">
                        <abc-textarea
                            v-model="formData.goodsPrintSpec"
                            placeholder="请输入"
                            :width="450"
                            :rows="2"
                        ></abc-textarea>
                    </abc-form-item>
                </abc-flex>
            </abc-form>
        </div>

        <template #footer>
            <abc-flex justify="flex-end" align="center" class="dialog-footer">
                <abc-space :size="12">
                    <abc-button @click="handleConfirm">
                        确定修改{{ selectedTab ? '简称' : '打印信息' }}
                    </abc-button>
                    <abc-button
                        variant="ghost"
                        @click="closed = true"
                    >
                        取消
                    </abc-button>
                </abc-space>
            </abc-flex>
        </template>
    </abc-dialog>
</template>

<script>
    import ECOrderAPI from '@/api/order';

    export default {
        name: 'GoodsPrintInfoDialog',
        props: {
            value: Boolean,
            detail: {
                type: Object,
                default: () => {},
            },
            finishFunc: {
                type: Function,
                default: null,
            },
        },
        data() {
            return {
                visible: false,
                closed: false,
                selectedTab: 0,
                tabsOptions: [
                    {
                        value: 0,
                        label: '修改面单打印信息',
                    },
                    {
                        value: 1,
                        label: '修改商品规格简称',
                    },
                ],
                formData: {
                    goodsPrintInfo: '',
                    goodsPrintSpec: '',
                    goodsShortName: '',
                    specShortName: '',
                },
            };
        },
        watch: {
            closed(newVal) {
                if (newVal) {
                    this.visible = false;
                    this.destroyElement();
                }
            },
        },
        created() {
            const {
                printInfo: {
                    printAbbreviation,
                } = {},
                productSpecAbbreviation: {
                    printAbbreviation: shortPrintAbbreviation,
                } = {},
            } = this.detail;
            if (printAbbreviation) {
                this.formData.goodsPrintInfo = printAbbreviation.productAbbreviation;
                this.formData.goodsPrintSpec = printAbbreviation.specificationAbbreviation;
            }
            if (shortPrintAbbreviation) {
                this.formData.goodsShortName = shortPrintAbbreviation.productAbbreviation;
                this.formData.specShortName = shortPrintAbbreviation.specificationAbbreviation;
            }
        },
        methods: {
            async handleConfirm() {
                try {
                    if (this.selectedTab === 1) {
                        const params = {
                            goodsSkuId: this.detail.ecGoodsSkuId,
                            productAbbreviation: this.formData.goodsShortName,
                            specificationAbbreviation: this.formData.specShortName,
                        };
                        await ECOrderAPI.updateOrderPrintShortName(params);
                    } else {
                        const params = {
                            orderItemId: this.detail.ecOrderItemId,
                            productAbbreviation: this.formData.goodsPrintInfo,
                            specificationAbbreviation: this.formData.goodsPrintSpec,
                        };
                        await ECOrderAPI.updateOrderPrintInfo(params);
                    }
                    this.$Toast({
                        type: 'success',
                        message: '修改成功',
                    });
                    this.finishFunc && this.finishFunc();
                    this.closed = true;
                } catch (err) {
                    console.error(err);
                }
            },
            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
        },
    };
</script>

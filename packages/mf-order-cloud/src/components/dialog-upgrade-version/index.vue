<template>
    <abc-modal
        v-if="visible"
        v-model="visible"
        :show-footer="false"
        title="需升级至专业版及以上版本"
        :close-on-click-modal="false"
        content-styles="text-align: center;"
        custom-class="upgrade-version-dialog"
    >
        <abc-flex
            vertical
            height="150"
            justify="center"
            align="center"
        >
            <abc-flex vertical :gap="8">
                <abc-text theme="gray-light">
                    当前版本为基础版
                </abc-text>
                <abc-text theme="gray-light">
                    请联系客户经理升级版本后使用
                </abc-text>
            </abc-flex>
            <abc-flex
                vertical
                style="height: 170px; margin-top: 16px;"
                justify="center"
                align="center"
            >
                <abc-image
                    :width="138"
                    :height="138"
                    class="rocket-img"
                    :src="upgradeImg"
                ></abc-image>
            </abc-flex>
            <abc-button
                size="large"
                width="120"
                height="40"
                type="primary"
                @click="handleClose"
            >
                我知道了
            </abc-button>
        </abc-flex>
    </abc-modal>
</template>

<script>
    import upgradeImg from '../../assets/images/install/install-upgrade.png';
    export default {
        name: 'UpgradeVersionDialog',
        data() {
            return {
                visible: false,
                upgradeImg,
            };
        },
        methods: {
            handleClose() {
                this.visible = false;
                this.destroyElement();
            },
            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
        },
    };
</script>

<style lang="scss">
.upgrade-version-dialog {
    width: 420px;
}
</style>

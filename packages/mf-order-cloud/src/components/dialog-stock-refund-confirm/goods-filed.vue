<template>
    <abc-flex
        class="goods-filed-wrapper"
    >
        <abc-flex class="goods-info" align="center">
            <div class="name ellipsis">
                {{ goods.goodsName || '' }}
            </div>
        </abc-flex>
        <abc-flex class="goods-describe" align="center">
            <abc-space>
                <span>
                    {{ goods.goodsSpec || '' }}
                </span>
                <span>
                    {{ goods.manufacturer || '' }}
                </span>
            </abc-space>
        </abc-flex>
    </abc-flex>
</template>

<script type="text/ecmascript-6">
    export default {
        name: 'GoodsFiled',
        props: {
            goods: {
                type: Object,
                required: true,
            },
        },
        data() {
            return {};
        },
    };
</script>

<style lang="scss">
.goods-filed-wrapper {
    width: 100%;

    .goods-info {
        margin-right: 8px;

        .name {
            font-size: 14px;
            font-style: normal;
            font-weight: bold;
            line-height: 22px; /* 150% */
            color: var(--abc-color-T1, #000000);
        }
    }

    .goods-describe {
        font-size: 12px;
        line-height: 16px; /* 157.143% */
        color: var(--abc-color-T2);
    }
}
</style>


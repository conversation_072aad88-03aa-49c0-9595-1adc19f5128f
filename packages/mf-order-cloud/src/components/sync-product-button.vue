<template>
    <abc-popover
        v-if="meituanService"
        width="780px"
        placement="bottom-end"
        trigger="hover"
        theme="white"
        :disabled="!loading"
    >
        <abc-dropdown
            slot="reference"
            placement="bottom-end"
            :disabled="loading"
            @change="handleSyncProduct"
        >
            <div slot="reference">
                <abc-button
                    :icon="loading ? '' : 's-referral-line'"
                    :disabled="disabled"
                    variant="ghost"
                    style="min-width: 128px;"
                >
                    <abc-flex v-if="loading" align="center">
                        <abc-loading-spinner small style="margin-right: 8px;"></abc-loading-spinner>
                        同步商品中…
                    </abc-flex>
                    <template v-else>
                        重新同步商品
                    </template>
                </abc-button>
            </div>
            <abc-dropdown-item label="同步在售商品" :value="1"></abc-dropdown-item>
            <abc-dropdown-item label="同步全部商品" :value="0"></abc-dropdown-item>
        </abc-dropdown>

        <abc-flex
            vertical
            justify="center"
            align="center"
            style="padding: 14px;"
            :gap="40"
        >
            <abc-text size="large" bold>
                同步网店商品
            </abc-text>
            <abc-flex
                v-if="crawlerLoading"
                vertical
                style="height: 228px;"
                justify="center"
                align="center"
                :gap="40"
            >
                <abc-loading-spinner middle></abc-loading-spinner>
                <abc-text size="large">
                    正在获取商品数据...
                </abc-text>
            </abc-flex>
            <template v-else>
                <abc-flex
                    vertical
                    justify="center"
                    align="center"
                    :gap="16"
                >
                    <abc-progress
                        :percentage="syncInfo.progress"
                        :custom-percentage="`${syncInfo.progress}%`"
                        variant="circle"
                        theme="green"
                    >
                    </abc-progress>
                    <abc-text theme="success" bold>
                        正在同步网店商品、自动绑定ABC商品
                    </abc-text>
                </abc-flex>

                <abc-flex>
                    <abc-flex
                        vertical
                        align="center"
                        gap="middle"
                        style="width: 171px;"
                    >
                        <abc-text theme="gray">
                            网店商品总数
                        </abc-text>
                        <abc-text size="large">
                            {{ syncInfo.total }}
                        </abc-text>
                    </abc-flex>
                    <abc-row
                        :gutter="[0, 'small']"
                        :wrap="true"
                        align="center"
                        style="width: 171px;"
                    >
                        <abc-col :span="11" style="text-align: right;">
                            <abc-text theme="gray">
                                已同步
                            </abc-text>
                        </abc-col>
                        <abc-col :span="2">
                            <abc-text theme="gray">
                                ｜
                            </abc-text>
                        </abc-col>
                        <abc-col :span="11">
                            <abc-text theme="gray">
                                已绑定
                            </abc-text>
                        </abc-col>

                        <abc-col :span="11" style="text-align: right;">
                            <abc-text size="large">
                                {{ syncInfo.synced }}
                            </abc-text>
                        </abc-col>
                        <abc-col :span="2">
                            <abc-text size="large">
                                ｜
                            </abc-text>
                        </abc-col>
                        <abc-col :span="11">
                            <abc-text size="large" :theme="syncInfo.progress === 100 && syncInfo.binded !== syncInfo.synced ? 'warning' : 'success'">
                                {{ syncInfo.binded }}
                            </abc-text>
                        </abc-col>
                    </abc-row>
                    <abc-flex
                        vertical
                        align="center"
                        gap="middle"
                        style="width: 171px;"
                    >
                        <abc-text theme="gray">
                            待同步
                        </abc-text>
                        <abc-text size="large">
                            {{ waitSync }}
                        </abc-text>
                    </abc-flex>
                    <abc-flex
                        vertical
                        align="center"
                        gap="middle"
                        style="width: 171px;"
                    >
                        <abc-text theme="gray">
                            剩余时长
                        </abc-text>
                        <abc-text size="large">
                            {{ syncInfo.progress === 100 ? '-' : syncTime }}
                        </abc-text>
                    </abc-flex>
                </abc-flex>
            </template>
        </abc-flex>
    </abc-popover>
</template>

<script>
    import { AuthStatus } from '@/daemon/crawler/common/constants';
    import { OrderCloudDaemonService } from '@/daemon/index.js';
    import {
        MEITUAN_EVENT_SYNC_PRODUCT_INFO,
        MEITUAN_EVENT_SYNC_PRODUCT_IS_DONE,
        MEITUAN_EVENT_SYNC_PRODUCT_IS_FAILED,
        MEITUAN_EVENT_SYNC_PRODUCT_SUMMARY_IS_DONE,
    } from '@/daemon/crawler/provider/meituan/constants';
    import {
        ECTypeEnum,
        EcShopTypeEnum,
    } from '@/utils/constants';
    import { LoadingSpinner as AbcLoadingSpinner } from '@abc/ui-pc';
    import ECAuthAPI from '@/api/auth';

    export default {
        name: 'SyncProductButton',
        components: {
            AbcLoadingSpinner,
        },
        props: {
            disabled: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                loading: false,
                meituanService: null,
                crawlerLoading: false,
                syncInfo: {
                    total: 0,
                    synced: 0,
                    binded: 0,
                    progress: 0,
                },
            };
        },
        computed: {
            // 待同步
            waitSync() {
                return this.syncInfo.total - this.syncInfo.synced;
            },
            // 剩余同步时长
            syncTime() {
                const totalSeconds = Math.round(this.waitSync * 5);
                const hours = Math.floor(totalSeconds / 3600);
                const minutes = Math.floor((totalSeconds % 3600) / 60);
                const seconds = totalSeconds % 60;
                let result = '';
                if (hours > 0) result += `${hours}小时`;
                if (minutes > 0) result += `${minutes}分`;
                result += `${seconds}秒`;
                return result;
            },
        },
        mounted() {
            this.init();
        },
        methods: {
            async init() {
                const crawlerManager = OrderCloudDaemonService.getInstance().getCrawlerManager();
                if (crawlerManager) {
                    const authMallList = await crawlerManager.getAuthMallList();
                    const MTMall = authMallList.find((mall) => mall.shopType === EcShopTypeEnum.O2O && mall.ecType === ECTypeEnum.MT);
                    this.meituanService = crawlerManager.getTargetService(MTMall.extMallId);
                    this.meituanService.on('event', (event) => {
                        if (event.type === MEITUAN_EVENT_SYNC_PRODUCT_IS_DONE) {
                            this.handleSyncIsDone();
                        }
                        if (event.type === MEITUAN_EVENT_SYNC_PRODUCT_IS_FAILED) {
                            this.handleSyncIsFailed();
                        }
                        if (event.type === MEITUAN_EVENT_SYNC_PRODUCT_SUMMARY_IS_DONE) {
                            this.syncInfo = this.meituanService.syncInfo;
                            this.crawlerLoading = false;
                        }
                        if (event.type === MEITUAN_EVENT_SYNC_PRODUCT_INFO) {
                            this.syncInfo = this.meituanService.syncInfo;
                        }
                    });

                    const { authStatus } = this.meituanService;
                    if (authStatus !== AuthStatus.AUTHED) return;
                    // 检查是否正在同步商品
                    this.loading = this.meituanService.isSyncingProduct;
                }
            },
            async handleSyncIsDone() {
                this.loading = false;
                if (!this._needTips) return;
                // 显示成功提示
                this.$alert({
                    type: 'success',
                    title: '同步商品成功',
                    content: '同步商品成功，可以开始查看或处理了',
                });
                this._needTips = false;
                this.$emit('finish');
            },
            async handleSyncIsFailed() {
                this.loading = false;
                if (!this._needTips) return;
                // 显示失败提示
                this.$alert({
                    type: 'error',
                    title: '同步商品失败',
                    content: '同步商品失败，可以重新尝试',
                });
                this._needTips = false;
            },
            async handleSyncProduct(val) {
                if (this.loading) return;

                this.crawlerLoading = true;
                this.loading = true;
                try {
                    if (!this.meituanService) {
                        this.$alert({
                            type: 'error',
                            title: '提示',
                            content: '需要使用ABC客户端',
                        });
                        return;
                    }

                    const {
                        ecMallId,
                        uuid,
                    } = this.meituanService;

                    await ECAuthAPI.syncMTFlag({
                        mallId: ecMallId,
                        clientId: uuid,
                        opType: 0,
                        isForced: 1,
                    });

                    // 开始同步商品
                    await this.meituanService.scrapeProductListOnce(val === 1, true);

                    this._needTips = true;
                } catch (error) {
                    console.error('同步商品失败', error);
                    this.$abcMessage.error('同步商品失败');
                }
            },
        },
    };
</script>

<template>
    <abc-dropdown
        :max-width="width"
        :min-width="width"
        custom-class="waybill-contact-address-dropdown"
        @change="handleContactAddressChange"
    >
        <div slot="reference" class="seller-address-dropdown-wrapper">
            <abc-edit-div
                v-model="selectedContactAddress"
                readonly
                :width="width"
                :style="{
                    width: `${width}px`,
                    cursor: 'pointer'
                }"
            >
            </abc-edit-div>
            <abc-delete-icon
                v-if="selectedContactAddress"
                class="clear-address-icon"
                theme="dark"
                @delete="handleClearAddress"
            ></abc-delete-icon>
        </div>
        <abc-dropdown-item
            v-for="(item,index) in shipperContactList"
            :key="index"
            :value="item"
        >
            <abc-flex class="patient" align="center">
                <h5>{{ item.name }}</h5>
                <span style="margin: 0 8px; color: var(--abc-color-T2);">{{
                    item.mobile
                }}</span>
                <abc-button type="text" @click.stop="handleAddShipperContact(item)">
                    修改
                </abc-button>
            </abc-flex>
            <div
                style="font-size: 12px; color: var(--abc-color-T2); word-break: break-all;"
            >
                {{ getAddressStr(item.address) }}
            </div>
        </abc-dropdown-item>
        <abc-dropdown-item style="padding: 0;">
            <div
                style="padding: 6px 12px; color: var(--abc-color-theme1); text-align: center;"
                @click="handleAddShipperContact({})"
            >
                <span>添加发货打印展示地址</span>
            </div>
        </abc-dropdown-item>
    </abc-dropdown>
</template>

<script>
    import ECOrderAPI from '../api/order';
    import DialogContactForm from '../components/dialog-contact-form';

    export default {
        name: 'ECShipPrintConfig',
        props: {
            value: Object,
            width: {
                type: Number,
                default: 220,
            },
        },
        data() {
            return {
                shipperContactList: [],
            };
        },
        computed: {
            selectedContactAddress() {
                const {
                    name,
                    mobile,
                    address,
                } = this.value || {};
                if (!name) return '';

                return `${name}&nbsp;&nbsp;${mobile}<br/>${this.getAddressStr(address)}`;
            },
        },
        created() {
            this.initHandler();
        },
        methods: {
            async initHandler() {
                try {
                    this.contentLoading = true;
                    await this.fetchShipperContactList();
                } catch (e) {
                    console.error(e);
                } finally {
                    this.contentLoading = false;
                }
            },
            handleAddShipperContact(item) {
                new DialogContactForm({
                    defaultData: item,
                    onRefresh: this.fetchShipperContactList,
                }).generateDialogAsync();
            },
            getAddressStr(item) {
                const {
                    city,
                    detail,
                    district,
                    province,
                } = item;
                const _arr = [];
                if (province) {
                    _arr.push(`${province}`);
                }
                if (city) {
                    _arr.push(`${city}`);
                }
                if (district) {
                    _arr.push(district);
                }
                if (detail) {
                    _arr.push(detail);
                }
                return _arr.join('');
            },
            async fetchShipperContactList() {
                const res = await ECOrderAPI.fetchShipperContactList();
                this.shipperContactList = res.rows;
            },
            handleContactAddressChange(item) {
                this.$emit('input', item);
                this.$emit('change', item);
            },
            handleClearAddress() {
                const sellerAddress = {
                    address: {
                        city: '',
                        cityCode: '',
                        country: '',
                        countryCode: '',
                        detail: '',
                        district: '',
                        districtCode: '',
                        province: '',
                        provinceCode: '',
                        town: '',
                        townCode: '',
                    },
                    mobile: '',
                    mobileCountryCode: '',
                    name: '',
                    phone: '',
                    phoneCountryCode: '',
                };
                this.$emit('input', sellerAddress);
                this.$emit('change', sellerAddress);
            },

        },
    };
</script>
<style lang="scss">
@import "src/styles/theme.scss";
@import "src/styles/mixin.scss";

.seller-address-dropdown-wrapper {
    position: relative;

    .clear-address-icon {
        position: absolute;
        top: 50%;
        right: 8px;
        visibility: hidden;
        transform: translate3d(0, -50%, 0);
    }

    &:hover {
        .clear-address-icon {
            visibility: visible;
        }
    }
}
</style>



<template>
    <abc-button
        v-if="meituanService"
        :icon="loading ? '' : 's-referral-line'"
        :disabled="disabled"
        variant="ghost"
        @click="handleSyncOrder"
    >
        <abc-flex v-if="loading" align="center">
            <abc-loading-spinner small style="margin-right: 8px;"></abc-loading-spinner>
            同步订单中…
        </abc-flex>
        <template v-else>
            重新同步订单
        </template>
    </abc-button>
</template>

<script>
    import { AuthStatus } from '@/daemon/crawler/common/constants';
    import { OrderCloudDaemonService } from '@/daemon/index.js';
    import {
        MEITUAN_EVENT_SYNC_IS_DONE,
        MEITUAN_EVENT_SYNC_IS_START,
    } from '@/daemon/crawler/provider/meituan/constants';
    import {
        ECTypeEnum,
        EcShopTypeEnum,
    } from '@/utils/constants';
    import { LoadingSpinner as AbcLoadingSpinner } from '@abc/ui-pc';
    import ECAuthAPI from '@/api/auth';

    export default {
        name: 'SyncOrderButton',
        components: {
            AbcLoadingSpinner,
        },
        props: {
            disabled: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                loading: false,
                meituanService: null,
            };
        },
        mounted() {
            this.init();
        },
        methods: {
            async init() {
                const crawlerManager = OrderCloudDaemonService.getInstance().getCrawlerManager();
                if (crawlerManager) {
                    const authMallList = await crawlerManager.getAuthMallList();
                    const MTMall = authMallList.find((mall) => mall.shopType === EcShopTypeEnum.O2O && mall.ecType === ECTypeEnum.MT);
                    this.meituanService = crawlerManager.getTargetService(MTMall.extMallId);
                    this.meituanService.on('event', (event) => {
                        if (event.type === MEITUAN_EVENT_SYNC_IS_DONE) {
                            this.handleSyncIsDone();
                        } else if (event.type === MEITUAN_EVENT_SYNC_IS_START) {
                            this.loading = true;
                        }
                    });

                    const {
                        authStatus,
                        MTOrderPrescriptionCrawler,
                    } = this.meituanService;
                    if (authStatus !== AuthStatus.AUTHED) return;
                    const { isSyncing } = MTOrderPrescriptionCrawler;
                    this.loading = isSyncing;
                }
            },
            async handleSyncIsDone() {
                this.loading = false;
                if (!this._needTips) return;
                // 显示成功提示
                this.$alert({
                    type: 'success',
                    title: '同步订单成功',
                    content: '同步订单成功，可以开始查看或处理了',
                });
                this._needTips = false;
            },
            async handleSyncOrder() {
                if (this.loading) return;

                this.loading = true;
                try {
                    if (!this.meituanService) {
                        this.$alert({
                            type: 'error',
                            title: '提示',
                            content: '需要使用ABC客户端',
                        });
                        return;
                    }
                    const {
                        ecMallId,
                        uuid,
                    } = this.meituanService;

                    await ECAuthAPI.syncMTFlag({
                        mallId: ecMallId,
                        clientId: uuid,
                        opType: 0,
                        isForced: 1,
                    });

                    this.$Toast({
                        message: '开始同步订单，预计1分钟',
                        type: 'success',
                    });

                    this._timer = setTimeout(() => {
                        location.reload();
                    }, 1000);
                    this._needTips = true;
                } catch (error) {
                    console.error('同步订单失败', error);
                } finally {
                    this.loading = false;
                }
            },
        },
    };
</script>

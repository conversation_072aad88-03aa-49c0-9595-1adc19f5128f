<template>
    <abc-popover
        placement="bottom-start"
        trigger="click"
        :visible-arrow="false"
        theme="yellow"
        padding-size="large"
        style="height: 100%;"
        size="large"
    >
        <abc-table-cell
            slot="reference"
            class="ecommerce-table-cell"
            vertical
            align="flex-start"
            justify="center"
            clickable
        >
            <abc-flex vertical gap="8">
                <div
                    v-for="(item, index) in goodsList"
                    :key="`${item.goodsId }_${ index}`"
                    :title="item.goodsName"
                >
                    ×{{ item.goodsCount }} <span v-if="item.isGift" class="danger" style="margin-right: 2px;">[赠品]</span>{{ item.productSpecAbbreviation?.printAbbreviation?.productAbbreviation || item.goodsName }} <abc-text>
                        {{ item.productSpecAbbreviation?.printAbbreviation?.specificationAbbreviation || item.goodsSpec || '' }}
                    </abc-text>
                </div>
            </abc-flex>
        </abc-table-cell>

        <div id="table-cell-goods-popover">
            <h5>订单SKU</h5>
            <abc-flex
                v-for="(item, index) in goodsList"
                :key="`${item.goodsId }_${ index}`"
                align="center"
                class="goods-item"
            >
                <abc-image
                    style="border-radius: var(--abc-border-radius-small);"
                    :src="item.goodsImg"
                    :width="44"
                    :height="44"
                ></abc-image>
                <abc-flex class="count" align="center" justify="center">
                    ×{{ item.goodsCount }}
                </abc-flex>
                <abc-flex class="goods-name" align="baseline" gap="small">
                    <abc-text style="flex: 1;">
                        <abc-tooltip
                            v-if="showEdit && (item.printInfo?.printAbbreviation?.productAbbreviation || item.printInfo?.printAbbreviation?.specificationAbbreviation)"
                            theme="black"
                            placement="top"
                            style="display: inline-block;"
                        >
                            <div>
                                <abc-text theme="warning-light">
                                    [打印信息修改]
                                </abc-text>
                            </div>
                            <template #content>
                                <p v-if="item.printInfo.printAbbreviation.productAbbreviation">
                                    商品打印信息：{{ item.printInfo.printAbbreviation.productAbbreviation }}
                                </p>
                                <p v-if="item.printInfo.printAbbreviation.specificationAbbreviation">
                                    规格打印信息：{{ item.printInfo.printAbbreviation.specificationAbbreviation }}
                                </p>
                            </template>
                        </abc-tooltip>
                        <abc-text v-if="item.isGift" theme="danger-light">
                            [赠品]
                        </abc-text>{{ item.productSpecAbbreviation?.printAbbreviation?.productAbbreviation || item.goodsName }}
                        {{ item.productSpecAbbreviation?.printAbbreviation?.specificationAbbreviation || item.goodsSpec || '' }}
                    </abc-text>
                    <abc-button v-if="showEdit" type="text" @click="handleEdit(item)">
                        修改
                    </abc-button>
                </abc-flex>
            </abc-flex>
        </div>
    </abc-popover>
</template>

<script>
    export default {
        props: {
            goodsList: {
                type: Array,
                required: true,
            },
            showEdit: {
                type: Boolean,
                default: false,
            },
        },
        methods: {
            handleEdit(item) {
                this.$emit('edit', item);
            },
        },
    };
</script>

<style lang="scss">
@import "../styles/theme.scss";
@import "../styles/mixin.scss";

#table-cell-goods-popover {
    width: 332px;
    max-height: 280px;

    h5 {
        margin-bottom: 8px;
        font-size: 14px;
        font-weight: bold;
        line-height: 22px; /* 157.143% */
        color: var(--abc-color-T1, #000000);
    }

    .goods-item {
        min-height: 44px;

        .count {
            width: 44px;
        }

        & + .goods-item {
            margin-top: 12px;
        }

        .goods-name {
            flex: 1;
            width: 0;
        }
    }

    .danger {
        color: var(--abc-color-R6);
    }
}
</style>

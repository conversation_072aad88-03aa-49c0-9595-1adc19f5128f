<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        append-to-body
        class="refund-batches-selector-dialog"
        :auto-focus="false"
        content-styles="width: 960px; padding: 24px;"
        title="选择批次"
        @close="$emit('close')"
    >
        <abc-layout v-abc-loading="contentLoading" class="dialog-content clearfix">
            <abc-form ref="abcForm" item-no-margin>
                <abc-table
                    :render-config="renderConfig"
                    :data-list="dataList"
                    style="height: 440px;"
                >
                    <template #topHeader>
                        <abc-flex justify="space-between" style="width: 100%;">
                            <goods-filed :vertical="false" :goods="productInfo"></goods-filed>
                            <abc-flex align="center" style="margin-right: -12px;">
                                <span style="min-width: 34px;">总量</span>

                                <abc-form-item 
                                    class="unit-count-wrap"
                                    :validate-event="validateRefundTotalCount"
                                >
                                    <abc-input-number
                                        v-model="curUnitCount"
                                        v-abc-focus-selected
                                        fixed-button
                                        button-placement="left"
                                        :width="54"
                                        disabled
                                    >
                                    </abc-input-number>
                                </abc-form-item>

                                <div class="unit">
                                    {{ curUnit }}
                                </div>
                            </abc-flex>
                        </abc-flex>
                    </template>
                    <template #productionDate="{ trData }">
                        <abc-table-cell>
                            {{ trData.batchInfo && trData.batchInfo.productionDate }}
                        </abc-table-cell>
                    </template>
                    <template #expiryDate="{ trData }">
                        <abc-table-cell>
                            {{ trData.batchInfo && trData.batchInfo.expiryDate }}
                        </abc-table-cell>
                    </template>
                    <template #packageCostPrice="{ trData }">
                        <abc-table-cell>
                            {{ trData.batchInfo && trData.batchInfo.packageCostPrice | formatMoney(false) }}
                        </abc-table-cell>
                    </template>
                    <template #inDate="{ trData }">
                        <abc-table-cell>
                            {{ trData.batchInfo && trData.batchInfo.inDate | parseTime('y-m-d') }}
                        </abc-table-cell>
                    </template>
                    <template #unitCount="{ trData }">
                        <abc-table-cell>
                            <abc-form-item :validate-event="validateRefundCount(trData)">
                                <abc-input-number
                                    v-model="trData.unitCount"
                                    v-abc-focus-selected
                                    fixed-button
                                    button-placement="left"
                                    :width="54"
                                    :config="{
                                        supportZero: true,
                                        max: trData.canRefundUnitCount,
                                        formatLength: supportDecimal ? 2 : undefined
                                    }"
                                    :auto-step="supportDecimal"
                                    @change="handleInputBatchCount"
                                >
                                </abc-input-number>
                            </abc-form-item>
                        </abc-table-cell>
                    </template>
                    <template #unit>
                        <abc-table-cell>
                            {{ curUnit }}
                        </abc-table-cell>
                    </template>
                </abc-table>
            </abc-form>
        </abc-layout>

        <div slot="footer" class="dialog-footer">
            <abc-button @click="handleClickConfirm">
                确定
            </abc-button>
            <abc-button type="blank" @click="closed = true">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script type="text/babel">
    import GoodsFiled from 'MfBase/goods-filed';
    import {
        isChineseMedicine,
        isGoods,
    } from 'MfBase/filters';
    import {
        clone, 
    } from '@/utils/index.js';
    import Big from 'big.js';

    export default {
        name: 'RefundBatchesSelectorDialog',
        components: {
            GoodsFiled,
        },
        props: {
            value: Boolean,
            chargeItem: {
                type: Object,
                required: true,
            },
            onConfirm: {
                type: Function,
                required: true,
            },
            countPreix: {
                type: String,
                default: '可退',
            },
            action: {
                type: String,
                default: '退单',
            },
            // 是否需要进价
            needCostPrice: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                visible: false,
                closed: false,
                contentLoading: false,
                dataList: [],

                useDismounting: this.chargeItem.useDismounting,
                curUnitCount: this.chargeItem.unitCount,
                canRefundUnitCount: this.chargeItem.unitCount,
                lastUnitCount: this.chargeItem.unitCount,
                curUnit: this.chargeItem.unit,
            };
        },
        computed: {
            renderConfig() {
                const list = [
                    {
                        'key': 'batchNo',
                        'label': '生产批号',
                        'style': {
                            'flex': '1',
                            'min-width': '120px',
                        },
                    },
                    {
                        'key': 'productionDate',
                        'label': '生产日期',
                        'style': {
                            'flex': '1',
                        },
                    },
                    {
                        'key': 'expiryDate',
                        'label': '效期',
                        'style': {
                            'flex': '1',
                        },
                    },
                    {
                        'key': 'packageCostPrice',
                        'label': '进价',
                        'colType': 'money4',
                        'style': {
                            'flex': '1',
                            'textAlign': 'right',
                        },
                    },
                    {
                        'key': 'inDate',
                        'label': '入库日期',
                        'colType': 'date',
                        'style': {
                            'flex': '1',
                        },
                    },
                    {
                        'key': 'canRefundUnitCount',
                        'label': `${this.countPreix}数量`,
                        'style': {
                            'flex': '1',
                            'textAlign': 'right',
                            'paddingRight': '24px',
                        },
                    },{
                        'key': 'unitCount',
                        'label': '数量',
                        'style': {
                            'width': '120px',
                            'maxWidth': '120px',
                            'textAlign': 'center',
                        },
                    },{
                        'key': 'unit',
                        'label': '单位',
                        'style': {
                            'width': '80px',
                            'maxWidth': '80px',
                            'textAlign': 'center',
                        },
                    },
                ];
                if (!this.needCostPrice) {
                    const index = list.findIndex((x) => x.key === 'packageCostPrice');
                    list.splice(index, 1);
                }

                return {
                    hasInnerBorder: false,
                    list,
                };
            },
            productInfo() {
                return this.chargeItem.productInfo;
            },
            isChineseMedicine() {
                const {
                    type,
                    subType,
                } = this.productInfo;
                return isChineseMedicine({
                    type,
                    subType,
                });
            },
            goodsSupportDecimal() {
                const {
                    type,
                    subType,
                    pieceNum,
                } = this.productInfo;
                return pieceNum === 1 && isGoods({
                    type,
                    subType,
                });
            },
            supportDecimal() {
                return this.isChineseMedicine || this.goodsSupportDecimal;
            },
            batchInfoDetails() {
                const {
                    _optionsBatchInfos,
                } = this.chargeItem;
                return _optionsBatchInfos || [];
            },

        },
        watch: {
            closed(newVal) {
                if (newVal) {
                    this.visible = false;
                    this.destroyElement();
                }
            },
        },
        created() {
            this.initRefundData();
        },
        methods: {
            validateRefundTotalCount(val, callback) {
                if (+this.curUnitCount === 0) {
                    callback({
                        validate: false,
                        message: `${this.action}数量不能为0`,
                    });
                    return;
                }
                if (+this.canRefundUnitCount < +this.curUnitCount) {
                    callback({
                        validate: false,
                        message: `${this.action}数量不能大于${this.countPreix}数量（${this.canRefundUnitCount}${this.curUnit || ''}）`,
                    });
                    return;
                }
                callback({
                    validate: true,
                });

            },
            validateRefundCount(item) {
                if (+item.unitCount === 0 && +this.curUnitCount === 0) {
                    return (_, callback) => {
                        callback({
                            validate: false,
                            message: `${this.action}数量不能为0`,
                        });
                    };
                }
                if (item.canRefundUnitCount < +item.unitCount) {
                    return (_, callback) => {
                        callback({
                            validate: false,
                            message: `${this.action}数量不能大于${this.countPreix}数量（${item.canRefundUnitCount}${item.unit || ''}）`,
                        });
                    };
                }
                return (_, callback) => {
                    callback({
                        validate: true,
                    });
                };

            },

            initRefundData() {
                this.dataList = clone(this.batchInfoDetails);
                this.dataList = this.dataList.map((it, index) => {
                    it.sort = index;
                    it.unitCount = 0;
                    it.useDismounting = this.useDismounting;
                    it.canRefundUnitCount = this.useDismounting ? it.pieceCount : it.packageCount;
                    return it;
                });
                // 初始化数量
                const sort = 0;
                this.addUnitCount(this.curUnitCount, sort);
            },

            addUnitCount(diffCount, sort) {
                this.dataList.forEach((it) => {
                    if (it.sort === sort) {
                        const diff = Big(+it.unitCount).plus(+diffCount).toNumber();

                        const overLimitCount = Big(diff).minus(it.canRefundUnitCount || 0).toNumber();

                        if (overLimitCount > 0) {
                            it.unitCount = it.canRefundUnitCount;
                            this.addUnitCount(overLimitCount, sort + 1);
                        } else {
                            it.unitCount = diff;
                        }
                    }
                });
            },
            reduceUnitCount(diffCount, sort) {
                this.dataList.forEach((it) => {
                    if (it.sort === sort) {
                        const diff = Big(+it.unitCount).plus(+diffCount).toNumber();

                        if (diff < 0) {
                            it.unitCount = 0;
                            this.reduceUnitCount(diff, sort - 1);
                        }

                        if (diff >= 0) {
                            it.unitCount = diff;
                        }
                    }
                });
            },

            handleInputBatchCount() {
                this.curUnitCount = this.dataList.reduce((sum, cur) => {
                    sum = Big(+sum).plus(+cur.unitCount || 0).toNumber();
                    return sum;
                }, 0);
                this.lastUnitCount = this.curUnitCount;
            },

            async validateForm() {
                return new Promise((resolve) => {
                    this.$refs.abcForm.validate((valid) => {
                        resolve(valid);    
                    });
                });
            },

            async handleClickConfirm() {
                if (!(await this.validateForm())) {
                    return;
                }
                if (this.curUnitCount !== this.canRefundUnitCount) {
                    return this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: `${this.action}数量为${this.canRefundUnitCount}${this.curUnit}`,
                    });
                }
                if (!this.curUnitCount) {
                    return this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: `${this.action}数量不能为0`,
                    });
                }
                this.onConfirm && this.onConfirm({
                    batchInfos: this.dataList.filter((it) => it.unitCount > 0).map((it) => {
                        return {
                            id: it.id,
                            batchId: it.batchId,
                            batchNo: it.batchNo,
                            expiryDate: it.expiryDate,
                            productionDate: it.productionDate,
                            inDate: it.inDate,
                            packageCostPrice: it.packageCostPrice,
                            pieceCount: it.useDismounting ? it.unitCount : undefined,
                            packageCount: it.useDismounting ? undefined : it.unitCount,
                        };
                    }),
                });
                this.closed = true;
            },
            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
        },
    };
</script>
<style lang="scss">
    .refund-batches-selector-dialog {
        .price {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            min-width: 100px;
            margin-right: 10px;
        }

        .unit-count-wrap {
            display: flex;
            justify-content: center;
            width: 120px;
            min-width: 120px;
        }

        .unit {
            width: 80px;
            min-width: 80px;
            max-width: 80px;
            padding-right: 4px;
            text-align: center;
        }
    }
</style>

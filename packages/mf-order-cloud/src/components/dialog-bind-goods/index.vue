<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        title="绑定ABC系统的商品"
        append-to-body
        size="huge"
        :show-header-border-bottom="false"
        content-styles="height: 608px;"
        custom-class="pharmacy__bind-goods-dialog"
    >
        <template #top-extend>
            <abc-tips-card-v2 theme="primary">
                <abc-flex justify="space-between" align="center">
                    <p>绑定 ABC 商品后，ABC 库存数量将实时同步到网店商品 SKU 中，网店订单发货时会自动在 ABC 系统下账扣库</p>
                </abc-flex>
            </abc-tips-card-v2>
        </template>
        <abc-section style="margin-top: 16px;">
            <abc-text bold size="normal">
                网店 SKU
            </abc-text>
            <abc-descriptions
                style="margin-top: 10px;"
                :column="1"
                :label-width="88"
                grid
                size="large"
                label-align="center"
            >
                <abc-descriptions-item
                    :label-style="{
                        color: '#7a8794',
                        display: 'flex',
                        alignItems: 'center'
                    }"
                    label="商品名称"
                >
                    {{ goodsName }}
                </abc-descriptions-item>
                <abc-descriptions-item
                    :label-style="{
                        color: '#7a8794',
                        display: 'flex',
                        alignItems: 'center'
                    }"
                    label="SKU"
                >
                    {{ desc }}
                </abc-descriptions-item>
            </abc-descriptions>
        </abc-section>

        <abc-space
            direction="vertical"
            align="left"
            :custom-style="{
                marginTop: '24px',
            }"
        >
            <abc-text bold size="normal">
                ABC 绑定商品
            </abc-text>
        </abc-space>

        <abc-section>
            <abc-form ref="bindGoodsFormRef" class="table-wrapper">
                <abc-table
                    custom
                    type="excel"
                    support-delete-tr
                    :show-hover-tr-bg="false"
                    :data-list="dataList"
                    cell-size="large"
                >
                    <div ref="topHeader" class="abc-table-top-header">
                        <goods-autocomplete
                            placeholder="输入商品名称或扫码添加"
                            :search.sync="keyword"
                            :focus-show="true"
                            :width="480"
                            size="medium"
                            class="goods-autocomplete-wrapper"
                            need-filter-disable
                            is-type-arr
                            format-count-key="stock"
                            :is-sell="1"
                            :inorder-config="0"
                            :clinic-id="clinicId"
                            @selectGoods="onSelectGoods"
                        >
                            <abc-icon slot="prepend" icon="a-plus13px"></abc-icon>
                        </goods-autocomplete>
                    </div>
                    <abc-table-header>
                        <abc-table-td :style="tableTdStyle.name">
                            ABC 绑定商品
                        </abc-table-td>
                        <abc-table-td :style="tableTdStyle.unitCount" align="right">
                            绑定数量
                        </abc-table-td>
                        <abc-table-td :style="tableTdStyle.unit">
                            绑定单位
                        </abc-table-td>
                        <abc-table-td :style="tableTdStyle.dispGoodsCount" align="right">
                            ABC 库存
                        </abc-table-td>
                        <abc-table-td :style="tableTdStyle.allocationStock">
                            分配的ABC库存
                        </abc-table-td>
                    </abc-table-header>
                    <abc-table-body>
                        <abc-table-tr v-for="(item, index) in dataList" :key="index" @delete-tr="handleDelete(index)">
                            <abc-table-td
                                :style="tableTdStyle.name"
                                custom-td
                            >
                                <div style="padding: 6px 12px;">
                                    <div style="height: 18px;">
                                        {{ item.name }}
                                    </div>
                                    <abc-text
                                        size="mini"
                                        theme="gray"
                                    >
                                        {{ item.displaySpec }}   {{ item.manufacturer }}
                                    </abc-text>
                                </div>
                            </abc-table-td>

                            <abc-table-td :style="tableTdStyle.unitCount" align="right" custom-td>
                                <abc-form-item required :validate-event="validateCount">
                                    <abc-input v-model="item.unitCount"></abc-input>
                                </abc-form-item>
                            </abc-table-td>
                            <abc-table-td :style="tableTdStyle.unit" align="center" custom-td>
                                <abc-form-item required>
                                    <abc-select
                                        v-model="item.unit"
                                        :title="item.unit"
                                        size="small"
                                        :width="80"
                                        :tabindex="-1"
                                        @change="handleChangeUnit(item)"
                                    >
                                        <abc-option
                                            v-for="it in item.unitOptions"
                                            :key="it.name"
                                            :label="it.name"
                                            :value="it.name"
                                        >
                                        </abc-option>
                                    </abc-select>
                                </abc-form-item>
                            </abc-table-td>
                            <abc-table-td
                                custom-td
                                :style="tableTdStyle.dispGoodsCount"
                                align="right"
                            >
                                <abc-text style="display: block; height: 100%; padding-right: 10px; line-height: 48px;" :theme="item && item.shortageWarnFlag ? 'warning-light' : 'black'">
                                    {{ item.dispGoodsCount }}
                                </abc-text>
                            </abc-table-td>
                            <abc-table-td
                                custom-td
                                :style="tableTdStyle.allocationStock"
                            >
                                <table-cell-allocation-stock
                                    :ec-type="ECTypeEnum.PDD"
                                    :item="item"
                                    :clinic-id="clinicId"
                                    @change="handleChangeAllocationStock"
                                ></table-cell-allocation-stock>
                            </abc-table-td>
                        </abc-table-tr>
                        <abc-content-empty
                            v-if="dataList.length === 0"
                            class="table-content-empty"
                            size="small"
                            show-icon
                            value="暂无数据"
                        >
                        </abc-content-empty>
                    </abc-table-body>
                </abc-table>
            </abc-form>
        </abc-section>
        <div slot="footer" class="dialog-footer">
            <abc-button
                type="primary"
                :loading="btnLoading"
                @click="handleBindHisGoods"
            >
                确定
            </abc-button>
            <abc-button type="blank" @click="visible = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import GoodsAutocomplete from 'MfBase/goods-autocomplete';
    import ECGoodsAPI from '@/api/goods';
    import {
        GoodsTypeEnum, PharmacyTypeEnum,
    } from '@abc/constants';
    import TableCellAllocationStock from '@/components/table-cell-allocation-stock.vue';
    import {
        AllocationStockType, ECTypeEnum,
    } from '@/utils/constants';
    export default {
        name: 'DialogBindGoods',
        components: {
            TableCellAllocationStock,
            GoodsAutocomplete,
        },
        props: {
            value: Boolean,
            clinicId: String,
            goodsId: String,
            goodsSkuId: String,
            ecMallId: String,
            mallName: String,
            goodsName: String,
            hisGoodsList: Array,
            desc: String,
        },
        data() {
            return {
                ECTypeEnum,
                visible: false,
                loading: false,
                btnLoading: false,
                keyword: '',
                dataList: [],
                unitArray: [],
                tableTdStyle: {
                    name: {
                        flex: '1',
                        textAlign: 'left',
                        cursor: 'pointer',
                        paddingLeft: '',
                    },
                    unitCount: {
                        flex: 'none',
                        textAlign: 'right',
                        width: '80px',
                        cursor: 'pointer',
                        paddingLeft: '',
                    },
                    unit: {
                        flex: 'none',
                        textAlign: 'center',
                        width: '80px',
                        cursor: 'pointer',
                        paddingLeft: '',
                    },
                    dispGoodsCount: {
                        flex: 'none',
                        textAlign: 'right',
                        width: '108px',
                        paddingLeft: '',
                    },
                    allocationStock: {
                        flex: 'none',
                        width: '130px',
                    },
                },
            };
        },
        watch: {
            visible(val) {
                if (!val) {
                    this.destroyElement();
                }
            },
            hisGoodsList: {
                handler(list) {
                    if (list?.length) {
                        this.dataList = list.map((item) => {
                            return {
                                id: item.id,
                                name: item.hisGoodsInfo.displayName,
                                bindPackageCount: item.bindPackageCount,
                                bindPieceCount: item.bindPieceCount,
                                dispGoodsCount: item.dispStockGoodsCount || item.hisGoodsInfo?.dispStockGoodsCount ,
                                unitCount: item?.useDismounting ? item?.bindPieceCount : item?.bindPackageCount,
                                hisGoodsId: item.hisGoodsId,
                                hisGoodsInfo: item.hisGoodsInfo,
                                unit: item?.unit,
                                unitOptions: this.getUnitOptions(item.hisGoodsInfo),
                                displaySpec: item.hisGoodsInfo?.displaySpec || '',
                                manufacturer: item.hisGoodsInfo?.manufacturer || '',
                                useDismounting: item?.useDismounting || 0,
                                packageUnit: item.hisGoodsInfo?.packageUnit || '',
                                pieceUnit: item.hisGoodsInfo?.pieceUnit || '',
                                dismounting: item.hisGoodsInfo?.dismounting || 0,
                                goodsName: this.goodsName,
                                goodsSkuName: `${this.goodsName} (${this.desc})`,
                                assignStockType: item.assignStockType,
                                assignStockRatio: item.assignStockRatio,
                                assignedStockPieceCount: item.assignedStockPieceCount,
                                assignedStockPackageCount: item.assignedStockPackageCount,
                                mallName: this.mallName,
                            };
                        });
                    }
                },
                immediate: true,
                deep: true,
            },
        },
        methods: {
            handleChangeAllocationStock(relatedGoodsSkuReqs) {
                this.$abcEventBus.$emit('refresh-ec-goods');
                this.dataList.map((item) => {
                    const _obj = relatedGoodsSkuReqs?.find((it) => it.id === item.id);
                    if (_obj) {
                        Object.assign(item, _obj);
                    }
                });
            },
            isChineseMedicine(goods) {
                return goods.type === 1 && goods.subType === 2;
            },
            handleChangeUnit(item) {
                item.useDismounting = +(
                    item.dismounting &&
                    item.unit === item.pieceUnit &&
                    item.unit !== item.packageUnit
                );
            },
            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
            validateCount(value, callback) {
                if (!+value) {
                    callback({
                        validate: false,
                        message: '不能为空',
                    });
                }
            },
            handleDelete(index) {
                this.dataList.splice(index, 1);
            },
            isStockGoods(goodsType, pharmacyType) {
                // 虚拟药房不判断库存逻辑
                if (pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY) return false;
                return (
                    goodsType === GoodsTypeEnum.MEDICINE ||
                    goodsType === GoodsTypeEnum.MATERIAL ||
                    goodsType === GoodsTypeEnum.GOODS
                );
            },
            async onSelectGoods(goods) {
                this.keyword = '';
                if (!goods) return false;
                if (goods.disabled) return false;
                const {
                    type,
                    displayName,
                    medicineCadn,
                    packageUnit,
                    pieceUnit,
                    stockPieceCount,
                    stockPackageCount,
                    shortageWarnFlag,
                    displaySpec,
                    manufacturer,
                    dismounting,
                } = goods || {};

                const defaultUnit = this.isStockGoods(type) ? '' : '次';
                const selectGoods = {
                    ecType: ECTypeEnum.PDD,
                    unit: packageUnit || pieceUnit || defaultUnit,
                    name: displayName || medicineCadn,
                    unitCount: '', // 单位数量
                    unitOptions: this.getUnitOptions(goods),
                    type,
                    hisGoodsId: goods.id,
                    hisGoodsInfo: goods,
                    dispGoodsCount: this.isChineseMedicine(goods) ? `${stockPieceCount}${pieceUnit}` : stockPieceCount ? `${stockPackageCount}${packageUnit}${stockPieceCount}${pieceUnit}` : `${stockPackageCount}${packageUnit}`,
                    shortageWarnFlag,
                    displaySpec,
                    pieceUnit,
                    dismounting,
                    packageUnit,
                    manufacturer,
                    useDismounting: packageUnit ? 0 : dismounting,
                    goodsName: this.goodsName,
                    goodsSkuName: `${this.goodsName} (${this.desc})`,
                    assignStockType: null,
                    assignStockRatio: null,
                    assignedStockPieceCount: null,
                    assignedStockPackageCount: null,
                    mallName: this.mallName,
                };
                console.log(selectGoods);
                this.dataList.unshift(selectGoods);
                const { data } = await ECGoodsAPI.fetchRelatedSku(goods.id, {
                    clinicId: this.clinicId,
                });
                if (data.relatedGoodsSku?.length) {
                    const {
                        assignStockType,
                    } = data.relatedGoodsSku[0];
                    selectGoods.assignStockType = assignStockType;
                    if (assignStockType === AllocationStockType.SHARE) {
                        selectGoods.assignedStockPieceCount = stockPieceCount;
                        selectGoods.assignedStockPackageCount = stockPackageCount;
                    }
                }
            },
            getUnitOptions(item) {
                const res = [];

                const {
                    dismounting,
                    pieceUnit,
                    packageUnit,
                } = item;

                if (dismounting) {
                    if (pieceUnit) {
                        res.push({ 'name': pieceUnit });
                    }
                    if (packageUnit && packageUnit !== pieceUnit) {
                        res.push({ 'name': packageUnit });
                    }
                } else {
                    res.push({ 'name': packageUnit });
                }
                return res;
            },
            async handleBindHisGoods() {
                this.$refs.bindGoodsFormRef.validate(async (valid) => {
                    if (valid) {
                        // const flag = this.dataList.some((item) => item.assignedStockPackageCount === null && item.assignedStockPieceCount === null);
                        // if (flag) {
                        //     this.$Toast.error('未分配库存');
                        //     return;
                        // }
                        this.btnLoading = true;
                        const arr = this.dataList;
                        const params = {
                            ecMallId: this.ecMallId,
                            hisGoodsList: arr.map((item) => {
                                const {
                                    unit,
                                    hisGoodsId,
                                    unitCount,
                                    name,
                                    useDismounting,
                                    pieceUnit,
                                    packageUnit,
                                    assignStockType,
                                    assignStockRatio,
                                    assignedStockPieceCount,
                                    assignedStockPackageCount,
                                } = item;

                                const calDismounting = +(
                                    useDismounting &&
                                    unit === pieceUnit &&
                                    unit !== packageUnit
                                );
                                return {
                                    name,
                                    unit,
                                    bindPieceCount: calDismounting ? unitCount : 0,
                                    bindPackageCount: calDismounting ? 0 : unitCount,
                                    useDismounting: calDismounting,
                                    hisGoodsId,
                                    assignStockType,
                                    assignStockRatio,
                                    assignedStockPieceCount,
                                    assignedStockPackageCount,
                                };
                            }),
                        };

                        try {
                            const res = await ECGoodsAPI.bindHisGoods(this.goodsId, this.goodsSkuId, params);
                            if (res) {

                                this.$abcEventBus.$emit('refresh-ec-goods');
                                this.visible = false;
                            }
                        } catch (err) {
                            this.$Toast.error(err.message || '绑定失败');
                            console.error(err);
                        } finally {
                            this.btnLoading = false;
                        }
                    }
                });

            },
        },
    };
</script>
<style lang="scss">
.pharmacy__bind-goods-dialog {
    .goods-autocomplete-wrapper {
        .abc-input__inner {
            height: 40px;
        }
    }

    .table-wrapper {
        margin-top: 10px;
    }
}
</style>

<template>
    <div>
        <abc-dialog
            v-if="visible"
            v-model="visible"
            title="退单库存处理"
            class="o2o-goods-out-dialog"
            append-to-body
            disabled-keyboard
            data-cy="o2o-goods-out-dialog"
            responsive
        >
            <template #title-append>
                <img
                    v-if="stockDealStatus === EcOrderAfterSaleStatus.DEAL_DONE"
                    style="position: absolute; top: 12px; left: 150px; z-index: 999999; width: 64px; height: 64px;"
                    src="~assets/images/icon/deal-stock.png"
                    alt=""
                />
            </template>

            <abc-layout v-abc-loading="contentLoading">
                <abc-section>
                    <abc-descriptions
                        :column="4"
                        :label-width="88"
                        label-align="left"
                        :bordered="true"
                        :grid="true"
                        :background="true"
                        size="large"
                        :custom-title-style="{ 'background-color': 'var(--abc-color-LY4)' }"
                    >
                        <template #title>
                            {{ ecTypeText }}#{{ orderDaySeq }} {{ receiverInfoStr }}
                            <abc-tag-v2
                                v-if="orderDetail.orderTypeFlag & 8"
                                variant="outline"
                                shape="square"
                                size="small"
                                theme="danger"
                                style="margin-left: 8px;"
                            >
                                处方
                            </abc-tag-v2>
                        </template>
                        <abc-descriptions-item label="退款">
                            <abc-money :value="orderDetail.refundAmount"></abc-money>
                        </abc-descriptions-item>
                        <abc-descriptions-item label="订单状态">
                            {{ orderStatusStr }}
                        </abc-descriptions-item>
                        <abc-descriptions-item label="退款理由">
                            {{ orderDetail.reason }}
                        </abc-descriptions-item>
                        <abc-descriptions-item label="退款时间">
                            {{ orderDetail.extCreated | parseTime }}
                        </abc-descriptions-item>
                        <abc-descriptions-item label="隐私号码">
                            {{ privacyPhone }}
                        </abc-descriptions-item>
                        <abc-descriptions-item label="备用号码">
                            {{ backupPhone }}
                        </abc-descriptions-item>
                        <abc-descriptions-item label="订单号">
                            {{ orderDetail.orderNo }}
                        </abc-descriptions-item>
                        <abc-descriptions-item label="下单时间">
                            {{ orderDetail.orderExtCreated | parseTime }}
                        </abc-descriptions-item>
                        <abc-descriptions-item label="顾客电话">
                            {{ customerPhone }}
                        </abc-descriptions-item>
                        <abc-descriptions-item label="顾客地址">
                            {{ receiverAddress || '' }}
                        </abc-descriptions-item>
                    </abc-descriptions>
                </abc-section>
                <abc-section>
                    <abc-form ref="abcForm" is-excel>
                        <abc-table
                            :render-config="renderConfig"
                            :data-list="flatFormItems"
                            cell-size="xxxlarge"
                            :show-hover-tr-bg="false"
                            :fixed-tr-height="false"
                            type="excel"
                        >
                            <template #orderGoodsInfo="{ trData }">
                                <table-cell-order :item="trData"></table-cell-order>
                            </template>
                            <template #ABCGoods="{ trData }">
                                <table-cell-goods disabled :item="trData"></table-cell-goods>
                            </template>
                            <template #afterSaleCount="{ trData }">
                                <abc-table-cell style="height: 100%;">
                                    {{ trData.unitCount }} {{ trData.unit }}
                                </abc-table-cell>
                            </template>
                            <template #returnBatches="{ trData }">
                                <abc-table-cell v-if="trData.stockDealResultType === AfterSaleItemStockDealResultType.NOT_RETURN_IN_STOCK" style="height: 100%; padding: 0;" :title="selectedTraceCodeListTitle(trData)">
                                    <abc-text style="margin-left: 12px;">
                                        -
                                    </abc-text>
                                </abc-table-cell>
                                <table-cell-batches
                                    v-else
                                    :item="trData"
                                    :required-batch-infos="trData.stockDealResultType === AfterSaleItemStockDealResultType.RETURN_IN_STOCK"
                                    @click="handleSelectBatches"
                                >
                                </table-cell-batches>
                            </template>
                            <template #dealWay="{ trData }">
                                <abc-table-cell style="height: 100%; padding: 0;">
                                    <abc-select v-model="trData.stockDealResultType" adaptive-width @change="handleChangeDeal(trData)">
                                        <abc-option :value="AfterSaleItemStockDealResultType.RETURN_IN_STOCK" label="退入库存"></abc-option>
                                        <abc-option :value="AfterSaleItemStockDealResultType.NOT_RETURN_IN_STOCK" label="不退库存"></abc-option>
                                    </abc-select>
                                </abc-table-cell>
                            </template>
                            <template #remark="{ trData }">
                                <abc-table-cell style="height: 100%; padding: 0;">
                                    <abc-input
                                        v-model="trData.remark"
                                        v-abc-focus-selected
                                        :max-length="50"
                                        adaptive-width
                                    ></abc-input>
                                </abc-table-cell>
                            </template>
                            <template #traceCode="{ trData }">
                                <abc-table-cell style="height: 100%; padding: 0;" :title="selectedTraceCodeListTitle(trData)">
                                    <abc-select
                                        v-if="canSetTraceCode(trData)"
                                        v-model="trData.selectedTraceCodeList"
                                        multiple
                                        correct-multiple-clear-value
                                        multi-label-mode="text"
                                        with-search
                                        clearable
                                        adaptive-width
                                        :max-tag="1"
                                        size="small"
                                        placement="bottom-end"
                                        disabled-input-enter
                                        custom-class="table-cell-trace-code-select"
                                        :fetch-suggestions="(key) => handleSearch(key, trData)"
                                    >
                                        <abc-option
                                            v-for="(option, key) in trData._filterOptionsTraceableCodeList"
                                            :key="key"
                                            :value="option.keyId"
                                            :label="`${option.no } ×${ option.count}`"
                                        >
                                            <abc-flex align="center" justify="space-between" style="width: 300px; min-height: 26px;">
                                                <span>{{ option.no }}</span>
                                                <abc-input-number
                                                    v-if="trData.selectedTraceCodeList.includes(option.keyId)"
                                                    v-model="option.count"
                                                    v-abc-focus-selected
                                                    v-abc-auto-focus
                                                    size="tiny"
                                                    fixed-button
                                                    :config="{
                                                        supportZero: false,
                                                        max: 99999,
                                                    }"
                                                    button-placement="left"
                                                ></abc-input-number>
                                                <abc-text v-else style="margin-left: 8px;" theme="gray">
                                                    ×{{ option.count }}
                                                </abc-text>
                                            </abc-flex>
                                        </abc-option>
                                    </abc-select>
                                    <abc-text v-else style="margin-left: 12px;">
                                        -
                                    </abc-text>
                                </abc-table-cell>
                            </template>
                        </abc-table>
                    </abc-form>
                </abc-section>
            </abc-layout>

            <div slot="footer" class="dialog-footer">
                <div style="flex: 1;"></div>
                <abc-button
                    v-if="!disabled"
                    style="min-width: 82px;"
                    :loading="btnLoading"
                    @click="handleClickDeal"
                >
                    处理
                </abc-button>
                <abc-button
                    v-if="!disabled"
                    style="min-width: 82px;"
                    variant="ghost"
                    :loading="btnLoading"
                    @click="handleSaveDraft"
                >
                    保存草稿
                </abc-button>
                <abc-button variant="ghost" @click="handleClickClose">
                    关闭
                </abc-button>
            </div>
        </abc-dialog>
    </div>
</template>

<script type="text/babel">
    import TableCellOrder from '@/components/o2o-goods-out-table/table-cell-order.vue';
    import TableCellGoods from '@/components/o2o-goods-out-table/table-cell-goods.vue';
    import TableCellBatches from '@/components/o2o-goods-out-table/table-cell-batches.vue';
    import DialogBatchesSelectorRefund from '@/components/dialog-batches-selector-refund/index.js';

    import {
        OrderStatusTextEnum, OrderLogisticsStatusTextEnum,
    } from '@/daemon/crawler/provider/meituan/constants.js';
    import {
        ECTypeText,
        AfterSaleItemStockDealResultType,
        EcOrderAfterSaleStatus,
    } from '@/utils/constants.js';
    import { isEqual } from 'MfBase/lodash';
    import {
        createGUID,
        clone,
    } from '@/utils/index.js';
    import ECOrderAPI from '@/api/order';
    import { mapGetters } from 'vuex';
    export default {
        name: 'OrderAfterSaleDialog',
        components: {
            TableCellOrder,
            TableCellGoods,
            TableCellBatches,
        },
        props: {
            afterSaleId: {
                type: String,
                required: true,
            },
            stockDealStatus: {
                type: Number,
                default: 0,
            },
            onRefresh: {
                type: Function,
            },
            isValidMall: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                EcOrderAfterSaleStatus,
                AfterSaleItemStockDealResultType,
                visible: false,
                btnLoading: false,
                loading: false,

                contentLoading: false,
                showPrescriptionDialog: false,
                orderDetail: {

                },
                flatFormItems: [],
                curStockDealStatus: this.stockDealStatus,
            };
        },
        computed: {
            ...mapGetters([
                'isChainAdmin',
            ]),
            disabled() {
                return this.curStockDealStatus === EcOrderAfterSaleStatus.DEAL_DONE || this.isChainAdmin || !this.isValidMall;
            },

            renderConfig() {
                return {
                    hasInnerBorder: true,
                    list: [
                        {
                            key: 'orderGoodsInfo',
                            label: '退货商品',
                            style: {
                                flex: '1',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'ABCGoods',
                            label: '绑定的ABC系统商品',
                            style: {
                                flex: '1',
                            },
                        },
                        {
                            key: 'afterSaleCount',
                            label: '退货数量',
                            style: {
                                flex: 'none',
                                width: '80px',
                            },
                        },
                        {
                            key: 'dealWay',
                            label: '退货库存处理',
                            style: {
                                flex: 'none',
                                width: '120px',
                            },
                        },
                        {
                            key: 'returnBatches',
                            label: '批号',
                            style: {
                                flex: 'none',
                                width: '150px',
                            },
                        },
                        {
                            key: 'remark',
                            label: '处理备注',
                            style: {
                                flex: 'none',
                                width: '134px',
                            },
                        },
                        {
                            key: 'traceCode',
                            label: '追溯码',
                            style: {
                                width: '260px',
                                maxWidth: '260px',
                            },
                        },

                    ],
                };
            },

            receiverInfoStr() {
                const {
                    orderReceiverNameMask = '',
                    orderReceiverPhoneMask = '',
                } = this.orderDetail || {};
                return `${orderReceiverNameMask} ${orderReceiverPhoneMask}`;
            },
            receiverAddress() {
                const {
                    orderReceiverAddressMask = '',
                } = this.orderDetail || {};
                return `${orderReceiverAddressMask || ''}`;
            },
            ecTypeText() {
                return ECTypeText[this.orderDetail.ecType] || '';
            },

            orderAdditionalInfo() {
                return this.orderDetail.orderAdditionalInfo || {};
            },
            orderDaySeq() {
                return this.orderAdditionalInfo.orderDaySeq || '';
            },
            recipientPhones() {
                return this.orderAdditionalInfo.recipientPhones || [];
            },
            //隐私号码
            privacyPhone() {
                return this.recipientPhones.find((it) => it.name === '隐私号码')?.phoneShow || '';
            },
            //备用号码
            backupPhone() {
                return this.recipientPhones.find((it) => it.name === '备用号码')?.phoneShow || '';
            },
            //顾客电话
            customerPhone() {
                return this.recipientPhones.find((it) => it.name === '顾客电话')?.phoneShow || '';
            },
            orderStatusStr() {
                const {
                    orderStatus,
                    logisticsStatus,
                } = this.orderDetail;
                const orderStatusStr = OrderStatusTextEnum[orderStatus] || '';
                const logisticsStatusStr = OrderLogisticsStatusTextEnum[logisticsStatus] || '';
                return `${orderStatusStr}（${logisticsStatusStr}）`;
            },
        },
        watch: {
            visible(val) {
                if (!val) {
                    this.destroyElement();
                }
            },
            showPrescriptionDialog(val) {
                val ? this.stopBarcodeDetect() : this.startBarcodeDetect();
            },
        },
        created() {
            this.initHandler();
        },
        methods: {
            async initHandler() {
                await this.fetchOrderDetail();
            },
            canSetTraceCode(item) {
                return item._filterOptionsTraceableCodeList?.length > 0;
            },
            handleSearch(key, item) {
                key = key.trim();
                const _arr = [];
                item._optionsTraceableCodeList?.forEach((it) => {
                    // if (it.no?.indexOf(key) > -1 && it.used === 1) {
                    if (it.no?.indexOf(key) > -1) {
                        _arr.push(it);
                    }
                });
                item._filterOptionsTraceableCodeList = _arr.map((it) => {
                    it.keyId = it.keyId || createGUID();
                    it.count = it.count || 1;
                    return it;
                });
            },
            async fetchOrderDetail() {
                this.contentLoading = true;
                try {
                    const res = await ECOrderAPI.getAfterSale(this.afterSaleId);

                    console.log(res);

                    this.flatFormItems = [];
                    res.items.forEach((it) => {
                        const keyId = it.keyId || createGUID();

                        const stock = it.enableOutStockRecord || {};
                        const selectedStock = it.selectedStockRecord || {};

                        const productInfo = stock.hisGoodsInfo || selectedStock.hisGoodsInfo;
                        const stockDetail = stock.stockDetail || {};
                        const selectedStockDetail = selectedStock.stockDetail || {};

                        const dispensingItem = {
                            id: it.id,
                            keyId,
                            clinicId: it.clinicId,
                            extGoodsImg: it.extGoodsImg,
                            extGoodsName: it.extGoodsName,
                            extGoodsCount: it.extGoodsCount,
                            extGoodsSkuId: it.extGoodsSkuId,
                            ecGoodsId: it.ecGoodsId,
                            ecGoodsSkuId: it.ecGoodsSkuId,
                            ecGoodsSku: it.ecGoodsSku,
                            unitCount: it.hisReturnStockUnitCount,
                            unit: it.hisGoodsUnit,
                            extSkuId: it.extGoodsId,
                            productId: stock.hisGoodsId,
                            productInfo,
                            useDismounting: stock.useDismounting,
                            packageCount: stock.packageCount,
                            pieceCount: stock.pieceCount,
                            stockId: stock.id,
                            relatedRecordId: selectedStock?.relatedRecordId || stock.id,
                            stockDealResultType: it.stockDealResultType || AfterSaleItemStockDealResultType.RETURN_IN_STOCK,
                            remark: it.remark || '',
                            _optionsBatchInfos: stockDetail.batchInfos || [],
                            _optionsTraceableCodeList: (stockDetail.traceableCodeList || []).map((code) => ({
                                ...code,
                                keyId: code.keyId || createGUID(),
                                count: code.count || 1,
                            })),

                            selectedStockRecordId: selectedStock?.id,
                            traceableCodeList: selectedStockDetail?.traceableCodeList || [],
                            batchInfos: selectedStockDetail?.undispenseBatchInfos || [],

                        };
                        this.handleSearch('', dispensingItem);
                        dispensingItem.selectedTraceCodeList = dispensingItem._optionsTraceableCodeList.filter((code) => {
                            return dispensingItem.traceableCodeList.some((item) => item.no === code.no);
                        }).map((code) => code.keyId);

                        this.flatFormItems.push(dispensingItem);
                    });
                    this._flatFormItemsCache = clone(this.flatFormItems);
                    this.orderDetail = Object.assign({}, {
                        refundAmount: res.refundAmount,
                        reason: res.reason,
                        extCreated: res.extCreated,
                        orderNo: res.orderNo,
                        orderExtCreated: res.orderExtCreated,
                        receiverInfo: res.receiverInfo,
                        ecType: res.ecType,
                        orderAdditionalInfo: res.orderAdditionalInfo,
                        orderStatus: res.orderStatus,
                        orderTypeFlag: res.orderTypeFlag,
                        logisticsStatus: res.orderLogisticsStatus,
                        orderReceiverAddressMask: res.orderReceiverAddressMask,
                        orderReceiverNameMask: res.orderReceiverNameMask,
                        orderReceiverPhoneMask: res.orderReceiverPhoneMask,
                    });
                } catch (e) {
                    console.error(e);
                } finally {
                    this.contentLoading = false;
                }
            },

            getItemId(item) {
                return item.id || item.keyId;
            },

            selectedTraceCodeListTitle(item) {
                return this.getTraceableCodeList(item).map((it) => `${it.no} *${it.count}`).join('\n');
            },

            handleChangeDeal(item) {
                const {
                    stockDealResultType,
                } = item;
                if (stockDealResultType === AfterSaleItemStockDealResultType.NOT_RETURN_IN_STOCK) {
                    item.batchInfos = [];
                }
            },


            handleSelectBatches(item) {
                this._batcherDialog = new DialogBatchesSelectorRefund({
                    chargeItem: item,
                    onConfirm: (data) => {
                        item.batchInfos = data.batchInfos;
                    },
                });
                this._batcherDialog.generateDialog();
            },

            getTraceableCodeList(item) {
                const {
                    selectedTraceCodeList,
                    _optionsTraceableCodeList,
                } = item;
                const _arr = [];
                selectedTraceCodeList?.forEach((codeKeyId) => {
                    const res = _optionsTraceableCodeList?.find((it) => it.keyId === codeKeyId);
                    if (res) {
                        _arr.push(res);
                    }
                });
                return _arr;
            },

            getPostData() {
                return {
                    items: this.flatFormItems.map((item) => {
                        const {
                            id,
                            selectedStockRecordId,
                            batchInfos,
                            relatedRecordId,
                            stockDealResultType,
                            remark,
                        } = item;

                        return {
                            id,
                            stockDealResultType,
                            remark,
                            selectedStockRecord: stockDealResultType === AfterSaleItemStockDealResultType.RETURN_IN_STOCK ? {
                                id: selectedStockRecordId,
                                relatedRecordId,
                                stockDetail: {
                                    traceableCodeList: this.getTraceableCodeList(item),
                                    undispenseBatchInfos: batchInfos,
                                },
                            } : undefined,
                        };
                    }),
                };
            },

            async validateOrder() {
                return new Promise((resolve) => {
                    this.$refs.abcForm.validate((valid) => {
                        resolve(valid);
                    });
                });
            },

            async handleSaveDraft() {
                // if (!(await this.validateOrder())) return;
                this.btnLoading = true;
                try {
                    await ECOrderAPI.updateAfterSale(this.afterSaleId, {
                        operationType: 0,
                        ...this.getPostData(),
                    });
                    this.$Toast({
                        type: 'success',
                        message: '保存成功',
                    });
                    this.handleClose();
                } catch (e) {
                    console.error(e);
                } finally {
                    this.btnLoading = false;
                }
            },

            async handleClickDeal() {
                if (!(await this.validateOrder())) return;
                this.btnLoading = true;
                try {
                    await ECOrderAPI.updateAfterSale(this.afterSaleId, {
                        operationType: 1,
                        ...this.getPostData(),
                    });
                    this.$Toast({
                        type: 'success',
                        message: '处理成功',
                    });
                    this.onRefresh && this.onRefresh();
                    this.handleClose();
                } catch (e) {
                    console.error(e);
                } finally {
                    this.btnLoading = false;
                }
            },

            handleClickClose() {
                if (this.disabled) {
                    this.handleClose();
                    return;
                }
                if (isEqual(this.flatFormItems, this._flatFormItemsCache)) {
                    this.handleClose();
                } else {
                    this.$confirm({
                        title: '提示',
                        content: '是否需要保存为草稿',
                        confirmText: '保存',
                        cancelText: '不保存',
                        onConfirm: () => {
                            this.handleSaveDraft();
                        },
                        onCancel: () => {
                            this.handleClose();
                        },
                    });
                }
            },

            handleClose() {
                this.visible = false;
            },
            destroyElement() {
                this.$destroy();
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
        },
    };
</script>
<style lang="scss">
    .table-cell-trace-code-select {
        .abc-option-item > label {
            display: flex;
            align-items: center !important;
        }
    }
</style>

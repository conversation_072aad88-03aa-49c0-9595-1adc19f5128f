<template>
    <abc-popover
        ref="popover"
        v-model="showPopover"
        placement="bottom-end"
        trigger="manual"
        :visible-arrow="false"
        theme="yellow"
        :popper-style="{
            padding: 0
        }"
        style="height: 100%;"
    >
        <abc-table-cell
            slot="reference"
            class="ecommerce-table-cell"
            vertical
            align="flex-start"
            justify="center"
            style="padding-right: 8px;"
        >
            <div class="ecommerce-table-cell__row">
                <template v-if="hasMergedOrder">
                    已合并{{ mergedOrders.length }}个订单
                </template>
                <template v-else>
                    {{ orderInfo.orderNo }}
                </template>
            </div>
            <div class="ecommerce-table-cell__row">
                <abc-space :size="12">
                    <template v-if="canLock">
                        <abc-link
                            v-if="orderInfo.orderLock"
                            size="small"
                            @click="handleUnLock"
                        >
                            解锁
                        </abc-link>
                        <abc-link
                            v-else
                            size="small"
                            @click="handleLock"
                        >
                            锁定
                        </abc-link>
                    </template>

                    <abc-link v-if="hasMergedOrder" size="small" @click="showPopover = !showPopover">
                        合单详情
                    </abc-link>
                    <abc-link
                        v-else
                        size="small"
                        @click="copyOrderNo($event)"
                    >
                        复制订单号
                    </abc-link>

                    <abc-link v-if="deliveryList.length" size="small" @click="handleClickCancel">
                        回收快递单
                    </abc-link>
                </abc-space>
            </div>
        </abc-table-cell>

        <order-popover
            v-abc-click-outside="
                () => {
                    showPopover = false;
                }
            "
            :order-info="orderInfo"
            :disabled="disabled"
            :can-merge-order-list="canMergeOrderList"
            @refresh="$emit('refresh')"
            @handleClose="handleClose"
        ></order-popover>
    </abc-popover>
</template>

<script>
    import {
        ECOrderStatusEnum, MergeStatusEnum,
    } from '../utils/constants.js';
    import ECOrderAPI from '../api/order.js';
    import { copy } from '@abc/utils-dom';
    import OrderPopover from './table-cell-order-popover.vue';
    import DialogTraceSelect from './dialog-trace-select';

    export default {
        components: { OrderPopover },
        props: {
            orderInfo: {
                required: true,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                MergeStatusEnum,
                canMergeOrderList: [],
                btnLoading: false,
                showPopover: false,
            };
        },
        computed: {
            mergedOrders() {
                return this.orderInfo.detailOrders || [];
            },
            hasMergedOrder() {
                return this.orderInfo.mergeStatus === MergeStatusEnum.MERGE_ED && this.mergedOrders.length > 0;
            },

            canLock() {
                return !this.disabled && this.orderInfo.orderStatus === ECOrderStatusEnum.WAIT;
            },

            deliveryList() {
                const {
                    extraDeliveryList,
                    giftDeliveryList,
                    wpId,
                    wpName,
                    trackingNumber,
                } = this.orderInfo;
                let _arr = [];
                if (trackingNumber) {
                    _arr.push({
                        wpId,
                        wpName,
                        waybillCode: trackingNumber,
                    });
                }
                _arr = _arr.concat(extraDeliveryList || []);
                _arr = _arr.concat(giftDeliveryList || []);
                _arr = _arr.map((it) => {
                    it.waybillCode = it.waybillCode || it.trackingNumber;
                    return it;
                });
                return _arr;
            },

        },
        methods: {
            handleClose() {
                this.showPopover = false;
                this.$refs.popover.doClose();
            },
            copyOrderNo(event) {
                copy(`${this.orderInfo.orderNo}`);
                this.$Toast({
                    message: '复制成功',
                    type: 'info',
                    referenceEl: event.target,
                });
            },
            handleUnLock() {
                this.orderLockSubmit(0);
            },
            handleLock() {
                this.orderLockSubmit(1);
            },
            async orderLockSubmit(orderLock = 0) {
                await ECOrderAPI.orderLock({
                    orderIds: [this.orderInfo.id],
                    orderLock,
                });
                this.$Toast({
                    message: orderLock ? '锁定成功' : '解锁成功',
                    type: 'success',
                });
                this.$emit('refresh');
            },

            handleClickCancel() {
                if (this.deliveryList.length === 0) return;
                if (this.deliveryList.length === 1) {
                    this.handleCancelConfirm(this.deliveryList);
                } else {
                    new DialogTraceSelect({
                        orderId: this.orderInfo.id,
                        onConfirm: this.handleCancelConfirm,
                    }).generateDialogAsync();
                }
            },
            handleCancelConfirm(list, callback) {
                let content = [
                    `${list.length > 1 ? '所选' : ''}快递单号将回收作废，该订单将变为[未打印]状态。`,
                    '是否确认回收？',
                ];
                if (this.isShipped) {
                    content = [
                        `${list.length > 1 ? '所选' : ''}快递单号将回收作废，是否确认回收？`,
                    ];
                }
                this.$confirm({
                    type: 'warn',
                    title: '回收单号确认',
                    content,
                    onConfirm: () => {
                        this.cancelEcWaybill(list);
                        callback && callback();
                    },
                });
            },
            async cancelEcWaybill(waybillList) {
                await ECOrderAPI.cancelEcWaybill({
                    waybillList: waybillList.map((it) => {
                        return {
                            ...it,
                            ecType: this.orderInfo.ecType,
                        };
                    }),
                });
                this.$emit('refresh');
                this.handleClose();
                this.$Toast({
                    type: 'success',
                    message: '回收成功',
                });
            },
        },
    };
</script>

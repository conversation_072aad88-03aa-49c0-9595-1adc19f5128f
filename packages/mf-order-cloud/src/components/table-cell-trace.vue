<template>
    <abc-popover
        ref="popover"
        placement="bottom-start"
        trigger="click"
        :visible-arrow="false"
        theme="yellow"
        padding-size="large"
        style="height: 100%;"
        :popper-style="{
            paddingRight: 0
        }"
        size="large"
        @show="handleOpen"
    >
        <abc-table-cell
            slot="reference"
            class="ecommerce-table-cell"
            vertical
            align="flex-start"
            justify="center"
            clickable
        >
            <abc-flex v-if="showWarn" align="center" class="ecommerce-table-cell__row warning">
                <abc-icon icon="Attention"></abc-icon>
                <abc-text theme="warning-light">
                    未打印快递单
                </abc-text>
            </abc-flex>
            <template v-else-if="extraDeliveryList.length">
                <div class="ecommerce-table-cell__row">
                    {{ displayWpName || '' }}
                </div>
                <div class="ecommerce-table-cell__row">
                    等{{ extraDeliveryList.length + 1 }}个快递
                </div>
            </template>
            <template v-else>
                <div class="ecommerce-table-cell__row">
                    {{ orderInfo.wpName || '' }}
                </div>
                <div class="ecommerce-table-cell__row">
                    {{ orderInfo.trackingNumber || '' }}
                </div>
            </template>
        </abc-table-cell>

        <div id="table-cell-trace-popover" v-abc-loading="contentLoading">
            <h5>物流信息</h5>
            <div v-if="logisticsTraceList.length === 0" class="no-data-wrapper">
                <abc-content-empty :show-icon="false" value="未查询到物流信息"></abc-content-empty>
            </div>
            <div v-for="item in logisticsTraceList" :key="item.waybillCode" class="logistics-trace">
                <abc-flex style="width: 100%;" align="center" justify="space-between">
                    <abc-flex align="center" style="font-size: 12px;">
                        {{ item.wpName }} {{ item.waybillCode }}
                        <abc-link style="margin-left: 4px;" @click="copyOrderNo($event, item.waybillCode)">
                            <abc-icon slot="prepend" size="14" icon="s-b-copy-line"></abc-icon>
                        </abc-link>
                    </abc-flex>

                    <abc-button
                        v-if="!disabledCancel"
                        variant="ghost"
                        size="small"
                        @click="handleClickCancel(item)"
                    >
                        回收单号
                    </abc-button>
                </abc-flex>
                <abc-divider
                    class="cut-line"
                    theme="dark"
                    margin="mini"
                    variant="dashed"
                ></abc-divider>
                <abc-steps
                    :active="active"
                    direction="vertical"
                    active-color="#1EC761"
                    separator="line"
                >
                    <abc-step v-for="(trace, index) in item.traceList" :key="trace.action + index" :index="index">
                        {{ trace.desc }}
                        <span slot="text">{{ trace.statusTime | formatDate('YYYY-MM-DD HH:mm') }}</span>
                    </abc-step>
                </abc-steps>
            </div>
        </div>
    </abc-popover>
</template>

<script>
    import ECOrderAPI from '../api/order.js';
    import { copy } from '@abc/utils-dom';
    import { formatDate } from '@abc/utils-date';
    import { ECOrderStatusEnum } from '../utils/constants';

    export default {
        filters: {
            formatDate,
        },
        props: {
            orderInfo: {
                required: true,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                logisticsTraceList: [],
                contentLoading: false,
                active: 0,
            };
        },
        computed: {
            extraDeliveryList() {
                return this.orderInfo.extraDeliveryList || [];
            },
            displayWpName() {
                const _arr = [];
                this.extraDeliveryList.forEach((item) => {
                    if (!_arr.find((it) => it.wpId === item.wpId)) {
                        _arr.push(item);
                    }
                });

                return _arr.map((it) => it.wpName).join('/');
            },
            disabledCancel() {
                return this.disabled || this.orderInfo.waybillPrintStatus !== 1;
            },
            isShipped() {
                return this.orderInfo.orderStatus === ECOrderStatusEnum.SHIPPED;
            },

            // 已发货且未打印，需要展示提示
            showWarn() {
                return this.isShipped && !this.orderInfo.waybillPrintStatus;
            },
        },
        created() {
        },
        methods: {
            copyOrderNo(event, waybillCode) {
                copy(`${waybillCode}`);
                this.$Toast({
                    message: '复制成功',
                    type: 'info',
                    referenceEl: event.target,
                });
            },
            handleOpen() {
                this.fetchLogisticsTrace();
            },
            handleClose() {
                this.$refs.popover.doClose();
            },
            async fetchLogisticsTrace() {
                try {
                    this.contentLoading = true;
                    const res = await ECOrderAPI.fetchLogisticsTrace({
                        ecType: this.orderInfo.ecType,
                        orderId: this.orderInfo.id,
                    });
                    this.logisticsTraceList = res?.logisticsTraceList || [];
                } catch (e) {
                    console.error(e);
                } finally {
                    this.contentLoading = false;
                }
            },

            handleClickCancel(item) {
                let content = [
                    '快递单号将回收作废，该订单将变为[未打印]状态。',
                    '是否确认回收？',
                ];
                if (this.isShipped) {
                    content = [
                        '快递单号将回收作废，是否确认回收？',
                    ];
                }
                this.$confirm({
                    type: 'warn',
                    title: '回收单号确认',
                    content,
                    onConfirm: () => {
                        this.cancelEcWaybill(item);
                    },
                });
            },
            async cancelEcWaybill(item) {
                await ECOrderAPI.cancelEcWaybill({
                    waybillList: [
                        {
                            waybillCode: item.waybillCode,
                            ecType: this.orderInfo.ecType,
                            wpId: item.wpId,
                        },
                    ],
                });
                this.$emit('refresh');
                this.handleClose();
                this.$Toast({
                    type: 'success',
                    message: '回收成功',
                });
            },
        },
    };
</script>

<style lang="scss">
    @import '@/styles/mixin.scss';

    #table-cell-trace-popover {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        width: 332px;
        max-height: 300px;
        overflow-y: scroll;

        @include scrollBar();

        .no-data-wrapper {
            width: 100%;
            height: 70px;
        }

        .logistics-trace {
            width: 100%;
        }

        .receiver-info,
        .merge-order-info {
            width: 100%;

            .title {
                display: flex;
                margin-bottom: 8px;

                h5 {
                    font-size: 14px;
                    font-weight: bold;
                    line-height: 22px; /* 157.143% */
                    color: var(--abc-color-T1, #000000);
                }
            }

            .merged-order-item {
                margin-top: 8px;
                font-size: 14px;
                line-height: 22px; /* 157.143% */
                color: var(--abc-color-T1, #000000);
            }

            .order-count {
                min-width: 28px;
            }

            .danger {
                color: var(--abc-color-R6);
            }
        }

        .cut-line {
            margin: 10px 0;
        }

        .after-sale-info {
            width: 100%;

            > div {
                line-height: 22px;

                & + div {
                    margin-top: 2px;
                }
            }

            .content {
                margin-left: 12px;
                font-size: 14px;
                line-height: 22px; /* 157.143% */
                color: var(--abc-color-T1, #000000);
            }
        }

        .total-info {
            margin: 0 0 6px;
            font-size: 12px;
            line-height: 16px; /* 133.333% */
            color: var(--abc-color-T1, #000000);
        }
    }
</style>

@import "./styles/theme.scss";
@import "./styles/mixin.scss";

.ellipsis-2 {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    word-wrap: break-word;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}

.abc-pharmacy-order-cloud-container {
    position: relative;

    .content-card-wrapper {
        display: flex;
        flex: 1;
        flex-direction: column;
        height: 0;
        margin-top: 0;

        .content-card-tabs {
            margin: 0 12px;
            border-top: 0;
            border-top-left-radius: 0;
            border-top-right-radius: 0;

            .abc-tabs-v2-item:first-child {
                border-top-left-radius: 0;
            }
        }
    }

    .ecommerce-table-section {
        flex: 1;
        height: 0;
        overflow-x: auto;
    }

    .ecommerce-table {
        .ecommerce-table-cell {
            height: 100%;
            padding: 12px;
            line-height: 22px;
        }

        .ecommerce-table-cell__remark {
            width: 100%;

            span {
                margin-right: 4px;
            }

            @include ellipsis;
        }

        .warning {
            color: var(--abc-color-Y2);
        }

        .success {
            color: var(--abc-color-G2);
        }

        .danger {
            color: var(--abc-color-R6);
        }

        .ecommerce-table-cell__row {
            width: 100%;
            line-height: 22px;

            @include maxline(1);

            & + .ecommerce-table-cell__row {
                margin-top: 2px;
            }
        }
    }
}

'use strict';
const { merge } = require('webpack-merge');
const prodEnv = require('./prod.env');
/**当全局属性设置为host时
*需要在当前局域网下共享
*需要把localhost换成当前主机ip
*使用时需要设置环境变量
*@bash
 * $ export NODE_ENV=host
 * $ npm run dev
 * --------or---------
 * $ npm run dev:host
 **/
let host = 'localhost';
console.log(process.env.NODE_ENV);
if (process.env.NODE_ENV === 'host') {
    host = getIPAdress();
}

// 本地开发 Module
const devModules = process.env.DEV_MODULES && process.env.DEV_MODULES.split(',');
if (devModules && devModules.length) {
    console.info('以下模块启用本地调试：modules => ', devModules);
}

module.exports = merge(prodEnv, {
    NODE_ENV: '"development"',
    //替换主机名
    region: '"oss-cn-shanghai"',
    bucket: '"cis-images-dev"',
    root: '"headimg_dev"',
    // 本次构建信息
    buildInfo: JSON.stringify({
        BUILD_TIME: new Date().toLocaleString(),
        BUILD_ENV: process.env.BUILD_ENV,
        BUILD_TAG: process.env.BUILD_TAG,
    }),
    devModules: JSON.stringify(devModules),
    SCRM_URL: JSON.stringify('https://scrm.dev.abczs.cn'),
    LOGIN_URL: JSON.stringify('http://localhost:8080/login'),
    MFE_LOCAL: process.env.MFE_LOCAL,
    PRINT_DEV: process.env.PRINT_DEV,
    LIS_ENV: process.env.LIS_ENV || false,
    PUBLIC_HEALTH_ENV: process.env.PUBLIC_HEALTH_ENV || false,
    SOCIAL_ENV: process.env.SOCIAL_ENV || false,
    SOCIAL_PORT: process.env.SOCIAL_PORT || 8081, // 一般情况，社保开发时pc项目占用8080端口，social项目占用8081端口
    EDITOR: '"webstorm"',
});


function getIPAdress() {
    const interfaces = require('os').networkInterfaces();
    for (const devName in interfaces) {
        const iface = interfaces[devName];
        for (let i = 0;i < iface.length;i++) {
            const alias = iface[i];
            if (alias.family === 'IPv4' && alias.address !== '127.0.0.1' && !alias.internal) {
                return alias.address;
            }
        }
    }
}

const Tool = require('abc-fed-build-tool');
const useOSS = process.env.BUILD_ENV === 'gray' || process.env.BUILD_ENV === 'test';

const ossType = process.env.OSS_TYPE || 'ali';
let env = process.env.BUILD_ENV;

if (env === 'feature') {
    env = 'test';
} else if (env === 'own' || env === 'private') {
    env = 'dev';
}

let mallApiBase = '';
let oaApiBase = '';
let scrmUrl = '';
let loginUrl = '';
if (env === 'dev') {
    mallApiBase = 'mall.dev.abczs.cn';
    oaApiBase = 'dev-oa.abczs.cn';
    scrmUrl = 'https://scrm.dev.abczs.cn';
    loginUrl = 'https://dev.abczs.cn/login';
} else if (env === 'test') {
    mallApiBase = 'mall.test.abczs.cn';
    oaApiBase = 'test-oa.abczs.cn';
    scrmUrl = 'https://scrm.test.abczs.cn';
    loginUrl = 'https://test.abczs.cn/login';
} else if (env === 'prod' || env === 'gray' || env === 'pre') {
    mallApiBase = 'mall.abcyun.cn';
    oaApiBase = 'oa.abcyun.cn';
    scrmUrl = 'https://scrm.abcyun.cn';
    loginUrl = 'https://abcyun.cn/login';
}

const strategy = {
    'ali': function(env) {
        return Tool.OSS.getOSSInfo(env, '', useOSS);
    },
    'tencent': function(env) {
        return Tool.OSS.getCOSInfo(env, '', useOSS);
    },
};

const bucketInfo = strategy[ossType](env);

module.exports = {
    NODE_ENV: '"production"',
    BUILD_ENV: JSON.stringify(process.env.BUILD_ENV),
    MALL_API_BASE: JSON.stringify(mallApiBase),
    OA_API_BASE: JSON.stringify(oaApiBase),
    SCRM_URL: JSON.stringify(scrmUrl),
    LOGIN_URL: JSON.stringify(loginUrl),
    OSS_TYPE: JSON.stringify(ossType),
    DOMAIN: JSON.stringify(bucketInfo.url),
    // 本次构建信息
    buildInfo: JSON.stringify({
        BUILD_TIME: new Date().toLocaleString(),
        BUILD_ENV: process.env.BUILD_ENV,
        BUILD_TAG: process.env.BUILD_TAG,
    }),
};

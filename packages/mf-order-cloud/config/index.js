'use strict';
const Tool = require('abc-fed-build-tool');
const path = require('path');

const ossType = process.env.OSS_TYPE || 'ali';
const useOSS = process.env.BUILD_ENV === 'gray' || process.env.BUILD_ENV === 'test';
const prefix = 'mf-order-cloud';
const strategy = {
    'ali': function(env) {
        return Tool.OSS.getOSSInfo(env, prefix, useOSS);
    },
    'tencent': function(env) {
        return Tool.OSS.getCOSInfo(env, prefix, useOSS);
    },
};

const bucketInfo = strategy[ossType](process.env.BUILD_ENV || 'dev');

const FeEngineStrategy = {
    'ali': function(env) {
        env = env === 'own' ? 'dev' : env === 'feature' ? 'test' : env;
        return Tool.OSS.getOSSInfo(env, 'abc-fe-engine');
    },
    'tencent': function(env) {
        env = env === 'own' ? 'dev' : env === 'feature' ? 'test' : env;
        return Tool.OSS.getCOSInfo(env, 'abc-fe-engine');
    },
};

const MedicalImagingViewerSDKStrategy = {
    'ali': function(env) {
        env = env === 'own' ? 'dev' : env === 'feature' ? 'test' : env;
        return Tool.OSS.getOSSInfo(env, 'abc-medical-imaging-viewer-sdk');
    },
    'tencent': function(env) {
        env = env === 'own' ? 'dev' : env === 'feature' ? 'test' : env;
        return Tool.OSS.getCOSInfo(env, 'abc-medical-imaging-viewer-sdk');
    },
};

const EmrEditorSDKStrategy = {
    'ali': function(env) {
        env = env === 'own' ? 'dev' : env === 'feature' ? 'test' : env;
        return Tool.OSS.getOSSInfo(env, 'abc-emr-editor-sdk');
    },
    'tencent': function(env) {
        env = env === 'own' ? 'dev' : env === 'feature' ? 'test' : env;
        return Tool.OSS.getCOSInfo(env, 'abc-emr-editor-sdk');
    },
};

const abcFeEngineLoaderUrl = `${FeEngineStrategy[ossType](process.env.BUILD_ENV || 'dev').url}loader.js`;
const abcMedicalImagingViewerSDKLoader = `${MedicalImagingViewerSDKStrategy[ossType](process.env.BUILD_ENV || 'dev').url}loader.js`;
const abcEmrEditorSDKLoader = process.env.BUILD_ENV ? `${EmrEditorSDKStrategy[ossType](process.env.BUILD_ENV || 'dev').url}loader.js` : '//192.168.10.112:12222/loader.js';

module.exports = {
    // 本地调试
    devonline: {

        // Paths
        assetsSubDirectory: 'static',
        assetsPublicPath: '/',
        abcFeEngineLoaderUrl,
        abcMedicalImagingViewerSDKLoader,
        abcEmrEditorSDKLoader,
        proxyTable: [
            {
                context: ['/api/mall/'],
                target: 'http://mall.dev.abczs.cn',
                changeOrigin: true,
            },
            {
                context: '/api/v2/bpcrm',
                target: 'http://dev-oa.abczs.cn',
                changeOrigin: true,
            },
            {
                context: '/rpc/supervision',
                target: 'https://dev.abczs.cn',
                changeOrigin: true,
            },
            {
                context: '/api/v3/goods/stocks/in/order-draft/*/(parse-file-data|parse-mall-data)',
                target: 'http://region1-dev.abczs.cn',
                ws: true,
                changeOrigin: true,
                selfHandleResponse: true,
                onProxyRes(proxyRes, req, res) {
                    if (proxyRes.headers['set-cookie']) {
                        proxyRes.headers['set-cookie'] = proxyRes.headers['set-cookie'].map((item) => {
                            item = item.replace(/[Dd]omain=\.?abczs.cn/g, `domain=${req.headers.host.split(':')[0]}`);
                            return item;
                        });
                    }
                    proxyRes.on('data', (data) => {
                        res.write(data);
                    });

                    proxyRes.on('end', () => {
                        res.end();
                    });
                },
                bypass(req, res, proxyOptions) {
                    req.headers.origin = 'http://region1-dev.abczs.cn';
                },
            },
            {
                context: '/api/',
                target: 'http://region1-dev.abczs.cn',
                ws: true,
                changeOrigin: true,
                onProxyRes(proxyRes, req, res) {
                    if (proxyRes.headers['set-cookie']) {
                        proxyRes.headers['set-cookie'] = proxyRes.headers['set-cookie'].map((item) => {
                            item = item.replace(/[Dd]omain=\.?abczs.cn/g, `domain=${req.headers.host.split(':')[0]}`);
                            return item;
                        });
                    }
                },
                bypass(req, res, proxyOptions) {
                    req.headers.origin = 'http://region1-dev.abczs.cn';
                },
            },
            {
                context: '/socket.io/',
                target: 'http://region1-dev.abczs.cn',
                changeOrigin: true,
                ws: true,
            },
        ],

        // Various Dev Server settings
        host: 'localhost', // can be overwritten by process.env.HOST
        port: 8080, // can be overwritten by process.env.PORT, if port is in use, a free one will be determined
        autoOpenBrowser: true,
        errorOverlay: true,
        notifyOnErrors: true,
        poll: false, // https://webpack.js.org/configuration/dev-server/#devserver-watchoptions-

        /**
         * Source Maps
         */

        // https://webpack.js.org/configuration/devtool/#development
        devtool: 'eval-cheap-module-source-map',

        // If you have problems debugging vue-files in devtools,
        // set this to false - it *may* help
        // https://vue-loader.vuejs.org/en/options.html#cachebusting
        cacheBusting: true,

        // CSS Sourcemaps off by default because relative paths are "buggy"
        // with this option, according to the CSS-Loader README
        // (https://github.com/webpack/css-loader#sourcemaps)
        // In our experience, they generally work as expected,
        // just be aware of this issue when enabling this option.
        cssSourceMap: true,
    },

    // 测试环境/生产环境
    build: {
        // Template for index.html
        index: path.resolve(__dirname, '../dist/index.html'),

        // Paths
        assetsRoot: path.resolve(__dirname, '../dist'),
        assetsSubDirectory: 'static',
        assetsPublicPath: bucketInfo.url,
        abcFeEngineLoaderUrl,
        abcMedicalImagingViewerSDKLoader,
        abcEmrEditorSDKLoader,
        staticPath: '/', //生产环境 staticPath:''

        /**
         * Source Maps
         */
        productionSourceMap: false,
        // https://webpack.js.org/configuration/devtool/#production
        devtool: '#source-map',

        // Gzip off by default as many popular static hosts such as
        // Surge or Netlify already gzip all static assets for you.
        // Before setting to `true`, make sure to:
        // npm install --save-dev compression-webpack-plugin
        productionGzip: false,
        productionGzipExtensions: ['js', 'css'],

        // Run the build command with an extra argument to
        // View the bundle analyzer report after build finishes:
        // `npm run build --report`
        // Set to `true` or `false` to always turn it on or off
        bundleAnalyzerReport: process.env.npm_config_report,
        bucketInfo,
    },
};


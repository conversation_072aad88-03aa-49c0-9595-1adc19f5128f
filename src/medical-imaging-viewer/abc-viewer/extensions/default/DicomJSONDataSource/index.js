import OHIF, { DicomMetadataStore, IWebApiDataSource } from '@ohif-core/index.js';

import getImageId from '../DicomWebDataSource/utils/getImageId';

const metadataProvider = OHIF.classes.MetadataProvider;

const mappings = {
    studyInstanceUid: 'StudyInstanceUID',
    patientId: 'PatientID',
};

const _store = {
    urls: [],
    // {
    //   url: url1
    //   studies: [Study1, Study2], // if multiple studies
    // }
    // {
    //   url: url2
    //   studies: [Study1],
    // }
    // }
};

const getMetaDataByURL = (url) => _store.urls.find((metaData) => metaData.url === url);

const findStudies = (key, value) => {
    const studies = [];
    _store.urls.forEach((metaData) => {
        metaData.studies.forEach((aStudy) => {
            if (aStudy[key] === value) {
                studies.push(aStudy);
            }
        });
    });
    return studies;
};

function createDicomJSONApi(dicomJsonConfig) {
    const { name } = dicomJsonConfig;

    const implementation = {
        initialize: async ({ params, query, url }) => {
            if (!url) url = query.get('url');
            const metaData = getMetaDataByURL(url);

            // if we have already cached the data from this specific url
            // We are only handling one StudyInstanceUID to run; however,
            // all studies for patientID will be put in the correct tab
            if (metaData) {
                return metaData.studies.map((aStudy) => aStudy.StudyInstanceUID);
            }

            const response = await fetch(url);
            const data = await response.json();

            const studyInstanceUIDs = data.studies.map(
                (study) => study.StudyInstanceUID,
            );

            let StudyInstanceUID;
            let SeriesInstanceUID;
            data.studies.forEach((study) => {
                StudyInstanceUID = study.StudyInstanceUID;

                study.series.forEach((series) => {
                    SeriesInstanceUID = series.SeriesInstanceUID;

                    series.instances.forEach((instance) => {
                        const { url: imageId, metadata: naturalizedDicom } = instance;

                        // Add imageId specific mapping to this data as the URL isn't necessarliy WADO-URI.
                        metadataProvider.addImageIdToUIDs(imageId, {
                            StudyInstanceUID,
                            SeriesInstanceUID,
                            SOPInstanceUID: naturalizedDicom.SOPInstanceUID,
                        });
                    });
                });
            });

            _store.urls.push({
                url,
                studies: [...data.studies],
            });

            return studyInstanceUIDs;
        },
        query: {
            studies: {
                mapParams: () => {},
                search: async (param) => {
                    const [key, value] = Object.entries(param)[0];
                    const mappedParam = mappings[key];

                    // todo: should fetch from dicomMetadataStore
                    const studies = findStudies(mappedParam, value);

                    return studies.map((aStudy) => ({
                        accession: aStudy.AccessionNumber,
                        date: aStudy.StudyDate,
                        description: aStudy.StudyDescription,
                        instances: aStudy.NumInstances,
                        modalities: aStudy.Modalities,
                        mrn: aStudy.PatientID,
                        patientName: aStudy.PatientName,
                        studyInstanceUid: aStudy.StudyInstanceUID,
                        NumInstances: aStudy.NumInstances,
                        time: aStudy.StudyTime,
                    }));
                },
                processResults: () => {
                    console.debug(' DICOMJson QUERY processResults');
                },
            },
            series: {
                // mapParams: mapParams.bind(),
                search: () => {
                    console.debug(' DICOMJson QUERY SERIES SEARCH');
                },
            },
            instances: {
                search: () => {
                    console.debug(' DICOMJson QUERY instances SEARCH');
                },
            },
        },
        retrieve: {
            series: {
                metadata: ({
                    StudyInstanceUID,
                    madeInClient = false,
                    customSort,
                } = {}) => {
                    if (!StudyInstanceUID) {
                        throw new Error(
                            'Unable to query for SeriesMetadata without StudyInstanceUID',
                        );
                    }

                    const study = findStudies('StudyInstanceUID', StudyInstanceUID)[0];
                    let series;

                    if (customSort) {
                        series = customSort(study.series);
                    } else {
                        series = study.series;
                    }

                    const seriesSummaryMetadata = series.map((series) => {
                        const seriesSummary = {
                            StudyInstanceUID: study.StudyInstanceUID,
                            ...series,
                        };
                        delete seriesSummary.instances;
                        return seriesSummary;
                    });

                    // Async load series, store as retrieved
                    function storeInstances(naturalizedInstances) {
                        DicomMetadataStore.addInstances(naturalizedInstances, madeInClient);
                    }

                    DicomMetadataStore.addSeriesMetadata(
                        seriesSummaryMetadata,
                        madeInClient,
                    );

                    function setSuccessFlag() {
                        const study = DicomMetadataStore.getStudy(
                            StudyInstanceUID,
                            madeInClient,
                        );
                        study.isLoaded = true;
                    }

                    const numberOfSeries = series.length;
                    series.forEach((series, index) => {
                        const instances = series.instances.map((instance) => {
                            const obj = {
                                ...instance.metadata,
                                url: instance.url,
                                imageId: instance.url,
                                ...series,
                            };
                            delete obj.instances;
                            return obj;
                        });
                        storeInstances(instances);
                        if (index === numberOfSeries - 1) setSuccessFlag();
                    });
                },
            },
        },
        store: {
            dicom: () => {
                console.debug(' DICOMJson store dicom');
            },
        },
        getImageIdsForDisplaySet(displaySet) {
            const { images } = displaySet;
            const imageIds = [];

            if (!images) {
                return imageIds;
            }

            displaySet.images.forEach((instance) => {
                const { NumberOfFrames } = instance;

                if (NumberOfFrames > 1) {
                    for (let i = 0; i < NumberOfFrames; i++) {
                        const imageId = getImageId({
                            instance,
                            frame: i,
                            config: dicomJsonConfig,
                        });
                        imageIds.push(imageId);
                    }
                } else {
                    const imageId = getImageId({ instance, config: dicomJsonConfig });
                    imageIds.push(imageId);
                }
            });

            return imageIds;
        },
        getImageIdsForInstance({ instance, frame }) {
            const imageIds = getImageId({
                instance,
                frame,
            });
            return imageIds;
        },
    };
    return IWebApiDataSource.create(implementation);
}

export { createDicomJSONApi };

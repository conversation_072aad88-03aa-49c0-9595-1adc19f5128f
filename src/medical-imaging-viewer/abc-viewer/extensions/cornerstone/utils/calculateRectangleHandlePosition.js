/**
 * 根据触发位置及其坐标点计算 start 和 end 的坐标点
 * @param triggerPosition 触发位置
 * @param triggerPoint 触发位置的坐标点
 * @param handles
 */
function calculateStartEndPosition(triggerPosition, triggerPoint, handles) {
    const {
        start,
        end,
    } = handles;

    const calculateStartEnd = {
        topRight: ({x, y}) => {
            end.x = x;
            start.y = y;
        },
        bottomLeft: ({x, y}) => {
            end.y = y;
            start.x = x;
        },
        topCenter: ({y}) => {
            start.y = y;
        },
        bottomCenter: ({y}) => {
            end.y = y;
        },
        leftCenter: ({x}) => {
            start.x = x;
        },
        rightCenter: ({x}) => {
            end.x = x;
        }
    }
    calculateStartEnd[triggerPosition]?.(triggerPoint);
}

/**
 * 根据 start 和 end，计算其他坐标点
 * @param handles
 */
function calculateAllHandles(handles) {
    const {
        start,
        end,
        topRight,
        bottomLeft,
        topCenter,
        rightCenter,
        bottomCenter,
        leftCenter,
    } = handles;

    const {x: startX, y: startY} = start;
    const {x: endX, y: endY} = end;

    topRight.x = endX;
    topRight.y = startY;

    bottomLeft.x = startX;
    bottomLeft.y = endY;

    topCenter.x = (startX + endX) / 2;
    topCenter.y = startY;

    bottomCenter.x = topCenter.x;
    bottomCenter.y = endY;

    leftCenter.x = startX;
    leftCenter.y = (startY + endY) / 2;

    rightCenter.x = endX;
    rightCenter.y = leftCenter.y;
}

/**
 * 根据触发位置及其坐标点计算矩形所有控制点
 * @param triggerPosition 触发位置
 * @param triggerPoint 触发位置的坐标点
 * @param handles
 */
export function calculateRectangleHandlePosition(triggerPosition, triggerPoint, handles) {
    calculateStartEndPosition(triggerPosition, triggerPoint, handles);
    calculateAllHandles(handles);
}

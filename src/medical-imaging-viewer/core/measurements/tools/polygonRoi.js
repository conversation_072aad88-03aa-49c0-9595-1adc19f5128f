const displayFunction = data => {
    let meanValue = '';
    const { cachedStats } = data;
    if (cachedStats && cachedStats.mean && !Number.isNaN(cachedStats.mean)) {
        meanValue = cachedStats.mean.toFixed(2) + ' HU';
    }
    return meanValue;
};

export const polygonRoi = {
    id: 'PolygonRoi',
    name: 'Polygon',
    toolGroup: 'allTools',
    cornerstoneToolType: 'FreehandRoiTool',
    options: {
        measurementTable: {
            displayFunction,
        },
        caseProgress: {
            include: true,
            evaluate: true,
        },
    },
};

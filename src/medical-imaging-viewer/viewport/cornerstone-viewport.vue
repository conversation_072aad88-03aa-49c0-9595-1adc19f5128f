<template>
    <div
        :style="customStyle"
        :class="['viewport-wrapper', className ? className : '']"
    >
        <div
            ref="elementRef"
            class="viewport-element"
            @mousedown.prevent
            @contextmenu.prevent
        >
            <!--This classname is important in that it tells `cornerstone` to not create a new canvas element when we "enable" the `viewport-element`-->
            <canvas ref="viewportCanvas" class="cornerstone-canvas" :style="canvasStyle"></canvas>
        </div>
        <slot name="overlay" v-bind="state"></slot>
    </div>
</template>
<script>
    import cornerstone from '@/medical-imaging-viewer/cornerstone-core/src/index';
    import cornerstoneTools from '@abc/cornerstone-tools';
    import debounce from 'lodash.debounce';
    import areStringArraysEqual from '../utils/areStringArraysEqual.js';

    const scrollToIndex = cornerstoneTools.importInternal('util/scrollToIndex');
    const { loadHandlerManager } = cornerstoneTools;

    const addToBeginning = true;
    const priority = -5;
    const requestType = 'interaction';


    const AVAILABLE_TOOL_MODES = ['active', 'passive', 'enabled', 'disabled'];

    const TOOL_MODE_FUNCTIONS = {
        active: cornerstoneTools.setToolActiveForElement,
        passive: cornerstoneTools.setToolPassiveForElement,
        enabled: cornerstoneTools.setToolEnabledForElement,
        disabled: cornerstoneTools.setToolDisabledForElement,
    };


    /**
     * Iterate over the provided tools; Add each tool to the target element
     *
     * @param {string[]|object[]} tools
     * @param {HTMLElement} element
     */
    function _addAndConfigureInitialToolsForElement(tools, element) {
        for (let i = 0; i < tools.length; i++) {
            const tool = typeof tools[i] === 'string' ?
                { name: tools[i] } :
                ({ ...tools[i] });
            const toolName = `${tool.name}Tool`; // Top level CornerstoneTools follow this pattern

            tool.toolClass = tool.toolClass || cornerstoneTools[toolName];

            if (!tool.toolClass) {
                console.warn(`Unable to add tool with name '${tool.name}'.`);
                continue;
            }

            cornerstoneTools.addToolForElement(
                element,
                tool.toolClass,
                tool.props || {},
            );

            const hasInitialMode = tool.mode && AVAILABLE_TOOL_MODES.includes(tool.mode);

            if (hasInitialMode) {
                // TODO: We may need to check `tool.props` and the tool class's prototype
                // to determine the name it registered with cornerstone. `tool.name` is not
                // reliable.
                const setToolModeFn = TOOL_MODE_FUNCTIONS[tool.mode];
                setToolModeFn(element, tool.name, tool.modeOptions || {});
            }
        }
    }

    export default {
        props: {
            imageIds: {
                type: Array,
                required: true,
                default: () => ['no-id://'],
            },
            imageIdIndex: {
                type: Number,
                default: 0,
            },
            activeTool: {
                type: String,
            },
            tools: {
                type: Array, // {name: string, toolClass: function, props: Object, mode: string, modeOptions: Object}
                default: () => [],
            },
            // cornerstone.enable options
            cornerstoneOptions: {
                type: Object,
                default: () => {},
            },
            // should prefetch?
            isStackPrefetchEnabled: {
                type: Boolean,
                default: false,
            },
            // CINE
            isPlaying: {
                type: Boolean,
                default: false,
            },
            frameRate: {
                type: Number, // Between 1 and ?
                default: 24,
            },
            initialViewport: {
                type: Object,
                default: () => {},
            },
            setViewportActive: {
                type: Function,
            },
            onNewImage: {
                type: Function,
            },
            onNewImageDebounced: {
                type: Function,
            },
            onNewImageDebounceTime: {
                type: Number,
                default: 0,
            },
            // Cornerstone Events
            onElementEnabled: {
                type: Function,
            },
            eventListeners: {
                type: Array, // {target: {element, cornerstone}, eventName: string, handler: Function},
            },
            startLoadHandler: {
                type: Function,
            },
            endLoadHandler: {
                type: Function,
            },
            loadIndicatorDelay: {
                type: Number,
                default: 45,
            },
            /** false to enable automatic viewport resizing */
            enableResizeDetector: {
                type: Boolean,
                default: true,
            },
            /** rate at witch to apply resize mode's logic */
            resizeRefreshRateMs: {
                type: Number,
                default: 200,
            },
            /** whether resize refresh behavior is exhibited as throttle or debounce */
            resizeRefreshMode: {
                type: String, // 'throttle' | 'debounce'
                default: 'debounce',
            },
            customStyle: {
                type: Object,
            },
            className: {
                type: String,
            },
            isOverlayVisible: {
                type: Boolean,
                default: true,
            },
            orientationMarkers: {
                type: Array,
                default: () => ['top', 'left'],
            },
        },

        data() {
            return {
                state: {
                    imageId: this.imageIds[this.imageIdIndex],
                    imageIdIndex: this.imageIdIndex,
                    imageProgress: 0,
                    isLoading: true,
                    error: null,
                    // Overlay
                    scale: undefined,
                    windowWidth: undefined,
                    windowCenter: undefined,
                    isOverlayVisible: this.isOverlayVisible,
                    // Orientation Markers
                    rotationDegrees: undefined,
                    isFlippedVertically: undefined,
                    isFlippedHorizontally: undefined,
                    isImageLoaded: false,
                },
                canvasAnimated: false,
            };
        },

        computed: {
            watchCompose() {
                return {
                    imageIds: this.imageIds,
                    imageIdIndex: this.imageIdIndex,
                    isStackPrefetchEnabled: this.isStackPrefetchEnabled,
                    activeTool: this.activeTool,
                    frameRate: this.frameRate,
                    isPlaying: this.isPlaying,
                    isOverlayVisible: this.isOverlayVisible,
                };
            },
            canvasStyle() {
                // 做过一次动画就取消动画
                if (this.canvasAnimated) {
                    return {};
                }
                return {
                    transition: '0.2s',
                    transform: `scale(${this.state.isImageLoaded ? 1 : 0})`,
                };
            },
        },

        watch: {
            watchCompose: {
                handler(newVal, oldVal) {
                    const {
                        imageIds: stack,
                        imageIdIndex: imageIndex,
                        isStackPrefetchEnabled,
                        activeTool,
                        frameRate,
                        isPlaying,
                    } = newVal;
                    const {
                        imageIds: prevStack,
                        imageIdIndex: prevImageIndex,
                        isStackPrefetchEnabled: prevIsStackPrefetchEnabled,
                        activeTool: prevActiveTool,
                        frameRate: prevFrameRate,
                        isPlaying: prevIsPlaying,
                    } = oldVal;

                    const element = this.$refs.elementRef;
                    if (!element) {
                        return;
                    }
                    const hasStackChanged = !areStringArraysEqual(prevStack, stack);
                    const hasImageIndexChanged = imageIndex != null && imageIndex !== prevImageIndex;

                    if (hasStackChanged) {
                        cornerstoneTools.clearToolState(element, 'stack');
                        cornerstoneTools.addToolState(element, 'stack', {
                            imageIds: [...stack],
                            currentImageIdIndex: imageIndex || 0,
                        });
                        try {
                            const imageId = stack[imageIndex || 0];
                            this.state.imageId = imageId;
                            cornerstoneTools.stopClip(element);

                            const requestFn = (imageId, options) => cornerstone
                                .loadAndCacheImage(imageId, options)
                                .then((image) => {
                                    cornerstone.displayImage(element, image, this.initialViewport);
                                    cornerstone.reset(element);
                                });
                            cornerstone.imageLoadPoolManager.addRequest(
                                () => requestFn(imageId, { addToBeginning, priority }),
                                requestType,
                                {
                                    imageId,
                                },
                                priority,
                                addToBeginning,
                            );
                        } catch (error) {

                        }
                    } else if (!hasStackChanged && hasImageIndexChanged) {
                        scrollToIndex(element, imageIndex);
                    }

                    const shouldStopStartStackPrefetch = (isStackPrefetchEnabled && hasStackChanged) ||
                        (!prevIsStackPrefetchEnabled && isStackPrefetchEnabled === true);
                    // Need to stop/start to pickup stack changes in prefetcher
                    if (shouldStopStartStackPrefetch) {
                        cornerstoneTools.stackPrefetch.enable(element);
                    }

                    const hasActiveToolChanges = activeTool !== prevActiveTool;
                    if (hasActiveToolChanges) {
                        this._trySetActiveTool(element, activeTool);
                    }

                    const validFrameRate = Math.max(frameRate, 1);
                    const shouldStart = (isPlaying !== prevIsPlaying && isPlaying) ||
                        (isPlaying && hasStackChanged);
                    const shouldPause = isPlaying !== prevIsPlaying && !isPlaying;
                    const hasFrameRateChanged = isPlaying && frameRate !== prevFrameRate;

                    if (shouldStart || hasFrameRateChanged) {
                        cornerstoneTools.playClip(element, validFrameRate);
                    } else if (shouldPause) {
                        cornerstoneTools.stopClip(element);
                    }

                },
                deep: true,
            },
        },

        mounted() {
            this._onNewImageDebounced = debounce((event) => {
                this.onNewImageHandler(event, this.onNewImageDebounced);
            }, this.onNewImageDebounceTime);


            this._resizeDebounced = debounce(this.resize, 16);
            this.resizeObserver = new ResizeObserver(() => {
                this._resizeDebounced();
            });

            const canvasTransitionListener = () => {
                this.canvasAnimated = true;
            };
            this.$refs.viewportCanvas.addEventListener('transitionend', canvasTransitionListener);

            this.resizeObserver.observe(this.$el);

            this.$on('hook:beforeDestroy', () => {
                this.$refs.viewportCanvas.removeEventListener('transitionend', canvasTransitionListener);
                this.resizeObserver.disconnect();
            });

            const { imageIds } = this;
            const { imageId, imageIdIndex } = this.state;

            // Cornerstone event listeners
            this._handleOnElementEnabledEvent();
            this._bindInternalCornerstoneEventListeners();
            this._bindExternalEventListeners();

            const element = this.$refs.elementRef;
            cornerstone.enable(element, this.cornerstoneOptions);

            // Element event listeners
            this._bindInternalElementEventListeners();
            this._bindExternalEventListeners('element');

            // Only after `uuid` is set for enabledElement
            this._setupLoadHandlers();
            try {
                // Setup "Stack State"
                cornerstoneTools.clearToolState(element, 'stack');
                cornerstoneTools.addStackStateManager(element, [
                    'stack',
                    'playClip',
                    'referenceLines',
                ]);
                cornerstoneTools.addToolState(element, 'stack', {
                    imageIds: [...imageIds],
                    currentImageIdIndex: imageIdIndex,
                });

                // Load first image in stack
                const options = {
                    addToBeginning,
                    priority,
                };
                const requestFn = (imageId, options) => cornerstone.loadAndCacheImage(imageId, options).then((image) => {
                    cornerstone.displayImage(element, image, this.initialViewport);
                });

                // 1. Load the image using the ImageLoadingPool
                cornerstone.imageLoadPoolManager.addRequest(
                    requestFn(imageId, options),
                    requestType,
                    {
                        imageId,
                    },
                    priority,
                    addToBeginning,
                );

                if (this.isStackPrefetchEnabled) {
                    cornerstoneTools.stackPrefetch.enable(element);
                }

                if (this.isPlaying) {
                    const validFrameRate = Math.max(this.frameRate, 1);
                    cornerstoneTools.playClip(element, validFrameRate);
                }

                _addAndConfigureInitialToolsForElement(this.tools, element);
                this._trySetActiveTool(element, this.activeTool);
                this.state.isLoading = false;
            } catch (error) {
                this.state.error = error;
                this.state.isLoading = false;
            }
        },

        beforeDestroy() {
            const element = this.$refs.elementRef;
            const clear = true;
            this._handleOnElementEnabledEvent(clear);
            this._bindInternalCornerstoneEventListeners(clear);
            this._bindInternalElementEventListeners(clear);
            this._bindExternalEventListeners('cornerstone', clear);
            this._bindExternalEventListeners('element', clear);
            this._setupLoadHandlers(clear);

            if (this.isStackPrefetchEnabled) {
                cornerstoneTools.stackPrefetch.disable(element);
            }
            cornerstoneTools.clearToolState(element, 'stackPrefetch');
            cornerstoneTools.stopClip(element);
            cornerstone.disable(element);
        },

        methods: {
            resize() {
                const element = this.$refs.elementRef;
                cornerstone.resize(element);
                cornerstone.reset(element);
            },
            _trySetActiveTool(element, activeToolName) {
                if (!element || !activeToolName) {
                    return;
                }

                const validTools = cornerstoneTools.store.state.tools.filter(
                    (tool) => tool.element === element,
                );
                const validToolNames = validTools.map((tool) => tool.name);

                if (!validToolNames.includes(activeToolName)) {
                    console.warn(
                        `Trying to set a tool active that is not "added". Available tools include: ${validToolNames.join(
                            ', ',
                        )}`,
                    );
                }

                cornerstoneTools.setToolActiveForElement(element, activeToolName, {
                    mouseButtonMask: 1,
                });
            },


            _handleOnElementEnabledEvent(clear = false) {
                const element = this.$refs.elementRef;
                const handler = (evt) => {
                    const enabledElement = evt.detail.element;
                    if (enabledElement === element) {
                        this.onElementEnabled(evt);
                    }
                };
                if (this.onElementEnabled && !clear) {
                    cornerstone.events.addEventListener(cornerstone.EVENTS.ELEMENT_ENABLED, handler);
                }
                if (clear) {
                    cornerstone.events.removeEventListener(cornerstone.EVENTS.ELEMENT_ENABLED, handler);
                }
            },
            _bindInternalCornerstoneEventListeners(clear = false) {
                const addOrRemoveEventListener = clear ?
                    'removeEventListener' :
                    'addEventListener';

                // Update image load progress
                cornerstone.events[addOrRemoveEventListener](
                    cornerstone.EVENTS.IMAGE_LOAD_PROGRESS,
                    this.onImageProgress,
                );

                // Update number of images loaded
                cornerstone.events[addOrRemoveEventListener](
                    cornerstone.EVENTS.IMAGE_LOADED,
                    this.onImageLoaded,
                );
            },
            onImageLoaded() {
                // console.log('onImageLoaded');
            },
            onImageProgress(e) {
                this.state.imageProgress = e.detail.percentComplete;
            },
            _bindExternalEventListeners(targetType, clear = false) {
                const element = this.$refs.elementRef;

                const addOrRemoveEventListener = clear ?
                    'removeEventListener' :
                    'addEventListener';

                // Unique list of event names
                const cornerstoneEvents = Object.values(cornerstone.EVENTS);
                const cornerstoneToolsEvents = Object.values(cornerstoneTools.EVENTS);
                const csEventNames = cornerstoneEvents.concat(cornerstoneToolsEvents);

                const targetElementOrCornerstone = targetType === 'element' ? element : cornerstone.events;

                // Bind our single handler to every cornerstone event
                for (let i = 0; i < csEventNames.length; i++) {
                    targetElementOrCornerstone[addOrRemoveEventListener](
                        csEventNames[i],
                        this._handleExternalEventListeners,
                    );
                }
            },
            _handleExternalEventListeners(event) {
                if (!this.eventListeners) {
                    return;
                }

                for (let i = 0; i < this.eventListeners.length; i++) {
                    const { eventName, handler } = this.eventListeners[i];

                    if (event.type === eventName) {
                        handler(event);
                    }
                }
            },
            _bindInternalElementEventListeners(clear = false) {
                const element = this.$refs.elementRef;
                const addOrRemoveEventListener = clear ?
                    'removeEventListener' :
                    'addEventListener';

                // Updates state's imageId, and imageIndex
                element[addOrRemoveEventListener](
                    cornerstone.EVENTS.NEW_IMAGE,
                    this._onNewImage,
                );

                // Updates state's imageId, and imageIndex
                element[addOrRemoveEventListener](
                    cornerstone.EVENTS.NEW_IMAGE,
                    this._onNewImageDebounced,
                );

                // Updates state's viewport
                element[addOrRemoveEventListener](
                    cornerstone.EVENTS.IMAGE_RENDERED,
                    this.onImageRendered,
                );

                // Set Viewport Active
                element[addOrRemoveEventListener](
                    cornerstoneTools.EVENTS.MOUSE_CLICK,
                    this._setViewportActive,
                );
                element[addOrRemoveEventListener](
                    cornerstoneTools.EVENTS.MOUSE_DOWN,
                    this._setViewportActive,
                );
                element[addOrRemoveEventListener](
                    cornerstoneTools.EVENTS.TOUCH_PRESS,
                    this._setViewportActive,
                );
                element[addOrRemoveEventListener](
                    cornerstoneTools.EVENTS.TOUCH_START,
                    this._setViewportActive,
                );
                element[addOrRemoveEventListener](
                    cornerstoneTools.EVENTS.STACK_SCROLL,
                    this._setViewportActive,
                );
            },

            onNewImageHandler(event, callback) {
                this.state.isImageLoaded = true;
                const { image } = event.detail;
                const { imageId } = image;
                const { sopInstanceUid } = cornerstone.metaData.get('generalImageModule', imageId) || {};
                const currentImageIdIndex = this.imageIds.indexOf(imageId);

                // TODO: Should we grab and set some imageId specific metadata here?
                // Could prevent cornerstone dependencies in child components.
                this.state.imageIdIndex = currentImageIdIndex;

                if (callback) {
                    callback({ currentImageIdIndex, sopInstanceUid });
                }
            },

            _onNewImage(event) {
                this.onNewImageHandler(event, this.onNewImage);
            },

            // TODO: May need to throttle?
            onImageRendered (event) {
                const { viewport } = event.detail;
                this.state.scale = viewport.scale;
                this.state.windowCenter = viewport.voi.windowCenter;
                this.state.windowWidth = viewport.voi.windowWidth;
                this.state.rotationDegrees = viewport.rotation;
                this.state.isFlippedVertically = viewport.vflip;
                this.state.isFlippedHorizontally = viewport.hflip;
            },
            _setViewportActive() {
                if (this.setViewportActive) {
                    this.setViewportActive(); // TODO: should take viewport index/ident?
                }
            },
            _setupLoadHandlers(clear = false) {
                const element = this.$refs.elementRef;
                if (clear) {
                    loadHandlerManager.removeHandlers(element);
                    return;
                }

                // We use this to "flip" `isLoading` to true, if our startLoading request
                // takes longer than our "loadIndicatorDelay"
                const startLoadHandler = (element) => {
                    clearTimeout(this.loadHandlerTimeout);

                    // Call user defined loadHandler
                    if (this.startLoadHandler) {
                        this.startLoadHandler(element);
                    }

                    // We're taking too long. Indicate that we're "Loading".
                    this.loadHandlerTimeout = setTimeout(() => {
                        this.state.isLoading = true;
                    }, this.loadIndicatorDelay);
                };

                const endLoadHandler = (element, image) => {
                    clearTimeout(this.loadHandlerTimeout);

                    // Call user defined loadHandler
                    if (this.endLoadHandler) {
                        this.endLoadHandler(element, image);
                    }

                    if (this.state.isLoading) {
                        this.state.isLoading = false;
                    }
                };

                loadHandlerManager.setStartLoadHandler(startLoadHandler, element);
                loadHandlerManager.setEndLoadHandler(endLoadHandler, element);
            },
        },
    };
</script>

import Vue from 'vue';
import { studyCache } from '@/medical-imaging-viewer/store/study-cache';

export const studyStore = Vue.observable({
    studyInstanceUIDs: '',
    index: 0,
    isLoading: false,
    studies: [],
});

async function initByPatientId(patientId) {
    studyStore.studies = await studyCache.getStudiesByPatientId(patientId);
    return studyStore.studies;
}

async function initByOutpatientId(outpatientId) {
    studyStore.studies = await studyCache.getStudiesByOutpatientId(outpatientId);
    return studyStore.studies;
}

function getStudy(StudyInstanceUID) {
    return studyStore.studies.find((study) => study.StudyInstanceUID === StudyInstanceUID);
}

function setStudyInstanceUIDs(uids) {
    studyStore.studyInstanceUIDs = uids;
}

function setIndex(index) {
    studyStore.index = index;
}

function setStudyLoading(isLoading) {
    studyStore.isLoading = isLoading;
}

function getMeasurementDataList() {
    let measurements = [];
    studyStore.studies.forEach((study) => {
        study.series.forEach((series) => {
            series.instances.forEach((instance) => {
                if (instance.labelData) {
                    measurements = measurements.concat(instance.labelData);
                }
            });
        });
    });
    return measurements;
}

function reset() {
    studyStore.studyInstanceUIDs = '';
    studyStore.index = 0;
    studyStore.isLoading = false;
}

export const studyService = {
    initByPatientId,
    initByOutpatientId,
    getStudy,
    setStudyInstanceUIDs,
    setIndex,
    setStudyLoading,
    getMeasurementDataList,
    reset,
};

import Index from '../index.vue';
import { MODULE_ID_MAP } from 'utils/constants';

// 避免提前打包 以下路由异步加载
const PageAsync = () => import('./page.js');

const Main = () => import('../frames/main.vue');

// 外部跳转都应该用该字段
export const HospitalInventoryStockRouterNameKeys = {
    index: '@HospitalInventoryStock',
    main: '@HospitalInventoryStockMain',
};

export default {
    path: 'inventory-stock',
    name: HospitalInventoryStockRouterNameKeys.index,
    component: Index,
    meta: {
        name: '库存',
        needAuth: true,
        pageAsyncClass: PageAsync,
        moduleId: MODULE_ID_MAP.goods,
    },
    children: [
        {
            path: '',
            component: Main,
            name: HospitalInventoryStockRouterNameKeys.main,
            meta: {
                name: '库存',
                needAuth: true,
                moduleId: MODULE_ID_MAP.goods,
            },
        },
    ],
};

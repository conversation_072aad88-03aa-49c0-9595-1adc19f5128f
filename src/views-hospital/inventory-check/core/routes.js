import Index from '../index.vue';
import { MODULE_ID_MAP } from 'utils/constants';

// 避免提前打包 以下路由异步加载
const PageAsync = () => import('./page.js');

const Main = () => import('../frames/main.vue');

// 外部跳转都应该用该字段
export const HospitalInventoryCheckRouterNameKeys = {
    index: '@HospitalInventoryCheck',
    main: '@HospitalInventoryCheckMain',
};

export default {
    path: 'inventory-check',
    name: HospitalInventoryCheckRouterNameKeys.index,
    component: Index,
    meta: {
        name: '盘点',
        needAuth: true,
        pageAsyncClass: PageAsync,
        moduleId: MODULE_ID_MAP.goodsCheck,
        visible(store) {
            return store.getters.currentPharmacyModulePermission.showCheck;
        },
    },
    children: [
        {
            path: '',
            component: Main,
            name: HospitalInventoryCheckRouterNameKeys.main,
            meta: {
                name: '盘点',
                needAuth: true,
                moduleId: MODULE_ID_MAP.goodsCheck,
                visible(store) {
                    return store.getters.currentPharmacyModulePermission.showCheck;
                },
            },
        },
    ],
};

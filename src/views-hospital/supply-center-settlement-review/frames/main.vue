<template>
    <hospital-inventory-page-container v-abc-loading:page="loading">
        <goods-settlement is-settlement-review></goods-settlement>
    </hospital-inventory-page-container>
</template>

<script type="text/ecmascript-6">
    import GoodsSettlement from 'views/inventory/goods-settlement.vue';
    import HospitalInventoryPageContainer from '@/views-hospital/components/inventory-page-container/index.vue';
    export default {
        name: 'HospitalPurchaseSettlementReviewMain',
        components: {
            HospitalInventoryPageContainer,
            GoodsSettlement,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        data() {
            return {
                loading: false,
            };
        },
    };
</script>



<template>
    <biz-setting-layout class="inspect-reg-set-wrapper">
        <biz-setting-content>
            <biz-setting-form :label-width="98">
                <biz-setting-form-group
                    title="预约模式"
                >
                    <biz-setting-form-item
                        label="预约检查时间段"
                    >
                        <abc-radio-group v-model="postData.serviceType">
                            <abc-radio :label="0" data-type="label-align">
                                按上午、下午、晚上预约
                            </abc-radio>
                        </abc-radio-group>
                    </biz-setting-form-item>
                </biz-setting-form-group>

                <biz-setting-form-group
                    title="放号设置"
                >
                    <biz-setting-form-item
                        label="放号时间"
                        label-line-height-size="medium"
                    >
                        <abc-space data-type="label-align">
                            <span>提前</span>

                            <abc-select
                                v-model="advanceTime"
                                :width="80"
                                :max-height="350"
                            >
                                <abc-option
                                    v-for="item in daysOptions"
                                    :key="item.value"
                                    :value="item.value"
                                    :label="item.label"
                                ></abc-option>
                            </abc-select>

                            <span>放号</span>
                        </abc-space>
                    </biz-setting-form-item>

                    <biz-setting-form-item
                        label="号源过期时间"
                    >
                        <biz-setting-form-item-indent>
                            <abc-checkbox
                                v-model="postData.enableAheadCloseReserve"
                                type="number"
                                data-type="label-align"
                            >
                                开启
                            </abc-checkbox>

                            <template #content>
                                <abc-space v-if="postData.enableAheadCloseReserve">
                                    <span>号源将在就诊结束</span>

                                    <abc-select
                                        v-model="aheadCloseReserveTimeStr"
                                        :width="90"
                                    >
                                        <abc-option
                                            v-for="item in closeReserveTimeOptions"
                                            :key="item.value"
                                            :value="item.value"
                                            :label="item.label"
                                        ></abc-option>
                                    </abc-select>

                                    <span>关闭预约</span>
                                </abc-space>
                            </template>
                        </biz-setting-form-item-indent>
                    </biz-setting-form-item>
                </biz-setting-form-group>

                <biz-setting-form-group title="签到设置">
                    <biz-setting-form-item label="预约签到">
                        <biz-setting-form-item-tip tip="开启后，预约患者需要签到后，才会进行检查">
                            <abc-checkbox
                                v-model="postData.needSignIn"
                                type="number"
                                data-type="label-align"
                            >
                                开启
                            </abc-checkbox>
                        </biz-setting-form-item-tip>
                    </biz-setting-form-item>

                    <biz-setting-form-item label="扫码签到">
                        <biz-setting-form-item-tip tip="开启后，患者到店可扫描检查科室或机房二维码自助签到">
                            <abc-checkbox
                                v-model="postData.needScanSignIn"
                                type="number"
                                data-type="label-align"
                            >
                                开启
                            </abc-checkbox>
                        </biz-setting-form-item-tip>
                    </biz-setting-form-item>
                </biz-setting-form-group>
            </biz-setting-form>

            <template #footer>
                <biz-setting-footer>
                    <abc-button
                        :loading="buttonLoading"
                        :disabled="!contentChanged"
                        @click="submit"
                    >
                        保存
                    </abc-button>
                </biz-setting-footer>
            </template>
        </biz-setting-content>
    </biz-setting-layout>
</template>

<script>
    import {
        BizSettingLayout,
        BizSettingContent,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index.js';
    import {
        BizSettingForm,
        BizSettingFormGroup,
        BizSettingFormItem,
        BizSettingFormItemTip,
        BizSettingFormItemIndent,
    } from '@/components-composite/setting-form/index.js';

    import {
        daysOptions, closeReserveTimeOptions,
    } from '@/views-hospital/settings-common/utils/constant';
    import ReservationAPI from 'api/registrations/reservation';
    import { pick1 } from 'utils/lodash';
    import { isEqual } from 'utils/lodash';
    import Clone from 'utils/clone';

    export default {
        components: {
            BizSettingLayout,
            BizSettingContent,
            BizSettingFooter,
            BizSettingForm,
            BizSettingFormGroup,
            BizSettingFormItem,
            BizSettingFormItemTip,
            BizSettingFormItemIndent,
        },

        data() {
            return {
                daysOptions,
                closeReserveTimeOptions,

                buttonLoading: false,

                postData: {
                    serviceType: 0,
                    needScanSignIn: 0,
                    needSignIn: 0,
                    enableAheadCloseReserve: 0,
                    reservationTimeRange: { // 放号时间
                        registerStartTime: '08:00',
                        startTime: {
                            hour: 0,
                            min: 60,
                        },
                        endTime: {
                            month: 3,
                            week: 0,
                            day: 0,
                        },
                    },
                    aheadCloseReserveTime: {
                        hour: 0,
                        min: 0,
                    },
                },

                cachePostData: {
                    serviceType: 0,
                    needScanSignIn: 0,
                    needSignIn: 0,
                    enableAheadCloseReserve: 0,
                    reservationTimeRange: { // 放号时间
                        registerStartTime: '08:00',
                        startTime: {
                            hour: 0,
                            min: 60,
                        },
                        endTime: {
                            month: 3,
                            week: 0,
                            day: 0,
                        },
                    },
                    aheadCloseReserveTime: {
                        hour: 0,
                        min: 0,
                    },
                },
            };
        },

        computed: {
            advanceTime: {
                get() {
                    return this.convertPostData2Value(
                        this.postData.reservationTimeRange.endTime,
                    );
                },
                set(val) {
                    this.postData.reservationTimeRange.endTime = this.convertValue2PostData(val);
                },
            },

            aheadCloseReserveTimeStr: {
                get() {
                    if (!this.postData.aheadCloseReserveTime) return '';

                    const {
                        hour, min,
                    } = this.postData.aheadCloseReserveTime;

                    return `${hour * 60 + min} 分钟`;
                },

                set(val) {
                    this.selectAdvanceTimeRange(val);
                },
            },

            contentChanged() {
                return !isEqual(this.postData, this.cachePostData);
            },
        },


        async created() {
            await this.fetchReservation();
        },

        methods: {
            async fetchReservation() {
                const { data } = await ReservationAPI.fetchReservationConfig({
                    registrationType: 10,
                });

                if (!data.reservationTimeRange) {
                    data.reservationTimeRange = {};
                }
                data.reservationTimeRange.registerStartTime = data.reservationTimeRange?.registerStartTime || '08:00';
                data.reservationTimeRange.startTime = data.reservationTimeRange?.startTime || {
                    hour: 0,
                    min: 60,
                };
                data.reservationTimeRange.endTime = data.reservationTimeRange?.endTime || {
                    month: 0,
                    week: 0,
                    day: 0,
                };

                data.aheadCloseReserveTime = data.aheadCloseReserveTime || {
                    min: 0,
                    hour: 1,
                };

                const obj = pick1(data, ['serviceType', 'needScanSignIn', 'needSignIn', 'enableAheadCloseReserve', 'reservationTimeRange', 'aheadCloseReserveTime']);

                this.postData = Clone(obj);
                this.cachePostData = Clone(obj);
            },

            selectAdvanceTimeRange(val) {
                const minutes = val.split(' ');

                if (!val || minutes.length < 2) {
                    this.postData.aheadCloseReserveTime.min = 0;
                    this.postData.aheadCloseReserveTime.hour = 0;
                }

                const min = minutes[0] % 60;
                const hour = Math.floor(minutes[0] / 60);
                this.postData.aheadCloseReserveTime.min = min;
                this.postData.aheadCloseReserveTime.hour = hour;
            },

            convertPostData2Value(postData) {
                let value = '';
                const range = postData;
                if (range.day) {
                    value = `${range.day} 天`;
                } else if (range.week) {
                    value = `${range.week} 周`;
                } else if (range.month) {
                    value = `${range.month} 个月`;
                }
                return value;
            },

            convertValue2PostData(value) {
                const range = {
                    day: 0,
                    week: 0,
                    month: 0,
                };
                const arr = value.split(' ');
                switch (arr[1]) {
                    case '天':
                        range.day = +arr[0];
                        break;
                    case '周':
                        range.week = +arr[0];
                        break;
                    case '个月':
                        range.month = +arr[0];
                        break;
                    default:
                }
                return range;
            },

            async submit() {
                if (this.buttonLoading) return false;

                this.buttonLoading = true;
                this.postData.needSignIn = +this.postData.needSignIn;
                this.postData.needScanSignIn = +this.postData.needScanSignIn;
                this.postData.type = 10;

                try {
                    await ReservationAPI.updateReservationConfig(this.postData);
                    await this.fetchReservation();
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });

                    this.buttonLoading = false;
                } catch (err) {
                    this.buttonLoading = false;
                }
            },
        },
    };
</script>

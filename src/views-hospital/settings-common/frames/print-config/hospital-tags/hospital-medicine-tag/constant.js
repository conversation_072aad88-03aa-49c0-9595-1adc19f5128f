export const hospitalMedicineTagExampleWesternData = [
    {
        'patientOrderId': 'ffffffff0000000034e997bc4234c000',
        'bedNo': '1',
        'wardName': '手术室病区',
        'planExecuteTime': '2025-03-25T08:29:00Z',
        'patient': {
            'patientId': 'ffffffff0000000034e997bc41d34000',
            'name': '吕汝鑫',
            'sex': '女',
            'age': {
                'year': 24,
                'month': 9,
                'day': 4,
            },
            'mobile': '17225475981',
        },
        'sourceFormType': 4,
        'usageInfo': {
            'specification': '西成药',
            'doseCount': null,
            'totalProcessCount': null,
            'usage': '口服',
            'dailyDosage': '',
            'freq': 'bid',
            'usageLevel': '2',
            'requirement': '与抗生素间隔2小时1',
        },
        'formItems': [
            {
                'groupId': null,
                'name': '阿莫西林胶囊（联邦阿莫仙）',
                'usageInfo': {
                    'dosage': '2',
                    'dosageUnit': '片',
                    'usage': '口服',
                    'freq': 'bid',
                    'days': 2,
                    'ivgtt': null,
                    'ivgttUnit': '',
                },
                'productInfo': {
                    'displaySpec': '20mg*20片',
                    'goodsVersion': 2,
                    'disable': 0,
                    'piecePrice': 1,
                    'packagePrice': 20,
                    'packageCostPrice': 20,
                    'typeId': 12,
                    'customTypeId': '0',
                    'shebao': {
                        'goodsId': 'ffffffff000000003470255ca51b0000',
                        'goodsType': 1,
                        'payMode': 0,
                        'isDummy': 0,
                        'medicineNum': '000026',
                        'medicalFeeGrade': 0,
                        'nationalCode': 'XJ01CAA040A001010500600',
                        'nationalCodeId': '14403000003540',
                        'shebaoPieceNum': 24,
                        'shebaoPieceUnit': '片',
                        'shebaoPackageUnit': '盒',
                        'shebaoMedicineNmpn': '国药准字H19983134',
                        'standardCode': '86900600000090',
                    },
                    'medicalFeeGrade': 0,
                    'subType': 1,
                    'priceType': 1,
                    'isPreciousDevice': 0,
                    'componentContentNum': null,
                    'componentContentUnit': null,
                    'medicineDosageNum': 20,
                    'medicineDosageUnit': 'mg',
                    'pieceNum': 20,
                    'pieceUnit': '片',
                    'packageUnit': '盒',
                    'composeUseDismounting': 0,
                    'composeSort': 0,
                    'composeFractionPrice': null,
                    'composeUnitCount': null,
                    'feeCategoryId': null,
                    'hospitalNeedExecutive': 0,
                    'dangerIngredient': 0,
                    'cmspec': '',
                },
                'unitCount': 2,
                'unit': '片',
                'ast': 1,
                'astResult': {
                    'operatorIds': [
                        '6e45706922a74966ab51e4ed1e604641',
                    ],
                    'operatorTime': '2025-01-21T02:16:00Z',
                    'result': '阴性',
                },
                'specialRequirement': '与抗生素间隔2小时1',
            },
        ],
    },
];

export const hospitalMedicineTagExampleChineseData = [
    {
        'patientOrderId': 'ffffffff0000000034e997bc4234c000',
        'bedNo': '1',
        'wardName': '手术室病区',
        'planExecuteTime': '2025-03-25T08:29:00Z',
        'patient': {
            'patientId': 'ffffffff0000000034e997bc41d34000',
            'name': '吕汝鑫',
            'sex': '女',
            'age': {
                'year': 24,
                'month': 9,
                'day': 4,
            },
            'mobile': '17225475981',
        },
        'sourceFormType': 6,
        'usageInfo': {
            'specification': '中药饮片',
            'doseCount': 3,
            'totalProcessCount': null,
            'usage': '煎服',
            'dailyDosage': '1日1剂',
            'freq': '1日3次',
            'usageLevel': '每次150ml',
            'requirement': '调和送服',
        },
        'formItems': [
            {
                'groupId': null,
                'name': '中药饮片',
                'usageInfo': {
                    'dosage': '每次150ml',
                    'dosageUnit': '',
                    'usage': '煎服',
                    'freq': '1日3次',
                    'days': null,
                    'ivgtt': null,
                    'ivgttUnit': '',
                },
                'productInfo': {
                    'displaySpec': null,
                },
                'unitCount': 1,
                'unit': 'g',
                'ast': null,
                'astResult': null,
                'specialRequirement': '调和送服',
            },
        ],
    },
];

<template>
    <setting-second-tab
        v-model="selectedValue"
        :options="options"
        @change="changeTab"
    ></setting-second-tab>
</template>

<script>
    import SettingSecondTab from 'views/settings/components/setting-second-tab/index.vue';

    export default {
        name: 'HospitalMedicalRecordTab' ,

        components: { SettingSecondTab },

        data() {
            return {
                selectedValue: 'printGoodsIn',
            };
        },

        computed: {
            options() {
                return [
                    {
                        label: '医嘱单',
                        value: 'doctor-medical-prescription',
                    },
                    {
                        label: '医嘱执行单',
                        value: 'doctor-nurse-prescription',
                    },
                    {
                        label: '领药退药单',
                        value: 'nurse-patient-dispensing',
                    },
                    {
                        label: '用药处方',
                        value: 'hospital-prescription',
                    },
                    {
                        label: '输液记录单',
                        value: 'hospital-infusion-record',
                    },
                    {
                        label: '住院证',
                        value: 'hospital-hospitalization-certificate',
                    },
                ];
            },
        },

        mounted() {
            this.selectedValue = this.$route.name;
        },

        methods: {
            changeTab(value) {
                this.$router.push({ name: value });
            },
        },
    };
</script>

export const DEPOSIT_RECEIPT_DATA = {
    'patientName': '张三',
    'hospitalNo': '000378',
    'departmentName': '测试儿科',
    'patientOrderId': 'ffffffff0000000034c30695821a0000',
    'chainId': 'ffffffff00000000146808c695534000',
    'clinicId': 'ffffffff00000000146808c695534004',
    'lastTransaction': {
        'id': '3801889769475833856',
        'payRequestId': '3801889768927133697',
        'hisChargeDepositId': '3801889764107124736',
        'payMode': 1,
        'paySubMode': 0,
        'payModeDisplayName': '现金',
        'type': 1,
        'paySource': null,
        'amount': 10000,
        'remark': null,
        'created': '2024-05-28T07:12:17Z',
        'createdBy': '6e45706922a74966ab51e4ed1e604641',
        'createdByName': '李海洋',
    },
    'totalAmount': 10000,
    'availableAmount': 10000,
    'qrCode': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFgAAABYCAYAAABxlTA0AAAAAklEQVR4AewaftIAAASLSURBVO3B0YrdRhQAwW5x//+XOx7IATPIita7ypOqBOKHVAyVpeKMylIxVEbFonKm4jtUdhVD5SccvB518HrUh99UfJXKmYpFZVTcpbJUDJWdypmKReVMxaJypeJvqCwHr0cdvB714Q9UrlTcUTFUrlQMlUXljMoVlZ+mcqVid/B61IeHqCwVZyquVOxUdhVDZVQsKlcqnnTwetTB61Ef/kcVP6XiqyqGylLxpIPXow5ej/rwBxU/TWVXMVSuVOxURsVQ2amMiq+q+KqD16M+/Ebl/1KxqIyKobJUDJVRcUfFUNmpjIqdynccvB518HqU/cIDVO6o+Bsq31Hxfzh4Perg9aiPyqi4ojIqFpW7Ku5S+aqKoXKHyndUnFFZDl6Psl/4l8qViqGyVJxRWSqGyqhYVEbFTmVUDJVdxVC5o+KKyqgYKlcqloPXow5ej/rwHyquqFxRGRW7iqFyV8WicqXijMqicqZiqbhSMVR2B69HHbweJRAnKhaVUXGXyndULCpnKq6oLBVDZVcxVEbFd6gsB69HffhNxVBZKobKqFhURsWuYqiMikXlCRV3qIyKobJUDJVdxVDZHbwedfB61KdiqHxVxRNUloozKndUjIqhslQMlZ3KqBgqi8qo2B28HnXwepT9wr9URsWiclfFTuVMxR0qo2KnMiqGyndUXFFZKobKqFgOXo/68AUVQ2WpOKOyVJxR2VUMlZ3KXRWLyqi4ojJUloqhMip2FbuD16MOXo/68JuKoXKHyqi4ovJVFUPlisoVlVGxqIyKoXKHypmK5eD1qIPXo+wXLqiMiisqu4orKqNiqOwqvkNlVzFURsUVlTsOXo/68JdUdhVDZaeyqxgqo2JROaOyVJxR+aqKr6oYKqNiOXg96uD1qA9/oHKl4g6Vuyq+SuWuiqFyRWWpOFOxqxgqy8HrUQevR31URsVdKlcqrqgsKqNiqCwVZyoWlVExVJaKKyp3qewqrhy8HiUQFyr+hspSMVRGxaJypWKojIqdyqjYqYyKncqoWFRGxVC5UrEcvB518HrUp+KMyqIyKobKT6g4o3JFZam4onJG5Y6KoTIqdiq7g9ejDl6P+vAfKobKqLii8lUqo2JXMVSuqOwqhspSMVR2Kt9x8HqUQFyoGCrfUTFUdhVDZVcxVHYVQ2VXsVP5GxWLyqjYHbwedfB61Kfiroq7VHYqu4qhMioWlaGyqzhTsaicUdlVXFG5S2U5eD3q4PUogfghFUNlV7FT+RsVi8qoGCpLxRWVUTFUloqhsqsYKqNiOXg9SiD+VfFVKqNip3Km4qepjIqdyqhYVEbFFZVdxZWD16MOXo/68AcqVyquqCwVQ2Wo7CqGypWKn6byHSq7g9ejDl6P+vCQiisVi8qViisqZ1R2FUNlVzFUloqhMioWlTMVy8HrUR8epnJGZakYKjuVUbGrOKOyVHyHyl0Vu4PXow5ej/rwBxU/reKuikVlqOwqzlQsKmcqFpWhcqViqCwVVw5ejzp4PerDb1R+ispO5UrFUFkqzqjsVL6jYqgsFUPlLpXl4PWofwCZHq4HnuYbRAAAAABJRU5ErkJggg==',
};

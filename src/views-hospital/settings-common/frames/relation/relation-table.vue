<template>
    <biz-setting-layout
        class="outpatient-setting-wrapper"
    >
        <biz-setting-content>
            <relation-setting-table :is-hospital="true" content-class="items-table-container"></relation-setting-table>
        </biz-setting-content>
    </biz-setting-layout>
</template>

<script>
    import RelationSettingTable from 'views/layout/relation-setting-table';
    import {
        BizSettingLayout,
        BizSettingContent,
    } from '@/components-composite/setting-form-layout/index.js';

    export default {
        name: 'RelationTable',
        components: {
            RelationSettingTable,
            BizSettingLayout,
            BizSettingContent,
        },
    };
</script>

<template>
    <div class="device-card-wrapper" @click="$emit('click')">
        <img :src="device.iconUrl" alt="" class="device-avatar" />

        <div class="device-info-wrapper">
            <div class="device-info-item">
                <div class="device-info-item-label">
                    检查类型
                </div>
                <div class="device-info-item-value">
                    {{ getDeviceTypeName() }}
                </div>
            </div>

            <div class="device-info-item">
                <div class="device-info-item-label">
                    品牌名称
                </div>
                <div class="device-info-item-value">
                    {{ device.manufacture }}
                </div>
            </div>

            <div class="device-info-item">
                <div class="device-info-item-label">
                    设备型号
                </div>
                <div class="device-info-item-value">
                    {{ device.model }}
                </div>
            </div>

            <div v-if="isChainAdmin" class="device-info-item">
                <div class="device-info-item-label">
                    所属门店
                </div>
                <div class="device-info-item-value">
                    {{ handleClinicInfo(device.clinicInfos) }}
                </div>
            </div>

            <div class="device-info-item">
                <div class="device-info-item-label">
                    检查机房
                </div>
                <div class="device-info-item-value">
                    {{ device.deviceRoomName }}
                </div>
            </div>

            <div class="device-info-item">
                <div class="device-info-item-label">
                    设备状态
                </div>
                <div class="device-info-item-value" :class="device.deviceStatus === DEVICE_STATUS.USING ? 'active' : 'disabled'">
                    {{ device.deviceStatusName }}
                </div>
            </div>
        </div>

        <div v-if="hasMark" class="device-card-type-mark">
            {{ getDeviceTypeName() }}
        </div>
    </div>
</template>

<script>
    import {
        DEVICE_STATUS,
        INSPECT_TYPE_ENUM,
    } from '@/views-hospital/inspect-setting/utils/constant';
    import { mapGetters } from 'vuex';

    export default {
        name: 'DeviceCard',
        props: {
            device: {
                type: Object,
                default: () => ({}),
            },

            hasMark: {
                type: Boolean,
                default: () => false,
            },
        },
        data() {
            return {
                DEVICE_STATUS,
            };
        },
        computed: {
            ...mapGetters(['isChainAdmin']),
            isEye() {
                return +this.device.goodsExtendSpec === 20;
            },
        },
        methods: {
            handleClinicInfo(info) {
                if (!info) return '';

                return `${[...new Set(info.map((o) => o.organ?.name))].join('、')}`;
            },

            getDeviceTypeName() {
                if (+this.device.goodsExtendSpec === 20) {
                    return '眼科检查';
                }

                return INSPECT_TYPE_ENUM[this.device.deviceType];
            },
        },
    };
</script>

<style lang="scss" scoped>
@import "src/styles/theme.scss";
@import 'src/styles/mixin.scss';

.device-card-wrapper {
    position: relative;
    box-sizing: border-box;
    display: flex;
    height: 192px;
    padding: 16px;
    cursor: pointer;
    border: 1px solid $P6;
    border-radius: var(--abc-border-radius-small);

    > img {
        flex-shrink: 0;
        width: 160px;
        height: 160px;
        margin-right: 24px;
    }

    .device-info-wrapper {
        flex: 1;
        overflow: hidden;

        .device-info-item {
            display: flex;
            margin-bottom: 6px;

            .device-info-item-label {
                margin-right: 16px;
                color: $T2;
            }

            .device-info-item-value {
                flex: 1;

                @include ellipsis;

                &.active {
                    color: #0eba52;
                }

                &.disabled {
                    color: #ff3333;
                }
            }
        }
    }

    .device-card-type-mark {
        position: absolute;
        top: 0;
        left: 0;
        padding: 4px 8px;
        color: #0090ff;
        background: #e2f0ff;
        border-radius: 4px 0 4px 0;
    }
}
</style>

<template>
    <medical-project-detail-dialog
        v-model="modalVisible"
        title="检查"
        custom-class="custom-add-project-dialog"
        :tab-options="tabOptions"
        :tab-title-index.sync="activeIndex"
        :auto-focus="false"
    >
        <template v-if="activeIndex === 0">
            <medical-project-detail-form
                ref="projectForm"
                v-abc-loading="loading"
                label-position="top"
                :label-width="80"
                class="add-project-from-wrapper"
            >
                <template #left>
                    <div class="add-project-from-left">
                        <h3 class="add-project-form-title">
                            {{ basicInfoTitle }}
                        </h3>

                        <abc-form-item-group
                            :grid-column-count="1"
                            grid
                        >
                            <abc-form-item
                                ref="nameFormItemRef"
                                :label="`${projectName}名称`"
                                required
                                hidden-red-dot
                            >
                                <abc-input
                                    v-model="goods.name"
                                    :placeholder="goodsNamePlaceHolder"
                                    :disabled="isChainSubStore || goodsDisabled"
                                    :max-length="40"
                                >
                                    <span v-if="goodsDisabled" slot="append" class="disable-tag">
                                        已停用
                                    </span>
                                </abc-input>
                            </abc-form-item>

                            <template v-if="!isChainAdmin">
                                <abc-form-item label="执行科室">
                                    <abc-select
                                        v-model="goods.executeDepartmentId"
                                        :show-value="goods.executeDepartmentName"
                                        adaptive-width
                                        :disabled="goodsDisabled"
                                        @change="handleSelectExecuteDepartment"
                                    >
                                        <abc-option
                                            v-for="(d,i) in departmentList"
                                            :key="i"
                                            v-bind="d"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                            </template>

                            <abc-form-item label="检查分类" required hidden-red-dot>
                                <abc-select
                                    :value="itemCategory"
                                    placeholder="请选择检查分类"
                                    :disabled="isChainSubStore || goodsDisabled"
                                    adaptive-width
                                    @change="itemCategoryChange"
                                >
                                    <abc-option
                                        v-for="(o, key) in filterDeviceList"
                                        :key="key"
                                        :index="key"
                                        :value="o.value"
                                        :label="o.label"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>

                            <abc-form-item label="二级分类">
                                <secondary-classification-select
                                    v-model="goods.customTypeId"
                                    :disabled="isChainSubStore || goodsDisabled"
                                    :type-id="primaryClassification.id"
                                    clearable
                                    adaptive-width
                                    @changeCustomTypes="$emit('changeCustomTypes')"
                                ></secondary-classification-select>
                            </abc-form-item>

                            <!-- 仅支持CT、DR、MR三种检查类型 -->
                            <abc-form-item v-if="showInspectPosition" label="部位分类">
                                <abc-select
                                    v-model="goods.inspectionSite"
                                    placeholder="请选择部位"
                                    :disabled="goodsDisabled || isChainSubStore"
                                    adaptive-width
                                >
                                    <abc-option
                                        v-for="(item, key) in inspectPositionOptions"
                                        :key="key"
                                        v-bind="item"
                                        :index="key"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>

                            <abc-form-item label="单位">
                                <select-usage
                                    v-model="goods.packageUnit"
                                    style="width: 100%;"
                                    :li-width="75.5"
                                    :options="unitArray"
                                    type="customUnit"
                                    size="medium"
                                    :disabled="!isAdmin || goodsDisabled"
                                    @modify-options="unitEditVisible = true"
                                >
                                </select-usage>
                            </abc-form-item>

                            <abc-form-item label="性别限制">
                                <abc-select
                                    v-model="goods.gender"
                                    :disabled="isChainSubStore || goodsDisabled"
                                    adaptive-width
                                >
                                    <abc-option label="男" :value="1"></abc-option>
                                    <abc-option label="女" :value="2"></abc-option>
                                    <abc-option label="不限" :value="0"></abc-option>
                                </abc-select>
                            </abc-form-item>

                            <!--如果是编辑就需要强校验，因为新增的时候可以系统生成-->
                            <abc-form-item
                                label="项目编码"
                                :required="editMode"
                                :error="projectCodeError"
                                hidden-red-dot
                            >
                                <abc-input
                                    v-model="goods.shortId"
                                    class="code-input"
                                    placeholder="系统生成或自定义"
                                    :max-length="20"
                                    type="number-en-char"
                                    :disabled="!isAdmin || goodsDisabled"
                                    :config="{ supportZero: true }"
                                >
                                </abc-input>
                            </abc-form-item>

                            <abc-form-item label="检查要求">
                                <abc-input
                                    v-model="goods.bizExtensions.samplingReq"
                                    placeholder="输入检查要求"
                                    :disabled="!isAdmin || goodsDisabled"
                                ></abc-input>
                            </abc-form-item>

                            <template v-if="canRenderIdentificationField">
                                <abc-form-item label="互认项目">
                                    <abc-checkbox
                                        v-model="goods.extendInfo.isMutualRecognition"
                                        type="number"
                                        :disabled="goodsDisabled"
                                    >
                                        呼包HR 包头市HR
                                    </abc-checkbox>
                                </abc-form-item>
                            </template>

                            <template v-if="!isChainAdmin">
                                <abc-form-item label="检查地址">
                                    <abc-flex vertical align="flex-start" :gap="8">
                                        <abc-radio-group v-model="goods.extendInfo.examAddressType">
                                            <abc-flex align="center" style="height: 32px;">
                                                <abc-radio :disabled="goodsDisabled" :label="0">
                                                    执行科室地址
                                                </abc-radio>

                                                <abc-radio :disabled="goodsDisabled" :label="1">
                                                    自定义地址
                                                </abc-radio>
                                            </abc-flex>
                                        </abc-radio-group>

                                        <abc-input
                                            v-model="goods.extendInfo.examAddress"
                                            :disabled="goodsDisabled"
                                            :max-length="50"
                                            :style="{ opacity: goods.extendInfo.examAddressType }"
                                            adaptive-width
                                        ></abc-input>
                                    </abc-flex>
                                </abc-form-item>
                            </template>
                        </abc-form-item-group>
                    </div>
                </template>

                <template #right>
                    <abc-flex vertical :gap="24" class="add-project-from-right">
                        <div v-if="isClinicalProject">
                            <h3 class="add-project-form-title">
                                检查项目
                            </h3>

                            <single-item-table
                                :data-source.sync="currentSelectedSystemIndicators"
                                :del-disabled="(isChainSubStore || goodsDisabled)"
                                :draggable="(isAdmin && !goodsDisabled)"
                                @delete-item="handleDeleteItem"
                            ></single-item-table>

                            <div style="margin-top: 12px;">
                                <abc-button
                                    v-if="!(isChainSubStore || goodsDisabled)"
                                    type="blank"
                                    class="addSubBtn"
                                    @click="toAddClinicalTarget"
                                >
                                    添加指标
                                </abc-button>
                            </div>
                        </div>

                        <fee-item-table
                            :goods="goods"
                            :fee-items.sync="goods.feeComposeList"
                            :package-cost-price.sync="goods.packageCostPrice"
                            :package-price.sync="goods.packagePrice"
                            :goods-type="{
                                type: goods.type, subType: goods.subType
                            }"
                            :sale-price.sync="goods.packagePrice"
                            :cost-price.sync="goods.packageCostPrice"
                            :medical-code-list.sync="goods.feeComposeList"
                            :compute-price-loading.sync="computePriceLoading"
                            @compute-price-finished="onComputePriceFinished"
                        ></fee-item-table>
                    </abc-flex>
                </template>
            </medical-project-detail-form>

            <abc-flex slot="footer" align="center" justify="space-between">
                <abc-space>
                    <template v-if="editMode">
                        <abc-popover
                            v-if="!goodsDisabled && isAdmin"
                            class="disable-goods-select"
                            trigger="click"
                            placement="bottom-start"
                            theme="white"
                            :visible-arrow="false"
                            :popper-style="{ padding: 0 }"
                        >
                            <abc-button slot="reference" variant="ghost" theme="danger">
                                停用
                            </abc-button>

                            <ul class="disable-goods-options">
                                <li @click="stop">
                                    停用但保留项目资料
                                </li>

                                <li @click="del">
                                    停用并删除项目资料
                                </li>
                            </ul>
                        </abc-popover>

                        <abc-button
                            v-if="!goodsDisabled && !isAdmin"
                            variant="ghost"
                            theme="danger"
                            @click="stop"
                        >
                            停用
                        </abc-button>

                        <abc-button
                            v-if="startVisible"
                            variant="ghost"
                            @click="start"
                        >
                            启用
                        </abc-button>
                    </template>
                </abc-space>

                <abc-space>
                    <abc-button
                        theme="primary"
                        :disabled="editMode && !contentChanged"
                        :loading="btnLoading || isLoadingRelatedItems"
                        @click="save"
                    >
                        确定
                    </abc-button>

                    <abc-button variant="ghost" @click="modalVisible = false">
                        取消
                    </abc-button>
                </abc-space>
            </abc-flex>

            <unit-editor
                v-if="unitEditVisible"
                v-model="unitEditVisible"
                :type="3"
                @update-unit="handleUnitEdited"
            ></unit-editor>
        </template>

        <template v-else>
            <store-set-price
                ref="store-set-price"
                :goods-id="id"
                :chain-price="goods.packagePrice"
                :check-all.sync="checkAll"
                @modify="handleModify"
                @close-store-set-price="modalVisible = false"
            ></store-set-price>

            <div slot="footer" class="dialog-footer add-project-set-footer">
                <div class="left-box" style="margin-left: 12px;">
                    <abc-checkbox
                        v-model="checkAll"
                        @change="checkAllHandler"
                    >
                        全选
                    </abc-checkbox>
                    <abc-button
                        variant="ghost"
                        @click="modifyClinicDisableStatus(0)"
                    >
                        批量启用
                    </abc-button>
                    <abc-button
                        variant="ghost"
                        @click="modifyClinicDisableStatus(1)"
                    >
                        批量停用
                    </abc-button>
                </div>

                <div style="display: flex; justify-content: flex-end;">
                    <abc-button
                        type="primary"
                        :disabled="submitDisabled"
                        @click="submitSave"
                    >
                        提交
                    </abc-button>
                    <abc-button
                        type="blank"
                        @click="modalVisible = false"
                    >
                        关闭
                    </abc-button>
                </div>
            </div>
        </template>

        <add-system-indicators-dialog
            v-if="isShowClinicalTargetDialog"
            v-model="isShowClinicalTargetDialog"
            :data-source="currentSystemIndicators"
            :item-category="Number(goods.bizExtensions.itemCategory)"
            @setSelectedSystemIndicators="handleSelectedSystemIndicators"
        >
        </add-system-indicators-dialog>
    </medical-project-detail-dialog>
</template>

<script>
    import UnitEditor from 'views/settings/diagnosis-treatment/components/unit-editor.vue';
    import SelectUsage from 'views/layout/select-group/index';

    import {
        CLINICAL_DEVICE_TYPE,
        CLINICAL_TYPE,
        DEVICE_TYPE, INSPECT_TYPE,
        INSPECT_TYPE_ENUM,
    } from '@/views-hospital/inspect-setting/utils/constant';
    import { mapGetters } from 'vuex';
    import TreatmentApi from 'api/treatment';
    import {
        GoodsSubTypeEnum, GoodsTypeEnum,
    } from '@abc/constants';
    import { DISABLED } from 'views/inventory/components/social-code-autocomplete/constant.js';
    import { isEqual } from 'utils/lodash';
    import Clone from 'utils/clone';
    import SettingAPI from 'api/settings';
    import GoodsAPI from 'api/goods';
    import {
        INSPECT_EXTEND_ENUM, INSPECT_POSITION,
    } from '@/views-hospital/inspect-setting/utils/constant';
    import FeeItemTable from '@/views/layout/fee-item-table/index.vue';

    import StoreSetPrice from 'views/settings/diagnosis-treatment/examinations/store-set-price';
    import StoreSetPriceMixins from 'views/settings/diagnosis-treatment/mixins/store-set-price-mixins';
    import SingleItemTable from '@/views-hospital/inspect-setting/components/single-item-table.vue';
    import AddSystemIndicatorsDialog
        from '@/views-hospital/inspect-setting/components/add-system-indicators-dialog.vue';
    import ExaminationAPI from 'api/examination';
    import { isBaoTouOrHuHeHaoTeCity } from '@/utils';
    import {
        checkBeforeDeleteGoods, checkBeforeDisableGoods,
    } from 'views/layout/goods-pre-operate-check';
    import {
        MedicalProjectDetailDialog,
        MedicalProjectDetailForm,
    } from '@/views/settings/diagnosis-treatment/components/medical-project-detail-dialog/index.js';
    import { useGoodsDetail } from 'views/settings/diagnosis-treatment/mixins/useGoodsDetail';

    const SecondaryClassificationSelect = () => import('@/views/inventory/goods/components/secondary-classification/secondary-select.vue');
    import { useMedicalCodeSync } from '@/views/settings/diagnosis-treatment/hooks/useMedicalCodeSync';

    const DEFAULT_PROJECT_DATA = {
        name: '', // 项目名称
        type: GoodsTypeEnum.EXAMINATION,
        subType: 2,
        bizExtensions: {
            itemCategory: '', // 检查类型
            samplingReq: '', // 检查要求
        },
        bizRelevantId: '', // 默认设备
        packagePrice: '', // 销售价格
        packageCostPrice: '', // 成本价格
        packageUnit: '', // 单位

        shortId: '', // 项目编码
        isSell: 1,
        extendSpec: INSPECT_EXTEND_ENUM.ris,
        feeComposeList: [],
        inspectionSite: undefined, // 检查部位
        disable: 0,
        // 性别限制
        gender: '',
        // 执行科室
        executeDepartmentId: '',
        deviceType: '',
        customTypeId: '',
        extendInfo: {
            isMutualRecognition: 0,
            examAddress: '', // 检查地址
            examAddressType: 0, // 检查地址类型 0 默认执行科室地址 1 自定义地址
        },
    };
    export default {
        name: 'AddProjectDialog',

        components: {
            SingleItemTable,
            UnitEditor,
            SelectUsage,
            FeeItemTable,
            StoreSetPrice,
            AddSystemIndicatorsDialog,
            SecondaryClassificationSelect,
            MedicalProjectDetailDialog,
            MedicalProjectDetailForm,
        },

        mixins: [StoreSetPriceMixins],

        model: {
            prop: 'visible',
            event: 'change',
        },

        props: [
            'visible',
            'mode',
            'id',
            'deviceType',
            'departmentList',
        ],

        setup() {
            const {
                computePriceLoading,
                handleSubmitWithComputePrice,
                handleComputePriceFinished,
            } = useGoodsDetail();

            const {
                isLoadingRelatedItems,
                saveOriginalCodeItems,
                handleSyncMedicalCodeAndPrice,
            } = useMedicalCodeSync();

            return {
                isLoadingRelatedItems,
                saveOriginalCodeItems,
                handleSyncMedicalCodeAndPrice,

                computePriceLoading,
                handleSubmitWithComputePrice,
                handleComputePriceFinished,
            };
        },

        data() {
            return {
                goods: DEFAULT_PROJECT_DATA,
                cacheGoods: DEFAULT_PROJECT_DATA,

                DEVICE_TYPE,
                DISABLED,

                customUnitList: [],
                sysUnitList: [],
                unitEditVisible: false,

                projectCodeError: { // 项目编码重复提示
                    error: false,
                    message: '',
                },

                btnLoading: false,

                inspectPositionOptions: Object.keys(INSPECT_POSITION).map((key) => {
                    return {
                        label: INSPECT_POSITION[key],
                        value: key,
                    };
                }).filter((item) => +item.value !== 0),
                activeIndex: 0,
                loading: false,
                isShowClinicalTargetDialog: false, //临床检查 添加指标弹窗
                systemIndicators: [], //指标
                currentSelectedSystemIndicators: [],
                itemCategory: '',
            };
        },

        computed: {
            ...mapGetters([
                'isAdmin',
                'goodsConfig',
                'currentClinic',
                'isChainSubStore',
                'isChainAdmin',
                'goodsPrimaryClassification',
                'clinicBasicConfig',
            ]),

            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),

            projectName() {
                return this.viewDistributeConfig.Settings.projectSetting.name;
            },

            basicInfoTitle() {
                return this.viewDistributeConfig.Settings.projectSetting.basicInfoTitle;
            },

            modalVisible: {
                get() {
                    return this.visible;
                },

                set(val) {
                    this.$emit('change', val);
                },
            },

            editMode() {
                return this.mode === 'edit';
            },

            tabOptions() {
                const arr = [];
                const label = this.editMode ? `编辑检查${this.projectName}` : `新建检查${this.projectName}`;

                arr.push({
                    label,
                    value: 0,
                });
                if (this.editMode && this.isChainAdmin) {
                    arr.push({
                        label: '门店定价',
                        value: 1,
                    });
                }

                return arr;
            },

            title() {
                if (!this.editMode) return `新增检查${this.projectName}`;
                return `编辑检查${this.projectName}`;
            },

            unitArray() {
                return this.sysUnitList.concat(this.customUnitList);
            },

            goodsDisabled() {
                return !!this.goods.disable;
            },

            contentChanged() {
                return !isEqual(this.goods, this.cacheGoods) || !isEqual(this.currentSelectedSystemIndicators, this.initCurrentSelectedSystemIndicators);
            },

            startVisible() {
                if (this.isChainSubStore) {
                    return this.goodsDisabled && !this.goods?.chainV2DisableStatus;
                }

                return this.goodsDisabled;
            },

            // filterClinicDeviceList() {
            //     const canUse = this.clinicDeviceList
            //         .filter((o) => o.deviceStatus === DEVICE_STATUS.USING)
            //         .map((m) => {
            //             return {
            //                 ...m,
            //                 formatLabel: m.name + (m.deviceRoomName ? `（${m.deviceRoomName}）` : ''),
            //             };
            //         });

            //     if (!this.goods.bizExtensions.itemCategory) return [];

            //     return canUse.filter((o) => {
            //         return o.deviceType === +this.goods.bizExtensions.itemCategory;
            //     });
            // },

            showInspectPosition() {
                return DEVICE_TYPE.filter((o) => (
                    o.label === 'CT' ||
                    o.label === 'MR' ||
                    o.label === 'DR'
                )).map((o) => o.value).includes(+this.goods.bizExtensions.itemCategory);
            },

            currentSystemIndicators() {
                const canSelectIndicatorType = [
                    INSPECT_TYPE.NORMAL,
                    INSPECT_TYPE.ENT,
                    INSPECT_TYPE.EYE,
                    INSPECT_TYPE.MOUTH,
                    INSPECT_TYPE.SURGERY,
                    INSPECT_TYPE.INTERNAL,
                    INSPECT_TYPE.GYNECOLOGY,
                ];
                return this.systemIndicators.filter((item) => {
                    return canSelectIndicatorType.includes(item.deviceType);
                }).map((item) => ({
                    ...item,
                    checked: this.currentSelectedSystemIndicators.some((bItem) => (bItem.sourceId === item.id || bItem.id === item.id)),
                }));
            },
            filterDeviceList() {
                if (this.isClinicalProject) {
                    return CLINICAL_DEVICE_TYPE;
                }
                return DEVICE_TYPE;
            },
            /**
             * @desc 一级分类
             */
            primaryClassification() {
                return (
                    this.goodsPrimaryClassification.find((item) => {
                        return item.goodsType === GoodsTypeEnum.EXAMINATION && item.goodsSubType === this.goods.subType;
                    }) || { id: '' }
                );
            },
            isClinicalProject() {
                return this.deviceType === CLINICAL_TYPE.value;
            },
            goodsNamePlaceHolder() {
                const deviceType = this.goods.bizExtensions.itemCategory;
                return (INSPECT_TYPE_ENUM[deviceType] || '').indexOf('检查') !== -1 ?
                    `案例：${INSPECT_TYPE_ENUM[deviceType]}` : `案例：${INSPECT_TYPE_ENUM[this.deviceType]}检查`;
            },
            isSupportIdentification() {
                return this.viewDistributeConfig.Settings.project.inspect.isSupportIdentification;
            },
            // 互认标识：包头市 + 医院 + 单店或子店 + CT/MR/DR
            canRenderIdentificationField() {
                return isBaoTouOrHuHeHaoTeCity(this.clinicBasicConfig.addressCityId) &&
                    this.isSupportIdentification &&
                    !this.isChainAdmin &&
                    [INSPECT_TYPE.CT, INSPECT_TYPE.DR, INSPECT_TYPE.MR].includes(+this.goods.bizExtensions.itemCategory);
            },
        },

        async created() {
            this.setSystemIndicators();
            this.getCustomUnit();

            if (this.mode === 'add') {
                this.goods = Clone(DEFAULT_PROJECT_DATA);
                if (this.isClinicalProject) {
                    this.itemCategory = 14;
                    this.goods.bizExtensions.itemCategory = 14;
                } else {
                    this.itemCategory = this.deviceType;
                    this.goods.bizExtensions.itemCategory = this.deviceType;
                }
                this.cacheGoods = Clone(this.goods);

                return;
            }
            this.loading = true;
            try {
                const { data } = await SettingAPI.examination.fetchExaminationById(this.id);
                const { examGoodsInfo } = data;
                if (this.isClinicalProject) {
                    const { items } = data;

                    this.currentSelectedSystemIndicators = Clone(items);
                    this.initCurrentSelectedSystemIndicators = Clone(items);
                }

                examGoodsInfo.feeComposeList = examGoodsInfo.feeComposeList || [];
                examGoodsInfo.gender = examGoodsInfo.gender ?? '';
                examGoodsInfo.extendInfo = examGoodsInfo.extendInfo || {};
                data.examGoodsInfo.extendInfo.examAddressType = data.examGoodsInfo.extendInfo.examAddressType ?? 0;
                data.examGoodsInfo.extendInfo.examAddress = data.examGoodsInfo.extendInfo.examAddress ?? '';
                examGoodsInfo.executeDepartmentId = examGoodsInfo.extendInfo.executeDepartments?.[0]?.id || '';
                examGoodsInfo.executeDepartmentName = examGoodsInfo.extendInfo.executeDepartments?.[0]?.name || '';

                this.goods = Clone(examGoodsInfo);
                this.itemCategory = this.goods.bizExtensions.itemCategory;
                this.cacheGoods = Clone(examGoodsInfo);
                this.saveOriginalCodeItems(Clone(this.goods.feeComposeList));
            } catch (error) {
                console.log(error);
            }
            this.loading = false;
        },

        beforeDestroy() {
            if (this.timer) {
                clearTimeout(this.timer);
                this.timer = null;
            }
        },

        methods: {
            async getCustomUnit() {
                const res = await TreatmentApi.fetchCustomUnit(GoodsTypeEnum.EXAMINATION);

                this.updateUnit(res);
            },

            updateUnit(unit) {
                const {
                    data: {
                        sysUnitList,
                        customUnitList,
                    },
                } = unit;

                this.sysUnitList = sysUnitList || [];
                this.customUnitList = customUnitList || [];
            },

            handleUnitEdited(data) {
                this.updateUnit(data);

                if (!this.goods.packageUnit) {
                    return;
                }

                const selected = this.unitArray.find(
                    (it) => it.name === this.goods.packageUnit,
                );

                if (!selected) {
                    return;
                }

                const {
                    data: {
                        sysUnitList = [],
                        customUnitList = [],
                    },
                } = data;
                const _unitArray = sysUnitList.concat(customUnitList);
                const updated = _unitArray.find(
                    (it) => it.id === selected.id,
                );
                if (
                    updated &&
                    this.goods.packageUnit !== updated.name
                ) {
                    this.goods.packageUnit = updated.name;
                }
                if (!updated) {
                    this.goods.packageUnit = '';
                }
            },

            save() {
                // 使用新的工具函数处理算费与提交的协调
                if (!this.handleSubmitWithComputePrice(() => this.save())) {
                    this.btnLoading = true;
                    return;
                }

                this.$refs.projectForm.validate((val) => {
                    if (!val) {
                        this.btnLoading = false;
                        return;
                    }

                    this.handleSyncMedicalCodeAndPrice(this.id ,this.goods.feeComposeList, async (feeComposeList) => {
                        this.goods.feeComposeList = feeComposeList;
                        const data = {
                            examGoodsInfo: {
                                ...this.goods,
                                extendInfo: {
                                    ...(this.goods.extendInfo || {}),
                                    executeDepartmentIds: [this.goods.executeDepartmentId],
                                },
                            },
                            items: [{}],
                        };
                        if (this.isClinicalProject) {
                            data.items = this.currentSelectedSystemIndicators;
                        }

                        if (!this.editMode) {
                            await this.post(data);
                        } else {
                            await this.put(this.id, data);
                        }
                    });
                });
            },

            async stop() {
                const flag = await checkBeforeDisableGoods(this.id, this.goods.name, this);
                if (!flag) return;

                try {
                    await GoodsAPI.switchGoodsDisabled(this.id, { disable: 1 });
                    this.$Toast({
                        message: '停用成功',
                        type: 'success',
                    });

                    this.$emit('refresh-project-list');
                    this.goods.disable = 1;
                    this.cacheGoods.disable = 1;
                } catch (error) {
                    console.error('disable error:', error);
                }
            },

            async start() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '启用后，医嘱中即可开出该项目，是否启用 ？',
                    onConfirm: async () => {
                        try {
                            await GoodsAPI.switchGoodsDisabled(this.id, { disable: 0 });
                            this.$Toast({
                                message: '启用成功',
                                type: 'success',
                            });

                            this.$emit('refresh-project-list');
                            this.goods.disable = 0;
                            this.cacheGoods.disable = 0;
                        } catch (error) {
                            console.error('disable error:', error);
                        }
                    },
                });
            },

            async del() {
                const flag = await checkBeforeDeleteGoods(this.id, this.goods.name, this);
                if (!flag) {
                    return;
                }

                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '删除后不可恢复，但不影响已开出医嘱。是否删除 ？',
                    onConfirm: async () => {
                        await SettingAPI.examination.deleteExaminationGoods(this.id);

                        this.$Toast({
                            message: '删除成功',
                            type: 'success',
                        });

                        this.$emit('refresh-project-list');
                        this.modalVisible = false;
                    },
                });
            },

            async put(id, data) {
                try {
                    this.btnLoading = true;

                    await SettingAPI.examination.updateExaminationGoods(
                        id,
                        data,
                    );

                    this.timer = setTimeout(() => {
                        this.$Toast({
                            type: 'success',
                            message: '修改成功',
                        });

                        this.modalVisible = false;

                        this.$emit('refresh-project-list');
                    }, 500);
                } catch (e) {
                    this.errorHandler(e);
                    this.btnLoading = false;
                }
            },

            async post(data) {
                try {
                    this.btnLoading = true;

                    await SettingAPI.examination.createExaminationGoods(data);

                    this.timer = setTimeout(() => {
                        this.$Toast({
                            type: 'success',
                            message: '新建成功',
                        });

                        this.modalVisible = false;

                        this.$emit('refresh-project-list');
                    }, 2500);
                } catch (e) {
                    this.errorHandler(e);
                    this.btnLoading = false;
                }
            },

            toAddClinicalTarget() {
                this.isShowClinicalTargetDialog = true;
            },

            async setSystemIndicators() {
                const examinationGoodsId = this.mode === 'add' ? null : this.id;
                try {
                    const res = await ExaminationAPI.getSystemIndicators(
                        examinationGoodsId, null, GoodsTypeEnum.EXAMINATION, GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Test,
                    );
                    this.systemIndicators = res.data.rows;
                } catch (e) {
                    console.log(e);
                }
            },

            handleSelectedSystemIndicators(data) {
                this.currentSelectedSystemIndicators = data;
            },

            handleDeleteItem(idx) {
                this.currentSelectedSystemIndicators.splice(idx, 1);
            },

            itemCategoryChange(val) {
                if (val !== this.itemCategory) {
                    this.currentSelectedSystemIndicators = [];
                }
                this.goods.bizExtensions.itemCategory = val;
                this.itemCategory = val;
            },

            errorHandler(e) {
                const {
                    code = 0,
                    message = '',
                } = e || {};
                if (code === 12204) {
                    this.projectCodeError = {
                        error: true,
                        message,
                    };
                } else {
                    this.$Toast({
                        type: 'error',
                        message,
                    });
                }
            },

            handleSelectExecuteDepartment(id) {
                const curExecuteDepartment = this.departmentList.find((item) => item.value === id);
                this.goods.executeDepartmentName = curExecuteDepartment.label;
            },

            onComputePriceFinished() {
                // 使用新的工具函数处理算费完成后的逻辑
                this.handleComputePriceFinished();
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";
@import 'src/styles/mixin.scss';

.custom-add-project-dialog {
    .abc-dialog-header {
        padding: 0;

        .abc-tabs {
            height: 40px;
            border-bottom: 0;
        }
    }
}

.add-project-from-wrapper {
    .add-project-form-title {
        margin-bottom: 16px;
        font-weight: bold;
        color: $T1;
    }

    .add-project-from-left {
        .disable-tag {
            color: $R2;
        }
    }

    .add-project-from-right {
        .fee-item-info-wrapper {
            padding: 0;
        }
    }
}

.add-project-dialog-footer {
    display: flex;
    justify-content: flex-end;

    .disable-goods-select {
        color: #ff3333;

        .cis-icon-dropdown_triangle {
            right: -14px;
            color: #ff3333;
        }
    }

    .disable-goods-options {
        padding: 2px 0;

        li {
            display: flex;
            align-items: center;
            height: 34px;
            padding: 8px;
            font-size: 14px;
            color: #000000;
            text-align: center;
            cursor: pointer;

            &:hover {
                background-color: $P4;
            }
        }
    }
}

.add-project-set-footer {
    justify-content: space-between !important;
}
</style>

<template>
    <abc-table
        :render-config="tableRenderConfig"
        :data-list="handleAutomaticChargeList"
        :pagination="tablePagination"
        empty-size="small"
        cell-size="large"
        style="min-height: 576px;"
        @pageChange="handlePageChange"
    >
        <template #projectName="{ trData }">
            <abc-table-cell>
                <abc-text>
                    {{ trData.projectName }}
                </abc-text>
                <abc-popover
                    placement="top-start"
                    trigger="hover"
                    theme="yellow"
                    width="200px"
                >
                    <span>该项目已从收费项目中删除</span>
                    <i
                        v-if="!trData.goodsItem"
                        slot="reference"
                        style="margin-left: 4px;"
                        class="iconfont cis-icon-Attention"
                    ></i>
                </abc-popover>
            </abc-table-cell>
        </template>

        <template #startDoneTime="{ trData }">
            <abc-table-cell>
                <time-select :time-config="trData.startDoneTime"></time-select>
            </abc-table-cell>
        </template>

        <template #referWards="{ trData }">
            <abc-table-cell>
                <abc-space v-if="trData.referWards" :size="4">
                    <abc-text>病区床位</abc-text>
                    <abc-tooltip-info>
                        <abc-text theme="gray">
                            以下病区使用了该收费项
                        </abc-text>
                        <div v-for="item in trData.referWards" :key="item.id">
                            {{ item.name }}
                        </div>
                    </abc-tooltip-info>
                </abc-space>
                <abc-text v-else>
                    医嘱
                </abc-text>
            </abc-table-cell>
        </template>

        <template #status="{ trData }">
            <abc-table-cell>
                <abc-select v-model="trData.status" :width="88">
                    <abc-option
                        v-for="(o,j) in statusOptions"
                        :key="j"
                        v-bind="o"
                    >
                    </abc-option>
                </abc-select>
            </abc-table-cell>
        </template>
    </abc-table>
</template>

<script>
    import TimeSelect from './time-select';
    import AutomaticChargeTable from './automatic-charge-table.js';

    export default {
        name: 'AutomaticChargeTable',
        components: {
            TimeSelect,
        },
        props: {
            value: {
                type: Array,
                default: () => {
                    return [];
                },
            },
        },
        data() {
            return {
                pageParams: {
                    pageIndex: 0,
                    pageSize: 10,
                },
            };
        },
        computed: {
            handleAutomaticChargeList() {
                const {
                    pageSize,
                    pageIndex,
                } = this.pageParams;
                return this.automaticChargeList.slice(pageSize * pageIndex, pageSize * (pageIndex + 1));
            },

            tablePagination() {
                return {
                    showTotalPage: true,
                    pageIndex: this.pageParams.pageIndex,
                    pageSize: this.pageParams.pageSize,
                    count: this.automaticChargeList.length,
                };
            },

            tableRenderConfig() {
                return AutomaticChargeTable.extendConfig({});
            },

            automaticChargeList: {
                set(v) {
                    this.$emit('input', v);
                },
                get() {
                    return this.value;
                },
            },

            statusOptions() {
                return [
                    {
                        label: '启用',
                        value: 0,
                    },
                    {
                        label: '停用',
                        value: 1,
                    },
                ];
            },
        },

        methods: {
            handlePageChange(index) {
                this.pageParams.pageIndex = index - 1;
            },
        },
    };
</script>

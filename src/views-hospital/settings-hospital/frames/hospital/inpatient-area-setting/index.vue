<template>
    <biz-fill-remain-height class="hospital-bed_setting--box">
        <template #header>
            <abc-manage-tabs
                v-if="showTabs"
                :option="tabsOption"
                @change="handleTabsChange"
            >
            </abc-manage-tabs>
        </template>

        <router-view></router-view>
    </biz-fill-remain-height>
</template>

<script>
    import BizFillRemainHeight from '@/components-composite/setting-form-layout/src/views/fill-remain-height.vue';
    import { AbcManageTabs } from 'views/settings/components/abc-manage';

    export default {
        name: 'Index',
        components: {
            AbcManageTabs, BizFillRemainHeight,
        },
        data() {
            return {
                tabsOption: [
                    {
                        label: '病区管理', value: '@inpatient-area-setting',
                    },
                ],
            };
        },
        computed: {
            showTabs() {
                const noTabsNames = [ '@inpatient-area-setting','@inpatient-ward-setting', '@hospital-bed-setting'];
                return noTabsNames.includes(this.$route.name);
            },
        },
        methods: {
            handleTabsChange(val) {
                this.$router.push({
                    name: val,
                });
            },
        },
    };
</script>

<style lang="scss">
.hospital-bed_setting--box {
    width: 100%;
    overflow-x: hidden;
}
</style>

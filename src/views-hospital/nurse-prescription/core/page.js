import {
    BasePage, getApp,
} from '@/core/index.js';
import PageStore from './store.js';
import AbcSocket from 'views/common/single-socket';
import * as business from 'MfFeEngine/business';

export default class HospitalNursePrescriptionPage extends BasePage {
    name = '@HospitalNursePrescription';

    constructor() {
        super({
            store: new PageStore(),
        });
        this.instanceKey = getApp().store.state.hospitalGlobal.currentWardAreaId;
    }

    async init() {
        await super.init();
        const { socket } = AbcSocket.getSocket();
        this.QlService = new business.NurseQlService(socket);
        await this.QlService.start(
            getApp().store.state.hospitalGlobal.currentWardAreaId,
        );
        this.AdviceService = new business.AdviceService(socket);
        await this.AdviceService.start();
        this.AdviceExecuteService = new business.AdviceExecuteService(socket);
        await this.AdviceExecuteService.start();
    }

    created(vm) {
        super.created(vm);
    }

    destroyed() {
        this.QlService?.stop();
        this.QlService = null;
        this.AdviceService?.stop();
        this.AdviceService = null;
        this.AdviceExecuteService?.stop();
        this.AdviceExecuteService = null;
        super.destroyed();
    }
}

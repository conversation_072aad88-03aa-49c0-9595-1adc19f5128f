<template>
    <abc-modal
        v-if="visible"
        v-model="visible"
        type="warn"
        show-icon
        preset="confirm"
        :title="modalTitle"
        confirm-text="确定"
        cancel-text="取消"
        size="large"
        dialog-content-styles="max-height: 500px"
        :close-after-confirm="false"
        :on-confirm="onConfirm"
        :confirm-loading="btnLoading"
        append-to-body
    >
        <abc-flex vertical :gap="8">
            <abc-text size="normal" theme="black">
                {{ modalContent }}
            </abc-text>

            <template v-for="(adviceItem, index) in summaryAdvices">
                <abc-flex
                    v-if="adviceItem.list.length"
                    :key="`nurse-pre-execute-advices-${index}`"
                    vertical
                    :gap="4"
                >
                    <abc-text size="normal" theme="black" bold>
                        {{ adviceItem.title }}
                    </abc-text>

                    <abc-flex vertical class="nurse-execute-auto-dispensing-content">
                        <abc-flex
                            v-for="(advice, idx) in adviceItem.list"
                            :key="advice.adviceExecuteId"
                            align="center"
                            :gap="12"
                            :style="`${idx !== 0 ? 'border-top: 1px solid var(--abc-color-P7)' : ''}`"
                            style="padding: 9px 12px;"
                        >
                            <abc-text v-if="advice.adviceExecuteName">
                                {{ advice.adviceExecuteName }}
                            </abc-text>
                            <abc-text>
                                <template v-if="advice.singleDosageCount && advice.singleDosageUnit">
                                    {{ advice.singleDosageCount }}{{ advice.singleDosageUnit }}
                                </template>
                                <template v-if="(advice.singleDosageCount && advice.singleDosageUnit) && ((advice.singleDispenseCount && advice.singleDispenseUnit) || (advice.dosageCount && advice.dosageUnit))">
                                    /
                                </template>
                                <template v-if="advice.singleDispenseCount && advice.singleDispenseUnit">
                                    {{ advice.singleDispenseCount }}{{ advice.singleDispenseUnit }}
                                </template>
                                <template v-else-if="advice.dosageCount && advice.dosageUnit">
                                    {{ advice.dosageCount }}{{ advice.dosageUnit }}
                                </template>
                            </abc-text>
                            <abc-text v-if="advice.usage">
                                {{ advice.usage }}
                            </abc-text>
                            <abc-text v-if="advice.freq">
                                {{ advice.freq }}
                            </abc-text>
                            <abc-text v-if="advice.freqInfo && advice.freqInfo.firstDayFrequency">
                                {{ advice.freqInfo.firstDayFrequency }}
                            </abc-text>
                            <abc-text v-if="advice.executeItemType !== 0">
                                【{{ advice.executeItemName }}】
                            </abc-text>
                        </abc-flex>
                    </abc-flex>
                </abc-flex>
            </template>
        </abc-flex>
    </abc-modal>
</template>

<script>
    import { CheckResultTypeEnum } from '@/views-hospital/nurse-prescription/utils';
    import MedicalPrescriptionAPI from 'api/hospital/medical-prescription';

    export default {
        name: 'NursePreExecuteModal',
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            adviceDetails: {
                type: Array,
                default() {
                    return [];
                },
            },
        },
        data() {
            return {
                visible: false,
                btnLoading: false,
                needManualApplyAdvices: [], // 以下医嘱需要手动申请领药
                autoDispenseAdvices: [], // 以下医嘱将自动发药
                needAutoApplyAdvices: [], // 以下医嘱将生成待领药单
                reAutoDispenseAdvices: [], // 以下医嘱将重新自动发药
            };
        },
        computed: {
            afterDispensed() {
                // 0:核对后可执行, 1:发药后可执行
                return this.$abcPage.$store.state.afterDispensed;
            },
            modalTitle() {
                return this.afterDispensed ? '执行失败' : '重新生成待领药单';
            },
            modalContent() {
                return this.afterDispensed ? '药品及物资医嘱需要先发药后才能执行' : '部分医嘱重新执行需要重新生成待领药单，是否确定';
            },
            summaryAdvices() {
                return [
                    {
                        title: '以下医嘱需要手动申请领药',
                        list: this.needManualApplyAdvices,
                    },
                    {
                        title: '以下医嘱将自动发药，[确定]后生效',
                        list: this.autoDispenseAdvices,
                    }, {
                        title: '以下医嘱将生成待领药单',
                        list: this.needAutoApplyAdvices,
                    }, {
                        title: '以下医嘱将重新自动发药',
                        list: this.reAutoDispenseAdvices,
                    },
                ];
            },
            isNeedAutoDispenseAdvices() {
                return this.adviceDetails.filter((x) => [
                    CheckResultTypeEnum.needToCallAutoDispense,
                    CheckResultTypeEnum.needToGenerateAutoDispense,
                    CheckResultTypeEnum.needToGenerateManualApply,
                ].includes(x.checkResultType));
            },
            isShowConfirmToast() {
                return !(this.afterDispensed && this.needManualApplyAdvices.length && !this.autoDispenseAdvices.length);
            },
        },
        watch: {
        },
        created() {
            this.preFormatAdviceDetails();
        },
        methods: {
            preFormatAdviceDetails() {
                for (const advice of this.adviceDetails) {
                    const { checkResultType } = advice;
                    if (this.afterDispensed) {
                        switch (checkResultType) {
                            case CheckResultTypeEnum.needToCallAutoDispense:
                                this.autoDispenseAdvices.push(advice);
                                break;
                            case CheckResultTypeEnum.needToManuallyApply:
                                this.needManualApplyAdvices.push(advice);
                                break;
                            case CheckResultTypeEnum.needToGenerateAutoDispense:
                                this.needManualApplyAdvices.push(advice);
                                break;
                            case CheckResultTypeEnum.needToGenerateManualApply:
                                this.needManualApplyAdvices.push(advice);
                                break;
                            default:
                                break;
                        }
                    } else {
                        switch (checkResultType) {
                            case CheckResultTypeEnum.needToCallAutoDispense:
                                this.reAutoDispenseAdvices.push(advice);
                                break;
                            case CheckResultTypeEnum.needToManuallyApply:
                                this.needAutoApplyAdvices.push(advice);
                                break;
                            case CheckResultTypeEnum.needToGenerateAutoDispense:
                                this.reAutoDispenseAdvices.push(advice);
                                break;
                            case CheckResultTypeEnum.needToGenerateManualApply:
                                this.needAutoApplyAdvices.push(advice);
                                break;
                            default:
                                break;
                        }
                    }
                }
            },
            onConfirm() {
                if (this.afterDispensed) {
                    if (this.isNeedAutoDispenseAdvices.length) {
                        this.onAutoDispenseConfirm(this.isNeedAutoDispenseAdvices);
                    }
                } else {
                    this.onAutoDispenseConfirm(this.adviceDetails);
                }
                this.visible = false;
            },
            async onAutoDispenseConfirm(isNeedAutoDispenseAdvices) {
                try {
                    const adviceExecuteIds = isNeedAutoDispenseAdvices.map((it) => it.adviceExecuteId);
                    await MedicalPrescriptionAPI.batchExecuteTaskWithAutoDispensing({ adviceExecuteIds });
                    if (this.isShowConfirmToast) {
                        this.$Toast({
                            type: 'success',
                            message: '操作成功',
                        });
                    }
                } catch (e) {
                    console.error(e);
                }
            },
        },
    };
</script>

<style lang="scss">
.nurse-execute-auto-dispensing-content {
    overflow: hidden;
    background-color: var(--abc-color-cp-white);
    border: 1px solid var(--abc-color-P7);
    border-radius: var(--abc-border-radius-small);
}
</style>

<template>
    <abc-select
        v-model="currentValue"
        :width="width"
        :placeholder="placeholder"
    >
        <abc-option
            v-for="item in nursesList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
        ></abc-option>
    </abc-select>
</template>

<script>
    import WardAreaAPI from 'api/hospital/setting/ward-area';
    import { getApp } from '@/core';

    export default {
        name: 'HospitalEmployeeSelect',
        props: {
            value: {
                type: String,
                default: '',
            },
            width: {
                type: Number,
                default: 228,
            },
            placeholder: {
                type: String,
                default: '请选择',
            },
            loading: Boolean,
        },
        data() {
            return {
                nursesList: [],
            };
        },
        computed: {
            currentValue: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
        },
        created() {
            const wardId = getApp().store.state.hospitalGlobal.currentWardAreaId;
            this.getWardAreaHospitalNurses(wardId);
        },
        methods: {
            async getWardAreaHospitalNurses(wardId) {
                try {
                    this.$emit('update:loading', true);
                    const { data } = await WardAreaAPI.getWardAreaHospitalNurses(wardId) || {};
                    this.nursesList = data?.rows || [];
                } catch (e) {
                    console.error(e);
                } finally {
                    this.$emit('update:loading', false);
                }
            },
        },
    };
</script>


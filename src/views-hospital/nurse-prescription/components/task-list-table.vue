<template>
    <div class="m-p-task-list-table-wrapper">
        <div class="m-p-task-list-table abc-hospital-table">
            <div class="table-header">
                <div class="th" :style="NPTaskListTable.checkbox.style">
                    <abc-checkbox
                        :value="isCheckAll"
                        :indeterminate="isIndeterminate"
                        control
                        @click="onCheckAll"
                    ></abc-checkbox>
                </div>
                <div class="th" :style="NPTaskListTable.bedNo.style">
                    {{ NPTaskListTable.bedNo.label }}
                </div>
                <div class="th" :style="NPTaskListTable.patientName.style">
                    {{ NPTaskListTable.patientName.label }}
                </div>
                <div class="th" :style="NPTaskListTable.planExecuteTime.style">
                    {{ NPTaskListTable.planExecuteTime.label }}
                </div>
                <div class="th" :style="NPTaskListTable.type.style">
                    {{ NPTaskListTable.type.label }}
                </div>
                <div class="th content-header" :style="NPTaskListTable.content.style">
                    {{ NPTaskListTable.content.label }}
                </div>
                <div class="th" :style="NPTaskListTable.treatmentType.style">
                    {{ NPTaskListTable.treatmentType.label }}
                </div>
                <div class="th" :style="NPTaskListTable.status.style">
                    {{ NPTaskListTable.status.label }}
                </div>
                <div class="th" :style="NPTaskListTable.remark.style">
                    {{ NPTaskListTable.remark.label }}
                </div>
                <div class="th" :style="NPTaskListTable.printText.style">
                    {{ NPTaskListTable.printText.label }}
                </div>
                <div class="th" :style="NPTaskListTable.executorName.style">
                    {{ NPTaskListTable.executorName.label }}
                </div>
                <div class="th" :style="NPTaskListTable.executeTime.style">
                    {{ NPTaskListTable.executeTime.label }}
                </div>
                <div class="th" :style="NPTaskListTable.createdByName.style">
                    {{ NPTaskListTable.createdByName.label }}
                </div>
                <div class="th" :style="NPTaskListTable.created.style">
                    {{ NPTaskListTable.created.label }}
                </div>
            </div>
            <div
                v-abc-loading="loading"
                class="table-body"
                :style="bodyStyle"
            >
                <template v-if="list.length > 0">
                    <div v-for="group in list" :key="group.id" class="group">
                        <div
                            v-for="(item, index) in group.timeGroupViews"
                            :key="item.id"
                            class="tr"
                            :style="adviceTrStyle(group, item)"
                            :class="{
                                warn: !!item.isAfterStopTime,
                            }"
                        >
                            <div class="td" :style="NPTaskListTable.checkbox.style">
                                <abc-checkbox
                                    v-model="item.checked"
                                    :disabled="isDisabledCheckbox(item)"
                                ></abc-checkbox>
                            </div>
                            <div class="td" :style="NPTaskListTable.bedNo.style">
                                {{ group.bedNo }} 床
                            </div>
                            <div class="td" :style="NPTaskListTable.patientName.style">
                                {{ group.patientName }}
                            </div>
                            <div class="td" :style="NPTaskListTable.planExecuteTime.style">
                                {{ item.planExecuteTime | parseTime('m-d h:i') }}
                            </div>
                            <div
                                class="td"
                                :style="{
                                    ...NPTaskListTable.type.style,
                                    'align-self': 'flex-start',
                                    ...adviceTdStyle(group),
                                }"
                            >
                                <template v-if="index === 0">
                                    <div
                                        :style="{
                                            display: 'flex',
                                            'align-items': 'center',
                                            ...adviceContentStyle(group, item)
                                        }"
                                    >
                                        {{ MedicalAdviceTypeStr[group.type] || '' }}
                                    </div>
                                </template>
                            </div>
                            <div
                                class="td"
                                :style="{
                                    ...NPTaskListTable.content.style,
                                    'align-self': 'flex-start',
                                }"
                            >
                                <template v-if="index === 0">
                                    <template>
                                        <div
                                            :style="{
                                                display: 'flex',
                                                'align-items': 'center',
                                                'flex-wrap': 'wrap',
                                                ...adviceContentStyle(group, item),
                                            }"
                                        >
                                            <div v-for="it in item.adviceExecutes" :key="it.id" class="advice-content">
                                                <span
                                                    :class="{ active: it.adviceId === selectedAdviceId }"
                                                    :style="{
                                                        ...adviceContentSpanStyle(it)
                                                    }"
                                                    @click="onShowAdviceDetail(it)"
                                                >
                                                    {{ it.name }}
                                                    <advice-content
                                                        v-if="!item.isAfterStopTime"
                                                        :group="group"
                                                        :item="{
                                                            ...item,
                                                            ...it
                                                        }"
                                                    ></advice-content>
                                                </span>
                                                <span v-if="!!item.isAfterStopTime">需撤销</span>
                                                <abc-popover
                                                    v-else-if="it.astResult"
                                                    ref="popover"
                                                    :key="`astResult${it.id}`"
                                                    placement="bottom-end"
                                                    trigger="hover"
                                                    theme="yellow"
                                                >
                                                    <div
                                                        slot="reference"
                                                        class="ast-result"
                                                    >
                                                        <div v-if="it.astResult.result === '阳性'" class="ast-tag red">
                                                            阳性(+)
                                                        </div>
                                                        <div v-else class="ast-tag blue">
                                                            阴性(-)
                                                        </div>
                                                    </div>
                                                    <div style="max-width: 600px;">
                                                        <p>类型：{{ it.astResult.result }}</p>
                                                        <p>备注：{{ it.astResult.operatorTime | parseTime('m-d h:i') }}</p>
                                                    </div>
                                                </abc-popover>
                                                <abc-popover
                                                    v-else-if="it.astFlag === AstEnum.PI_SHI"
                                                    placement="bottom-end"
                                                    trigger="manual"
                                                    theme="white"
                                                    :value="curAstItemId === it.adviceId"
                                                >
                                                    <abc-button
                                                        slot="reference"
                                                        type="blank"
                                                        size="small"
                                                        class="execute-btn blank"
                                                        style="width: 59px; margin-right: 20px;"
                                                        @click="curAstItemId = it.adviceId"
                                                    >
                                                        皮试
                                                    </abc-button>

                                                    <div class="hospital-nurse-ast-dialog">
                                                        <div>{{ it.medicineCadn }}</div>
                                                        <div class="ast-record-item">
                                                            <span class="label">皮试护士</span>
                                                            <span>{{ userInfo && userInfo.name }}</span>
                                                        </div>
                                                        <div class="ast-record-item">
                                                            <span class="label">皮试时间</span>
                                                            <span>{{ new Date() | parseTime('y-m-d h:i') }}</span>
                                                        </div>
                                                        <div class="ast-record-item">
                                                            <span class="label">结果</span>
                                                            <abc-radio-group v-model="astExeResult">
                                                                <abc-radio label="阴性">
                                                                    阴性
                                                                </abc-radio>
                                                                <abc-radio label="阳性">
                                                                    阳性
                                                                </abc-radio>
                                                            </abc-radio-group>
                                                        </div>
                                                        <div class="opt-footer">
                                                            <div class="tips">
                                                                提醒：按规范要求皮试结果确定后不可修改
                                                            </div>
                                                            <div class="btn-group">
                                                                <abc-button
                                                                    size="small"
                                                                    :loading="astLoading"
                                                                    @click="submitAst"
                                                                >
                                                                    确定
                                                                </abc-button>
                                                                <abc-button
                                                                    type="blank"
                                                                    size="small"
                                                                    @click="curAstItemId = null"
                                                                >
                                                                    取消
                                                                </abc-button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </abc-popover>
                                                <div
                                                    v-else-if="it.astFlag === AstEnum.MIAN_SHI || it.astFlag === AstEnum.XU_YONG"
                                                    class="ast-tag green"
                                                >
                                                    {{ it.astFlag === AstEnum.MIAN_SHI ? '免试' : '续用' }}
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </template>
                            </div>
                            <div
                                class="td"
                                :style="{
                                    ...NPTaskListTable.treatmentType.style,
                                    ...adviceTdStyle(group),
                                    height: '40px',
                                    display: 'flex',
                                    'align-items': 'center',
                                }"
                            >
                                {{ TreatmentTypeStr[group.diagnosisTreatmentType] || '' }}
                            </div>
                            <div
                                class="td"
                                :style="{
                                    ...NPTaskListTable.status.style,
                                }"
                            >
                                {{ MedicalAdviceStatusStr[item.status] || '' }}
                            </div>
                            <div class="th" :style="NPTaskListTable.remark.style">
                                <advice-remark-content :item="item.adviceExecutes[0]" :group="group"></advice-remark-content>
                            </div>
                            <div class="td" :style="NPTaskListTable.printText.style">
                            </div>
                            <div class="td" :style="NPTaskListTable.executorName.style">
                                {{ item.executorName || '' }}
                            </div>
                            <div class="td" :style="NPTaskListTable.executeTime.style">
                                {{ item.executeTime | parseTime('m-d h:i') }}
                            </div>
                            <div class="td" :style="NPTaskListTable.createdByName.style">
                                {{ group.createdByName || '' }}
                            </div>
                            <div class="td" :style="NPTaskListTable.created.style">
                                {{ group.created | parseTime('m-d h:i') }}
                            </div>

                            <div
                                v-if="item.adviceExecutes && item.adviceExecutes.length > 1"
                                class="group-line"
                                :style="{
                                    ...groupLineStyle(group, item),
                                }"
                            ></div>
                        </div>
                    </div>
                </template>
                <abc-content-empty v-else top="100px" value="暂无数据"></abc-content-empty>
            </div>
        </div>

        <medical-prescription-detail-popover
            v-if="isShowDetailPopover"
            :id="selectedAdviceId"
            :key="selectedAdviceId"
            v-model="isShowDetailPopover"
            v-abc-click-outside="
                () => {
                    isShowDetailPopover = false;
                    selectedAdviceId = '';
                    selectedExecuteAdviceId = '';
                }
            "
            @success="$emit('success')"
        ></medical-prescription-detail-popover>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';
    import MedicalPrescriptionDetailPopover
        from '@/views-hospital/medical-prescription/components/medical-prescription-detail-popover.vue';
    import {
        NPTaskListTable,
    } from '@/views-hospital/nurse-prescription/utils/task-list-table.js';
    import {
        MedicalAdviceStatusStr,
        MedicalAdviceTypeStr,
        TreatmentTypeStr,
        TreatmentTypeEnum,
    } from '@/views-hospital/medical-prescription/utils/constants.js';
    import { AstEnum } from '@/views-hospital/medical-prescription/utils/constants.js';
    import MedicalPrescriptionAPI from 'api/hospital/medical-prescription/index.js';
    import { parseTime } from '@/utils';
    import adviceContent from '@/views-hospital/nurse-prescription/components/advice-content';
    import adviceRemarkContent from '@/views-hospital/nurse-prescription/components/advice-remark-content';

    export default {
        components: {
            MedicalPrescriptionDetailPopover,
            adviceContent,
            adviceRemarkContent,
        },
        props: {
            list: {
                type: Array,
                default: () => [],
            },
            loading: Boolean,
        },
        data() {
            return {
                AstEnum,
                NPTaskListTable,
                MedicalAdviceTypeStr,
                MedicalAdviceStatusStr,
                TreatmentTypeStr,
                TreatmentTypeEnum,
                isShowDetailPopover: false,
                isCheckAll: false,
                isIndeterminate: false,
                curAstItemId: null,
                astExeResult: '',
                astLoading: false,
                selectedAdviceId: '',
                selectedExecuteAdviceId: '',
            };
        },
        computed: {
            ...mapGetters([
                'userInfo',
            ]),
            bodyStyle() {
                if (this.list.length > 0) {
                    return {
                        maxHeight: 'calc(100vh - 224px)',
                        minHeight: 'auto',
                        height: 'auto',
                        overflow: 'auto',
                    };
                }
                return {
                    height: 'calc(100vh - 224px)',
                };
            },
        },
        watch: {
            list: {
                handler(val) {
                    this.isCheckAll = false;
                    this.isIndeterminate = false;
                    if (val.length > 0) {
                        this.isCheckAll = val.every((x) => {
                            return x.timeGroupViews?.every((y) => y.checked);
                        });
                        if (!this.isCheckAll) {
                            this.isIndeterminate = val.some((x) => {
                                return x.timeGroupViews?.some((y) => y.checked);
                            });
                        }
                    }
                },
                deep: true,
            },
        },
        methods: {
            onCheckAll() {
                let val = false;
                if (this.isIndeterminate) {
                    val = false;
                } else {
                    val = !this.isCheckAll;
                }
                this.list.forEach((x) => {
                    x.timeGroupViews.forEach((item) => {
                        if (!this.isDisabledCheckbox(item)) {
                            this.$set(item, 'checked', val);
                        }
                    });
                });
                this.$emit('update:list', this.list);
            },
            async submitAst() {
                try {
                    this.astLoading = true;
                    await MedicalPrescriptionAPI.saveAstResult({
                        adviceId: this.curAstItemId,
                        operatorId: this.userInfo?.id,
                        operatorTime: parseTime(new Date(), 'y-m-d h:i', true),
                        result: this.astExeResult,
                    });
                    this.curAstItemId = null;
                    this.$emit('astConfirm');
                } catch (e) {
                    console.error(e);
                } finally {
                    this.astLoading = false;
                }
            },

            onShowAdviceDetail(item) {
                const {
                    adviceId, id,
                } = item;
                this.isShowDetailPopover = true;
                this.selectedAdviceId = adviceId;
                this.selectedExecuteAdviceId = id;
            },

            adviceTrStyle(group, item) {
                let height = 0;
                if (item.adviceExecutes.length >= group.timeGroupViews.length) {
                    height = item.adviceExecutes.length * 40 / group.timeGroupViews.length;
                } else {
                    height = 40;
                }
                return {
                    height: `${height}px`,
                };
            },

            groupLineStyle(group, item) {
                let height = 0;
                let left = 0;
                const contentDom = document.querySelector('.content-header');
                left = contentDom.offsetLeft + contentDom.offsetWidth - 10;
                if (item.adviceExecutes.length >= group.timeGroupViews.length) {
                    height = (item.adviceExecutes.length - 1) * 40;
                } else {
                    height = (group.timeGroupViews.length - 1) * 40;
                }
                return {
                    height: `${height}px`,
                    left: `${left}px`,
                };
            },

            adviceTdStyle(group) {
                if (group.timeGroupViews.length > 1) {
                    return {
                        'border-left': `1px solid ${this.$style.P6}`,
                    };
                }
            },

            adviceContentStyle(group, item) {
                let height = 0;
                if (item) {
                    height = Math.max(group.timeGroupViews.length, item.adviceExecutes.length) * 40;
                } else {
                    height = group.timeGroupViews.length * 40;
                }
                const style = {
                    height: `${height - 1}px`,
                    background: 'white',
                    margin: '0 -4px',
                    padding: '0 4px',
                };
                if (group.timeGroupViews.length > 1) {
                    style.background = 'white';
                }
                return style;
            },

            adviceContentSpanStyle(it) {
                const {
                    astResult, astFlag,
                } = it;
                if (astResult || (typeof astFlag === 'number')) {
                    return {
                        width: 'calc(100% - 90px)',
                    };
                }
            },

            isDisabledCheckbox(item) {
                const { diagnosisTreatmentType } = item;
                return [
                    TreatmentTypeEnum.TRANSFER_DEPARTMENT,
                    TreatmentTypeEnum.DISCHARGE_WITH_MEDICINE,
                ].includes(diagnosisTreatmentType);
            },
        },
    };
</script>

<style module lang="scss" src="@/styles/theme.module.scss">
</style>
<style lang="scss">
    @import "src/styles/abc-common.scss";

    .m-p-task-list-table-wrapper {
        position: relative;

        .m-p-task-list-table {
            .table-body {
                min-height: 500px;
            }

            .mp-item-detail-info {
                position: relative;
                display: flex;
                align-items: center;
                height: 40px;
            }

            .group {
                position: relative;

                .advice-content {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    width: 100%;
                    height: 40px;
                    cursor: pointer;

                    >span:first-child {
                        display: inline-block !important;

                        @include ellipsis;

                        &:hover,
                        &.active {
                            color: $B1;

                            span.gray {
                                color: $B1;
                            }
                        }
                    }

                    .gray {
                        >span {
                            display: inline;
                            margin: 0 2px;
                        }
                    }

                    .ast-tag {
                        display: inline-block;
                        width: 56px;
                        margin-right: 20px;
                        line-height: 24px;
                        text-align: center;
                        background: $P4;
                        border-radius: var(--abc-border-radius-small);

                        &.green {
                            color: $G2;
                        }

                        &.red {
                            color: #ff0000;
                        }

                        &.blue {
                            color: #004c97;
                        }
                    }
                }

                .group-line {
                    position: absolute;
                    top: 20px;
                    display: block;
                    width: 7px;
                    content: '';
                    border-top: 2px solid #8f8f8f;
                    border-right: 2px solid #8f8f8f;
                    border-bottom: 2px solid #8f8f8f;
                }

                .tr {
                    &.warn {
                        color: $Y2;

                        .gray {
                            color: $T2;
                        }
                    }
                }

                &:last-child {
                    .tr {
                        border-bottom: none;
                    }
                }
            }
        }

        .medical-prescription-detail-popover {
            top: 168px;
            right: 24px;
        }
    }

    .hospital-nurse-ast-dialog {
        width: 372px;

        > div {
            color: $T1;
        }

        .abc-radio-group {
            padding-left: 0;
        }

        .abc-radio-group,
        .abc-radio {
            height: 28px;
            line-height: 28px;
        }

        .opt-footer {
            display: flex;
            align-items: center;
            margin-top: 12px;

            .tips {
                font-size: 12px;
                color: $R2;
            }

            .btn-group {
                margin-left: auto;
            }
        }

        .ast-record-item {
            display: flex;
            align-items: center;

            & + .ast-record-item {
                margin-top: 10px;
            }

            .label {
                width: 80px;
                max-width: 80px;
                line-height: 28px;
            }
        }
    }
</style>


<template>
    <span v-if="item.examSheetAbnormalInfo">
        <span
            class="abnormal-info"
            :class="getAbnormalClass"
        >
            {{ abnormalStr }}
        </span>
        <span v-if="examinationSheetId" class="report-btn" @click.self="showReport = true">查看报告</span>

        <report-detail-dialog
            v-if="showReport"
            v-model="showReport"
            :patient-id="patientInfo.id"
            :examination-id="examinationSheetId"
        ></report-detail-dialog>
    </span>
</template>

<script>
    const ReportDetailDialog = () => import('src/views/outpatient/common/report-detail-dialog.vue');

    export default {
        name: 'AdviceExamContent',
        components: { ReportDetailDialog },
        props: {
            item: {
                type: Object,
                default: () => {},
            },
        },
        data() {
            return {
                showReport: false,
            };
        },
        computed: {
            abnormalStr() {
                const { simpleExamSheet } = this.item;
                const {
                    isAbnormal, examSheetAbnormalInfo, 
                } = simpleExamSheet || {};
                if (isAbnormal === 0) return '阴性';
                if (!examSheetAbnormalInfo) return '';
                const {
                    abnormalCount,
                    simpleInfo,
                } = examSheetAbnormalInfo || {};
                if (simpleInfo) return simpleInfo;
                if (abnormalCount) return `${abnormalCount}项异常`;
                return '';
            },
            getAbnormalClass() {
                const { simpleExamSheet } = this.item;
                const {
                    isAbnormal, examSheetAbnormalInfo, 
                } = simpleExamSheet || {};
                if (isAbnormal === 0) return 'blue';
                if (!examSheetAbnormalInfo) return '';
                const { simpleInfo } = examSheetAbnormalInfo || {};
                if (!simpleInfo) return '';
                if (simpleInfo === '阳性') return 'red';
                return '';
            },
            examinationSheetId() {
                const { simpleExamSheet } = this.item;
                const { examSheetAbnormalInfo } = simpleExamSheet || {};
                if (!examSheetAbnormalInfo) return '';
                const {
                    id,
                } = examSheetAbnormalInfo || {};
                return id;
            },
        },
    };
</script>

<style lang="scss" scoped>
    .abnormal-info {
        margin-right: 8px;
        font-size: 13px;
        color: $Y2;
        text-align: right;

        &.blue {
            color: #004c97;
        }

        &.red {
            color: #ff0000;
        }
    }

    .report-btn {
        font-size: 13px;
        color: $B2;
        cursor: pointer;
    }
</style>

<template>
    <div v-abc-loading:page="pageLoading">
        <abc-container-center-top-head>
            <abc-tabs
                v-model="currentTab"
                :option="tabOptions"
                :border-style="{ borderBottom: 'none' }"
                size="huge"
                @change="changeTab"
            ></abc-tabs>
        </abc-container-center-top-head>

        <component
            :is="curComponent"
            :key="currentTab"
            :patient-order-ids="patientOrderIds"
            :advice-status-list="adviceStatusList"
            :current-tab="currentTab"
            :page-loading.sync="pageLoading"
            :is-page-loading="true"
            display-print
            @todoCount="onGetTodoCount"
        ></component>
    </div>
</template>

<script>
    import {
        MedicalAdviceStatusEnum,
    } from '@/views-hospital/medical-prescription/utils/constants.js';
    import MedicalPrescription from '@/views-hospital/nurse-prescription/frames/medical-prescription.vue';
    import NurseExecute from '@/views-hospital/nurse-prescription/frames/nurse-execute.vue';

    export default {
        name: 'NursePrescriptionMain',
        inject: {
            $abcPage: {
                default: {},
            },
        },
        data() {
            return {
                currentTab: '',
                showCheckedAdviceExecuteCount: 0,
                showInitAdviceCount: 0,
                showWaitingStopOrUndoAdviceCount: 0,
                patientOrderIds: [],
                pageLoading: false,
            };
        },
        computed: {
            curComponent() {
                switch (this.currentTab) {
                    case '':
                        return MedicalPrescription;
                    case MedicalAdviceStatusEnum.EXECUTED:
                        return NurseExecute;
                    default:
                        return MedicalPrescription;
                }
            },
            tabOptions() {
                return [
                    {
                        label: '核对',
                        value: MedicalAdviceStatusEnum.CHECKED,
                        statisticsNumber: this.showInitAdviceCount || '',
                    },
                    {
                        label: '执行',
                        value: MedicalAdviceStatusEnum.EXECUTED,
                        statisticsNumber: this.showCheckedAdviceExecuteCount || '',
                    },
                    {
                        label: '停止/撤销',
                        value: MedicalAdviceStatusEnum.UNDONE,
                        statisticsNumber: this.showWaitingStopOrUndoAdviceCount || '',
                    },
                    {
                        label: '全部',
                        value: '',
                    },
                ];
            },
            adviceStatusList() {
                switch (this.currentTab) {
                    case MedicalAdviceStatusEnum.CHECKED:
                        // 待核对
                        return [MedicalAdviceStatusEnum.INIT];
                    case MedicalAdviceStatusEnum.UNDONE:
                        // 停止、撤销
                        return [
                            MedicalAdviceStatusEnum.STOPPED,
                            MedicalAdviceStatusEnum.UNDONE,
                        ];
                    default:
                        return [];
                }
            },

            checkedQuickItems() {
                return this.$abcPage.$store.checkedQuickItems;
            },
        },
        watch: {
            // 如果路由有变化，会再次执行该方法
            checkedQuickItems: {
                handler(val) {
                    this.patientOrderIds = val.map((it) => {
                        return it.id;
                    });
                },
                deep: true,
            },
        },

        created() {
            // 默认选中[核对]
            this.currentTab = +this.$route.query.contentTab || +MedicalAdviceStatusEnum.CHECKED;
        },

        beforeDestroy() {
        },

        methods: {
            changeTab(index, item) {
                this.currentTab = item.value;
                this.$router.replace({
                    query: {
                        qlTab: this.$route.query.qlTab,
                        contentTab: this.currentTab,
                    },
                });
            },
            onGetTodoCount({
                showCheckedAdviceExecuteCount,
                showInitAdviceCount,
                showWaitingStopOrUndoAdviceCount,
            }) {
                this.showCheckedAdviceExecuteCount = showCheckedAdviceExecuteCount;
                this.showInitAdviceCount = showInitAdviceCount;
                this.showWaitingStopOrUndoAdviceCount = showWaitingStopOrUndoAdviceCount;
            },
        },
    };
</script>

<style module lang="scss" src="@/styles/theme.module.scss">
</style>

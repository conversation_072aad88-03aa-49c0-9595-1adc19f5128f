import {
    BusinessType,
    CollectionStatus,
} from '@/views-hospital/sample-collection/utils/constant';
import { formatDate } from '@abc/utils-date';
import { ChargeSheetType } from '@/views-hospital/sample-collection/utils/constant';
import { EXAM_SHEET_TYPE } from 'views/examination/util/constants';
import { getExamOutsourcingTag } from '@/views/examination/util';
import {
    GoodsSubTypeEnum, GoodsTypeEnum,
} from '@abc/constants/src';

const isCollectionStatusRefused = (row) => {
    return row.sampleStatus === CollectionStatus.refused;
};

const isOutSourcingGoods = (row) => {
    return row.coFlag === EXAM_SHEET_TYPE.sendToCenterOrgan;
};

export const handleTableHeader = (fn) => [
    {
        key: 'orderNo',
        label: '条码编号',
        style: {
            width: '148px',
            textAlign: 'left',
        },
        customRender(h, row) {
            return (
                <abc-table-cell>
                    <abc-text theme={isCollectionStatusRefused(row) ? 'danger' : 'primary'}>
                        {row.orderNo ?? ''}
                    </abc-text>
                </abc-table-cell>
            );
        },
    },
    {
        label: '姓名',
        key: 'name',
        style: {
            width: '86px',
            textAlign: 'left',
        },
        customRender(h, row) {
            return (
                <abc-table-cell>
                    <abc-text theme={isCollectionStatusRefused(row) ? 'danger' : 'black'}>
                        {row.patient?.name ?? ''}
                    </abc-text>
                    {
                        row.patient?.isMember ?
                            <img
                                src={require('assets/images/crm/<EMAIL>')}
                                style={{
                                    width: '14px',height: '14px',marginLeft: '4px',
                                }}
                                alt
                            /> :
                            null
                    }
                </abc-table-cell>
            );
        },
    },
    {
        label: '性别',
        key: 'sex',
        style: {
            width: '48px',
            textAlign: 'center',
        },
        customRender(h, row) {
            return (
                <abc-table-cell>
                    <abc-text theme={isCollectionStatusRefused(row) ? 'danger' : 'black'}>
                        {row.patient?.sex ?? ''}
                    </abc-text>
                </abc-table-cell>
            );
        },
    },
    {
        label: '年龄',
        key: 'age',
        style: {
            width: '56px',
            textAlign: 'right',
        },
        customRender(h, row) {
            return (
                <abc-table-cell>
                    <abc-text theme={isCollectionStatusRefused(row) ? 'danger' : 'black'}>
                        {row.patient?.age?.year ?? ''}
                    </abc-text>
                </abc-table-cell>
            );
        },
    },
    {
        key: 'businessType',
        label: '渠道',
        style: {
            width: '60px',
            textAlign: 'center',
        },
        customRender(h, row) {
            return (
                <abc-table-cell>
                    <abc-text>
                        {BusinessType[row.businessType]}
                    </abc-text>
                </abc-table-cell>
            );
        },
    },
    {
        key: 'chargeFormItemStatus',
        label: '状态',
        style: {
            width: '86px',
            textAlign: 'center',
        },
        customRender(h, row) {
            let displayName = row.chargeFormItemStatusName ?? '';
            if ([ChargeSheetType.PUBLIC_HEALTH_GROUP,
                ChargeSheetType.PUBLIC_HEALTH_GROUP_PERSONAL_ADDITION,
                ChargeSheetType.PERSONAL_PUBLIC_HEALTH].includes(row.chargeSheetType)) {
                displayName = '无需结算';
            }
            return (
                <abc-table-cell>
                    <abc-text theme={['已收费', '已计费'].includes(row.chargeFormItemStatusName) ? 'gray' : 'black'}>
                        {displayName ?? ''}
                    </abc-text>
                </abc-table-cell>
            );
        },
    },
    {
        key: 'sampleStatus',
        label: '样本状态',
        style: {
            width: '100px',
            textAlign: 'center',
        },
        customRender(h, row) {
            const status = {
                0: '待采集',
                10: '已采集',
                20: '已采集',
                30: '拒收',
            };
            return (
                <abc-table-cell>
                    <abc-tag-v2
                        theme={isCollectionStatusRefused(row) ? 'danger' : row.sampleStatus === 0 ? 'primary' : 'success'}
                        variant="outline"
                    >
                        {status[row.sampleStatus] || ''}
                    </abc-tag-v2>
                </abc-table-cell>
            );
        },
    },
    {
        key: 'sampleType',
        label: '样本类型',
        style: {
            width: '100px',
            textAlign: 'center',
        },
        customRender(h, row) {
            return (
                <abc-table-cell>
                    <abc-text theme={isCollectionStatusRefused(row) ? 'danger' : 'black'}>
                        {row.sampleType ?? ''}
                    </abc-text>
                </abc-table-cell>
            );
        },
    },
    {
        key: 'samplePipe',
        label: '试管编号',
        style: {
            width: '100px',
            textAlign: 'left',
        },
        customRender(h, row) {
            if (row.samplePipe && row.samplePipe.code) {
                return (
                    <abc-table-cell>
                        {
                            row.samplePipe.color ? <img src={row.samplePipe.color} style={{
                                width: '14px',
                                height: '14px',
                                marginTop: '2px',
                            }} alt/> : null
                        }
                        <abc-text theme={isCollectionStatusRefused(row) ? 'danger' : 'black'}>
                            {row.samplePipe?.code ?? ''}
                        </abc-text>
                    </abc-table-cell>
                );
            }

            return null;
        },
    },
    {
        key: 'examinationName',
        label: '检验项目',
        style: {
            width: '140px',
            textAlign: 'left',
        },
        customRender(h, row) {
            return (
                <abc-table-cell>
                    <abc-flex
                        align="center"
                        gap={8}
                    >
                        <abc-text
                            theme={isCollectionStatusRefused(row) ? 'danger' : 'black'}
                            style={{ maxWidth: isOutSourcingGoods(row) ? '56px' : '140px' }}
                            class='ellipsis'
                            title={row.examinationName ?? ''}
                        >
                            { row.examinationName ?? '' }
                        </abc-text>

                        {
                            isOutSourcingGoods(row) ? (
                                <abc-tag-v2
                                    shape="square"
                                    theme="warning"
                                    variant="outline"
                                    size="tiny"
                                    style="min-width: 64px"
                                >
                                    { getExamOutsourcingTag({
                                        type: GoodsTypeEnum.EXAMINATION, subType: GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Inspect,
                                    }) }
                                </abc-tag-v2>
                            ) : null
                        }
                    </abc-flex>
                </abc-table-cell>
            );
        },
    },
    {
        key: 'sellerDepartmentName',
        label: '申请科室',
        style: {
            width: '104px',
            textAlign: 'left',
        },
        customRender(h, row) {
            return (
                <abc-table-cell>
                    <abc-text theme={isCollectionStatusRefused(row) ? 'danger' : 'black'}>
                        {row.sellerDepartmentName ?? ''}
                    </abc-text>
                </abc-table-cell>
            );
        },
    },
    {
        key: 'doctorName',
        label: '申请医生',
        style: {
            width: '86px',
            textAlign: 'left',
        },
        customRender(h, row) {
            return (
                <abc-table-cell>
                    <abc-text theme={isCollectionStatusRefused(row) ? 'danger' : 'black'}>
                        {row.doctorName ?? ''}
                    </abc-text>
                </abc-table-cell>
            );
        },
    },
    {
        key: 'extendDiagnosisInfos',
        label: '临床诊断',
        style: {
            width: '170px',
            textAlign: 'left',
        },
        customRender(h, row) {
            return (
                <abc-table-cell>
                    <abc-text theme={isCollectionStatusRefused(row) ? 'danger' : 'black'}>
                        {fn(row.diagnosisInfos || [])}
                    </abc-text>
                </abc-table-cell>
            );
        },
    },

    {
        key: 'samplerName',
        label: '采样人',
        style: {
            width: '86px',
            textAlign: 'left',
        },
        customRender(h, row) {
            return (
                <abc-table-cell>
                    <abc-text theme={isCollectionStatusRefused(row) ? 'danger' : 'black'}>
                        {row.samplerName ?? ''}
                    </abc-text>
                </abc-table-cell>
            );
        },
    },
    {
        key: 'sampleTime',
        label: '采样时间',
        style: {
            width: '152px',
            textAlign: 'left',
        },
        customRender(h, row) {
            return (
                <abc-table-cell>
                    <abc-text
                        theme={isCollectionStatusRefused(row) ? 'danger' : 'black'}
                        v-abc-title={[formatDate(row.sampleTime, 'MM-DD HH:mm') ?? '', formatDate(row.sampleTime, 'YYYY-MM-DD HH:mm') ?? '']}
                    >
                        {formatDate(row.sampleTime, 'YYYY-MM-DD HH:mm')}
                    </abc-text>
                </abc-table-cell>
            );
        },
    },
    {
        key: 'collectorName',
        label: '核收人',
        style: {
            width: '86px',
            textAlign: 'left',
        },
        customRender(h, row) {
            return (
                <abc-table-cell>
                    <abc-text theme={isCollectionStatusRefused(row) ? 'danger' : 'black'}>
                        {row.collectorName ?? ''}
                    </abc-text>
                </abc-table-cell>
            );
        },
    },
    {
        key: 'collectTime',
        label: '核收时间',
        style: {
            width: '100px',
            textAlign: 'left',
        },
        customRender(h, row) {
            return (
                <abc-table-cell>
                    <abc-text
                        theme={isCollectionStatusRefused(row) ? 'danger' : 'black'}
                        v-abc-title={[formatDate(row.collectTime, 'MM-DD HH:mm') ?? '', formatDate(row.collectTime, 'YYYY-MM-DD HH:mm') ?? '']}
                    >
                        {formatDate(row.collectTime, 'YYYY-MM-DD HH:mm')}
                    </abc-text>
                </abc-table-cell>
            );
        },
    },
    {
        key: 'rejectReason',
        label: '拒绝原因',
        style: {
            minWidth: '80px',
            flex: 1,
            textAlign: 'left',
        },
        customRender(h, row) {
            return (
                <abc-table-cell>
                    <abc-text theme={isCollectionStatusRefused(row) ? 'danger' : 'black'}>
                        {row.rejectReason ? row.rejectReason.list.join(',') + (row.rejectReason.remark ? `, 备注: ${row.rejectReason.remark}` : '') : ''}
                    </abc-text>
                </abc-table-cell>
            );
        },
    },
];


export const SampleInfoTableHeader = [
    {
        key: 'checked',
        style: {
            width: '40px',
            flex: 'none',
        },
    },
    {
        key: 'no',
        style: {
            width: '130px',
            flex: 'none',
        },
    },
    {
        key: 'sampleType',
        style: {
            width: '100px',
            flex: 'none',
        },
    },
    {
        key: 'tube',
        style: {
            width: '100px',
            flex: 'none',
        },
    },
    {
        key: 'examinationName',
        style: {
            width: '130px',
        },
    },
    {
        key: 'sampleReq',
        style: {
            width: '90px',
        },
    },
    {
        key: 'status',
        style: {
            width: '70px',
            flex: 'none',
        },
    },
];

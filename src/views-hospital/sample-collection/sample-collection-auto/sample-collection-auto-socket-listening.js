import Logger from 'utils/logger';
import { createGUID } from '@/utils';
import SampleCollectionPrintManager
    from '@/views-hospital/sample-collection/sample-collection-auto/sample-collection-print-manager';
/**
 * @desc socket消息监听
 * <AUTHOR>
 * @date 2021-07-14 11:41:55
 * @params
 * @return
 */
export default class SampleCollectionAutoSocketListening {
    static TAG = 'SocketMessageListening';

    constructor(socket) {
        this._socket = socket;
        this.printManager = new SampleCollectionPrintManager();
        this._handleMessage = this.handleMessage.bind(this);
    }

    start() {
        this._registerSocketListener();
    }

    stop() {
        this._unregisterSocketListener();
    }

    notifyPrint(socketData, uuid) {
        this.printManager.print(socketData, uuid);
    }

    handleMessage(socketData) {
        const uuid = createGUID();

        Logger.report({
            scene: 'auto_print_sample_collection',
            data: {
                scene: 'socket_data',
                uuid,
                info: '收到 socket 消息',
                data: {
                    socketData,
                },
            },
        });

        // 判断是否需要处理这个消息
        this.notifyPrint(socketData, uuid);
    }

    // 注册事件监听
    _registerSocketListener() {
        this._socket.on('examination.auto_print.barcode', this._handleMessage, '', { mode: 'unicast' });
    }

    _unregisterSocketListener() {
        this._socket.off('examination.auto_print.barcode', this._handleMessage);
        this.printManager.destroy();
        this.printManager = null;
    }
}

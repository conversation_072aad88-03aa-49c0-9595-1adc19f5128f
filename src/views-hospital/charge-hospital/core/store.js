import BasePageStore from '@/core/page/store.js';
import HospitalChargeAPI from 'api/hospital/charge.js';
import { SettleQLTabEnum } from '@/views-hospital/charge-hospital/utils/index.js';

/**
* @desc 费用页面局部store，伴随page生命周期
*/
export default class HospitalChargeHospitalPageStore extends BasePageStore {
    constructor() {
        const namespace = '@HospitalChargeHospital';
        const state = {
            scrollParams: {
                keyword: '',
                offset: 0,
                limit: 50,
                tab: SettleQLTabEnum.IN_HOSPITAL,
                beginDate: '',
                endDate: '',
                invoiceStatusFlag: 0, // 发票状态筛选
            },
            quickList: [],
            qlIsLast: false,
            selectedQuickItem: {},
            currentPatient: {},
        };
        super(namespace, state);
    }


    get quickList() {
        return this.state.quickList || [];
    }

    get selectedQuickItem() {
        return this.state.selectedQuickItem || {};
    }

    /**
     * @desc 获取ql
     * <AUTHOR>
     * @date 2023-01-10 15:39:55
     */
    async fetchQuickList() {
        const scrollParams = {
            ...this.state.scrollParams,
        };



        const { data } = await HospitalChargeAPI.fetchQuickList(scrollParams);

        if (!data) return {};

        const {
            keyword,
            limit,
            offset,
            rows,
            total,
        } = data;

        data.qlIsLast = (offset / limit) === Math.ceil(total / limit);
        const {
            tab,
        } = this.state.scrollParams;
        if (scrollParams.offset !== offset || scrollParams.keyword !== keyword || scrollParams.tab !== tab) return {};

        //当返回的offset是0直接替换，不做concat
        if (offset === 0 && scrollParams.offset === 0) {
            this.state.quickList = rows;
        } else {
            this.state.quickList = this.state.quickList.concat(rows);
        }
        this.state.scrollParams.offset = offset + limit;

        return data;
    }

    async refreshQuickList() {
        const scrollParams = {
            ...this.state.scrollParams,
        };
        if (scrollParams.keyword) return false;
        scrollParams.offset = 0;
        const { data } = await HospitalChargeAPI.fetchQuickList(scrollParams);
        this.state.quickList = data.rows;
    }

    clearQuickList() {
        this.state.quickList = [];
    }
    setScrollParams(params) {
        Object.assign(this.state.scrollParams, params);
    }

    setSelectedQuickItem(newSelectedItem) {
        this.state.selectedQuickItem = newSelectedItem;
    }

    updatePatientInfo(data) {
        Object.assign(this.state.selectedQuickItem.patient, data);
    }
}

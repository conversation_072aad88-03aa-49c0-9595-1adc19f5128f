<template>
    <hospital-inventory-page-container v-abc-loading:page="loading">
        <purchase-offline-plan></purchase-offline-plan>
    </hospital-inventory-page-container>
</template>

<script type="text/ecmascript-6">
    import PurchaseOfflinePlan from '@/views/purchase/offline-purchase-plan/purchase-plan.vue';
    import HospitalInventoryPageContainer from '@/views-hospital/components/inventory-page-container/index.vue';
    export default {
        name: 'HospitalPurchaseOfflinePlanMain',
        components: {
            HospitalInventoryPageContainer,
            PurchaseOfflinePlan,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        data() {
            return {
                loading: false,
            };
        },
    };
</script>



import AbcAccess from '@/access/utils';
import IncomeStatementReportSummaryTable
    from '@/views-hospital/statistics/pages/revenue/components/department-income/components/summary';
import IncomeStatementReportOutpatientTable
    from '@/views-hospital/statistics/pages/revenue/components/department-income/components/outpatient';
import IncomeStatementReportInpatientTable
    from '@/views-hospital/statistics/pages/revenue/components/department-income/components/inpatient';
import IncomeStatementPeReportTable
    from '@/views-hospital/statistics/pages/revenue/components/department-income/components/pe';

export const TABLE_CATEGORY_ENUM = Object.freeze({
    ENTIRE_HOSPITAL_SUMMARY: 1,
    OUTPATIENT: 2,
    INPATIENT: 3,
    PE: 4,
});

export const tableCategoryLabelEnum = Object.freeze({
    [TABLE_CATEGORY_ENUM.ENTIRE_HOSPITAL_SUMMARY]: '全院收入汇总',
    [TABLE_CATEGORY_ENUM.OUTPATIENT]: '门诊收入汇总',
    [TABLE_CATEGORY_ENUM.INPATIENT]: '住院收入汇总',
    [TABLE_CATEGORY_ENUM.PE]: '体检收入汇总',
});

export const tableCategoryOptions = Object.freeze([
    {
        label: tableCategoryLabelEnum[TABLE_CATEGORY_ENUM.ENTIRE_HOSPITAL_SUMMARY],value: TABLE_CATEGORY_ENUM.ENTIRE_HOSPITAL_SUMMARY,
    },
    {
        label: tableCategoryLabelEnum[TABLE_CATEGORY_ENUM.OUTPATIENT],value: TABLE_CATEGORY_ENUM.OUTPATIENT,
    },
    {
        label: tableCategoryLabelEnum[TABLE_CATEGORY_ENUM.INPATIENT],value: TABLE_CATEGORY_ENUM.INPATIENT,
    },
    {
        label: tableCategoryLabelEnum[TABLE_CATEGORY_ENUM.PE],
        value: TABLE_CATEGORY_ENUM.PE,
        hidden: !AbcAccess.getPurchasedByKey(AbcAccess.accessMap.PHYSICAL_EXAMINATION),
    },
]);

export const tableInstanceConfigEnum = Object.freeze({
    [TABLE_CATEGORY_ENUM.ENTIRE_HOSPITAL_SUMMARY]: IncomeStatementReportSummaryTable,
    [TABLE_CATEGORY_ENUM.OUTPATIENT]: IncomeStatementReportOutpatientTable,
    [TABLE_CATEGORY_ENUM.INPATIENT]: IncomeStatementReportInpatientTable,
    [TABLE_CATEGORY_ENUM.PE]: IncomeStatementPeReportTable,
});

export const ReportTypeLabelEnum = Object.freeze({
    [TABLE_CATEGORY_ENUM.ENTIRE_HOSPITAL_SUMMARY]: '全院',
    [TABLE_CATEGORY_ENUM.OUTPATIENT]: '门诊',
    [TABLE_CATEGORY_ENUM.INPATIENT]: '住院',
    [TABLE_CATEGORY_ENUM.PE]: '体检',
});

<template>
    <div class="main-content">
        <div style="padding: 24px 24px 40px;" class="template-manager-wrapper medical-record-template-setting">
            <template-manager
                :owner-type="ownerType"
                :template-scene-type="TemplateSceneTypeEnum.MEDICAL_RECORD"
                disabled-use
            >
            </template-manager>
        </div>
    </div>
</template>

<script>
    import { TemplateSceneTypeEnum } from 'utils/constants.js';

    export default {
        name: 'MedicalRecordTemplateSetting',

        components: {
            TemplateManager: () => import('views/layout/templates-manager/hospital/index.vue'),
        },

        props: {
            ownerType: {
                type: Number,
                required: true,
            },
        },

        data() {
            return {
                TemplateSceneTypeEnum,
            };
        },
    };
</script>

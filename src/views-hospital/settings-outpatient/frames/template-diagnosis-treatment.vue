<template>
    <div class="main-content">
        <div style="padding: 24px 24px 40px;" class="template-manager-wrapper diagnosis-treatment-template-setting">
            <template-manager
                :owner-type="ownerType"
                :template-scene-type="TemplateSceneTypeEnum.DIAGNOSIS_TREATMENT"
                disabled-use
            >
            </template-manager>
        </div>
    </div>
</template>

<script>
    import { TemplateSceneTypeEnum } from 'utils/constants.js';

    export default {
        name: 'DiagnosisTreatmentTemplateSetting',

        components: {
            TemplateManager: () => import('views/layout/templates-manager/hospital/index.vue'),
        },

        props: {
            ownerType: {
                type: Number,
                required: true,
            },
        },

        data() {
            return {
                TemplateSceneTypeEnum,
            };
        },
    };
</script>

<style lang="scss">
.template-manager-wrapper.diagnosis-treatment-template-setting {
    height: 656px;

    .templates-manager-wrapper {
        height: 100%;
        border-bottom: 0;

        .templates-manager-content {
            height: 100%;
            max-height: 900px;
            border-bottom: 1px solid var(--templates-manager-border-color);
        }
    }
}
</style>


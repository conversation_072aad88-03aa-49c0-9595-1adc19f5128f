<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        title="入院登记"
        size="small"
        append-to-body
        :auto-focus="false"
    >
        <abc-form
            ref="form"
            label-position="left"
            :label-width="80"
        >
            <abc-form-item-group grid>
                <abc-form-item label="患者">
                    <abc-space>
                        <abc-text>{{ finishInfo.patientName }}</abc-text>
                        <abc-text>{{ finishInfo.patientSex }}</abc-text>
                        <abc-text>{{ finishInfo.patientAge.year }}岁</abc-text>
                    </abc-space>
                </abc-form-item>
                <abc-form-item label="科室">
                    <abc-text>{{ finishInfo.departmentName }}</abc-text>
                </abc-form-item>
                <abc-form-item label="病区">
                    <abc-text>{{ finishInfo.wardName }}</abc-text>
                </abc-form-item>
                <abc-form-item label="入院时间" required hidden-red-dot>
                    <abc-date-time-picker
                        v-model="inpatientTime"
                        class="date-time"
                        :clearable="false"
                        :space="4"
                        :date-width="137"
                        :describe-list="inpatientTimeOptions.describeList"
                    >
                    </abc-date-time-picker>
                </abc-form-item>
                <abc-form-item label="押金金额">
                    <abc-input
                        v-model.number="payMoney"
                        type="number"
                        :config="{
                            formatLength: 2, max: 99999999, supportZero: false
                        }"
                    >
                        <label slot="prepend" class="prepend">
                            <abc-currency-symbol-icon :size="16" color="#aab4bf"></abc-currency-symbol-icon>
                        </label>
                    </abc-input>
                </abc-form-item>
            </abc-form-item-group>
        </abc-form>
        <div slot="footer" class="dialog-footer">
            <abc-button :loading="btnLoading" @click="updateHospitalStatus">
                登记
            </abc-button>
            <abc-button variant="ghost" @click="closeDialog">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import { mapGetters } from 'vuex';

    import { formatDate } from '@abc/utils-date';
    import AbcCommonChargeDialog from 'components/common-charge-dialog/index.js';
    import { PayModeEnum } from '@/service/charge/constants.js';
    import DepositAPI from 'api/hospital/deposit.js';
    import PatientOrderAPI from 'api/hospital/patient-order';
    import { navigateToAggregatePaymentContentSetting } from '@/core/navigate-helper.js';
    import { red } from 'utils/math.js';
    import AbcCurrencySymbolIcon from 'views/common/components/currency-symbol-icon/index.vue';
    import { HospitalActionEnum } from '@/views-hospital/register/utils/constants';

    export default {
        name: 'RegisterFinishDialog',
        components: { AbcCurrencySymbolIcon },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            value: Boolean,
            patientOrderId: {
                type: String,
                required: true,
            },
            finishInfo: {
                type: Object,
                required: true,
                default() {
                    return {};
                },
            },
        },
        data() {
            return {
                formatDate,
                btnLoading: false,
                payMoney: '',
                inpatientTime: new Date(),
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
            ]),
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            currentQuickItem() {
                return this.$abcPage.$store.selectedQuickItem;
            },
            inpatientTimeOptions() {
                return {
                    describeList: [{
                        date: formatDate(this.finishInfo.inpatientTime, 'YYYY-MM-DD'),
                        describe: '计划入院',
                    }],
                };
            },
        },
        created() {
            const isToday = formatDate(this.finishInfo.inpatientTime, 'YYYY-MM-DD') === formatDate(new Date(), 'YYYY-MM-DD');
            this.inpatientTime = isToday ? this.finishInfo.inpatientTime : '';
            this.payMoney = this.finishInfo.adviceDeposit && this.finishInfo.adviceDeposit.toFixed(2);
        },

        methods: {
            closeDialog() {
                this.showDialog = false;
            },

            async updateHospitalStatus() {
                this.$refs.form.validate(async (valid) => {
                    if (valid) {
                        try {
                            this.btnLoading = true;
                            const { data } = await PatientOrderAPI.updatePatientOrderStatus(this.currentQuickItem.id,{
                                action: HospitalActionEnum.REGISTER_PROCESS,
                                inpatientTime: formatDate(this.inpatientTime, 'YYYY-MM-DD HH:mm') || '',
                            });
                            this.$emit('updateSelectedQuickItem',data);
                            this.$emit('updatePatientInfo');
                            if (this.payMoney > 0) {
                                this.handleClickCharge();
                            } else {
                                this.$Toast({
                                    message: '登记成功',
                                    type: 'success',
                                });
                                this.closeDialog();
                            }
                        } catch (e) {
                            if (e?.code === 400) {
                                this.$Toast({
                                    message: e.message,
                                    type: 'error',
                                });
                            }
                        } finally {
                            this.btnLoading = false;
                        }
                    }
                });
            },

            handleClickCharge() {
                this._chargeDialog = new AbcCommonChargeDialog({
                    dialogTitle: '缴押金',
                    hiddenPayModeList: [
                        PayModeEnum.SOCIAL_CARD,
                        PayModeEnum.ARREARS,
                        PayModeEnum.MEMBER_CARD,
                        PayModeEnum.PATIENT_CARD,
                    ],
                    receivableFee: +this.payMoney,
                    onAbcPayOpenCallback: () => {
                        navigateToAggregatePaymentContentSetting(this.currentClinic);
                    },
                    submit: this.depositPayHandler,
                    onChargeSuccess: this.chargeSuccess,
                    onClose: this.closeDialog,
                });
                this._chargeDialog.generateDialog({ parent: this });
            },
            async depositPayHandler(chargeData) {
                const res = await DepositAPI.pay(this.patientOrderId, {
                    amount: chargeData.amount,
                });
                this.payMoney = red(this.payMoney, chargeData.amount);
                res.needPay = this.payMoney || 0;
                return res;
            },
            chargeSuccess() {
                this.$emit('charge-success');
                if (this._chargeDialog) {
                    this._chargeDialog.destroyDialog();
                    this._chargeDialog = null;
                }
            },
            submitHandler(chargeData) {
                return DepositAPI.pay(this.patientOrderId, {
                    amount: chargeData.amount,
                });
            },
        },
    };
</script>

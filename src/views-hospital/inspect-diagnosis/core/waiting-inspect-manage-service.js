import InspectAPI from '@/api/hospital/inspect';
/**
 * @desc 管理彩超待检查列表
 * <AUTHOR>
 * @date 2023-07-28 10:58:33
 * @params
 * @return
 */

function generatePromise() {
    let resolve;
    let reject;
    const promise = new Promise((res, rej) => {
        resolve = res;
        reject = rej;
    });
    return {
        promise,
        resolve,
        reject,
    };
}

export default class WaitingInspectManageService {
    static instance = null;

    constructor() {
        this.promise = null;
        this.resolve = null;
        this.reject = null;

        this.waitingList = [];
        this.pagination = {
            limit: 999,
            offset: 0,
            total: 0,
        };
    }

    start() {
        if (this.promise) return this.promise;

        const {
            promise, resolve, reject,
        } = generatePromise();
        this.promise = promise;
        this.resolve = resolve;
        this.reject = reject;

        this.initWaitingList();
        return this.promise;
    }

    async initWaitingList() {
        try {
            const { data } = await InspectAPI.getCaptureRecordList({
                limit: this.pagination.limit,
                offset: this.pagination.offset,
            });
            this.waitingList = data?.rows || [];
            this.pagination.total = data?.total || 0;

            this.resolve(data?.rows || []);
        } catch (e) {
            this.reject(e);
        }
    }


    async loadMore() {
        if (this.pagination.offset + this.pagination.limit >= this.pagination.total) return;
        this.pagination.offset += this.pagination.limit;

        const { data } = await InspectAPI.getCaptureRecordList({
            limit: this.pagination.limit,
            offset: this.pagination.offset,
        });

        this.waitingList = [
            ...this.waitingList,
            ...(data?.rows || []),
        ];
    }

    getWaitingList() {
        return this.waitingList;
    }

    /**
     * @desc 检查是否存在待检查列表中
     * <AUTHOR>
     * @date 2023-08-02 14:55:25
     * @params
     * @return
     */
    captureRecordExistInServer(id) {
        return this.waitingList.find((i) => i.examinationSheetId === id && !i.temp);
    }
    /**
     * @desc 添加待检查申请单
     * <AUTHOR>
     * @date 2023-07-28 11:02:52
     * @params
     * @return
     */
    async addWaitingItem(item) {
        try {
            const existItem = this.captureRecordExistInServer(item.examinationSheetId);
            if (existItem) {
                await this.incrementCaptureImageCount(existItem);
                return;
            }

            const payload = {
                ...item,
                captureVideoCount: item.captureVideoCount + 1,
            };

            if (payload.hasOwnProperty('temp')) {
                delete payload.temp;
            }

            await InspectAPI.addCaptureRecord(payload);

            this.updateLocalCaptureRecordById(item.examinationSheetId, {
                captureVideoCount: item.captureVideoCount + 1,
                temp: false,
            });
        } catch (e) {
            console.log('新增一个待检查列表', e);
        }
    }


    /**
     * @desc 移除待检查申请单
     * <AUTHOR>
     * @date 2023-07-28 11:03:22
     * @params
     * @return
     */
    async deleteWaitingItem(examinationSheetId) {
        try {
            const existItem = this.captureRecordExistInServer(examinationSheetId);
            if (existItem) {
                await InspectAPI.deleteCaptureRecordById(examinationSheetId);

                this.deleteLocalCaptureRecord(existItem.examinationSheetId);
            }
        } catch (e) {
            console.log('移除一个待检查申请单', e);
        }
    }

    // 更新截图的数量
    async incrementCaptureImageCount(oldItem) {
        oldItem.captureVideoCount++;
        await InspectAPI.updateCaptureRecordById(oldItem.examinationSheetId, oldItem);
    }

    // 删除截图的数量
    async decreaseCaptureImageCount(examinationSheetId) {
        const existItem = this.captureRecordExistInServer(examinationSheetId);

        if (existItem) {
            existItem.captureVideoCount--;
            existItem.captureVideoCount = Math.max(existItem.captureVideoCount, 0);
            await InspectAPI.updateCaptureRecordById(existItem.examinationSheetId, existItem);
            return;
        }

        this.reducedCountByOne(examinationSheetId);
    }


    static getInstance() {
        if (!WaitingInspectManageService.instance) {
            WaitingInspectManageService.instance = new WaitingInspectManageService();
        }
        return WaitingInspectManageService.instance;
    }

    addTempCaptureRecord(data) {
        const item = this.waitingList.find((i) => i.examinationSheetId === data.examinationSheetId);
        if (item) {
            return;
        }

        this.waitingList.push({
            ...data,
            temp: true,
        });
    }
    updateLocalCaptureRecordById(id, data) {
        const item = this.waitingList.find((i) => i.examinationSheetId === id);

        if (item) {
            Object.assign(item, data);
        }
    }

    reducedCountByOne(id) {
        const item = this.waitingList.find((i) => i.examinationSheetId === id);

        if (item) {
            item.captureVideoCount--;
        }
    }

    deleteLocalCaptureRecord(id) {
        const index = this.waitingList.findIndex((i) => i.examinationSheetId === id);
        if (index > -1) {
            this.waitingList.splice(index, 1);
        }
    }
}

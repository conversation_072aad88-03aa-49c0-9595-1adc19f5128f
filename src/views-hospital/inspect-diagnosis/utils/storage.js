export default class Storage {
    static set(key, data) {
        let ret = true;
        try {
            window.localStorage.setItem(key, JSON.stringify(data));
        } catch (e) {
            ret = false;
            console.warn('storage.set failed', e);
        }
        return ret;
    }

    static get(key) {
        let data = null;
        try {
            const val = window.localStorage.getItem(key);

            if (val) {
                data = JSON.parse(val);
            }
        } catch (e) {
            console.warn('storage.get failed', e);
        }
        return data;
    }

    static remove(key) {
        let ret = true;
        try {
            window.localStorage.removeItem(key);
        } catch (e) {
            ret = false;
            console.warn('storage.remove failed', e);
        }
        return ret;
    }
}

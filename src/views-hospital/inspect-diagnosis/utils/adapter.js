import {
    createGUID, tryJSONParse,
} from '@/utils';
import { EXAMINATION_STATUS } from '@/views-hospital/inspect-diagnosis/utils/constant';
import { convertData } from 'utils/adapter/common-adapter';
import { COMPONENT_TYPE_ENUM } from '@/views-hospital/inspect-diagnosis/utils/constant';

// 检查报告详情适配器
export function inspectDetailAdapter(data) {
    const fields = [
        'name',
        'checkerName',
        'testerName',
        'checkTime',
        'checkerId',
        'reportTime',
        'testTime',
        'testerId',
        'orderNo',
        'patientOrderNumber',
        'recordDoctorName',
        'departmentName',
        'doctorName',
        'chargeFormItemStatus',
        'deviceType',
        'businessType',
        'created',
        'examinationApplySheetId',
        'bedNumber',
        'clinicPrintName',
        'wardAreaName',
        'deviceName',
        'examinationApplySheetNo',
        {
            sourceKey: 'status',
            defaultValue: EXAMINATION_STATUS.WAIT,
        },
        {
            sourceKey: 'patient', defaultValue: {},
        },
        {
            destination: 'consultationDoctorName',
            sourceKey: 'examinationSheetReport.consultationDoctorName',
        },
        {
            destination: 'recordDoctorName',
            sourceKey: 'examinationSheetReport.recordDoctorName',
        },
        {
            sourceKey: 'examinationSheetReport',
            defaultValue: {},
            fields: [
                {
                    sourceKey: 'imageFiles',
                    defaultValue: [],
                },
                {
                    sourceKey: 'diagnosisEntryItems',
                    defaultValue: [],
                    formatFn: (value) => {
                        return value.map((item) => {
                            if (!item.keyId) {
                                item.keyId = createGUID();
                            }
                            return item;
                        });
                    },
                },
                {
                    sourceKey: 'diagnosisFlag',
                    defaultValue: null,
                },
                {
                    sourceKey: 'videoDescription',
                    defaultValue: '',
                },
                {
                    sourceKey: 'suggestion',
                    defaultValue: '',
                },
                {
                    sourceKey: 'advice',
                    defaultValue: '',
                },
                {
                    sourceKey: 'versionFlag',
                    defaultValue: 0,
                },
                {
                    sourceKey: 'inspectionSite',
                    defaultValue: '',
                },
                {
                    sourceKey: 'deviceModelDesc',
                    defaultValue: '',
                },
                {
                    sourceKey: 'principalDoctorDesign',
                    defaultValue: '',
                },
                {
                    sourceKey: 'consultationDoctorId',
                    defaultValue: '',
                },
                {
                    sourceKey: 'recordDoctorId',
                    defaultValue: '',
                },
                {
                    sourceKey: 'diagnosisFlag',
                    defaultValue: null,
                },
            ],
        },
        {
            sourceKey: 'examinationApplySheetView',
            defaultValue: {},
            fields: [
                {
                    sourceKey: 'dcm4cheeView',
                    defaultValue: {},
                    fields: [
                        {
                            sourceKey: 'patient',
                            defaultValue: {},
                        },
                        {
                            sourceKey: 'worklist',
                            defaultValue: [],
                        },
                    ],
                },
                {
                    sourceKey: 'dcm4cheeDetail',
                    defaultValue: {},
                },
            ],
        },
        {
            sourceKey: 'itemsValue',
            defaultValue: [],
            formatFn: (value) => {
                for (const item of value) {
                    if (item.componentType === COMPONENT_TYPE_ENUM.toothSelect) {
                        if (!item.value) {
                            item.value = {
                                toothNos: [],
                                value: '',
                            };
                        }
                        if (!Array.isArray(item.value.toothNos)) {
                            item.value.toothNos = [];
                        }
                        if (item.value.value === undefined) {
                            item.value.value = '';
                        }
                    }
                }
                return value;
            },
            fields: [
                'value',
                {
                    sourceKey: 'ref',
                    defaultValue: {},
                    formatFn: (value) => {
                        return tryJSONParse(value);
                    },
                },
                {
                    sourceKey: 'refDetails',
                    defaultValue: [],
                    fields: [
                        {
                            sourceKey: 'ref',
                            defaultValue: {},
                            formatFn: (value) => {
                                return tryJSONParse(value);
                            },
                        },
                    ],
                },
                {
                    sourceKey: 'isDeleted',
                    defaultValue: 0,
                },
            ],
        },
        {
            sourceKey: 'extendDiagnosisInfos',
            defaultValue: [],
        },
        {
            sourceKey: 'deviceView',
            defaultValue: {},
        },
        {
            sourceKey: 'attachments',
            defaultValue: [],
        },
        {
            sourceKey: 'organPrintView',
            defaultValue: {},
        },
        {
            sourceKey: 'peSheetSimpleView',
            defaultValue: {},
        },
        {
            sourceKey: 'sampleStatus',
            defaultValue: 0,
        },
        {
            sourceKey: 'additionalExaminationSheetReports',
            defaultValue: [],
        },
    ];

    return convertData(data,fields);
}

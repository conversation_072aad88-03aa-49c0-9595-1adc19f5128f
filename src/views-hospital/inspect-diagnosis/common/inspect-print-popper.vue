<template>
    <print-popper
        size="small"
        style="margin: 0;"
        placement="bottom"
        :options="printOptions"
        print-text="打印"
        data-cy="inspect-print"
        @print="printReport"
        @select-print-setting="openPrintConfigSettingDialog"
    >
    </print-popper>
</template>

<script>
    import PrintPopper from 'views/print/popper.vue';
    import { EXAMINATION_STATUS } from '@/views-hospital/inspect-diagnosis/utils/constant';
    const PrintInspectApiModule = () => import('@/printer/print-api/inspect');
    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');
    import { mapGetters } from 'vuex';
    import ABCPrinterConfig from '@/printer/config';

    export default {
        name: 'InspectPrintPopper',

        components: { PrintPopper },

        props: {
            applySheetId: {
                type: String,
                default: '',
            },
            examSheetId: {
                type: String,
                default: '',
            },
            inspectSheetNo: {
                type: String,
                default: '',
            },
            scene: {
                type: String,
                default: '',
            },
            status: {
                type: Number,
                default: EXAMINATION_STATUS.WAIT,
            },
        },
        computed: {
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),

            isSupportInspectNoPrint() {
                return this.viewDistributeConfig.Inspect.isSupportInspectNoPrint;
            },

            printOptions() {
                return [
                    {
                        value: this.isSupportInspectNoPrint ? '检查单条码' : '检查标签',
                        disabled: !this.applySheetId,
                    },
                    {
                        value: '检查报告',
                        disabled: this.status === EXAMINATION_STATUS.WAIT,
                    },
                ];
            },
        },
        methods: {
            async printReport([option]) {
                const { InspectPrintAPI } = await PrintInspectApiModule();
                if (option === '检查标签') {
                    InspectPrintAPI.printInspectLabel(this.applySheetId);
                }

                if (option === '检查单条码') {
                    InspectPrintAPI.printInspectSheetNo(this.applySheetId, this.inspectSheetNo);
                }

                if (option === '检查报告') {
                    InspectPrintAPI.printReport(this.examSheetId);
                }
            },

            async openPrintConfigSettingDialog() {
                const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                if (this.isSupportInspectNoPrint) {
                    const config = ABCPrinterConfig.ticket.find((item) => item.key === 'inspect-label');
                    config.label = '检查单条码';
                }
                new PrintConfigDialog({ scene: this.scene }).generateDialogAsync({ parent: this });
            },
        },
    };
</script>

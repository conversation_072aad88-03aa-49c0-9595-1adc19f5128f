<template>
    <div class="inspect-form-item-select-wrapper">
        <template v-if="editable">
            <abc-select v-model="curValue" adaptive-width>
                <abc-option
                    v-for="(op,idx) in options"
                    :key="idx"
                    v-bind="op"
                ></abc-option>
            </abc-select>
        </template>

        <template v-else>
            <div class="inspect-form-item-select-value">
                <span v-abc-title.ellipsis="item.value"></span>
            </div>
        </template>
    </div>
</template>

<script>
    export default {
        name: 'InspectFormItemSelect',

        props: {
            item: {
                type: Object,
                default: () => ({}),
            },

            needUnit: <PERSON>olean,

            editable: Boolean,

            value: {
                type: String,
                default: '',
            },
        },

        computed: {
            curValue: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },

            options() {
                return (this.item.options || []).map((item) => ({
                    label: item,
                    value: item,
                }));
            },
        },
    };
</script>

<style lang="scss">
.inspect-form-item-select-wrapper {
    position: relative;
    width: 100%;
    height: 100%;

    .abc-input-wrapper,
    .abc-select-wrapper {
        width: 100%;
        height: 100% !important;

        .abc-input__inner {
            width: 100%;
            height: 100% !important;
            background-color: transparent;
            border-color: transparent;
            border-radius: 0;
        }
    }

    .abc-input-wrapper {
        &.has-unit {
            .abc-input__inner {
                padding-right: 92px;
            }
        }
    }

    .inspect-form-item-select-value {
        display: flex;
        align-items: center;
        width: 100%;
        height: 100%;
        padding-right: 10px;
        padding-left: 10px;

        >span {
            flex: 1;
            min-width: 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}
</style>

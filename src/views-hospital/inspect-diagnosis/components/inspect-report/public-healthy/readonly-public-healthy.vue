<template>
    <div class="public-healthy-inspect-report-wrapper">
        <div :class="['public-healthy-main-content', { 'public-healthy-main-content-disabled': disabled }]">
            <div class="form-header">
                公卫体检
            </div>

            <div class="public-healthy-base-info">
                <div class="base-info-item">
                    <div class="base-info-item-label">
                        体检日期
                    </div>

                    <div class="base-info-item-value">
                        {{ formatDate(previewBusinessTime, 'YYYY-MM-DD') }}
                    </div>
                </div>

                <div class="base-info-item">
                    <div class="base-info-item-label">
                        责任医生
                    </div>

                    <div class="base-info-item-value">
                        <abc-select
                            :value="previewPrincipalDoctorId"
                            :width="126"
                            :disabled="disabled"
                        >
                            <abc-option
                                v-for="(d, i) in doctorList"
                                :key="i"
                                v-bind="d"
                            ></abc-option>
                        </abc-select>
                    </div>
                </div>

                <div v-if="supportDoctorSign && isImgUrl(curPrincipalDoctorHandSign)" class="base-info-item">
                    <div class="base-info-item-label" style="margin-right: 4px;">
                        签名：
                    </div>

                    <div class="base-info-item-value">
                        <img
                            :src="curPrincipalDoctorHandSign"
                            height="26px"
                            alt=""
                        />
                    </div>
                </div>
            </div>

            <div
                v-for="(p, i) in projectList"
                :key="i"
                class="public-healthy-project-item"
            >
                <div class="project-item-name">
                    {{ p.label }}
                </div>

                <div class="project-item-form">
                    <component
                        :is="p.is"
                        :ref="p.value"
                        :patient="patient"
                        :disabled="disabled"
                        :form="previewForm"
                    ></component>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import RegistrationAPI from 'api/registrations';

    import PhSymptom from './ph-symptom.vue';
    import PHGeneralCondition from './ph-general-condition.vue';
    import PHAssistInspect from './ph-assist-inspect.vue';
    import PHCurrentDisease from './ph-current-disease.vue';
    import PhInHospitalHistory from './ph-in-hospital-history.vue';
    import PhBedHistory from './ph-bed-history.vue';
    import PHInoculation from './ph-inoculation.vue';
    import PHLifeStyle from './ph-life-style.vue';
    import PhOrgans from './ph-organs.vue';
    import PHCheckBody from './ph-check-body.vue';
    import PHHealthyEvaluation from './ph-healthy-evaluation.vue';
    import PHHealthyGuide from './ph-healthy-guide.vue';

    import { formatDate } from '@abc/utils-date';
    import { covertItemsToPublicHealthyForm } from '@/views-hospital/inspect-diagnosis/utils';
    import {
        publicHealthyForm,
    } from '@/views-hospital/inspect-diagnosis/utils/public-healthy-constant';
    import {
        MODULE_ID_MAP,
        ROLE_DOCTOR_ID,
        ROLE_PH_CHECK_DOCTOR_ID,
    } from 'utils/constants';
    import { isImgUrl } from 'views/physical-examination/assessment/utils';
    import {
        getPublicHealthConfig,
    } from 'views/physical-examination/integrated/public-health-sync/distribute/distribute-helper';

    export default {
        name: 'ReadonlyPublicHealthyInspectReport',

        inject: {
            $abcPage: {
                default: () => {},
            },
        },

        provide() {
            return {
                publicHealthyIndex: null,
            };
        },

        props: {
            form: {
                type: Object,
                default: () => ({}),
            },

            previewFormData: {
                type: Object ,
                default: () => ({}),
            },

            businessTime: {
                type: String,
                default: '',
            },

            principalDoctorId: {
                type: String,
                default: '',
            },

            patientOrderId: {
                type: String,
                default: '',
            },

            patient: {
                type: Object,
                default: () => ({}),
            },
        },
        data() {
            return {
                disabled: true,
                curTab: 'symptom',
                projectList: [
                    {
                        label: '症状', value: 'symptom',is: PhSymptom,
                    },
                    {
                        label: '一般状况', value: 'generalCondition',is: PHGeneralCondition,
                    },
                    {
                        label: '生活方式', value: 'lifeStyle',is: PHLifeStyle,
                    },
                    {
                        label: '脏器功能', value: 'organs', is: PhOrgans,
                    },
                    {
                        label: '查体', value: 'checkBody', is: PHCheckBody,
                    },
                    {
                        label: '辅助检查', value: 'assistInspect', is: PHAssistInspect,
                    },
                    {
                        label: '健康问题', value: 'currentDisease',is: PHCurrentDisease,
                    },
                    {
                        label: '住院史', value: 'inHospitalHistory', is: PhInHospitalHistory,
                    },
                    {
                        label: '家庭病床史', value: 'bedHistory',is: PhBedHistory,
                    },
                    {
                        label: '主要用药情况', value: 'pharmacyCondition', is: getPublicHealthConfig().viewComponents.phPharmacyConditionComponent,
                    },
                    {
                        label: '预防接种史', value: 'inoculation', is: PHInoculation,
                    },
                    {
                        label: '健康评价', value: 'healthyEvaluation', is: PHHealthyEvaluation,
                    },
                    {
                        label: '健康指导', value: 'healthyGuide', is: PHHealthyGuide,
                    },
                ],
                isFixed: false,

                referenceDataDialogVis: false,

                doctorListWhenPreview: [],
            };
        },
        computed: {
            doctorList() {
                return this.doctorListWhenPreview;
            },
            previewForm() {
                const itemsValue = this.previewFormData?.itemsValue || [];
                return covertItemsToPublicHealthyForm(itemsValue, publicHealthyForm);
            },
            previewBusinessTime() {
                return this.previewFormData?.peSheetSimpleView?.businessTime || '';
            },
            previewPrincipalDoctorId() {
                return this.previewFormData?.examinationSheetReport?.principalDoctorId;
            },
            curPrincipalDoctorId: {
                get() {
                    return this.principalDoctorId;
                },
                set(v) {
                    this.$emit('update:principalDoctorId', v);
                },
            },
            curPrincipalDoctorHandSign() {
                const item = this.doctorList.find((d) => d.value === this.curPrincipalDoctorId) || {};
                return item.handSign || '';
            },
            supportDoctorSign() {
                return getPublicHealthConfig().publicHealthForm.supportDoctorSign;
            },
        },

        mounted() {
            // 预览单独拉取责任医生列表
            this.getDoctorList();
        },
        methods: {
            isImgUrl,
            formatDate,

            /**
             * 获取责任医生列表
             * 只在预览时拉取，非预览时会在页面初始化时拉取
             * 模块：检查诊断、检查
             * 角色：医生、总检医生
             */
            async getDoctorList() {
                try {
                    const { data } = await RegistrationAPI.fetchlistByCondition({
                        moduleIds: [ MODULE_ID_MAP.hospitalInspectDiagnosis, MODULE_ID_MAP.inspect ],
                        roles: [
                            ROLE_DOCTOR_ID,
                            ROLE_PH_CHECK_DOCTOR_ID,
                        ],
                    });
                    data.rows = data.rows || [];

                    this.doctorListWhenPreview = data.rows.map((e) => ({
                        label: e.employeeName, value: e.employeeId, roles: e.roles,handSign: e.handSign,
                    }));
                } catch (error) {
                    console.error(error);
                }
            },
        },
    };
</script>

<style lang='scss'>
@import "~styles/theme";

.public-healthy-inspect-report-wrapper {
    display: flex;
    background-color: #ffffff;
    border: 1px solid $P6;
    border-radius: var(--abc-border-radius-small);

    .public-healthy-nav-sidebar {
        width: 136px;
        border-right: 1px solid $P6;

        &-tab {
            padding: 53px 0 0 8px;
        }
    }

    .public-healthy-main-content {
        flex: 1;

        .form-header {
            display: flex;
            align-items: center;
            height: 40px;
            padding: 0 12px;
            font-weight: 500;
            background-color: #f9fafc;
            border-bottom: 1px solid $P6;
        }

        .public-healthy-base-info {
            display: flex;
            gap: 32px;
            align-items: center;
            height: 48px;
            padding: 0 12px;
            border-bottom: 1px solid $P6;

            .base-info-item {
                display: flex;
                align-items: center;

                .base-info-item-label {
                    margin-right: 8px;
                    color: $T2;
                }
            }
        }

        .public-healthy-project-item {
            display: flex;
            width: 100%;

            .project-item-name {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                width: 84px;
                padding: 12px;
                font-weight: 500;
                border-right: 1px solid $P6;
                border-bottom: 1px solid $P6;
            }

            .project-item-form {
                flex: 1;
                border-bottom: 1px solid $P6;
            }
        }

        & > .public-healthy-project-item:last-child {
            .project-item-name {
                border-bottom: none;
            }

            .project-item-form {
                border-bottom: none;

                .ph-row-wrapper:last-child {
                    .ph-row-wrapper:last-child {
                        >.ph-col-wrapper {
                            border-bottom: none;
                        }
                    }
                }
            }
        }
    }

    .public-healthy-main-content-disabled {
        background-color: $abcBgDisabled;

        .abc-table-normal-wrapper {
            background-color: $abcBgDisabled;
        }
    }
}
</style>

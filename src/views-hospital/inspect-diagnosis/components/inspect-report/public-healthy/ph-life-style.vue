<template>
    <!-- 一般状况 -->
    <div class="ph-life-style-wrapper">
        <ph-table-form :form-items="formItems"></ph-table-form>
    </div>
</template>

<script>
    import {
        PHTableForm,
    } from './components';
    import {
        FORM_ITEM_TYPE,
        PUBLIC_HEALTHY_FORM_KEYS,
        PUBLIC_HEALTHY_OPTION,
    } from '@/views-hospital/inspect-diagnosis/utils/public-healthy-constant';
    import UpdateFormMixin from './update-form';
    import themeStyle from 'styles/theme.module.scss';
    const singleLabelStyle = {
        width: '120px',
        flex: 'none',
    };
    const disabledBgStyle = { 'background-color': themeStyle.abcBgDisabled };

    export default {
        name: 'PHLifeStyle',

        components: {
            'ph-table-form': PHTableForm,
        },

        mixins: [UpdateFormMixin],

        props: {
            form: {
                type: Object,
                default: () => ({}),
            },
            disabled: {
                type: Boolean,
                default: false,
            },
        },

        computed: {
            curForm: {
                get() {
                    return this.form;
                },

                set(v) {
                    this.$emit('update:form', v);
                },
            },

            formItems() {
                return [
                    // 体育锻炼
                    [
                        {
                            label: '体育锻炼',
                            style: singleLabelStyle,
                        },
                        {
                            formItems: [
                                [
                                    {
                                        label: '锻炼频率',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return !!this.curForm[PUBLIC_HEALTHY_FORM_KEYS.exerciseFrequency].value;
                                        },
                                    },
                                    {
                                        label: '',
                                        formItemType: FORM_ITEM_TYPE.radioGroup,
                                        options: PUBLIC_HEALTHY_OPTION.exerciseFrequency,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.exerciseFrequency].value,
                                        valueNonEmpty: () => {
                                            return !!this.curForm[PUBLIC_HEALTHY_FORM_KEYS.exerciseFrequency].value;
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.exerciseFrequency, 'value' ], v);
                                            },

                                            change: (v) => {
                                                if (v === 4) {
                                                    const form = { ...this.curForm };
                                                    form[PUBLIC_HEALTHY_FORM_KEYS.eachExerciseTime] = '';
                                                    form[PUBLIC_HEALTHY_FORM_KEYS.exerciseTime] = '';
                                                    form[PUBLIC_HEALTHY_FORM_KEYS.exerciseWay] = '';
                                                    this.curForm = form;
                                                }
                                            },
                                        },
                                        disabled: this.disabled,
                                    },
                                ],
                                [
                                    {
                                        label: '每次锻炼时间',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.eachExerciseTime] !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.exerciseFrequency].value === 4;
                                        },
                                    },
                                    {
                                        label: '',
                                        formItemType: FORM_ITEM_TYPE.input,
                                        unit: '分钟',
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.eachExerciseTime],
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.eachExerciseTime] !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.exerciseFrequency].value === 4;
                                        },
                                        disabled: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.exerciseFrequency].value === 4 || this.disabled,
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.eachExerciseTime ], v);
                                            },
                                        },
                                        maxLength: 4,
                                        type: 'number',
                                    },
                                    {
                                        label: '坚持锻炼时间',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.exerciseTime] !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.exerciseFrequency].value === 4;
                                        },
                                    },
                                    {
                                        label: '',
                                        formItemType: FORM_ITEM_TYPE.input,
                                        unit: '年',
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.exerciseTime],
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.exerciseTime] !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.exerciseFrequency].value === 4;
                                        },
                                        disabled: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.exerciseFrequency].value === 4 || this.disabled,
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.exerciseTime ], v);
                                            },
                                        },
                                        maxLength: 3,
                                        type: 'number',
                                    },
                                ],
                                [
                                    {
                                        label: '锻炼方式',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.exerciseWay]?.trim() !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.exerciseFrequency].value === 4;
                                        },
                                    },
                                    {
                                        label: '',
                                        formItemType: FORM_ITEM_TYPE.input,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.exerciseWay],
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.exerciseWay]?.trim() !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.exerciseFrequency].value === 4;
                                        },
                                        disabled: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.exerciseFrequency].value === 4 || this.disabled,
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.exerciseWay ], v);
                                            },
                                        },
                                        maxLength: 50,
                                    },
                                ],
                            ],
                        },
                    ],

                    // 饮食习惯
                    [
                        {
                            label: '饮食习惯',
                            style: singleLabelStyle,
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.eatingHabits].value.length > 0;
                            },
                        },
                        {
                            label: '',
                            formItemType: FORM_ITEM_TYPE.checkboxGroup,
                            options: PUBLIC_HEALTHY_OPTION.eatingHabits,
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.eatingHabits].value,
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.eatingHabits].value.length > 0;
                            },
                            on: {
                                input: (v) => {
                                    this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.eatingHabits, 'value' ], v);
                                },
                            },
                            disabled: this.disabled,
                        },
                    ],

                    // 吸烟情况
                    [
                        {
                            label: '吸烟情况',
                            style: singleLabelStyle,
                        },
                        {
                            formItems: [
                                [
                                    {
                                        label: '吸烟情况',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.smokingStatus].value !== '';
                                        },
                                    },
                                    {
                                        label: '',
                                        formItemType: FORM_ITEM_TYPE.radioGroup,
                                        options: PUBLIC_HEALTHY_OPTION.smokingStatus,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.smokingStatus].value,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.smokingStatus].value !== '';
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.smokingStatus,'value' ], v);
                                            },

                                            change: (v) => {
                                                const clearKeys = [];
                                                if (v === 1) {
                                                    clearKeys.push(
                                                        PUBLIC_HEALTHY_FORM_KEYS.dailySmokingVolume,
                                                        PUBLIC_HEALTHY_FORM_KEYS.ageOfStartingSmoking,
                                                        PUBLIC_HEALTHY_FORM_KEYS.quitSmokingAge,
                                                    );
                                                } else if (v === 2) {
                                                    clearKeys.push(
                                                        PUBLIC_HEALTHY_FORM_KEYS.dailySmokingVolume,
                                                    );
                                                } else {
                                                    clearKeys.push(
                                                        PUBLIC_HEALTHY_FORM_KEYS.quitSmokingAge,
                                                    );
                                                }

                                                const form = { ...this.curForm };
                                                for (const key of clearKeys) {
                                                    form[key] = '';
                                                }
                                                this.curForm = form;
                                            },
                                        },
                                        disabled: this.disabled,
                                    },
                                ],
                                [
                                    {
                                        label: '日吸烟量',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.dailySmokingVolume] !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.smokingStatus].value === 1 || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.smokingStatus].value === 2;
                                        },
                                    },
                                    {
                                        label: '',
                                        formItemType: FORM_ITEM_TYPE.input,
                                        unit: '支',
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.dailySmokingVolume],
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.dailySmokingVolume] !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.smokingStatus].value === 1 || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.smokingStatus].value === 2;
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.dailySmokingVolume ], v);
                                            },
                                        },
                                        maxLength: 3,
                                        disabled: this.disabled || (
                                            [ 1,2 ].includes(this.curForm[PUBLIC_HEALTHY_FORM_KEYS.smokingStatus].value)
                                        ),
                                        type: 'number',
                                    },
                                ],
                                [
                                    {
                                        label: '开始吸烟年龄',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.ageOfStartingSmoking] !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.smokingStatus].value === 1;
                                        },
                                    },
                                    {
                                        label: '',
                                        formItemType: FORM_ITEM_TYPE.input,
                                        unit: '岁',
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.ageOfStartingSmoking],
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.ageOfStartingSmoking] !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.smokingStatus].value === 1;
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.ageOfStartingSmoking ], v);
                                            },
                                        },
                                        maxLength: 3,
                                        disabled: this.disabled || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.smokingStatus].value === 1,
                                        type: 'number',
                                    },
                                    {
                                        label: '戒烟年龄',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.quitSmokingAge] !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.smokingStatus].value !== 2;
                                        },
                                    },
                                    {
                                        label: '',
                                        formItemType: FORM_ITEM_TYPE.input,
                                        unit: '岁',
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.quitSmokingAge],
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.quitSmokingAge] !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.smokingStatus].value !== 2;
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.quitSmokingAge ], v);
                                            },
                                        },
                                        maxLength: 3,
                                        disabled: this.disabled || (
                                            [1,3].includes(this.curForm[PUBLIC_HEALTHY_FORM_KEYS.smokingStatus].value)
                                        ),
                                        type: 'number',
                                    },
                                ],
                            ],
                        },
                    ],

                    // 饮酒情况
                    [
                        {
                            label: '饮酒情况',
                            style: singleLabelStyle,
                        },
                        {
                            formItems: [
                                [
                                    {
                                        label: '饮酒频率',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value !== '';
                                        },
                                    },
                                    {
                                        label: '',
                                        formItemType: FORM_ITEM_TYPE.radioGroup,
                                        options: PUBLIC_HEALTHY_OPTION.drinkingFrequency,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value !== '';
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency,'value' ], v);
                                            },
                                        },
                                        disabled: this.disabled,
                                    },
                                ],
                                [
                                    {
                                        label: '平均日饮酒量',
                                        style: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value === 1 ? {
                                            ...disabledBgStyle,...singleLabelStyle,
                                        } : singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.averageDailyAlcoholConsumption] !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value === 1;
                                        },
                                    },
                                    {
                                        label: '',
                                        unit: '两',
                                        formItemType: FORM_ITEM_TYPE.input,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.averageDailyAlcoholConsumption],
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.averageDailyAlcoholConsumption] !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value === 1;
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.averageDailyAlcoholConsumption ], v);
                                            },
                                        },
                                        maxLength: 3,
                                        type: 'number',
                                        disabled: this.disabled || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value === 1,
                                    },
                                ],
                                [
                                    {
                                        label: '是否戒酒',
                                        style: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value === 1 ? {
                                            ...disabledBgStyle,...singleLabelStyle,
                                        } : singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.isQuitDrinking].value !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value === 1;
                                        },
                                    },
                                    {
                                        label: '',
                                        formItemType: FORM_ITEM_TYPE.radioGroup,
                                        options: PUBLIC_HEALTHY_OPTION.stopDrinking,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.isQuitDrinking].value,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.isQuitDrinking].value !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value === 1;
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.isQuitDrinking, 'value' ], v);
                                            },
                                        },
                                        style: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value === 1 && disabledBgStyle,
                                        disabled: this.disabled || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value === 1,
                                    },
                                    {
                                        label: '戒酒年龄',
                                        style: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value === 1 ? {
                                            ...disabledBgStyle,...singleLabelStyle,
                                        } : singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return !(this.curForm[PUBLIC_HEALTHY_FORM_KEYS.isQuitDrinking] === 2 &&
                                                this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value !== 1 &&
                                                this.curForm[PUBLIC_HEALTHY_FORM_KEYS.stopDrinkingAge] === '');
                                        },
                                    },
                                    {
                                        label: '',
                                        formItemType: FORM_ITEM_TYPE.input,
                                        unit: '岁',
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.stopDrinkingAge],
                                        valueNonEmpty: () => {
                                            return !(this.curForm[PUBLIC_HEALTHY_FORM_KEYS.isQuitDrinking] === 2 &&
                                                this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value !== 1 &&
                                                this.curForm[PUBLIC_HEALTHY_FORM_KEYS.stopDrinkingAge] === '');
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.stopDrinkingAge ], v);
                                            },
                                        },
                                        maxLength: 3,
                                        type: 'number',
                                        disabled: this.disabled || (
                                            this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value === 1
                                        ) || (
                                            this.curForm[PUBLIC_HEALTHY_FORM_KEYS.isQuitDrinking].value === 1
                                        ),
                                    },
                                ],
                                [
                                    {
                                        label: '开始饮酒年龄',
                                        style: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value === 1 ? {
                                            ...disabledBgStyle,...singleLabelStyle,
                                        } : singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.startDrinkingAge] !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value === 1;
                                        },
                                    },
                                    {
                                        label: '',
                                        formItemType: FORM_ITEM_TYPE.input,
                                        unit: '岁',
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.startDrinkingAge],
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.startDrinkingAge] !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value === 1;
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.startDrinkingAge ], v);
                                            },
                                        },
                                        maxLength: 3,
                                        type: 'number',
                                        disabled: this.disabled || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value === 1,
                                    },
                                    {
                                        label: '近一年内是否曾醉酒',
                                        style: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value === 1 ? {
                                            ...disabledBgStyle,...singleLabelStyle,
                                        } : singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.isDrunkInThePastYear].value !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value === 1;
                                        },
                                    },
                                    {
                                        label: '',
                                        formItemType: FORM_ITEM_TYPE.radioGroup,
                                        options: PUBLIC_HEALTHY_OPTION.drunk,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.isDrunkInThePastYear].value,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.isDrunkInThePastYear].value !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value === 1;
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.isDrunkInThePastYear,'value' ], v);
                                            },
                                        },
                                        style: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value === 1 && disabledBgStyle,
                                        disabled: this.disabled || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value === 1,
                                    },
                                ],
                                [
                                    {
                                        label: '饮酒种类',
                                        style: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value === 1 ? {
                                            ...disabledBgStyle,...singleLabelStyle,
                                        } : singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingHabits].value.length || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value === 1;
                                        },
                                    },
                                    {
                                        label: '',
                                        formItemType: FORM_ITEM_TYPE.checkboxGroup,
                                        options: PUBLIC_HEALTHY_OPTION.drinkingType,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingHabits].value,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingHabits].value !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value === 1;
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.drinkingHabits,'value' ], v);
                                            },
                                        },
                                        style: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value === 1 && disabledBgStyle,
                                        disabled: this.disabled || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.drinkingFrequency].value === 1,
                                    },
                                ],
                            ],
                        },
                    ],

                    // 职业病危害因素
                    [
                        {
                            label: '职业病危害因素',
                            rowSpan: 6,
                            style: singleLabelStyle,
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value !== '';
                            },
                        },
                        {
                            formItems: [
                                [
                                    {
                                        formItemType: FORM_ITEM_TYPE.radioGroup,
                                        options: PUBLIC_HEALTHY_OPTION.occupationalHazardFactors,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value !== '';
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors,'value' ], v);
                                            },

                                            change: (v) => {
                                                if (v === 1) {
                                                    const form = { ...this.curForm };

                                                    form[PUBLIC_HEALTHY_FORM_KEYS.typeOfWork] = '';
                                                    form[PUBLIC_HEALTHY_FORM_KEYS.timeInBusiness] = '';
                                                    form[PUBLIC_HEALTHY_FORM_KEYS.chemicalMaterial].value = '';
                                                    form[PUBLIC_HEALTHY_FORM_KEYS.chemicalMaterial].text = '';
                                                    form[PUBLIC_HEALTHY_FORM_KEYS.chemicalMaterial].hasProtect = '';
                                                    form[PUBLIC_HEALTHY_FORM_KEYS.dust].value = '';
                                                    form[PUBLIC_HEALTHY_FORM_KEYS.dust].text = '';
                                                    form[PUBLIC_HEALTHY_FORM_KEYS.dust].hasProtect = '';
                                                    form[PUBLIC_HEALTHY_FORM_KEYS.radioactiveMaterial].value = '';
                                                    form[PUBLIC_HEALTHY_FORM_KEYS.radioactiveMaterial].text = '';
                                                    form[PUBLIC_HEALTHY_FORM_KEYS.radioactiveMaterial].hasProtect = '';
                                                    form[PUBLIC_HEALTHY_FORM_KEYS.physicalFactors].value = '';
                                                    form[PUBLIC_HEALTHY_FORM_KEYS.physicalFactors].text = '';
                                                    form[PUBLIC_HEALTHY_FORM_KEYS.physicalFactors].hasProtect = '';
                                                    form[PUBLIC_HEALTHY_FORM_KEYS.otherFactors].value = '';
                                                    form[PUBLIC_HEALTHY_FORM_KEYS.otherFactors].text = '';
                                                    form[PUBLIC_HEALTHY_FORM_KEYS.otherFactors].hasProtect = '';

                                                    this.curForm = form;
                                                }
                                            },
                                        },
                                        disabled: this.disabled,
                                    },
                                ],
                                [
                                    {
                                        label: '工种',
                                        style: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1 ? {
                                            ...disabledBgStyle,...singleLabelStyle,
                                        } : singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.typeOfWork]?.trim() !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1;
                                        },
                                    },
                                    {
                                        formItemType: FORM_ITEM_TYPE.input,
                                        label: '',
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.typeOfWork],
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.typeOfWork]?.trim() !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1;
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.typeOfWork ], v);
                                            },
                                        },
                                        maxLength: 20,
                                        disabled: this.disabled ||
                                            this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1,
                                    },
                                    {
                                        label: '从业时间',
                                        style: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1 ? {
                                            ...disabledBgStyle,...singleLabelStyle,
                                        } : singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.timeInBusiness] !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1;
                                        },
                                    },
                                    {
                                        formItemType: FORM_ITEM_TYPE.input,
                                        label: '',
                                        unit: '年',
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.timeInBusiness],
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.timeInBusiness] !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1;
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.timeInBusiness ], v);
                                            },
                                        },
                                        maxLength: 3,
                                        type: 'number',
                                        disabled: this.disabled ||
                                            this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1,
                                    },
                                ],
                                [

                                    {
                                        label: '毒物种类',
                                        rowSpan: 5,
                                        style: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1 ? {
                                            ...disabledBgStyle,...singleLabelStyle,
                                        } : singleLabelStyle,
                                    },
                                    {
                                        formItems: [
                                            [
                                                {
                                                    label: '化学物质',
                                                    style: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1 ? {
                                                        ...disabledBgStyle,
                                                        flex: 'none',
                                                        width: 'calc(25% - 30px)',
                                                    } : {
                                                        flex: 'none',
                                                        width: 'calc(25% - 30px)',
                                                    },
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.chemicalMaterial].value?.trim() !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1;
                                                    },
                                                },
                                                {
                                                    label: '',
                                                    formItemType: FORM_ITEM_TYPE.input,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.chemicalMaterial].value,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.chemicalMaterial].value?.trim() !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1;
                                                    },
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.chemicalMaterial,'value' ], v);
                                                        },
                                                    },
                                                    maxLength: 20,
                                                    disabled: this.disabled ||
                                                        this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1,
                                                },
                                                {
                                                    label: '防护措施',
                                                    style: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1 ? {
                                                        ...disabledBgStyle,...singleLabelStyle,
                                                    } : singleLabelStyle,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.chemicalMaterial].hasProtect !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1;
                                                    },
                                                },
                                                {
                                                    formItemType: FORM_ITEM_TYPE.select,
                                                    label: '',
                                                    options: PUBLIC_HEALTHY_OPTION.occupationalHazardFactors,
                                                    disabled: this.disabled ||
                                                        this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.chemicalMaterial].hasProtect,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.chemicalMaterial].hasProtect !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1;
                                                    },
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.chemicalMaterial,'hasProtect' ], v);
                                                        },

                                                        change: (v) => {
                                                            if (v === 1) {
                                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.chemicalMaterial,'text' ], '');
                                                            }
                                                        },
                                                    },
                                                },
                                                {
                                                    formItemType: FORM_ITEM_TYPE.input,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.chemicalMaterial].text,
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.chemicalMaterial,'text' ], v);
                                                        },
                                                    },
                                                    maxLength: 20,
                                                    disabled: this.disabled ||
                                                        this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1 ||
                                                        this.curForm[PUBLIC_HEALTHY_FORM_KEYS.chemicalMaterial].hasProtect === 1,
                                                },
                                            ],
                                            [
                                                {
                                                    label: '粉尘',
                                                    style: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1 ? {
                                                        ...disabledBgStyle,
                                                        flex: 'none',
                                                        width: 'calc(25% - 30px)',
                                                    } : {
                                                        flex: 'none',
                                                        width: 'calc(25% - 30px)',
                                                    },
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.dust].value?.trim() !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1;
                                                    },
                                                },
                                                {
                                                    label: '',
                                                    formItemType: FORM_ITEM_TYPE.input,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.dust].value,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.dust].value?.trim() !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1;
                                                    },
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.dust,'value' ], v);
                                                        },
                                                    },
                                                    maxLength: 20,
                                                    disabled: this.disabled ||
                                                        this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1,
                                                },
                                                {
                                                    label: '防护措施',
                                                    style: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1 ? {
                                                        ...disabledBgStyle,...singleLabelStyle,
                                                    } : singleLabelStyle,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.dust].hasProtect !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1;
                                                    },
                                                },
                                                {
                                                    formItemType: FORM_ITEM_TYPE.select,
                                                    label: '',
                                                    options: PUBLIC_HEALTHY_OPTION.occupationalHazardFactors,
                                                    disabled: this.disabled ||
                                                        this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.dust].hasProtect,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.dust].hasProtect !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1;
                                                    },
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.dust,'hasProtect' ], v);
                                                        },

                                                        change: (v) => {
                                                            if (v === 1) {
                                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.dust,'text' ], '');
                                                            }
                                                        },
                                                    },
                                                },
                                                {
                                                    formItemType: FORM_ITEM_TYPE.input,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.dust].text,
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.dust,'text' ], v);
                                                        },
                                                    },
                                                    maxLength: 20,
                                                    disabled: this.disabled ||
                                                        this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1 ||
                                                        this.curForm[PUBLIC_HEALTHY_FORM_KEYS.dust].hasProtect === 1,
                                                },
                                            ],
                                            [
                                                {
                                                    label: '放射物质',
                                                    style: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1 ? {
                                                        ...disabledBgStyle,
                                                        flex: 'none',
                                                        width: 'calc(25% - 30px)',
                                                    } : {
                                                        flex: 'none',
                                                        width: 'calc(25% - 30px)',
                                                    },
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.radioactiveMaterial].value?.trim() !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1;
                                                    },
                                                },
                                                {
                                                    label: '',
                                                    formItemType: FORM_ITEM_TYPE.input,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.radioactiveMaterial].value,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.radioactiveMaterial].value?.trim() !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1;
                                                    },
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.radioactiveMaterial,'value' ], v);
                                                        },
                                                    },
                                                    maxLength: 20,
                                                    disabled: this.disabled ||
                                                        this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1,
                                                },
                                                {
                                                    label: '防护措施',
                                                    style: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1 ? {
                                                        ...disabledBgStyle,...singleLabelStyle,
                                                    } : singleLabelStyle,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.radioactiveMaterial].hasProtect !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1;
                                                    },
                                                },
                                                {
                                                    formItemType: FORM_ITEM_TYPE.select,
                                                    label: '',
                                                    options: PUBLIC_HEALTHY_OPTION.occupationalHazardFactors,
                                                    disabled: this.disabled ||
                                                        this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.radioactiveMaterial].hasProtect,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.radioactiveMaterial].hasProtect !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1;
                                                    },
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.radioactiveMaterial,'hasProtect' ], v);
                                                        },

                                                        change: (v) => {
                                                            if (v === 1) {
                                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.radioactiveMaterial,'text' ], '');
                                                            }
                                                        },
                                                    },
                                                },
                                                {
                                                    formItemType: FORM_ITEM_TYPE.input,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.radioactiveMaterial].text,
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.radioactiveMaterial,'text' ], v);
                                                        },
                                                    },
                                                    maxLength: 20,
                                                    disabled: this.disabled ||
                                                        this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1 ||
                                                        this.curForm[PUBLIC_HEALTHY_FORM_KEYS.radioactiveMaterial].hasProtect === 1,
                                                },
                                            ],
                                            [
                                                {
                                                    label: '物理因素',
                                                    style: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1 ? {
                                                        ...disabledBgStyle,
                                                        flex: 'none',
                                                        width: 'calc(25% - 30px)',
                                                    } : {
                                                        flex: 'none',
                                                        width: 'calc(25% - 30px)',
                                                    },
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.physicalFactors].value?.trim() !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1;
                                                    },
                                                },
                                                {
                                                    label: '',
                                                    formItemType: FORM_ITEM_TYPE.input,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.physicalFactors].value,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.physicalFactors].value?.trim() !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1;
                                                    },
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.physicalFactors,'value' ], v);
                                                        },
                                                    },
                                                    maxLength: 20,
                                                    disabled: this.disabled ||
                                                        this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1,
                                                },
                                                {
                                                    label: '防护措施',
                                                    style: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1 ? {
                                                        ...disabledBgStyle,...singleLabelStyle,
                                                    } : singleLabelStyle,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.physicalFactors].hasProtect !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1;
                                                    },
                                                },
                                                {
                                                    formItemType: FORM_ITEM_TYPE.select,
                                                    label: '',
                                                    options: PUBLIC_HEALTHY_OPTION.occupationalHazardFactors,
                                                    disabled: this.disabled ||
                                                        this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.physicalFactors].hasProtect,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.physicalFactors].hasProtect !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1;
                                                    },
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.physicalFactors,'hasProtect' ], v);
                                                        },

                                                        change: (v) => {
                                                            if (v === 1) {
                                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.physicalFactors,'text' ], '');
                                                            }
                                                        },
                                                    },
                                                },
                                                {
                                                    formItemType: FORM_ITEM_TYPE.input,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.physicalFactors].text,
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.physicalFactors,'text' ], v);
                                                        },
                                                    },
                                                    maxLength: 20,
                                                    disabled: this.disabled ||
                                                        this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1 ||
                                                        this.curForm[PUBLIC_HEALTHY_FORM_KEYS.physicalFactors].hasProtect === 1,
                                                },
                                            ],
                                            [
                                                {
                                                    label: '其他',
                                                    style: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1 ? {
                                                        ...disabledBgStyle,
                                                        flex: 'none',
                                                        width: 'calc(25% - 30px)',
                                                    } : {
                                                        flex: 'none',
                                                        width: 'calc(25% - 30px)',
                                                    },
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.otherFactors].value?.trim() !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1;
                                                    },
                                                },
                                                {
                                                    label: '',
                                                    formItemType: FORM_ITEM_TYPE.input,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.otherFactors].value,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.otherFactors].value?.trim() !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1;
                                                    },
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.otherFactors,'value' ], v);
                                                        },
                                                    },
                                                    maxLength: 20,
                                                    disabled: this.disabled ||
                                                        this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1,
                                                },
                                                {
                                                    label: '防护措施',
                                                    style: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1 ? {
                                                        ...disabledBgStyle,...singleLabelStyle,
                                                    } : singleLabelStyle,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.otherFactors].hasProtect !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1;
                                                    },
                                                },
                                                {
                                                    formItemType: FORM_ITEM_TYPE.select,
                                                    label: '',
                                                    options: PUBLIC_HEALTHY_OPTION.occupationalHazardFactors,
                                                    disabled: this.disabled ||
                                                        this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.otherFactors].hasProtect,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.otherFactors].hasProtect !== '' || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1;
                                                    },
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.otherFactors,'hasProtect' ], v);
                                                        },

                                                        change: (v) => {
                                                            if (v === 1) {
                                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.otherFactors,'text' ], '');
                                                            }
                                                        },
                                                    },
                                                },
                                                {
                                                    formItemType: FORM_ITEM_TYPE.input,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.otherFactors].text,
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.otherFactors,'text' ], v);
                                                        },
                                                    },
                                                    maxLength: 20,
                                                    disabled: this.disabled ||
                                                        this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hasOccupationalHazardFactors].value === 1 ||
                                                        this.curForm[PUBLIC_HEALTHY_FORM_KEYS.otherFactors].hasProtect === 1,
                                                },
                                            ],
                                        ],
                                    },
                                ],
                            ],
                        },
                    ],
                ];
            },
        },
    };
</script>

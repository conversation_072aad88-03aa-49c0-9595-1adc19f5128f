<template>
    <abc-flex vertical="vertical" :gap="8">
        <abc-text>
            还有<abc-text theme="warning-light" bold>
                {{ list.length }}
            </abc-text>个项目未完成填写
        </abc-text>
        <div class="display-empty-project-wrapper">
            <abc-row :gutter="[16,3]" class="display-empty-project-content" wrap="wrap">
                <abc-col v-for="(item,idx) in list" :key="idx" :span="12">
                    <div class="ellipsis" style="max-width: 100%;">
                        · {{ item.label }}
                    </div>
                </abc-col>
            </abc-row>
        </div>
    </abc-flex>
</template>
<script>
    export default {
        name: 'DisplayEmptyProjectDialog',
        props: {
            list: {
                type: Array,
                default: () => ([]),
            },
        },
        data() {
            return {

            };
        },
    };
</script>

<style scoped lang="scss">
.display-empty-project-wrapper {
    width: 390px;
    padding: 8px 12px;
    color: #e5892d;
    border: 1px solid #eaedf1;
    border-radius: var(--abc-border-radius-small);

    .display-empty-project-content {
        min-height: 60px;
        max-height: 142px;
        overflow-y: auto;
    }
}
</style>

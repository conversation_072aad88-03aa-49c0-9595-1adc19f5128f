<template>
    <div class="ph-tooth-item-wrapper">
        <div class="ph-tooth-item-label">
            齿列
        </div>

        <div class="ph-checkbox-options">
            <div class="ph-checkbox-option-item">
                <abc-checkbox 
                    :value="!!curValue[0].value" 
                    :disabled="disabled"
                    @change="v => handleChange(0, v, options[0].value)"
                >
                    {{ options[0].label }}
                </abc-checkbox>
            </div>

            <div class="ph-checkbox-option-item">
                <abc-checkbox 
                    :value="!!curValue[1].value" 
                    :disabled="disabled"
                    @change="v => handleChange(1, v, options[1].value)"
                >
                    {{ options[1].label }}
                </abc-checkbox>

                <tooth-selector 
                    v-if="curValue[1].value"
                    v-model="curValue[1].toothNos" 
                    fixed
                    :disabled="disabled"
                ></tooth-selector>
            </div>

            <div class="ph-checkbox-option-item">
                <abc-checkbox 
                    :value="!!curValue[2].value" 
                    :disabled="disabled"
                    @change="v => handleChange(2, v, options[2].value)"
                >
                    {{ options[2].label }}
                </abc-checkbox>

                <tooth-selector 
                    v-if="curValue[2].value"
                    v-model="curValue[2].toothNos" 
                    fixed
                    :disabled="disabled"
                ></tooth-selector>
            </div>

            <div class="ph-checkbox-option-item">
                <abc-checkbox 
                    :value="!!curValue[3].value" 
                    :disabled="disabled"
                    @change="v => handleChange(3, v, options[3].value)"
                >
                    {{ options[3].label }}
                </abc-checkbox>

                <tooth-selector 
                    v-if="curValue[3].value"
                    v-model="curValue[3].toothNos" 
                    fixed
                    :disabled="disabled"
                ></tooth-selector>
            </div>
        </div>
    </div>
</template>

<script>
    import { 
        DENTITION_ENUM,
        covertObjectToOptions,
    } from '@/views-hospital/inspect-diagnosis/utils/public-healthy-constant';
    import ToothSelector from '@/views-dentistry/outpatient/common/medical-record/tooth-selector.vue';

    export default {
        name: 'PHToothItem',

        components: {
            ToothSelector,
        },

        props: {
            value: {
                type: Array,
                default: () => ([
                    {}, {}, {}, {},
                ]),
            },

            disabled: Boolean,
        },

        computed: {
            curValue: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },

            options() {
                return covertObjectToOptions(DENTITION_ENUM);
            },
        },

        methods: {
            handleChange(idx, v, label) {
                const curValue = [ ...this.curValue ];

                curValue[idx].value = v ? label : '';

                this.curValue = curValue;
            },
        },
    };
</script>

<style lang='scss'>
.ph-tooth-item-wrapper {
    position: relative;
    height: 100%;
    min-height: 32px;

    .ph-tooth-item-label {
        position: absolute;
        top: 50%;
        left: 12px;
        z-index: 9;
        color: $T2;
        transform: translateY(-50%);
    }

    .ph-checkbox-options {
        display: flex;
        flex-wrap: wrap;
        gap: 6px 16px;
        height: 100%;
        padding: 6px;
        padding-left: 62px;

        .ph-checkbox-option-item {
            display: inline-flex;
            gap: 6px;
        }

        .abc-checkbox-wrapper {
            margin-left: 0;
        }
    }
}
</style>
<template>
    <abc-popover
        width="228"
        placement="top"
        trigger="click"
        theme="yellow"
        :disabled="isUrl"
    >
        <div slot="reference" class="hand-sign-upload">
            <img
                v-if="isUrl"
                :src="curValue"
                height="26px"
                alt=""
                @click="previewIt()"
            />
            <abc-text
                v-else
                theme="warning-light"
            >
                未设置手写签名
            </abc-text>
            <div v-if="isUrl && !disabledDelete" class="delete-bar" @click.stop="deleteUrl"></div>
        </div>


        <abc-flex
            vertical
            :gap="14"
            align="center"
            style="width: 196px;"
        >
            <abc-text size="mini">
                审核医生未设置手写签名，完成 设置后，刷新页面自动更新签名或手动上传签名
            </abc-text>
            <audit-upload-item
                v-model="curValue"
                file-path="inspect"
                :width="120"
                size="small"
                :show-icon="false"
                :need-re-upload="false"
                placeholder="上传签名"
                @input="$emit('upload')"
            ></audit-upload-item>
        </abc-flex>

        <abc-preview
            v-if="showPreview"
            v-model="showPreview"
            :index="0"
            enable-compress
            :lists="[{ url: curValue }]"
        ></abc-preview>
    </abc-popover>
</template>

<script>
    import AuditUploadItem from 'components/file-upload/index.vue';
    import { isImgUrl } from 'views/physical-examination/assessment/utils';

    export default {
        name: 'HandSignUpload',
        components: { AuditUploadItem },
        props: {
            value: {
                type: String,
                default: '',
            },
            disabledDelete: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                showPreview: false,
            };
        },
        computed: {
            curValue: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            isUrl() {
                return this.isImgUrl(this.curValue);
            },
        },
        methods: {
            isImgUrl,
            previewIt() {
                this.showPreview = true;
            },
            deleteUrl() {
                this.curValue = '';
            },
        },
    };
</script>

<style lang="scss" scoped>
.hand-sign-upload {
    position: relative;
    cursor: pointer;

    .delete-bar {
        position: absolute;
        top: -8px;
        right: -8px;
        width: 16px;
        height: 16px;
        visibility: hidden;
        background: url('~@/assets/images/<EMAIL>') no-repeat center;
        background-size: contain;
        opacity: 0;
        transition: opacity 0.2s;

        &:hover {
            background: url('~@/assets/images/<EMAIL>') no-repeat center;
            background-size: contain;
        }
    }

    &:hover .delete-bar {
        visibility: visible;
        opacity: 1;
    }
}
</style>

<template>
    <!-- 查体 -->
    <div class="ph-check-body-wrapper">
        <ph-table-form :form-items="formItems"></ph-table-form>
    </div>
</template>

<script>
    import {
        PHTableForm,
    } from './components';
    import {
        FORM_ITEM_TYPE,
        PUBLIC_HEALTHY_FORM_KEYS,
        PUBLIC_HEALTHY_OPTION,
    } from '@/views-hospital/inspect-diagnosis/utils/public-healthy-constant';
    import UpdateFormMixin from './update-form';
    import themeStyle from 'styles/theme.module.scss';
    import {
        getPublicHealthConfig,
    } from 'views/physical-examination/integrated/public-health-sync/distribute/distribute-helper';

    const singleLabelStyle = {
        width: '120px',
        flex: 'none',
    };
    const disabledBgStyle = { 'background-color': themeStyle.abcBgDisabled };
    export default {
        name: 'PHCheckBody',

        components: {
            'ph-table-form': PHTableForm,
        },

        mixins: [UpdateFormMixin],

        props: {
            form: {
                type: Object,
                default: () => ({}),
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            patient: {
                type: Object,
                default: () => ({}),
            },
        },
        computed: {
            curForm: {
                get() {
                    const { breastMultiSelect } = getPublicHealthConfig().publicHealthForm;
                    const breastMultiSelectValue = this.form[PUBLIC_HEALTHY_FORM_KEYS.mammaryGland]?.value;
                    /**
                     * 兼容旧数据，如果乳腺是单选
                     */
                    if (breastMultiSelect && !Array.isArray(breastMultiSelectValue)) {
                        return {
                            ...this.form,
                            [PUBLIC_HEALTHY_FORM_KEYS.mammaryGland]: {
                                value: breastMultiSelectValue ? [ breastMultiSelectValue ] : [],
                                text: this.form[PUBLIC_HEALTHY_FORM_KEYS.mammaryGland]?.text || '',
                            },
                        };
                    }
                    return this.form;
                },

                set(v) {
                    this.$emit('update:form', v);
                },
            },

            sex() {
                return this.patient.sex;
            },

            formItems() {
                const { breastMultiSelect } = getPublicHealthConfig().publicHealthForm;

                const currentBreastConfig = breastMultiSelect ? [
                    {
                        label: '乳腺',
                        style: singleLabelStyle,
                        valueNonEmpty: () => {
                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.mammaryGland].value.length > 0;
                        },
                    },
                    {
                        formItems: [
                            [
                                {
                                    formItemType: FORM_ITEM_TYPE.checkboxGroup,
                                    options: PUBLIC_HEALTHY_OPTION.mammaryGland,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.mammaryGland].value,
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.mammaryGland].value.length > 0;
                                    },
                                    customStyle: {
                                        checkboxOptions: {
                                            width: '100%',
                                        },
                                    },
                                    style: {
                                        flex: 'none',
                                        width: 'calc(50% + 60px)',
                                    },
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.mammaryGland, 'value' ], v);
                                        },
                                    },
                                    disabled: this.disabled,
                                },
                                {
                                    formItemType: FORM_ITEM_TYPE.input,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.mammaryGland].text,
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.mammaryGland, 'text' ], v);
                                        },
                                    },
                                    maxLength: 20,
                                    disabled: this.disabled || this.checkDisabled(this.curForm[PUBLIC_HEALTHY_FORM_KEYS.mammaryGland].value, PUBLIC_HEALTHY_OPTION.mammaryGland),
                                },
                            ],
                        ],
                    },
                ] : [
                    {
                        label: '乳腺',
                        style: singleLabelStyle,
                        valueNonEmpty: () => {
                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.mammaryGland].value !== '';
                        },
                    },
                    {
                        formItems: [
                            [
                                {
                                    formItemType: FORM_ITEM_TYPE.radioGroup,
                                    options: PUBLIC_HEALTHY_OPTION.mammaryGland,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.mammaryGland].value,
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.mammaryGland].value !== '';
                                    },
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.mammaryGland, 'value' ], v);
                                        },
                                        change: (v) => {
                                            if (v !== 5) {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.mammaryGland, 'text' ], '');
                                            }
                                        },
                                    },
                                    disabled: this.disabled,
                                    style: {
                                        flex: 'none',
                                        width: 'calc(50% + 60px)',
                                    },
                                },
                                {
                                    formItemType: FORM_ITEM_TYPE.input,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.mammaryGland].text,
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.mammaryGland, 'text' ], v);
                                        },
                                    },
                                    maxLength: 20,
                                    disabled: this.disabled || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.mammaryGland].value !== 5,
                                },
                            ],
                        ],
                    },
                ];

                return [
                    [
                        {
                            label: '皮肤',
                            style: singleLabelStyle,
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.skin].value !== '';
                            },
                        },
                        {
                            formItems: [
                                [
                                    {
                                        formItemType: FORM_ITEM_TYPE.radioGroup,
                                        options: PUBLIC_HEALTHY_OPTION.skin,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.skin].value,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.skin].value !== '';
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.skin, 'value' ], v);
                                            },

                                            change: (v) => {
                                                if (v !== 7) {
                                                    this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.skin, 'text' ], '');
                                                }
                                            },
                                        },
                                        disabled: this.disabled,
                                    },
                                    {
                                        formItemType: FORM_ITEM_TYPE.input,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.skin].text,
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.skin, 'text' ], v);
                                            },
                                        },
                                        maxLength: 20,
                                        disabled: this.disabled || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.skin].value !== 7,
                                    },
                                ],
                            ],
                        },
                    ],
                    [
                        {
                            label: '巩膜',
                            style: singleLabelStyle,
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.sclera].value !== '';
                            },
                        },
                        {
                            formItems: [
                                [
                                    {
                                        formItemType: FORM_ITEM_TYPE.radioGroup,
                                        options: PUBLIC_HEALTHY_OPTION.sclera,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.sclera].value,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.sclera].value !== '';
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.sclera, 'value' ], v);
                                            },

                                            change: (v) => {
                                                if (v !== 4) {
                                                    this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.sclera, 'text' ], '');
                                                }
                                            },
                                        },
                                        disabled: this.disabled,
                                    },
                                    {
                                        formItemType: FORM_ITEM_TYPE.input,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.sclera].text,
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.sclera, 'text' ], v);
                                            },
                                        },
                                        maxLength: 20,
                                        disabled: this.disabled || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.sclera].value !== 4,
                                    },
                                ],
                            ],
                        },
                    ],
                    [
                        {
                            label: '淋巴结',
                            style: singleLabelStyle,
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.lymphNodes].value !== '';
                            },
                        },
                        {
                            formItems: [
                                [
                                    {
                                        formItemType: FORM_ITEM_TYPE.radioGroup,
                                        options: PUBLIC_HEALTHY_OPTION.lymphNodes,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.lymphNodes].value,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.lymphNodes].value !== '';
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.lymphNodes, 'value' ], v);
                                            },

                                            change: (v) => {
                                                if (v !== 4) {
                                                    this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.lymphNodes, 'text' ], '');
                                                }
                                            },
                                        },
                                        disabled: this.disabled,
                                    },
                                    {
                                        formItemType: FORM_ITEM_TYPE.input,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.lymphNodes].text,
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.lymphNodes, 'text' ], v);
                                            },
                                        },
                                        maxLength: 20,
                                        disabled: this.disabled || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.lymphNodes].value !== 4,
                                    },
                                ],
                            ],
                        },
                    ],
                    [
                        {
                            label: '肺',
                            style: singleLabelStyle,
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.barrelChest].value !== '';
                            },
                        },
                        {
                            formItems: [
                                [
                                    {
                                        label: '桶状胸',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.barrelChest].value !== '';
                                        },
                                    },
                                    {
                                        label: '',
                                        formItemType: FORM_ITEM_TYPE.radioGroup,
                                        options: PUBLIC_HEALTHY_OPTION.barrelChest,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.barrelChest].value,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.barrelChest].value !== '';
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.barrelChest, 'value' ], v);
                                            },
                                        },
                                        disabled: this.disabled,
                                    },
                                ],
                                [
                                    {
                                        label: '呼吸音',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.breathSounds].value !== '';
                                        },
                                    },
                                    {
                                        formItems: [
                                            [
                                                {
                                                    label: '',
                                                    formItemType: FORM_ITEM_TYPE.radioGroup,
                                                    options: PUBLIC_HEALTHY_OPTION.breathSounds,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.breathSounds].value,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.breathSounds].value !== '';
                                                    },
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.breathSounds, 'value' ], v);
                                                        },

                                                        change: (v) => {
                                                            if (v !== 2) {
                                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.breathSounds, 'text' ], '');
                                                            }
                                                        },
                                                    },
                                                    disabled: this.disabled,
                                                },
                                                {
                                                    formItemType: FORM_ITEM_TYPE.input,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.breathSounds].text,
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.breathSounds, 'text' ], v);
                                                        },
                                                    },
                                                    maxLength: 20,
                                                    disabled: this.disabled || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.breathSounds].value !== 2,
                                                },
                                            ],
                                        ],
                                    },
                                ],
                                [
                                    {
                                        label: '罗音',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.rale].value !== '';
                                        },
                                    },
                                    {
                                        formItems: [
                                            [
                                                {
                                                    label: '',
                                                    formItemType: FORM_ITEM_TYPE.radioGroup,
                                                    options: PUBLIC_HEALTHY_OPTION.rale,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.rale].value,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.rale].value !== '';
                                                    },
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.rale, 'value' ], v);
                                                        },

                                                        change: (v) => {
                                                            if (v !== 4) {
                                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.rale, 'text' ], '');
                                                            }
                                                        },
                                                    },
                                                    disabled: this.disabled,
                                                },
                                                {
                                                    formItemType: FORM_ITEM_TYPE.input,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.rale].text,
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.rale, 'text' ], v);
                                                        },
                                                    },
                                                    maxLength: 20,
                                                    disabled: this.disabled || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.rale].value !== 4,
                                                },
                                            ],
                                        ],
                                    },
                                ],
                            ],
                        },
                    ],
                    [
                        {
                            label: '心脏',
                            style: singleLabelStyle,
                            rowSpan: 2,
                        },
                        {
                            formItems: [
                                [
                                    {
                                        label: '心率',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.heartRate] !== '';
                                        },
                                    },
                                    {
                                        label: '',
                                        formItemType: FORM_ITEM_TYPE.input,
                                        unit: '次/分钟',
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.heartRate],
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.heartRate] !== '';
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.heartRate ], v);
                                            },
                                        },
                                        maxLength: 3,
                                        type: 'number',
                                        disabled: this.disabled,
                                        style: {
                                            flex: 'none',
                                            width: 'calc(50% - 60px)',
                                        },
                                    },
                                    {
                                        label: '心律',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.heartRhythm].value !== '';
                                        },
                                    },
                                    {
                                        label: '',
                                        formItemType: FORM_ITEM_TYPE.radioGroup,
                                        options: PUBLIC_HEALTHY_OPTION.heartRhythm,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.heartRhythm].value,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.heartRhythm].value !== '';
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.heartRhythm, 'value' ], v);
                                            },
                                        },
                                        disabled: this.disabled,
                                    },
                                ],
                                [
                                    {
                                        label: '杂音',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.noise].value !== '';
                                        },
                                    },
                                    {
                                        formItems: [
                                            [
                                                {
                                                    label: '',
                                                    formItemType: FORM_ITEM_TYPE.radioGroup,
                                                    options: PUBLIC_HEALTHY_OPTION.noise,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.noise].value,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.noise].value !== '';
                                                    },
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.noise, 'value' ], v);
                                                        },

                                                        change: (v) => {
                                                            if (v !== 2) {
                                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.noise, 'text' ], '');
                                                            }
                                                        },
                                                    },
                                                    disabled: this.disabled,
                                                },
                                                {
                                                    formItemType: FORM_ITEM_TYPE.input,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.noise].text,
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.noise, 'text' ], v);
                                                        },
                                                    },
                                                    maxLength: 20,
                                                    disabled: this.disabled || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.noise].value !== 2,
                                                },
                                            ],
                                        ],
                                    },
                                ],
                            ],
                        },
                    ],
                    [
                        {
                            label: '腹部',
                            style: singleLabelStyle,
                            rowSpan: 5,
                        },
                        {
                            formItems: [
                                [
                                    {
                                        label: '压痛',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.tenderness].value !== '';
                                        },
                                    },
                                    {
                                        formItems: [
                                            [
                                                {
                                                    label: '',
                                                    formItemType: FORM_ITEM_TYPE.radioGroup,
                                                    options: PUBLIC_HEALTHY_OPTION.tenderness,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.tenderness].value,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.tenderness].value !== '';
                                                    },
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.tenderness, 'value' ], v);
                                                        },

                                                        change: (v) => {
                                                            if (v !== 2) {
                                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.tenderness, 'text' ], '');
                                                            }
                                                        },
                                                    },
                                                    disabled: this.disabled,
                                                },
                                                {
                                                    formItemType: FORM_ITEM_TYPE.input,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.tenderness].text,
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.tenderness, 'text' ], v);
                                                        },
                                                    },
                                                    maxLength: 20,
                                                    disabled: this.disabled || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.tenderness].value !== 2,
                                                },
                                            ],
                                        ],
                                    },
                                ],
                                [
                                    {
                                        label: '包块',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.lumps].value !== '';
                                        },
                                    },
                                    {
                                        formItems: [
                                            [
                                                {
                                                    label: '',
                                                    formItemType: FORM_ITEM_TYPE.radioGroup,
                                                    options: PUBLIC_HEALTHY_OPTION.lumps,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.lumps].value,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.lumps].value !== '';
                                                    },
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.lumps, 'value' ], v);
                                                        },

                                                        change: (v) => {
                                                            if (v !== 2) {
                                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.lumps, 'text' ], '');
                                                            }
                                                        },
                                                    },
                                                    disabled: this.disabled,
                                                },
                                                {
                                                    formItemType: FORM_ITEM_TYPE.input,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.lumps].text,
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.lumps, 'text' ], v);
                                                        },
                                                    },
                                                    maxLength: 20,
                                                    disabled: this.disabled || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.lumps].value !== 2,
                                                },
                                            ],
                                        ],
                                    },
                                ],
                                [
                                    {
                                        label: '肝大',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hepatomegaly].value !== '';
                                        },
                                    },
                                    {
                                        formItems: [
                                            [
                                                {
                                                    label: '',
                                                    formItemType: FORM_ITEM_TYPE.radioGroup,
                                                    options: PUBLIC_HEALTHY_OPTION.hepatomegaly,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hepatomegaly].value,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hepatomegaly].value !== '';
                                                    },
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.hepatomegaly, 'value' ], v);
                                                        },
                                                        change: (v) => {
                                                            if (v !== 2) {
                                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.hepatomegaly, 'text' ], '');
                                                            }
                                                        },
                                                    },
                                                    disabled: this.disabled,
                                                },
                                                {
                                                    formItemType: FORM_ITEM_TYPE.input,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hepatomegaly].text,
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.hepatomegaly, 'text' ], v);
                                                        },
                                                    },
                                                    maxLength: 20,
                                                    disabled: this.disabled || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hepatomegaly].value !== 2,
                                                },
                                            ],
                                        ],
                                    },
                                ],
                                [
                                    {
                                        label: '脾大',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.splenomegaly].value !== '';
                                        },
                                    },
                                    {
                                        formItems: [
                                            [
                                                {
                                                    label: '',
                                                    formItemType: FORM_ITEM_TYPE.radioGroup,
                                                    options: PUBLIC_HEALTHY_OPTION.splenomegaly,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.splenomegaly].value,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.splenomegaly].value !== '';
                                                    },
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.splenomegaly, 'value' ], v);
                                                        },
                                                        change: (v) => {
                                                            if (v !== 2) {
                                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.splenomegaly, 'text' ], '');
                                                            }
                                                        },
                                                    },
                                                    disabled: this.disabled,
                                                },
                                                {
                                                    formItemType: FORM_ITEM_TYPE.input,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.splenomegaly].text,
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.splenomegaly, 'text' ], v);
                                                        },
                                                    },
                                                    maxLength: 20,
                                                    disabled: this.disabled || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.splenomegaly].value !== 2,
                                                },
                                            ],
                                        ],
                                    },
                                ],
                                [
                                    {
                                        label: '移动性浊音',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.mobileVoicedness].value !== '';
                                        },
                                    },
                                    {
                                        formItems: [
                                            [
                                                {
                                                    label: '',
                                                    formItemType: FORM_ITEM_TYPE.radioGroup,
                                                    options: PUBLIC_HEALTHY_OPTION.mobileVoicedness,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.mobileVoicedness].value,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.mobileVoicedness].value !== '';
                                                    },
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.mobileVoicedness, 'value' ], v);
                                                        },
                                                        change: (v) => {
                                                            if (v !== 2) {
                                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.mobileVoicedness, 'text' ], '');
                                                            }
                                                        },
                                                    },
                                                    disabled: this.disabled,
                                                },
                                                {
                                                    formItemType: FORM_ITEM_TYPE.input,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.mobileVoicedness].text,
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.mobileVoicedness, 'text' ], v);
                                                        },
                                                    },
                                                    maxLength: 20,
                                                    disabled: this.disabled || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.mobileVoicedness].value !== 2,
                                                },
                                            ],
                                        ],
                                    },
                                ],
                            ],
                        },
                    ],
                    [
                        {
                            label: '下肢水肿',
                            style: singleLabelStyle,
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.lowerLimbEdema].value !== '';
                            },
                        },
                        {
                            formItemType: FORM_ITEM_TYPE.radioGroup,
                            options: PUBLIC_HEALTHY_OPTION.lowerLimbEdema,
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.lowerLimbEdema].value,
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.lowerLimbEdema].value !== '';
                            },
                            on: {
                                input: (v) => {
                                    this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.lowerLimbEdema, 'value' ], v);
                                },
                            },
                            disabled: this.disabled,
                        },
                    ],
                    [
                        {
                            label: '足背动脉搏动',
                            style: singleLabelStyle,
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.dorsalisPedisArteryPulse].value !== '';
                            },
                        },
                        {
                            formItemType: FORM_ITEM_TYPE.radioGroup,
                            options: PUBLIC_HEALTHY_OPTION.dorsalisPedisArteryPulse,
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.dorsalisPedisArteryPulse].value,
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.dorsalisPedisArteryPulse].value !== '';
                            },
                            on: {
                                input: (v) => {
                                    this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.dorsalisPedisArteryPulse, 'value' ], v);
                                },
                            },
                            disabled: this.disabled,
                        },
                    ],
                    [
                        {
                            label: '肛门指检',
                            style: singleLabelStyle,
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.digitalAnalExamination].value !== '';
                            },
                        },
                        {
                            formItems: [
                                [
                                    {
                                        formItemType: FORM_ITEM_TYPE.radioGroup,
                                        options: PUBLIC_HEALTHY_OPTION.digitalAnalExamination,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.digitalAnalExamination].value,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.digitalAnalExamination].value !== '';
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.digitalAnalExamination, 'value' ], v);
                                            },
                                            change: (v) => {
                                                if (v !== 5) {
                                                    this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.digitalAnalExamination, 'text' ], '');
                                                }
                                            },
                                        },
                                        disabled: this.disabled,
                                        style: {
                                            flex: 'none',
                                            width: 'calc(50% + 60px)',
                                        },
                                    },
                                    {
                                        formItemType: FORM_ITEM_TYPE.input,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.digitalAnalExamination].text,
                                        on: {
                                            input: (v) => {
                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.digitalAnalExamination, 'text' ], v);
                                            },
                                        },
                                        maxLength: 20,
                                        disabled: this.disabled || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.digitalAnalExamination].value !== 5,
                                    },
                                ],
                            ],
                        },
                    ],
                    currentBreastConfig,
                    [
                        {
                            label: '妇科',
                            style: this.sex !== '女' ? {
                                ...disabledBgStyle,...singleLabelStyle,
                            } : singleLabelStyle,
                            rowSpan: 5,
                        },
                        {
                            formItems: [
                                [
                                    {
                                        label: '外阴',
                                        style: this.sex !== '女' ? {
                                            ...disabledBgStyle,...singleLabelStyle,
                                        } : singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.gynecologicalVulva].value !== '' || this.sex !== '女';
                                        },
                                    },
                                    {
                                        formItems: [
                                            [
                                                {
                                                    label: '',
                                                    style: this.sex !== '女' && disabledBgStyle,
                                                    formItemType: FORM_ITEM_TYPE.radioGroup,
                                                    options: PUBLIC_HEALTHY_OPTION.gynecologicalVulva,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.gynecologicalVulva].value,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.gynecologicalVulva].value !== '' || this.sex !== '女';
                                                    },
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.gynecologicalVulva, 'value' ], v);
                                                        },
                                                        change: (v) => {
                                                            if (v !== 2) {
                                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.gynecologicalVulva, 'text' ], '');
                                                            }
                                                        },
                                                    },
                                                    disabled: this.disabled || this.sex !== '女',
                                                },
                                                {
                                                    formItemType: FORM_ITEM_TYPE.input,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.gynecologicalVulva].text,
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.gynecologicalVulva, 'text' ], v);
                                                        },
                                                    },
                                                    maxLength: 20,
                                                    disabled: this.disabled || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.gynecologicalVulva].value !== 2,
                                                },
                                            ],
                                        ],
                                    },
                                ],
                                [
                                    {
                                        label: '阴道',
                                        style: this.sex !== '女' ? {
                                            ...disabledBgStyle,...singleLabelStyle,
                                        } : singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.gynecologicalVagina].value !== '' || this.sex !== '女';
                                        },
                                    },
                                    {
                                        formItems: [
                                            [
                                                {
                                                    label: '',
                                                    style: this.sex !== '女' && disabledBgStyle,
                                                    formItemType: FORM_ITEM_TYPE.radioGroup,
                                                    options: PUBLIC_HEALTHY_OPTION.gynecologicalVagina,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.gynecologicalVagina].value,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.gynecologicalVagina].value !== '' || this.sex !== '女';
                                                    },
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.gynecologicalVagina, 'value' ], v);
                                                        },
                                                        change: (v) => {
                                                            if (v !== 2) {
                                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.gynecologicalVagina, 'text' ], '');
                                                            }
                                                        },
                                                    },
                                                    disabled: this.disabled || this.sex !== '女',
                                                },
                                                {
                                                    formItemType: FORM_ITEM_TYPE.input,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.gynecologicalVagina].text,
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.gynecologicalVagina, 'text' ], v);
                                                        },
                                                    },
                                                    maxLength: 20,
                                                    disabled: this.disabled || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.gynecologicalVagina].value !== 2,
                                                },
                                            ],
                                        ],
                                    },
                                ],
                                [
                                    {
                                        label: '宫颈',
                                        style: this.sex !== '女' ? {
                                            ...disabledBgStyle,...singleLabelStyle,
                                        } : singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.gynecologicalCervix].value !== '' || this.sex !== '女';
                                        },
                                    },
                                    {
                                        formItems: [
                                            [
                                                {
                                                    label: '',
                                                    style: this.sex !== '女' && disabledBgStyle,
                                                    formItemType: FORM_ITEM_TYPE.radioGroup,
                                                    options: PUBLIC_HEALTHY_OPTION.gynecologicalCervix,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.gynecologicalCervix].value,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.gynecologicalCervix].value !== '' || this.sex !== '女';
                                                    },
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.gynecologicalCervix, 'value' ], v);
                                                        },
                                                        change: (v) => {
                                                            if (v !== 2) {
                                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.gynecologicalCervix, 'text' ], '');
                                                            }
                                                        },
                                                    },
                                                    disabled: this.disabled || this.sex !== '女',
                                                },
                                                {
                                                    formItemType: FORM_ITEM_TYPE.input,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.gynecologicalCervix].text,
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.gynecologicalCervix, 'text' ], v);
                                                        },
                                                    },
                                                    maxLength: 20,
                                                    disabled: this.disabled || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.gynecologicalCervix].value !== 2,
                                                },
                                            ],
                                        ],
                                    },
                                ],
                                [
                                    {
                                        label: '宫体',
                                        style: this.sex !== '女' ? {
                                            ...disabledBgStyle,...singleLabelStyle,
                                        } : singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.gynecologicalUterus].value !== '' || this.sex !== '女';
                                        },
                                    },
                                    {
                                        formItems: [
                                            [
                                                {
                                                    label: '',
                                                    style: this.sex !== '女' && disabledBgStyle,
                                                    formItemType: FORM_ITEM_TYPE.radioGroup,
                                                    options: PUBLIC_HEALTHY_OPTION.gynecologicalUterus,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.gynecologicalUterus].value,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.gynecologicalUterus].value !== '' || this.sex !== '女';
                                                    },
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.gynecologicalUterus, 'value' ], v);
                                                        },
                                                        change: (v) => {
                                                            if (v !== 2) {
                                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.gynecologicalUterus, 'text' ], '');
                                                            }
                                                        },
                                                    },
                                                    disabled: this.disabled || this.sex !== '女',
                                                },
                                                {
                                                    formItemType: FORM_ITEM_TYPE.input,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.gynecologicalUterus].text,
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.gynecologicalUterus, 'text' ], v);
                                                        },
                                                    },
                                                    maxLength: 20,
                                                    disabled: this.disabled || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.gynecologicalUterus].value !== 2,
                                                },
                                            ],
                                        ],
                                    },
                                ],
                                [
                                    {
                                        label: '附件',
                                        style: this.sex !== '女' ? {
                                            ...disabledBgStyle,...singleLabelStyle,
                                        } : singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.gynecologicalExamination].value !== '' || this.sex !== '女';
                                        },
                                    },
                                    {
                                        formItems: [
                                            [
                                                {
                                                    label: '',
                                                    style: this.sex !== '女' && disabledBgStyle,
                                                    formItemType: FORM_ITEM_TYPE.radioGroup,
                                                    options: PUBLIC_HEALTHY_OPTION.gynecologicalExamination,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.gynecologicalExamination].value,
                                                    valueNonEmpty: () => {
                                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.gynecologicalExamination].value !== '' || this.sex !== '女';
                                                    },
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.gynecologicalExamination, 'value' ], v);
                                                        },
                                                        change: (v) => {
                                                            if (v !== 2) {
                                                                this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.gynecologicalExamination, 'text' ], '');
                                                            }
                                                        },
                                                    },
                                                    disabled: this.disabled || this.sex !== '女',
                                                },
                                                {
                                                    formItemType: FORM_ITEM_TYPE.input,
                                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.gynecologicalExamination].text,
                                                    on: {
                                                        input: (v) => {
                                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.gynecologicalExamination, 'text' ], v);
                                                        },
                                                    },
                                                    maxLength: 20,
                                                    disabled: this.disabled || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.gynecologicalExamination].value !== 2,
                                                },
                                            ],
                                        ],
                                    },
                                ],
                            ],
                        },
                    ],
                    [
                        {
                            label: '其他',
                            style: singleLabelStyle,
                        },
                        {
                            formItemType: FORM_ITEM_TYPE.input,
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.checkBodyOther],
                            on: {
                                input: (v) => {
                                    this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.checkBodyOther ], v);
                                },
                            },
                            disabled: this.disabled,
                        },
                    ],
                ];
            },
        },
        watch: {
            sex: {
                handler(v) {
                    this.updateFormBySex(v);
                },
                immediate: true,
                deep: true,
            },
        },
        methods: {
            updateFormBySex(sex) {
                if (sex !== '女') {
                    this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.gynecologicalVulva, 'value' ], '');
                    this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.gynecologicalVagina, 'value' ], '');
                    this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.gynecologicalCervix, 'value' ], '');
                    this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.gynecologicalUterus, 'value' ], '');
                    this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.gynecologicalExamination, 'value' ], '');
                }
            },
            checkDisabled(value,options) {
                const otherValue = options.find((op) => op.label === '其他').value;
                return !(value || []).includes(otherValue);
            },
        },
    };
</script>

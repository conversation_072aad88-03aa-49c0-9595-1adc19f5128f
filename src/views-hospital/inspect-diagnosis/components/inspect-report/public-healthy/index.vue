<template>
    <div class="public-healthy-inspect-report-wrapper">
        <div ref="sidebar" class="public-healthy-nav-sidebar">
            <div
                class="public-healthy-nav-sidebar-tab"
                :style="{
                    position: isFixed ? 'fixed' : 'unset' ,top: '104px'
                }"
            >
                <abc-tabs
                    :value="curTab"
                    size="middle"
                    :width="120"
                    :custom-gap="16"
                    :option="projectList"
                    direction="vertical"
                    :need-animation="false"
                    @change="handleTabChange"
                ></abc-tabs>
            </div>
        </div>

        <div :class="['public-healthy-main-content', { 'public-healthy-main-content-disabled': disabled }]">
            <div class="public-healthy-base-info">
                <abc-text bold size="large">
                    公卫体检
                </abc-text>
                <div class="public-healthy-base-info-right">
                    <div class="base-info-item">
                        <div class="base-info-item-label">
                            体检日期
                        </div>

                        <div class="base-info-item-value">
                            <abc-date-picker
                                v-model="curBusinessTime"
                                :width="124"
                                size="tiny"
                                :editable="true"
                            >
                            </abc-date-picker>
                        </div>
                    </div>

                    <div class="base-info-item">
                        <div class="base-info-item-value">
                            <abc-select
                                v-model="curPrincipalDoctorId"
                                :width="126"
                                placeholder="责任医生"
                                :disabled="disabled"
                                size="tiny"
                                with-search
                                clearable
                                :fetch-suggestions="handleSearch"
                                @change="handleSelectPrincipalDoctorChange"
                            >
                                <abc-option
                                    v-for="(d, i) in doctorList"
                                    :key="i"
                                    v-bind="d"
                                ></abc-option>
                            </abc-select>
                        </div>
                    </div>
                    <div v-if="supportDoctorSign && curPrincipalDoctorId" class="base-info-item">
                        <div class="base-info-item-label" style="margin-right: 4px;">
                            签名：
                        </div>

                        <div class="base-info-item-value">
                            <hand-sign-upload v-model="curPrincipalDoctorSign" :disabled-delete="disabledHandSignDelete" @upload="disabledHandSignDelete = false"></hand-sign-upload>
                        </div>
                    </div>
                    <div class="base-info-item">
                        <abc-button
                            variant="ghost"
                            size="small"
                            theme="default"
                            @click="handleUseDefaultData()"
                        >
                            一键填充未填项目
                        </abc-button>
                    </div>
                </div>
            </div>

            <div
                v-for="(p, i) in projectList"
                :key="i"
                class="public-healthy-project-item"
            >
                <div class="project-item-name">
                    {{ p.label }}
                </div>

                <div class="project-item-form">
                    <component
                        :is="p.is"
                        :ref="p.value"
                        :disabled="disabled"
                        :patient="patient"
                        :form="form"
                        :doctor-sign-by-apply="doctorSignByApply"
                        @update:form="form => $emit('update:form', form)"
                    ></component>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import PhSymptom from './ph-symptom.vue';
    import PHGeneralCondition from './ph-general-condition.vue';
    import PHAssistInspect from './ph-assist-inspect.vue';
    import PHCurrentDisease from './ph-current-disease.vue';
    import PhInHospitalHistory from './ph-in-hospital-history.vue';
    import PhBedHistory from './ph-bed-history.vue';
    import PHInoculation from './ph-inoculation.vue';
    import PHLifeStyle from './ph-life-style.vue';
    import PhOrgans from './ph-organs.vue';
    import PHCheckBody from './ph-check-body.vue';
    import PHHealthyEvaluation from './ph-healthy-evaluation.vue';
    import PHHealthyGuide from './ph-healthy-guide.vue';

    import { formatDate } from '@abc/utils-date';
    import {
        defaultPublicHealthyFormData,
    } from '@/views-hospital/inspect-diagnosis/utils/public-healthy-constant';

    import {
        ROLE_DOCTOR_ID,
        ROLE_PH_CHECK_DOCTOR_ID,
    } from 'utils/constants';
    import {
        mergeDefaultIntoRender,
    } from '@/views-hospital/inspect-diagnosis/utils/public-healthy-helper';
    import Clone from 'utils/clone';
    import { isEmptyObject } from 'utils/lodash';
    import HandSignUpload
        from '@/views-hospital/inspect-diagnosis/components/inspect-report/public-healthy/components/hand-sign-upload.vue';
    import {
        getPublicHealthConfig,
    } from 'views/physical-examination/integrated/public-health-sync/distribute/distribute-helper';
    import { INSPECT_EVENT_KEY } from '@/views-hospital/inspect-diagnosis/utils/constant';
    import DisplayEmptyProjectDialog
        from '@/views-hospital/inspect-diagnosis/components/inspect-report/public-healthy/components/display-empty-project.vue';

    export default {
        name: 'PublicHealthyInspectReport',

        components: {
            HandSignUpload,
        },

        inject: {
            $abcPage: {
                default: () => {},
            },
        },

        provide() {
            return {
                publicHealthyIndex: this,
            };
        },

        props: {
            form: {
                type: Object,
                default: () => ({}),
            },
            publicHealthyInitForm: {
                type: Object,
                default: () => ({}),
            },

            businessTime: {
                type: String,
                default: '',
            },

            disabled: {
                type: Boolean,
                default: false,
            },

            principalDoctorId: {
                type: String,
                default: '',
            },
            principalDoctorSign: {
                type: String,
                default: '',
            },

            patientOrderId: {
                type: String,
                default: '',
            },

            patient: {
                type: Object,
                default: () => ({}),
            },
            phItemWithGroupIdList: {
                type: Array,
                default: () => ([]),
            },
        },
        data() {
            return {
                curTab: 'symptom',
                projectList: [
                    {
                        label: '症状', value: 'symptom',is: PhSymptom,
                    },
                    {
                        label: '一般状况', value: 'generalCondition',is: PHGeneralCondition,
                    },
                    {
                        label: '生活方式', value: 'lifeStyle',is: PHLifeStyle,
                    },
                    {
                        label: '脏器功能', value: 'organs', is: PhOrgans,
                    },
                    {
                        label: '查体', value: 'checkBody', is: PHCheckBody,
                    },
                    {
                        label: '辅助检查', value: 'assistInspect', is: PHAssistInspect,
                    },
                    {
                        label: '健康问题', value: 'currentDisease',is: PHCurrentDisease,
                    },
                    {
                        label: '住院史', value: 'inHospitalHistory', is: PhInHospitalHistory,
                    },
                    {
                        label: '家庭病床史', value: 'bedHistory',is: PhBedHistory,
                    },
                    {
                        label: '主要用药情况', value: 'pharmacyCondition', is: getPublicHealthConfig().viewComponents.phPharmacyConditionComponent,
                    },
                    {
                        label: '预防接种史', value: 'inoculation', is: PHInoculation,
                    },
                    {
                        label: '健康评价', value: 'healthyEvaluation', is: PHHealthyEvaluation,
                    },
                    {
                        label: '健康指导', value: 'healthyGuide', is: PHHealthyGuide,
                    },
                ],
                isFixed: false,

                doctorSearchKey: '', // 选择责任医生的搜索

                useDefaultData: false,//是否使用默认值填充
                userModifyData: {},
                doctorSignByApply: {},
                disabledHandSignDelete: false,
            };
        },
        computed: {
            doctorList() {
                const curDoctorList = this.$abcPage.$store.state.operateDoctorList;
                // 页面初始化拉取的医生列表没有过滤角色，这里单独过滤
                return curDoctorList.filter((d) => {
                    return d.roles.includes(ROLE_DOCTOR_ID) || d.roles.includes(ROLE_PH_CHECK_DOCTOR_ID);
                }).filter((d) => {
                    return d.label.includes(this.doctorSearchKey);
                });
            },
            curPrincipalDoctorId: {
                get() {
                    return this.principalDoctorId;
                },
                set(v) {
                    this.$emit('update:principalDoctorId', v);
                },
            },
            curPrincipalDoctorSign: {
                get() {
                    return this.principalDoctorSign;
                },
                set(v) {
                    this.$emit('update:principalDoctorSign', v);
                },
            },
            supportDoctorSign() {
                return getPublicHealthConfig().publicHealthForm.supportDoctorSign;
            },
            curBusinessTime: {
                get() {
                    return this.businessTime;
                },
                set(val) {
                    this.$emit('update:businessTime',val);
                },
            },
        },
        watch: {
            form: {
                handler() {
                    this.$nextTick(() => {
                        this.projectList.forEach((item) => {
                            if (this.$refs[item.value] && this.$refs[item.value].length > 0) {
                                const formGroup = this.$refs[item.value][0];
                                if (formGroup) {
                                    const isNonEmpty = formGroup.formGroupValidateNonEmpty(formGroup.formItems);
                                    if (isNonEmpty) {
                                        this.$set(item,'withRedDot',false);
                                    } else {
                                        this.$set(item,'withRedDot',true);
                                    }
                                }
                            }
                        });
                    });
                },
                immediate: true,
                deep: true,
            },
        },
        created() {
            this.$on('form-item-change', (key, value) => {
                this.userModifyData[key] = value;
                this.updateFormByMerge();
            });
        },

        mounted() {
            this.handleProjectListOffsetTop();
            this.$abcEventBus.$on(INSPECT_EVENT_KEY.CALC_NOT_FINISH_DATA, () => {
                this.calcNotFinishFormItem();
            },this);
        },
        beforeDestroy() {
            const abcContainer = document.querySelector('.content-container');
            abcContainer.removeEventListener('scroll', this.scrollHandler);

            this.$abcEventBus.$offVmEvent(this._uid);
        },
        methods: {
            formatDate,
            handleUseDefaultData() {
                this.useDefaultData = true;
                this.updateFormByMerge();
            },
            updateFormByMerge() {
                if (this.useDefaultData) {
                    mergeDefaultIntoRender(defaultPublicHealthyFormData,this.form,this.userModifyData);
                } else {
                    //merge init + user
                    if (!isEmptyObject(this.publicHealthyInitForm)) {
                        const form = Clone(this.publicHealthyInitForm);
                        Object.assign(form,this.userModifyData);
                        this.$emit('update:form',form);
                    }
                }
            },
            handleTabChange(item) {
                try {
                    const abcContainer = document.querySelector('.content-container');
                    const { offsetTop } = this.$refs[item][0].$el;
                    abcContainer.scrollTo({
                        behavior: 'instant', top: offsetTop - 40,
                    });
                    const timer = setTimeout(() => {
                        this.curTab = item;
                        clearInterval(timer);
                    },10);
                } catch (e) {
                    console.error(e);
                }
            },
            scrollHandler(v) {
                try {
                    const top = this.$refs.sidebar.getBoundingClientRect().top - (48 + 56);
                    this.isFixed = top <= 0;
                    this.handleChangeTab(v.target.scrollTop);
                } catch (e) {
                    console.log(e);
                }
            },

            handleProjectListOffsetTop() {
                try {
                    this.projectList.forEach((item) => {
                        const { offsetTop } = this.$refs[item.value][0].$el;
                        this.$set(item,'offsetTop',offsetTop);
                    });
                    const abcContainer = document.querySelector('.content-container');
                    abcContainer.addEventListener('scroll', this.scrollHandler, false);
                } catch (e) {
                    console.log(e);
                }
            },

            handleChangeTab(curScrollTop) {
                const len = this.projectList.length;
                let flag = true;
                for (let i = len - 1; i >= 0; i--) {
                    const curReference = this.projectList[i]?.offsetTop; // 当前参考值
                    if (flag && curScrollTop >= curReference) {
                        flag = false;
                        this.curTab = this.projectList[i].value;
                    }
                }
            },

            handleSearch(key) {
                this.doctorSearchKey = key;
            },

            handleSelectPrincipalDoctorChange(val) {
                const item = this.doctorList.find((d) => d.value === val) || {};
                this.curPrincipalDoctorSign = item.handSign || '';
                this.disabledHandSignDelete = true;
            },
            calcNotFinishFormItem() {
                const emptyFormItems = [];
                this.projectList.forEach((item) => {
                    if (this.$refs[item.value] && this.$refs[item.value].length > 0) {
                        const formGroup = this.$refs[item.value][0];
                        if (formGroup) {
                            const emptyFormItem = formGroup.calcEmptyFormItems(formGroup.formItems);
                            if (emptyFormItem && emptyFormItem.length > 0) emptyFormItems.push(emptyFormItem);
                        }
                    }
                });
                const result = emptyFormItems.flat().filter((it) => it.label);
                if (result && result.length > 0) {
                    const h = this.$createElement;
                    this.$confirm({
                        type: 'warn',
                        title: '是否确定完成检查？',
                        content: h(DisplayEmptyProjectDialog, {
                            props: {
                                list: result,
                            },
                        }),
                        closeAfterConfirm: true,
                        onConfirm: () => {
                            this.$abcEventBus.$emit(INSPECT_EVENT_KEY.PH_CONTINUE_FINISH);
                        },
                    });
                } else {
                    this.$abcEventBus.$emit(INSPECT_EVENT_KEY.PH_CONTINUE_FINISH);
                }
            },
        },
    };
</script>

<style lang='scss'>
@import "~styles/theme";

@media screen and (max-width: 1440px) {
    .public-healthy-inspect-report-wrapper {
        .abc-tabs {
            width: 91px;
            padding-right: 12px;
        }
    }
}

@media screen and (max-width: 1660px) {
    .public-healthy-base-info {
        gap: 20px !important;
    }
}

.public-healthy-inspect-report-wrapper {
    display: flex;
    background-color: #ffffff;
    border: 1px solid $P6;
    border-radius: var(--abc-border-radius-small);

    .public-healthy-nav-sidebar {
        min-width: 136px;
        border-right: 1px solid $P6;

        &-tab {
            padding: 53px 8px 0 8px;

            .abc-tabs-item {
                height: fit-content;

                .abc-tabs-item-content-wrapper > span {
                    line-height: 18px;
                }
            }
        }
    }

    .public-healthy-main-content {
        flex: 1;

        .public-healthy-base-info {
            display: flex;
            gap: 32px;
            align-items: center;
            justify-content: space-between;
            height: 48px;
            padding: 0 12px;
            border-bottom: 1px solid $P6;

            &-right {
                display: flex;
                flex: none;
                gap: 8px;

                .base-info-item {
                    display: flex;
                    align-items: center;

                    .base-info-item-label {
                        margin-right: 8px;
                        color: $T2;
                    }
                }
            }
        }

        .public-healthy-project-item {
            display: flex;
            flex-direction: column;
            width: 100%;

            .project-item-name {
                display: inline-flex;
                align-items: center;
                width: 100%;
                padding: 12px;
                font-weight: 500;
                background-color: #f9fafc;
                //border-right: 1px solid $P6;
                border-bottom: 1px solid $P6;
            }

            .project-item-form {
                flex: 1;
                border-bottom: 1px solid $P6;
            }
        }

        & > .public-healthy-project-item:last-child {
            //.project-item-name {
            //    border-bottom: none;
            //}

            .project-item-form {
                border-bottom: none;

                .ph-row-wrapper:last-child {
                    .ph-row-wrapper:last-child {
                        >.ph-col-wrapper {
                            border-bottom: none;
                        }
                    }
                }
            }
        }
    }

    .public-healthy-main-content-disabled {
        background-color: $abcBgDisabled;
    }
}
</style>

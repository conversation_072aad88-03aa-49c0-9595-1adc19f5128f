<template>
    <!-- 辅助检查 -->
    <div class="ph-assist-inspect-wrapper">
        <ph-table-form :form-items="formItems"></ph-table-form>
    </div>
</template>

<script>
    import {
        PHTableForm,
    } from './components';
    import {
        FORM_ITEM_TYPE, PUBLIC_HEALTHY_FORM_KEYS, PUBLIC_HEALTHY_OPTION,
    } from '@/views-hospital/inspect-diagnosis/utils/public-healthy-constant';
    import UpdateFormMixin
        from '@/views-hospital/inspect-diagnosis/components/inspect-report/public-healthy/update-form';
    import themeStyle from 'styles/theme.module.scss';
    import TableFormUploader from '@/views-ophthalmology/inspect/components/table-form-uploader.vue';
    import DoctorSign
        from '@/views-hospital/inspect-diagnosis/components/inspect-report/public-healthy/components/doctor-sign.vue';
    import { accessCommonImage } from '@/assets/configure/access-file';
    import {
        getPublicHealthConfig,
    } from 'views/physical-examination/integrated/public-health-sync/distribute/distribute-helper';
    const singleLabelStyle = {
        width: '120px',
        flex: 'none',
    };
    const disabledBgStyle = { 'background-color': themeStyle.abcBgDisabled };
    export default {
        name: 'PHAssistInspect',
        components: {
            'ph-table-form': PHTableForm,
        },
        mixins: [UpdateFormMixin],
        props: {
            form: {
                type: Object,
                default: () => ({}),
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            patient: {
                type: Object,
                default: () => ({}),
            },
            doctorSignByApply: {
                type: Object,
                default: () => ({}),
            },
        },
        computed: {
            curForm: {
                get() {
                    return this.form;
                },

                set(v) {
                    this.$emit('update:form', v);
                },
            },
            sex() {
                return this.patient.sex;
            },
            ECGFormItems() {
                const formItems = [
                    [
                        {
                            label: '',
                            options: PUBLIC_HEALTHY_OPTION.abnormal,
                            formItemType: FORM_ITEM_TYPE.select,
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.ECG].value,
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.ECG].value !== '';
                            },
                            on: {
                                input: (v) => {
                                    this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.ECG,'value'], v);
                                },
                            },
                            disabled: this.disabled,
                        },
                        {
                            label: '',
                            formItemType: FORM_ITEM_TYPE.input,
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.ECG].text,

                            on: {
                                input: (v) => {
                                    this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.ECG, 'text' ], v);
                                },
                            },
                            disabled: !(this.curForm[PUBLIC_HEALTHY_FORM_KEYS.ECG].value === 2) || this.disabled,
                            maxLength: 50,
                        },
                    ],
                ];

                const { supportDoctorSign } = getPublicHealthConfig().publicHealthForm;

                if (supportDoctorSign) {
                    formItems.push([
                        {
                            label: '',
                            isCustomRender: true,
                            customComponent: DoctorSign,
                            disabledDelete: this.doctorSignByApply.ECG,
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.ECG].doctorSign,
                            on: {
                                input: (v) => {
                                    this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.ECG, 'doctorSign' ], v);
                                },
                            },
                        },
                        {
                            label: '',
                            isCustomRender: true,
                            customComponent: TableFormUploader,
                            accept: accessCommonImage(),
                            uploadDescription: '报告附件支持png、jpg格式，且大小不得超过10M',
                            maxUploadCount: 3,
                            maxUploadSize: 1024 * 1024 * 10,
                            isLimitMaxCount: true,
                            isOnlyPreviewImg: true,
                            width: '30px',
                            height: '30px',
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.ECG].reportImgs,
                        },
                    ]);
                }
                return formItems;
            },
            bScanFormItems() {
                const { supportDoctorSign } = getPublicHealthConfig().publicHealthForm;

                const formItems = [
                    [
                        {
                            label: '胸部B超',
                            style: singleLabelStyle,
                        },
                        {
                            label: '',
                            options: PUBLIC_HEALTHY_OPTION.abnormal,
                            formItemType: FORM_ITEM_TYPE.select,
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.bScan].value,
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.bScan].value !== '';
                            },
                            on: {
                                input: (v) => {
                                    this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.bScan,'value'], v);
                                },
                            },
                            disabled: this.disabled,
                            style: {
                                flex: 'none',
                                width: 'calc(50% - 120px)',
                            },
                        },
                        {
                            label: '',
                            formItemType: FORM_ITEM_TYPE.input,
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.bScan].text,
                            on: {
                                input: (v) => {
                                    this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.bScan, 'text' ], v);
                                },
                            },
                            disabled: !(this.curForm[PUBLIC_HEALTHY_FORM_KEYS.bScan].value === 2) || this.disabled,
                            maxLength: 50,
                        },
                    ],
                    [
                        {
                            label: '其他',
                            style: singleLabelStyle,
                        },
                        {
                            label: '',
                            options: PUBLIC_HEALTHY_OPTION.abnormal,
                            formItemType: FORM_ITEM_TYPE.select,
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.bScanOther].value,
                            on: {
                                input: (v) => {
                                    this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.bScanOther,'value'], v);
                                },
                            },
                            disabled: this.disabled,
                            style: {
                                flex: 'none',
                                width: 'calc(50% - 120px)',
                            },
                        },
                        {
                            label: '',
                            formItemType: FORM_ITEM_TYPE.input,
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.bScanOther].text,
                            on: {
                                input: (v) => {
                                    this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.bScanOther, 'text' ], v);
                                },
                            },
                            disabled: !(this.curForm[PUBLIC_HEALTHY_FORM_KEYS.bScanOther].value === 2) || this.disabled,
                            maxLength: 50,
                        },
                    ],
                ];
                if (supportDoctorSign) {
                    formItems.push([
                        {
                            label: '',
                            isCustomRender: true,
                            customComponent: DoctorSign,
                            disabledDelete: this.doctorSignByApply.bScan,
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.bScan].doctorSign,
                            on: {
                                input: (v) => {
                                    this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.bScan, 'doctorSign' ], v);
                                },
                            },
                        },
                        {
                            label: '',
                            isCustomRender: true,
                            customComponent: TableFormUploader,
                            uploadDescription: '报告附件支持png、jpg格式，且大小不得超过10M',
                            accept: accessCommonImage(),
                            maxUploadCount: 3,
                            maxUploadSize: 1024 * 1024 * 10,
                            isLimitMaxCount: true,
                            isOnlyPreviewImg: true,
                            width: '30px',
                            height: '30px',
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.bScan].reportImgs,
                        },
                    ]);
                }
                return formItems;
            },
            chestXRayFormItems() {
                const { supportDoctorSign } = getPublicHealthConfig().publicHealthForm;
                const formItems = [
                    [
                        {
                            label: '',
                            options: PUBLIC_HEALTHY_OPTION.abnormal,
                            formItemType: FORM_ITEM_TYPE.select,
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.chestXRay].value,
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.chestXRay].value !== '';
                            },
                            on: {
                                input: (v) => {
                                    this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.chestXRay,'value'], v);
                                },
                            },
                            disabled: this.disabled,
                        },
                        {
                            label: '',
                            formItemType: FORM_ITEM_TYPE.input,
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.chestXRay].text,
                            on: {
                                input: (v) => {
                                    this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.chestXRay, 'text' ], v);
                                },
                            },
                            disabled: !(this.curForm[PUBLIC_HEALTHY_FORM_KEYS.chestXRay].value === 2) || this.disabled,
                            maxLength: 50,
                        },
                    ],
                ];
                if (supportDoctorSign) {
                    formItems.push([
                        {
                            label: '',
                            isCustomRender: true,
                            customComponent: DoctorSign,
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.chestXRay].doctorSign,
                            disabledDelete: this.doctorSignByApply.chestXRay,
                            on: {
                                input: (v) => {
                                    this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.chestXRay, 'doctorSign' ], v);
                                },
                            },
                        },
                        {
                            label: '',
                            isCustomRender: true,
                            customComponent: TableFormUploader,
                            uploadDescription: '报告附件支持png、jpg格式，且大小不得超过10M',
                            accept: accessCommonImage(),
                            maxUploadCount: 3,
                            maxUploadSize: 1024 * 1024 * 10,
                            isLimitMaxCount: true,
                            isOnlyPreviewImg: true,
                            width: '30px',
                            height: '30px',
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.chestXRay].reportImgs,
                        },
                    ]);
                }
                return formItems;
            },
            formItems() {
                const {
                    ecgRowSpan,
                    chestXRayRowSpan,
                    bScanRowSpan,
                    bloodExaminationAdvance,
                    urinalysisNormalInput,
                    supportUA,
                } = getPublicHealthConfig().publicHealthForm;

                // 普通血常规;
                const bloodExaminationNormalConfig = [
                    {
                        label: '血常规',
                        rowSpan: 2,
                        style: singleLabelStyle,
                    },
                    {
                        formItems: [
                            [
                                {
                                    label: '血红蛋白',
                                    style: singleLabelStyle,
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hemoglobin] !== '';
                                    },
                                },
                                {
                                    label: '',
                                    unit: 'g/L',
                                    formItemType: FORM_ITEM_TYPE.input,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hemoglobin],
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hemoglobin] !== '';
                                    },
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.hemoglobin], v);
                                        },
                                    },
                                    config: {
                                        formatLength: 2, supportFraction: true,
                                    },
                                    maxLength: 6,
                                    disabled: this.disabled,
                                    type: 'number',
                                },
                            ],
                            [
                                {
                                    label: '血小板',
                                    style: singleLabelStyle,
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.platelet] !== '';
                                    },
                                },
                                {
                                    label: '',
                                    unit: '10^9/L',
                                    formItemType: FORM_ITEM_TYPE.input,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.platelet],
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.platelet] !== '';
                                    },
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.platelet], v);
                                        },
                                    },
                                    config: {
                                        formatLength: 2, supportFraction: true,
                                    },
                                    maxLength: 6,
                                    disabled: this.disabled,
                                    type: 'number',
                                },
                            ],
                        ],
                    },
                    {
                        formItems: [
                            [
                                {
                                    label: '白细胞',
                                    style: singleLabelStyle,
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.leukocyte] !== '';
                                    },
                                },
                                {
                                    label: '',
                                    unit: '10^9/L',
                                    formItemType: FORM_ITEM_TYPE.input,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.leukocyte],
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.leukocyte] !== '';
                                    },
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.leukocyte], v);
                                        },
                                    },
                                    config: {
                                        formatLength: 2, supportFraction: true,
                                    },
                                    maxLength: 6,
                                    disabled: this.disabled,
                                    type: 'number',
                                },
                            ],
                            [
                                {
                                    label: '其他',
                                    style: singleLabelStyle,
                                },
                                {
                                    label: '',
                                    formItemType: FORM_ITEM_TYPE.input,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.bloodRoutineOther],
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.bloodRoutineOther], v);
                                        },
                                    },
                                    disabled: this.disabled,
                                    maxLength: 50,
                                },
                            ],
                        ],
                    },
                ];

                // 加强版血常规;
                const bloodExaminationAdvanceConfig = [
                    {
                        label: '血常规',
                        rowSpan: 5,
                        style: singleLabelStyle,
                    },
                    {
                        formItems: [
                            [
                                {
                                    label: '血红蛋白',
                                    style: singleLabelStyle,
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hemoglobin] !== '';
                                    },
                                },
                                {
                                    label: '',
                                    unit: 'g/L',
                                    formItemType: FORM_ITEM_TYPE.input,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hemoglobin],
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.hemoglobin] !== '';
                                    },
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.hemoglobin], v);
                                        },
                                    },
                                    config: {
                                        formatLength: 2, supportFraction: true,
                                    },
                                    maxLength: 6,
                                    disabled: this.disabled,
                                    type: 'number',
                                },
                                {
                                    label: '白细胞',
                                    style: singleLabelStyle,
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.leukocyte] !== '';
                                    },
                                },
                                {
                                    label: '',
                                    unit: '10^9/L',
                                    formItemType: FORM_ITEM_TYPE.input,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.leukocyte],
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.leukocyte] !== '';
                                    },
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.leukocyte], v);
                                        },
                                    },
                                    config: {
                                        formatLength: 2, supportFraction: true,
                                    },
                                    maxLength: 6,
                                    disabled: this.disabled,
                                    type: 'number',
                                },
                            ],
                            [
                                {
                                    label: '中性粒细胞',
                                    style: singleLabelStyle,
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.NEU] !== '';
                                    },
                                },
                                {
                                    label: '',
                                    unit: '10^9/L',
                                    formItemType: FORM_ITEM_TYPE.input,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.NEU],
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.NEU] !== '';
                                    },
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.NEU], v);
                                        },
                                    },
                                    config: {
                                        formatLength: 2, supportFraction: true,
                                    },
                                    maxLength: 6,
                                    disabled: this.disabled,
                                    type: 'number',
                                },
                                {
                                    label: '淋巴细胞',
                                    style: singleLabelStyle,
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.LYM] !== '';
                                    },
                                },
                                {
                                    label: '',
                                    unit: '10^9/L',
                                    formItemType: FORM_ITEM_TYPE.input,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.LYM],
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.LYM] !== '';
                                    },
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.LYM], v);
                                        },
                                    },
                                    config: {
                                        formatLength: 2, supportFraction: true,
                                    },
                                    maxLength: 6,
                                    disabled: this.disabled,
                                    type: 'number',
                                },
                            ],
                            [
                                {
                                    label: '单核细胞',
                                    style: singleLabelStyle,
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.MON] !== '';
                                    },
                                },
                                {
                                    label: '',
                                    unit: '10^9/L',
                                    formItemType: FORM_ITEM_TYPE.input,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.MON],
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.MON] !== '';
                                    },
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.MON], v);
                                        },
                                    },
                                    config: {
                                        formatLength: 2, supportFraction: true,
                                    },
                                    maxLength: 6,
                                    disabled: this.disabled,
                                    type: 'number',
                                },
                                {
                                    label: '嗜酸性粒细胞',
                                    style: singleLabelStyle,
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.EOS] !== '';
                                    },
                                },
                                {
                                    label: '',
                                    unit: '10^9/L',
                                    formItemType: FORM_ITEM_TYPE.input,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.EOS],
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.EOS] !== '';
                                    },
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.EOS], v);
                                        },
                                    },
                                    config: {
                                        formatLength: 2, supportFraction: true,
                                    },
                                    maxLength: 6,
                                    disabled: this.disabled,
                                    type: 'number',
                                },
                            ],
                            [
                                {
                                    label: '嗜碱性粒细胞',
                                    style: singleLabelStyle,
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.BAS] !== '';
                                    },
                                },
                                {
                                    label: '',
                                    unit: '10^9/L',
                                    formItemType: FORM_ITEM_TYPE.input,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.BAS],
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.BAS] !== '';
                                    },
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.BAS], v);
                                        },
                                    },
                                    config: {
                                        formatLength: 2, supportFraction: true,
                                    },
                                    maxLength: 6,
                                    disabled: this.disabled,
                                    type: 'number',
                                },
                                {
                                    label: '血小板',
                                    style: singleLabelStyle,
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.platelet] !== '';
                                    },
                                },
                                {
                                    label: '',
                                    unit: '10^9/L',
                                    formItemType: FORM_ITEM_TYPE.input,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.platelet],
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.platelet] !== '';
                                    },
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.platelet], v);
                                        },
                                    },
                                    config: {
                                        formatLength: 2, supportFraction: true,
                                    },
                                    maxLength: 6,
                                    disabled: this.disabled,
                                    type: 'number',
                                },
                            ],
                            [],
                        ],
                    },
                ];

                // 普通尿常规
                const urinalysisNormalConfig = [
                    {
                        label: '尿常规',
                        rowSpan: 3,
                        style: singleLabelStyle,
                    },
                    {
                        formItems: [
                            [
                                {
                                    label: '尿蛋白',
                                    style: singleLabelStyle,
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.PRO] !== '';
                                    },
                                },
                                {
                                    label: '',
                                    subLabel: '-',
                                    unit: 'g/L',
                                    formItemType: FORM_ITEM_TYPE.input,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.PRO],
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.PRO] !== '';
                                    },
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.PRO], v);
                                        },
                                    },
                                    config: {
                                        formatLength: 2, supportFraction: true,
                                    },
                                    maxLength: 6,
                                    disabled: this.disabled,
                                    type: 'number',
                                },
                                {
                                    label: '尿糖',
                                    style: singleLabelStyle,
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.GLU] !== '';
                                    },
                                },
                                {
                                    label: '',
                                    subLabel: '-',
                                    unit: '10^9/L',
                                    formItemType: FORM_ITEM_TYPE.input,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.GLU],
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.GLU] !== '';
                                    },
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.GLU], v);
                                        },
                                    },
                                    config: {
                                        formatLength: 2, supportFraction: true,
                                    },
                                    maxLength: 6,
                                    disabled: this.disabled,
                                    type: 'number',
                                },
                            ],
                            [
                                {
                                    label: '尿酮体',
                                    style: singleLabelStyle,
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.KET] !== '';
                                    },
                                },
                                {
                                    label: '',
                                    subLabel: '-',
                                    unit: '10^9/L',
                                    formItemType: FORM_ITEM_TYPE.input,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.KET],
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.KET] !== '';
                                    },
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.KET], v);
                                        },
                                    },
                                    config: {
                                        formatLength: 2, supportFraction: true,
                                    },
                                    maxLength: 6,
                                    disabled: this.disabled,
                                    type: 'number',
                                },
                                {
                                    label: '尿潜血',
                                    style: singleLabelStyle,
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.BLD] !== '';
                                    },
                                },
                                {
                                    label: '',
                                    subLabel: '-',
                                    formItemType: FORM_ITEM_TYPE.input,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.BLD],
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.BLD] !== '';
                                    },
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.BLD], v);
                                        },
                                    },
                                    config: {
                                        formatLength: 2, supportFraction: true,
                                    },
                                    maxLength: 6,
                                    disabled: this.disabled,
                                    type: 'number',
                                },

                            ],
                            [
                                {
                                    label: '其他',
                                    style: singleLabelStyle,
                                },
                                {
                                    label: '',
                                    formItemType: FORM_ITEM_TYPE.input,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.urinalysisOther],
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.urinalysisOther], v);
                                        },
                                    },
                                    disabled: this.disabled,
                                    maxLength: 50,
                                },
                            ],
                        ],
                    },
                ];

                // 加强版尿常规 简单输入框
                const urinalysisAdvanceConfig = [
                    {
                        label: '尿常规',
                        rowSpan: 3,
                        style: singleLabelStyle,
                    },
                    {
                        formItems: [
                            [
                                {
                                    label: '尿蛋白',
                                    style: singleLabelStyle,
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.PRO] !== '';
                                    },
                                },
                                {
                                    label: '',
                                    subLabel: '-',
                                    formItemType: FORM_ITEM_TYPE.input,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.PRO],
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.PRO] !== '';
                                    },
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.PRO], v);
                                        },
                                    },
                                    maxLength: 6,
                                    disabled: this.disabled,
                                },
                                {
                                    label: '尿糖',
                                    style: singleLabelStyle,
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.GLU] !== '';
                                    },
                                },
                                {
                                    label: '',
                                    subLabel: '-',
                                    formItemType: FORM_ITEM_TYPE.input,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.GLU],
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.GLU] !== '';
                                    },
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.GLU], v);
                                        },
                                    },
                                    maxLength: 6,
                                    disabled: this.disabled,
                                },
                            ],
                            [
                                {
                                    label: '尿酮体',
                                    style: singleLabelStyle,
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.KET] !== '';
                                    },
                                },
                                {
                                    label: '',
                                    subLabel: '-',
                                    formItemType: FORM_ITEM_TYPE.input,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.KET],
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.KET] !== '';
                                    },
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.KET], v);
                                        },
                                    },
                                    maxLength: 6,
                                    disabled: this.disabled,
                                },
                                {
                                    label: '尿潜血',
                                    style: singleLabelStyle,
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.BLD] !== '';
                                    },
                                },
                                {
                                    label: '',
                                    formItemType: FORM_ITEM_TYPE.input,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.BLD],
                                    valueNonEmpty: () => {
                                        return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.BLD] !== '';
                                    },
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.BLD], v);
                                        },
                                    },
                                    maxLength: 6,
                                    disabled: this.disabled,
                                },

                            ],
                            [
                                {
                                    label: '其他',
                                    style: singleLabelStyle,
                                },
                                {
                                    label: '',
                                    formItemType: FORM_ITEM_TYPE.input,
                                    value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.urinalysisOther],
                                    on: {
                                        input: (v) => {
                                            this.updateForm([ PUBLIC_HEALTHY_FORM_KEYS.urinalysisOther], v);
                                        },
                                    },
                                    disabled: this.disabled,
                                    maxLength: 50,
                                },
                            ],
                        ],
                    },
                ];

                // 当前血常规
                const bloodExaminationConfig = bloodExaminationAdvance ? bloodExaminationAdvanceConfig : bloodExaminationNormalConfig;
                // 当前尿常规
                const urinalysisConfig = urinalysisNormalInput ? urinalysisAdvanceConfig : urinalysisNormalConfig;


                return [
                    [
                        {
                            label: '空腹血糖',
                            style: singleLabelStyle,
                            valueNonEmpty: () => {
                                return !!this.curForm[PUBLIC_HEALTHY_FORM_KEYS.FBG].value[0];
                            },
                        },
                        {
                            label: '',
                            unit: 'mmol/L',
                            formItemType: FORM_ITEM_TYPE.input,
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.FBG].value[0],
                            valueNonEmpty: () => {
                                return !!this.curForm[PUBLIC_HEALTHY_FORM_KEYS.FBG].value[0];
                            },
                            on: {
                                input: (v) => {
                                    const arr = [v, this.curForm[PUBLIC_HEALTHY_FORM_KEYS.FBG].value[1]];
                                    this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.FBG, 'value'], arr);
                                },
                            },
                            config: {
                                formatLength: 2, supportFraction: true,
                            },
                            disabled: this.disabled,
                            maxLength: 6,
                            type: 'number',
                            style: {
                                flex: 'none',
                                width: 'calc(50% - 60px)',
                            },
                        },
                        {
                            label: '或',
                            style: singleLabelStyle,
                            valueNonEmpty: () => {
                                return !!this.curForm[PUBLIC_HEALTHY_FORM_KEYS.FBG].value[1];
                            },
                        },
                        {
                            label: '',
                            unit: 'mmol/L',
                            formItemType: FORM_ITEM_TYPE.input,
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.FBG].value[1],
                            valueNonEmpty: () => {
                                return !!this.curForm[PUBLIC_HEALTHY_FORM_KEYS.FBG].value[1];
                            },
                            on: {
                                input: (v) => {
                                    const arr = [this.curForm[PUBLIC_HEALTHY_FORM_KEYS.FBG].value[0], v];
                                    this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.FBG, 'value'], arr);
                                },
                            },
                            config: {
                                formatLength: 2, supportFraction: true,
                            },
                            maxLength: 6,
                            disabled: this.disabled,
                            type: 'number',
                        },
                    ],
                    bloodExaminationConfig,
                    urinalysisConfig,
                    [
                        {
                            label: '尿微量白蛋白',
                            style: singleLabelStyle,
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.mALB] !== '';
                            },
                        },
                        {
                            label: '',
                            unit: 'mmol/L',
                            formItemType: FORM_ITEM_TYPE.input,
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.mALB],
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.mALB] !== '';
                            },
                            on: {
                                input: (v) => {
                                    this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.mALB], v);
                                },
                            },
                            config: {
                                formatLength: 2, supportFraction: true,
                            },
                            maxLength: 6,
                            disabled: this.disabled,
                            type: 'number',
                        },
                    ],
                    [
                        {
                            label: '大便隐血',
                            style: singleLabelStyle,
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.FOB].value !== '';
                            },
                        },
                        {
                            label: '',
                            options: PUBLIC_HEALTHY_OPTION.diagnosisStatus,
                            formItemType: FORM_ITEM_TYPE.select,
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.FOB].value,
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.FOB].value !== '';
                            },
                            on: {
                                input: (v) => {
                                    this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.FOB, 'value'], v);
                                },
                            },
                            disabled: this.disabled,
                        },
                    ],
                    [
                        {
                            label: '肝功能',
                            rowSpan: 3,
                            style: singleLabelStyle,
                        },
                        {
                            formItems: [
                                [
                                    {
                                        label: '血清谷丙转氨酶',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.ALT] !== '';
                                        },
                                    },
                                    {
                                        label: '',
                                        unit: 'u/L',
                                        formItemType: FORM_ITEM_TYPE.input,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.ALT],
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.ALT] !== '';
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.ALT], v);
                                            },
                                        },
                                        config: {
                                            formatLength: 2, supportFraction: true,
                                        },
                                        maxLength: 6,
                                        disabled: this.disabled,
                                        type: 'number',
                                    },
                                    {
                                        label: '血清谷草转氨酶',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.AST] !== '';
                                        },
                                    },
                                    {
                                        label: '',
                                        unit: 'u/L',
                                        formItemType: FORM_ITEM_TYPE.input,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.AST],
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.AST] !== '';
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.AST], v);
                                            },
                                        },
                                        config: {
                                            formatLength: 2, supportFraction: true,
                                        },
                                        maxLength: 6,
                                        disabled: this.disabled,
                                        type: 'number',
                                    },
                                ],
                                [
                                    {
                                        label: '胆红素',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.bilirubin] !== '';
                                        },
                                    },
                                    {
                                        label: '',
                                        unit: 'μmol/L',
                                        formItemType: FORM_ITEM_TYPE.input,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.bilirubin],
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.bilirubin] !== '';
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.bilirubin], v);
                                            },
                                        },
                                        config: {
                                            formatLength: 2, supportFraction: true,
                                        },
                                        maxLength: 6,
                                        disabled: this.disabled,
                                        type: 'number',
                                    },
                                    {
                                        label: '结合胆红素',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.SDB] !== '';
                                        },
                                    },
                                    {
                                        label: '',
                                        unit: 'μmol/L',
                                        formItemType: FORM_ITEM_TYPE.input,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.SDB],
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.SDB] !== '';
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.SDB], v);
                                            },
                                        },
                                        config: {
                                            formatLength: 2, supportFraction: true,
                                        },
                                        maxLength: 6,
                                        disabled: this.disabled,
                                        type: 'number',
                                    },

                                ],
                                [
                                    {
                                        label: '白蛋白',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.albumin] !== '';
                                        },
                                    },
                                    {
                                        label: '',
                                        unit: 'g/L',
                                        formItemType: FORM_ITEM_TYPE.input,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.albumin],
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.albumin] !== '';
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.albumin], v);
                                            },
                                        },
                                        config: {
                                            formatLength: 2, supportFraction: true,
                                        },
                                        maxLength: 6,
                                        disabled: this.disabled,
                                        type: 'number',
                                        style: {
                                            flex: 'none',
                                            width: 'calc(50% - 120px)',
                                        },
                                    },
                                    {
                                        label: '',
                                    },
                                ],
                            ],
                        },
                    ],
                    [
                        {
                            label: '肾功能',
                            rowSpan: 2,
                            style: singleLabelStyle,
                        },
                        {
                            formItems: [
                                [
                                    {
                                        label: '血清肌酐',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.SCR] !== '';
                                        },
                                    },
                                    {
                                        label: '',
                                        unit: 'μmol/L',
                                        formItemType: FORM_ITEM_TYPE.input,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.SCR],
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.SCR] !== '';
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.SCR], v);
                                            },
                                        },
                                        config: {
                                            formatLength: 2, supportFraction: true,
                                        },
                                        maxLength: 6,
                                        disabled: this.disabled,
                                        type: 'number',
                                    },
                                    {
                                        label: '血尿素',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.urea] !== '';
                                        },
                                    },
                                    {
                                        label: '',
                                        unit: 'mmol/L',
                                        formItemType: FORM_ITEM_TYPE.input,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.urea],
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.urea] !== '';
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.urea], v);
                                            },
                                        },
                                        config: {
                                            formatLength: 2, supportFraction: true,
                                        },
                                        maxLength: 6,
                                        disabled: this.disabled,
                                        type: 'number',
                                    },
                                ],
                                [
                                    {
                                        label: '血钾浓度',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.bloodPotassiumConcentration] !== '';
                                        },
                                    },
                                    {
                                        label: '',
                                        unit: 'mmol/L',
                                        formItemType: FORM_ITEM_TYPE.input,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.bloodPotassiumConcentration],
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.bloodPotassiumConcentration] !== '';
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.bloodPotassiumConcentration], v);
                                            },
                                        },
                                        config: {
                                            formatLength: 2, supportFraction: true,
                                        },
                                        maxLength: 6,
                                        disabled: this.disabled,
                                        type: 'number',
                                    },
                                    {
                                        label: '血钠浓度',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.bloodSodiumConcentration] !== '';
                                        },
                                    },
                                    {
                                        label: '',
                                        unit: 'mmol/L',
                                        formItemType: FORM_ITEM_TYPE.input,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.bloodSodiumConcentration],
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.bloodSodiumConcentration] !== '';
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.bloodSodiumConcentration], v);
                                            },
                                        },
                                        config: {
                                            formatLength: 2, supportFraction: true,
                                        },
                                        maxLength: 6,
                                        disabled: this.disabled,
                                        type: 'number',
                                    },
                                ],
                            ].concat(supportUA ? [
                                [
                                    {
                                        label: '尿酸',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.UA] !== '';
                                        },
                                    },
                                    {
                                        label: '',
                                        unit: 'μmol/L',
                                        formItemType: FORM_ITEM_TYPE.input,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.UA],
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.UA] !== '';
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.UA], v);
                                            },
                                        },
                                        config: {
                                            formatLength: 2, supportFraction: true,
                                        },
                                        maxLength: 6,
                                        disabled: this.disabled,
                                        type: 'number',
                                    },
                                ],
                            ] : []),
                        },
                    ],
                    [
                        {
                            label: '血脂',
                            rowSpan: 2,
                            style: singleLabelStyle,
                        },
                        {
                            formItems: [
                                [
                                    {
                                        label: '总胆固醇',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.TC] !== '';
                                        },
                                    },
                                    {
                                        label: '',
                                        unit: 'mmol/L',
                                        formItemType: FORM_ITEM_TYPE.input,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.TC],
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.TC] !== '';
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.TC], v);
                                            },
                                        },
                                        config: {
                                            formatLength: 2, supportFraction: true,
                                        },
                                        maxLength: 6,
                                        disabled: this.disabled,
                                        type: 'number',
                                    },
                                ],
                                [
                                    {
                                        label: '血清低密度值蛋白胆固醇',
                                        style: {
                                            height: '60px',
                                            ...singleLabelStyle,
                                        },
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.LDL_C] !== '';
                                        },
                                    },
                                    {
                                        label: '',
                                        unit: 'mmol/L',
                                        formItemType: FORM_ITEM_TYPE.input,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.LDL_C],
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.LDL_C] !== '';
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.LDL_C], v);
                                            },
                                        },
                                        config: {
                                            formatLength: 2, supportFraction: true,
                                        },
                                        maxLength: 6,
                                        disabled: this.disabled,
                                        type: 'number',
                                    },
                                ],
                            ],
                        },
                        {
                            formItems: [
                                [
                                    {
                                        label: '甘油三酯',
                                        style: singleLabelStyle,
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.TG] !== '';
                                        },
                                    },
                                    {
                                        label: '',
                                        unit: 'mmol/L',
                                        formItemType: FORM_ITEM_TYPE.input,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.TG],
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.TG] !== '';
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.TG], v);
                                            },
                                        },
                                        config: {
                                            formatLength: 2, supportFraction: true,
                                        },
                                        maxLength: 6,
                                        disabled: this.disabled,
                                        type: 'number',
                                    },
                                ],
                                [
                                    {
                                        label: '血清高密度值蛋白胆固醇',
                                        style: {
                                            height: '60px',
                                            ...singleLabelStyle,
                                        },
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.HDL_C] !== '';
                                        },
                                    },
                                    {
                                        label: '',
                                        unit: 'mmol/L',
                                        formItemType: FORM_ITEM_TYPE.input,
                                        value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.HDL_C],
                                        valueNonEmpty: () => {
                                            return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.HDL_C] !== '';
                                        },
                                        on: {
                                            input: (v) => {
                                                this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.HDL_C], v);
                                            },
                                        },
                                        config: {
                                            formatLength: 2, supportFraction: true,
                                        },
                                        maxLength: 6,
                                        disabled: this.disabled,
                                        type: 'number',
                                    },
                                ],
                            ],
                        },
                    ],
                    [
                        {
                            label: '糖化血红蛋白',
                            style: singleLabelStyle,
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.HbA1c] !== '';
                            },
                        },
                        {
                            label: '',
                            unit: '%',
                            formItemType: FORM_ITEM_TYPE.input,
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.HbA1c],
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.HbA1c] !== '';
                            },
                            on: {
                                input: (v) => {
                                    this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.HbA1c], v);
                                },
                            },
                            config: {
                                formatLength: 2, supportFraction: true,
                            },
                            maxLength: 6,
                            disabled: this.disabled,
                            type: 'number',
                        },
                    ],
                    [
                        {
                            label: '乙型肝炎表面抗原',
                            style: {
                                height: '52px',
                                ...singleLabelStyle,
                            },
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.HBsAg].value !== '';
                            },
                        },
                        {
                            label: '',
                            options: PUBLIC_HEALTHY_OPTION.diagnosisStatus,
                            formItemType: FORM_ITEM_TYPE.select,
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.HBsAg].value,
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.HBsAg].value !== '';
                            },
                            on: {
                                input: (v) => {
                                    this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.HBsAg, 'value'], v);
                                },
                            },
                            disabled: this.disabled,
                        },
                    ],
                    [
                        {
                            label: '心电图',
                            rowSpan: ecgRowSpan,
                            style: singleLabelStyle,
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.ECG].value !== '';
                            },
                        },
                        {
                            formItems: this.ECGFormItems,
                        },
                    ],
                    [
                        {
                            label: '胸片X线片',
                            rowSpan: chestXRayRowSpan,
                            style: singleLabelStyle,
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.chestXRay].value !== '';
                            },
                        },
                        {
                            formItems: this.chestXRayFormItems,
                        },
                    ],
                    [
                        {
                            label: 'B超',
                            rowSpan: bScanRowSpan,
                            style: singleLabelStyle,
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.bScan].value !== '';
                            },
                        },
                        {
                            formItems: this.bScanFormItems,
                        },
                    ],
                    [
                        {
                            label: '宫颈涂片',
                            style: this.sex !== '女' ? {
                                ...disabledBgStyle, ...singleLabelStyle,
                            } : singleLabelStyle,
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.TCT].value !== '' || this.sex !== '女';
                            },
                        },
                        {
                            label: '',
                            style: this.sex !== '女' && disabledBgStyle,
                            options: PUBLIC_HEALTHY_OPTION.abnormal,
                            formItemType: FORM_ITEM_TYPE.select,
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.TCT].value,
                            valueNonEmpty: () => {
                                return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.TCT].value !== '' || this.sex !== '女';
                            },
                            on: {
                                input: (v) => {
                                    this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.TCT, 'value'], v);
                                },
                            },
                            disabled: this.disabled || this.sex !== '女',
                        },
                        {
                            label: '',
                            formItemType: FORM_ITEM_TYPE.input,
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.TCT].text,
                            on: {
                                input: (v) => {
                                    this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.TCT, 'text'], v);
                                },
                            },
                            disabled: !(this.curForm[PUBLIC_HEALTHY_FORM_KEYS.TCT].value === 2) || this.disabled || this.sex !== '女',
                            maxLength: 50,
                        },
                    ],
                    [
                        {
                            label: '其他',
                            style: singleLabelStyle,
                        },
                        {
                            label: '',
                            formItemType: FORM_ITEM_TYPE.input,
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.assistInspectOther],
                            on: {
                                input: (v) => {
                                    this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.assistInspectOther], v);
                                },
                            },
                            maxLength: 50,
                            disabled: this.disabled,
                        },
                    ],
                ];
            },
        },

        watch: {
            sex: {
                handler(v) {
                    this.updateFormBySex(v);
                },
                immediate: true,
                deep: true,
            },
        },
        methods: {
            updateFormBySex(sex) {
                if (sex !== '女') {
                    this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.TCT, 'value'], '');
                }
            },
        },
    };
</script>

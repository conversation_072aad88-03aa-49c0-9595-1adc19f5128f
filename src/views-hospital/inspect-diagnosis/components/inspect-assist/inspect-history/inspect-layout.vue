<template>
    <div class="inspect-detail-layout">
        <div v-if="title" class="inspect-detail-layout-title">
            <span>{{ title }}</span>
            <span>
                <slot name="btn"></slot>
            </span>
        </div>

        <template v-if="contentWithStyle">
            <div
                class="inspect-detail-layout-content"
                :style="{
                    'padding': paddingWithStyle ? '0 16px' : 'none'
                }"
                v-html="content"
            >
            </div>
        </template>

        <template v-else>
            <div
                :style="{
                    'padding': paddingWithStyle ? '0 16px' : 'none'
                }"
            >
                <slot name="content">
                    <span>{{ content }}</span>
                </slot>
            </div>
        </template>

        <div v-if="isEmptyContent" class="inspect-detail-layout-empty-content">
            <span>暂无</span>
        </div>

        <div v-if="imageList.length" class="inspect-detail-layout-image">
            <abc-image
                v-for="(img,i) in showImageList"
                :key="i"
                :src="getUrl(img)"
                fit="cover"
                :width="80"
                :height="60"
                @click="previewImage(img, showImageList)"
            ></abc-image>
            <span v-if="imageList.length > 3" class="image-total">
                {{ imageList.length }}张
            </span>
        </div>
    </div>
</template>

<script>
    import { medicalImagingViewerDialogService } from '@/medical-imaging-viewer/store/medical-imaging-viewer-dialog';
    import { isPDF } from '@/utils';
    import pdfImage from '@/assets/images/pdf-icon.png';

    export default {
        name: 'InspectLayout',
        props: {
            title: {
                type: String,
                default: '',
            },
            content: {
                type: [String, Array, Object],
                default: '',
            },
            imageList: {
                type: Array,
                default: () => [],
            },
            contentWithStyle: {
                type: Boolean,
                default: false,
            },
            paddingWithStyle: {
                type: Boolean,
                default: false,
            },
            isEmptyContent: {
                type: Boolean,
                default: false,
            },
        },
        computed: {
            showImageList() {
                return this.imageList.slice(0, 3);
            },
        },
        methods: {
            getUrl(file) {
                if (isPDF(file.url)) {
                    return pdfImage;
                }
                return file.url;
            },

            previewImage(file) {
                if (isPDF(file.url)) {
                    window.open(file.url, '_blank');
                    return;
                }

                medicalImagingViewerDialogService.previewImageAttachmentNoFileValidate({
                    attachments: this.imageList,
                    attachment: file,
                });
            },
        },
    };
</script>

<style lang='scss'>
@import '~styles/theme.scss';

.inspect-detail-layout {
    font-size: 13px;

    .inspect-detail-layout-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 4px;
        line-height: 18px;

        >span:first-child {
            font-weight: bold;
        }
    }

    .inspect-detail-layout-content {
        line-height: 18px;
        word-break: break-word;
        white-space: pre-wrap;
    }

    .inspect-detail-layout-empty-content {
        line-height: 18px;
        color: $T3;
        white-space: pre-wrap;
    }

    .inspect-detail-layout-image {
        display: flex;
        align-items: center;
        width: 100%;

        & + .inspect-detail-layout-image {
            margin-left: 4px;
        }

        .abc-image-wrapper {
            margin-right: 4px;
            cursor: pointer;
            border-radius: 2px;
        }

        .image-total {
            font-size: 14px;
            font-weight: 400;
            line-height: 18px;
            color: $B1;
        }
    }
}
</style>

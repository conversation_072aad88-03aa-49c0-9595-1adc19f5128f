<template>
    <main class="inspect-assist sidebar-container">
        <assist-header
            v-model="curTab"
            :is-fang-she="isFangShe"
            :is-video-cam-mode="isVideoCamMode"
            :device-type="deviceType"
            @change="handleTabChange"
        ></assist-header>

        <template v-if="!noImageDevice">
            <!-- 影像 - 彩超 -->
            <report-image
                v-show="isVideoCamMode && !historyReportShow"
            ></report-image>

            <!-- 影像 - 放射科 -->
            <dicom-viewer
                v-if="isFangShe"
                ref="assistModule"
                :study-instance-uid="studyInstanceUid"
                :scheduled-procedure-step-id="scheduledProcedureStepId"
                :apply-no="applyNo"
                :editable="reportIsEditable"
                :patient-id="patientId"
            ></dicom-viewer>
        </template>

        <!-- 历史报告 -->
        <div
            v-show="historyReportShow"
            class="inspect-history-report-wrapper"
        >
            <inspect-history
                :patient-id="patientId"
                :cur-report-item="curReportItem"
                is-need-copy
                :editable="reportIsEditable"
            ></inspect-history>
        </div>
    </main>
</template>

<script>
    import { mapGetters } from 'vuex';
    import AssistHeader from './inspect-assist-header.vue';
    import ReportImage from './report-image-wrapper.vue';
    import DicomViewer from './dicom-viewer.vue';
    import { INSPECT_EVENT_KEY } from '@/views-hospital/inspect-diagnosis/utils/constant';
    import WaitingInspectManageService from '@/views-hospital/inspect-diagnosis/core/waiting-inspect-manage-service.js';
    import { NO_IMAGE_DEVICE } from '@/views-hospital/inspect-diagnosis/utils/constant';
    import { INSPECT_TYPE } from '@/views-hospital/inspect-setting/utils/constant';
    import { EXAMINATION_STATUS } from '@/views-hospital/inspect-diagnosis/utils/constant';


    export default {
        name: 'InspectAssist',
        components: {
            AssistHeader,
            InspectHistory: () => import('./inspect-history/index.vue'),
            ReportImage,
            DicomViewer,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        data() {
            return {
                curTab: '影像',
                historyReportShow: false,
            };
        },

        computed: {
            ...mapGetters(['inspect']),

            ...mapGetters('inspect', [
                'needCheck',
            ]),

            reportId() {
                return this.$route.params.id;
            },
            deviceType() {
                return this.inspect?.selectedItem?.deviceType;
            },
            dcm4cheeDetail() {
                return this.$abcPage.$store.state.postData.examinationApplySheetView?.dcm4cheeDetail;
            },
            studyInstanceUid() {
                return this.dcm4cheeDetail?.studyInstanceUid;
            },
            scheduledProcedureStepId() {
                return this.dcm4cheeDetail?.scheduledProcedureStepId;
            },
            applyNo() {
                return this.$abcPage.$store.state.postData.examinationApplySheetNo;
            },

            patientId() {
                return this.inspect.selectedPatient?.id;
            },

            isVideoCamMode() {
                const type = this.inspect?.selectedItem?.deviceType;

                if (!type) return false;

                return [
                    INSPECT_TYPE.CDU,
                    INSPECT_TYPE.GASTROSCOPE,
                ].includes(type);
            },

            noImageDevice() {
                return NO_IMAGE_DEVICE.includes(this.inspect?.selectedItem?.deviceType);
            },

            curReportItem() {
                return this.inspect.selectedItem;
            },
            reportIsEditable() {
                return this.checkReportIsEditable();
            },
            isFangShe() {
                return [
                    INSPECT_TYPE.MR,
                    INSPECT_TYPE.MG,
                    INSPECT_TYPE.DR,
                    INSPECT_TYPE.CT,
                ].includes(this.deviceType);
            },
        },
        mounted() {
            // 路由完成跳转事件
            this.$abcEventBus.$on(INSPECT_EVENT_KEY.ROUTE_CHANGE_FINISHED, (deviceType) => {
                this.resetState(deviceType);
            }, this);

            WaitingInspectManageService.getInstance().start();

            if (!this.isFangShe) {
                this.curTab = '历史报告';
                this.historyReportShow = true;
            }
        },
        methods: {
            handleTabChange(label) {
                this.historyReportShow = label === '历史报告';
            },
            resetState(deviceType) {
                switch (deviceType) {
                    case INSPECT_TYPE.CDU:
                    case INSPECT_TYPE.MR:
                    case INSPECT_TYPE.MG:
                    case INSPECT_TYPE.DR:
                    case INSPECT_TYPE.CT:
                    case INSPECT_TYPE.GASTROSCOPE:
                        this.historyReportShow = false;
                        this.curTab = '影像';
                        break;
                    case INSPECT_TYPE.OTHER:
                    case INSPECT_TYPE.MDB:
                        this.historyReportShow = true;
                        this.curTab = '历史报告';
                        break;
                    default:
                        this.historyReportShow = true;
                        this.curTab = '历史报告';
                        break;
                }
            },
            // 检查报告是否可编辑
            checkReportIsEditable() {
                const {
                    postData, editTag,
                } = this.$abcPage.$store.state;

                if ([EXAMINATION_STATUS.WAIT, EXAMINATION_STATUS.WAIT_WRITE ].includes(postData.status)) {
                    return true;
                }

                if (postData.status === EXAMINATION_STATUS.WAIT_CHECK && editTag) {
                    return true;
                }

                return !!(!this.needCheck && editTag);
            },
        },
    };
</script>

<style lang="scss" scoped>
.inspect-assist {
    position: relative;
    height: 100%;

    .inspect-history-report-wrapper {
        position: absolute;
        top: 56px;
        left: 0;
        width: 100%;
        height: calc(100% - 56px);
    }
}
</style>

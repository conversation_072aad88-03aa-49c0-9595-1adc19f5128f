<template>
    <main class="inspect__report-image-wrapper">
        <web-cam
            v-if="isUniversalWebcam"
            ref="webCamImageRef"
            :disabled="!isEditing"
            :device-type="deviceType"
            @image-cancel="handleImageCancel"
            @image-confirm="handleImageConfirm"
            @update-cur-sheet-id="updateSheetId"
        ></web-cam>

        <report-image
            v-else
            ref="imageRef"
            :disabled="!isEditing"
            @image-cancel="handleImageCancel"
            @image-confirm="handleImageConfirm"
            @update-cur-sheet-id="updateSheetId"
        ></report-image>
    </main>
</template>

<script>
    import ReportImage from '@/views-hospital/inspect-diagnosis/components/inspect-assist/report-image.vue';
    import WebCam from '@/views-hospital/inspect-diagnosis/components/inspect-assist/web-cam.vue';
    import { mapGetters } from 'vuex';
    import { INSPECT_TYPE } from '@/views-hospital/inspect-setting/utils/constant';
    import {
        EXAMINATION_STATUS, INSPECT_EVENT_KEY,
    } from '@/views-hospital/inspect-diagnosis/utils/constant';

    export default {
        name: 'InspectReportImageWrapper',
        components: {
            ReportImage,
            WebCam,
        },

        inject: {
            $abcPage: {
                default: {},
            },
        },
        data() {
            return {
                imageList: [],

                sheetId: '',
            };
        },
        computed: {
            ...mapGetters(['inspect']),

            ...mapGetters('inspect', [
                'needCheck',
            ]),

            isEditing() {
                const { id } = this.$route.params;
                return this.checkReportIsEditing() && this.sheetId === id;
            },

            isUniversalWebcam() {
                const type = this.inspect?.selectedItem?.deviceType;

                if (!type) return false;

                return [
                    INSPECT_TYPE.GASTROSCOPE,
                ].includes(type);
            },

            deviceType() {
                return this.inspect?.selectedItem?.deviceType || '';
            },
        },

        async mounted() {
            this.$abcEventBus.$on(INSPECT_EVENT_KEY.resetCollectedImageStatus, () => {
                this.$refs.imageRef && this.$refs.imageRef.saveImageCheckStatus();
                this.$refs.webCamImageRef && this.$refs.webCamImageRef.saveImageCheckStatus();
            }, this);
        },
        beforeDestroy() {
            this.$abcEventBus.$offVmEvent(this._uid);
        },
        methods: {
            updateSheetId(id) {
                this.sheetId = id;
            },
            handleImageCancel(url) {
                if (!this.isEditing) {
                    return;
                }
                this.$abcEventBus.$emit(INSPECT_EVENT_KEY.removeImage, url);
            },
            handleImageConfirm(url) {
                if (!this.isEditing) {
                    this.$Toast({
                        type: 'warn',
                        message: '检查报告当前状态不可编辑',
                    });
                    return;
                }
                this.$abcEventBus.$emit(INSPECT_EVENT_KEY.addImage, url);
            },
            checkReportIsEditing() {
                const {
                    postData, editTag,
                } = this.$abcPage.$store.state;

                if ([EXAMINATION_STATUS.WAIT, EXAMINATION_STATUS.WAIT_WRITE].includes(postData.status)) {
                    return true;
                }

                if (postData.status === EXAMINATION_STATUS.WAIT_CHECK) {
                    return editTag;
                }

                if (postData.status === EXAMINATION_STATUS.CHECKED && !this.needCheck) {
                    return editTag;
                }

                return false;
            },
        },
    };
</script>

<style lang="scss">
.inspect__report-image-wrapper {
    height: calc(100% - 56px);
    overflow: hidden;
    background: #000000;
}
</style>

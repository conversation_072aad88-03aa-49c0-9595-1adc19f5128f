<template>
    <div class="inspect-diagnosis-quick-list-wrapper">
        <biz-quick-list
            :tools="toolList"
            :is-last="isLast"
            :loading="loading"
            :show-empty="quickList.length === 0"
            data-cy="inspect-quick-list"
            :scroll-load-func="fetchQuickList"
        >
            <template #search>
                <abc-search
                    v-model="searchBarFilterParams.searchKey"
                    placeholder="搜索"
                    focus-placeholder="姓名 / 手机 / 诊号 / 项目"
                    style="width: 100%;"
                    data-cy="inspect-search-abc-input"
                    @search="handleSearch"
                    @clear="handleClear"
                >
                    <read-card
                        v-if="isEnableReadSocialCardByType && !searchBarFilterParams.searchKey"
                        slot="appendInner"
                        :business-type="ReadCardBusinessTypeEnum.TREATMENT"
                        @read-card="onReadCard"
                    ></read-card>
                </abc-search>
            </template>

            <template #operate>
                <template v-if="!searchBarFilterParams.searchKey">
                    <!-- 设备 -->
                    <abc-popover
                        v-model="devicePopoverVisible"
                        placement="bottom-start"
                        trigger="manual"
                        theme="custom"
                        popper-class="hospital-device-dropdown-wrapper"
                        data-cy="inspect-device-abc-popover"
                    >
                        <div
                            slot="reference"
                            class="hospital-device"
                            @mouseenter="devicePopoverVisible = true"
                            @mouseleave="devicePopoverVisible = false"
                        >
                            <span
                                class="device-name"
                                :title="selectedDepartment.name"
                            >
                                {{ selectedDepartment.name }}
                            </span>

                            <span v-if="!!selectedDepartment.todo.waitExamineCount" class="device-todo-count">
                                {{
                                    selectedDepartment.todo.waitExamineCount <= 99 ? (
                                        selectedDepartment.todo.waitExamineCount
                                    ) : `99+`
                                }}
                            </span>
                        </div>

                        <div
                            class="normal-wrapper"
                            @mouseenter="devicePopoverVisible = true"
                            @mouseleave="devicePopoverVisible = false"
                        >
                            <div class="popover-card">
                                <div class="hospital-device-dropdown">
                                    <div
                                        v-for="d in departmentTodoList"
                                        :key="d.id"
                                        class="hospital-dropdown-item"
                                        @click="handleSelectDepartment(d.id)"
                                    >
                                        <span class="drop-device-name" :title="d.name">
                                            {{ d.name }}
                                        </span>

                                        <span>
                                            <span class="todo-label">待检</span>
                                            <span class="device-todo">{{ d.todo.waitExamineCount }}</span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </abc-popover>

                    <div class="quick-list-tabs_right_part">
                        <ql-filter-popover
                            :filter-params="searchBarFilterParams"
                            :is-support-channel-filter="isSupportChannelFilter"
                            :co-clinic-list="coClinicList"
                            @filter="okQlFilter"
                        ></ql-filter-popover>
                    </div>
                </template>

                <template v-else>
                    <div class="search-tips">
                        搜索到
                        <span
                            style=" margin: 0 4px; font-size: 12px; color: #000000;"
                        >
                            {{ totalCount }}
                        </span>
                        条信息
                    </div>
                </template>
            </template>

            <template #default>
                <ul>
                    <div
                        v-for="(item, idx) in filterList"
                        :key="idx"
                        class="ql-filter-content"
                    >
                        <span class="ellipsis">{{ item.label }}：{{ item.value }}</span>

                        <abc-delete-icon
                            @delete="resetQlFilter(item.key)"
                        ></abc-delete-icon>
                    </div>

                    <biz-quick-list-item
                        v-for="item in quickList"
                        :key="item.id"
                        :quick-item="item"
                        :is-active="selectedItem && selectedItem.id === item.id"
                        :is-done="item.status === EXAMINATION_STATUS.CHECKED"
                        :status-class="{
                            'green-2': item.status === EXAMINATION_STATUS.WAIT_WRITE,
                            gray: item.status === EXAMINATION_STATUS.CHECKED,
                            'more-width': true,
                        }"
                        :show-status="true"
                        with-describe
                        @select="handleSelectItem(item)"
                    >
                        <template #patient-name-append>
                            <span class="ellipsis" style="margin-left: 4px;">
                                {{ item.name }}
                            </span>
                        </template>

                        <template v-if="isSendToCenterOrgan(item)" #content>
                            <abc-tag-v2
                                shape="square"
                                theme="warning"
                                variant="outline"
                                size="tiny"
                            >
                                外包
                            </abc-tag-v2>
                        </template>

                        <template #status>
                            {{ getStatusName(item.status) }}
                        </template>

                        <template #abstract>
                            <span>
                                {{ item.orderNo }}
                            </span>

                            <span v-if="isReceiveFromCoOrgan(item) && item.coClinicName" style="margin-left: 8px;">
                                {{ item.coClinicName }}
                            </span>
                        </template>

                        <template #date>
                            {{ item.created | quickListTime }}
                        </template>
                    </biz-quick-list-item>
                </ul>
            </template>

            <template v-if="enableCalling" #customTools>
                <!--检查叫号-->
                <div class="entry-item-group">
                    <inspect-call-number>
                        <div class="entry-item">
                            <abc-icon
                                icon="s-volume-color"
                                size="20"
                            ></abc-icon>

                            <div class="content">
                                叫号
                            </div>
                            <div class="describe">
                                <abc-icon icon="Arrow_Rgiht"></abc-icon>
                            </div>
                        </div>
                    </inspect-call-number>
                </div>
            </template>
        </biz-quick-list>

        <!--预约登记 & 快速开单-->
        <reg-regist-dialog
            v-if="regVisible"
            v-model="regVisible"
            :editable="true"
            :registors="doctorList"
        ></reg-regist-dialog>

        <!--眼科检查设备上机-->
        <inspect-device-dialog
            v-if="showDeviceDialog"
            ref="deviceDataDialog"
            v-model="showDeviceDialog"
            :exam-sheet-id="selectedItem.id"
            :default-device-model-id="selectedItem.deviceModelId"
            @complete="handleComplete"
            @refresh="handleRefresh"
        >
        </inspect-device-dialog>

        <!--眼科新增检查-->
        <add-inspect-dialog
            v-if="showAddInspectDialog"
            v-model="showAddInspectDialog"
        ></add-inspect-dialog>
    </div>
</template>

<script>
    import {
        mapGetters, mapState,
    } from 'vuex';
    // api
    import RegistrationAPI from '@/api/registrations/index';
    // constants
    import {
        EXAMINATION_STATUS,
        customOutpatientBusinessType,
        mergeOutpatientBusinessType,
        InspectStatusTextEnum,
        BusinessTypeTextEnum,
        INSPECT_REPORT_TYPE,
        INSPECT_EVENT_KEY,
    } from '@/views-hospital/inspect-diagnosis/utils/constant';
    import { ReadCardBusinessTypeEnum } from '@/views/layout/read-card/costants.js';
    // util
    import { parseTime } from '@/utils';
    import {
        getMonthStartDate, getWeekStartDate, prevDate,
    } from '@abc/utils-date';
    import { debounce } from '@/utils/lodash';
    import AbcAccess from '@/access/utils';
    // component
    import InspectCallNumber from '@/views-hospital/inspect-diagnosis/components/call-number/index.vue';
    import SettingAPI from 'api/settings';
    import { EXAM_SHEET_TYPE } from 'views/examination/util/constants';
    import BizQuickList from '@/components-composite/biz-quick-list/index.js';
    import BizQuickListItem from '@/components-composite/biz-quick-list-item/index.js';
    import QlFilterPopover from '@/views-hospital/inspect-diagnosis/components/quick-list/ql-filter-popover.vue';
    import AbcSearch from 'components/abc-search/index.vue';
    import AbcSocket from 'views/common/single-socket';
    import ReadCard from '@/views/layout/read-card/index.vue';

    export default {
        name: 'InspectList',
        components: {
            ReadCard,
            AbcSearch,
            QlFilterPopover,
            BizQuickListItem,
            RegRegistDialog: () => import('@/views-hospital/inspect-regist/components/reg-regist-dialog.vue'),
            InspectCallNumber,
            BizQuickList,
            InspectDeviceDialog: () => import('@/views-hospital/inspect-diagnosis/components/inspect-online/inspect-device-dialog.vue'),
            AddInspectDialog: () => import('@/views-ophthalmology/inspect/common/add-inspect-dialog.vue'),
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        data() {
            // 两天前
            const now = new Date();
            const today = parseTime(now, 'y-m-d', true);
            // 昨天
            const lastDay = parseTime(prevDate(now), 'y-m-d', true);
            // 两天前
            const last2Day = parseTime(prevDate(now, 2), 'y-m-d', true);
            // 本周一
            const weekStartDate = parseTime(getWeekStartDate(now), 'y-m-d', true);
            // 本月1号
            const monthStartDate = parseTime(getMonthStartDate(now), 'y-m-d', true);

            this._today = today;
            this._lastDay = lastDay;
            this._last2Day = last2Day;
            this._weekStartDate = weekStartDate;
            this._monthStartDate = monthStartDate;

            return {
                ReadCardBusinessTypeEnum,
                EXAMINATION_STATUS,
                isLast: false,
                loading: false,
                searchBarFilterParams: {
                    searchKey: '',
                    datePickerValue: [ today, today ],
                    executeDepartmentId: null,
                    status: '',
                    businessType: '',
                    coClinicId: '',
                },
                totalCount: 0,

                // 科室待办列表
                departmentTodoList: [],
                // 科室列表
                departmentList: [],

                regVisible: false,

                coClinicList: [], // 合作诊所列表

                devicePopoverVisible: false,
                dateCustomSelectStr: '',

                showDeviceDialog: false,
                showAddInspectDialog: false,
            };
        },
        computed: {
            ...mapGetters([
                'inspect',
                'userInfo',
                'clinicBasic',
            ]),

            ...mapGetters('viewDistribute',[
                'viewDistributeConfig',
            ]),

            ...mapState('inspect',{
                enableCalling: (state) => state.callNumberRule.enableCalling,
            }),

            isEnableReadSocialCardByType() {
                return this.clinicBasic.isEnableReadSocialCardByType;
            },

            isOpenCreateInspectSheet() {
                return this.viewDistributeConfig.Inspect.isOpenCreateInspectSheet ||
                    AbcAccess.getPurchasedByKey(AbcAccess.accessMap.PHYSICAL_EXAMINATION);
            },

            isOphthalmology() {
                return this.viewDistributeConfig.Inspect.isOphthalmology;
            },

            routeKey() {
                return this.viewDistributeConfig.Inspect.routeKey;
            },

            quickList() {
                return this.inspect.quickList;
            },

            toolList() {
                const arr = [];
                if (this.isOphthalmology) {
                    arr.push(...[{
                        iconUrl: '',
                        label: '上机检查',
                        describe: '',
                        icon: 's-flask-color',
                        iconColor: '#0072F9',
                        handler: () => {
                            this.showDeviceDialog = true;
                        },
                    },{
                        iconUrl: '',
                        label: '快速开单',
                        describe: '',
                        icon: 's-order-color',
                        iconColor: '#20B150',
                        handler: this.addNew,
                    }]);
                } else {
                    if (this.isOpenCreateInspectSheet) {
                        arr.push({
                            iconUrl: '',
                            label: '快速开单',
                            describe: '',
                            icon: 's-order-color',
                            iconColor: '#20B150',
                            handler: () => {
                                this.regVisible = true;
                            },
                        });
                    }
                }

                return arr;
            },

            selectedItem: {
                get() {
                    return this.inspect.selectedItem || { subType: '' };
                },
                set(v) {
                    this.$store.dispatch('setSelectedItem', {
                        type: 'inspect',
                        selectedItem: v,
                    });
                },
            },

            doctorList() {
                return this.$abcPage.$store.state.operateDoctorList.map((item) => ({
                    employeeId: item.value,
                    employeeName: item.label,
                }));
            },

            datePickerStr: {
                get() {
                    const startDate = this.searchBarFilterParams.datePickerValue[0];
                    const endDate = this.searchBarFilterParams.datePickerValue[1];
                    if (startDate === this._today && endDate === this._today) {
                        return '今天';
                    } if (startDate === this._lastDay && endDate === this._lastDay) {
                        return '昨天';
                    } if (startDate === this._last2Day && endDate === this._today) {
                        return '最近3天';
                    } if (startDate === this._weekStartDate && endDate === this._today) {
                        return '本周';
                    } if (startDate === this._monthStartDate && endDate === this._today) {
                        return '本月';
                    } if (startDate === endDate) {
                        return startDate.split('-').splice(1, 2).join('-');
                    }
                    const _start = startDate.split('-').splice(1, 2).join('-');
                    const _end = endDate.split('-').splice(1, 2).join('-');
                    return `${_start} ~ ${_end}`;

                },
            },

            filterList() {
                const {
                    datePickerValue,
                    status,
                    businessType,
                } = this.searchBarFilterParams;

                const curCoClinicName = this.coClinicList?.find((d) => d.id === this.searchBarFilterParams.coClinicId)?.name;

                return [
                    {
                        label: '检查时间',
                        value: this.datePickerStr,
                        isHidden: datePickerValue[0] === this._today && datePickerValue[1] === this._today,
                        key: 'datePickerValue',
                    },
                    {
                        label: '检查状态',
                        value: InspectStatusTextEnum[status],
                        isHidden: status === '',
                        key: 'status',
                    },
                    {
                        label: '开单渠道',
                        value: businessType === customOutpatientBusinessType ? '门诊' : BusinessTypeTextEnum[businessType],
                        isHidden: businessType === '',
                        key: 'businessType',
                    },
                    {
                        label: '合作诊所',
                        value: curCoClinicName,
                        isHidden: this.searchBarFilterParams.coClinicId === '',
                        key: 'coClinicId',
                    },
                ].filter((item) => !item.isHidden);
            },

            selectedDepartment() {
                return this.departmentTodoList.find((d) => d.id === this.searchBarFilterParams.executeDepartmentId) || {
                    todo: {
                        waitExamineCount: 0,
                    },
                };
            },

            // 是否支持渠道筛选
            isSupportChannelFilter() {
                return this.viewDistributeConfig.Inspect.isSupportChannelFilter;
            },
        },
        async created() {
            // 筛选防抖
            this.debounceFilterQuickList = debounce(() => {
                this.filterQuickList();
            }, 250, true);

            this.debounceRefreshQuickList = debounce(() => {
                this.fetchQuickList({
                    isRefresh: true,
                });
            }, 250, true);

            // 获取科室列表,需要在quick-list之前获取，生成科室待办列表需要科室数据
            await this.fetchDepartmentList();

            // 初始化quick-list
            await Promise.all([
                this.fetchCoClinicList(),
                this.filterQuickList(),
            ]);

            this.handleSocketEvent();
        },
        beforeDestroy() {
            this.$store.dispatch('clearQuickList', 'inspect');

            this.$store.dispatch('setScrollParams', {
                type: 'inspect',
                scrollParams: {
                    keyword: '',
                    offset: 0,
                },
            });

            this.$store.dispatch('setSelectedItem', {
                type: 'inspect',
                selectedItem: null,
            });
        },
        methods: {
            handleSelectDepartment(deviceId) {
                this.searchBarFilterParams.executeDepartmentId = deviceId;
                this.devicePopoverVisible = false;
                this.handleSearch();
            },

            okQlFilter(filterParams) {
                Object.assign(this.searchBarFilterParams, filterParams);
                this.handleSearch();
            },
            // 检验单是否送往中心门店执行
            isSendToCenterOrgan(item) {
                return item.coFlag === EXAM_SHEET_TYPE.sendToCenterOrgan;
            },

            // 检验单是否来自合作门店
            isReceiveFromCoOrgan(item) {
                return item.coFlag === EXAM_SHEET_TYPE.receiveFromCoOrgan;
            },

            // 获取科室列表
            async fetchDepartmentList() {
                try {
                    const res = await RegistrationAPI.fetchDoctorRegsFee(
                        this.userInfo.id, '', '', 1, 1,
                    );
                    this.departmentList = res.map((item) => {
                        return {
                            id: item.departmentId,
                            name: item.departmentName,
                        };
                    }).filter((item) => item.name !== '');
                } catch (e) {
                    console.error('拉取科室列表错误：', e);
                }
            },

            // 筛选quick-list
            async filterQuickList() {
                this.loading = true;
                this.isLast = false;

                // 清空当前数据列表
                await this.$store.dispatch('clearQuickList', 'inspect');
                // 设置查询参数
                await this.$store.dispatch('setScrollParams', {
                    type: 'inspect',
                    scrollParams: {
                        ...this.searchBarFilterParams,
                        keyword: this.searchBarFilterParams.searchKey,
                        beginDate: this.searchBarFilterParams.datePickerValue[0],
                        endDate: this.searchBarFilterParams.datePickerValue[1],
                        businessType: this.searchBarFilterParams.businessType === customOutpatientBusinessType ?
                            mergeOutpatientBusinessType : this.searchBarFilterParams.businessType,
                        type: 2,
                        offset: 0,
                    },
                });

                await this.fetchQuickList();
                this.selectFirst();
                this.loading = false;
            },

            // 选择检查项
            handleSelectItem(item) {
                const {
                    blank,
                    ris,
                } = this.routeKey;

                if (!item.id) {
                    this.selectedItem = null;
                    this.$router.push({
                        name: blank,
                    });
                    return;
                }

                const preSelectedItem = this.selectedItem;

                const name = this.getReportTypeRouteName(item.subType);

                this.$router.push({
                    name,
                    params: {
                        id: item.id,
                    },
                }).then(() => {
                    this.selectedItem = item;

                    name === ris && this.$abcEventBus.$emit(
                        INSPECT_EVENT_KEY.ROUTE_CHANGE_FINISHED,
                        item.deviceType,
                    );
                }).catch(() => {
                    this.selectedItem = preSelectedItem;
                });
            },

            getReportTypeRouteName(type) {
                const {
                    ris,
                    ophthalmology,
                    blank,
                } = this.routeKey;

                switch (type) {
                    case INSPECT_REPORT_TYPE.normal:
                    case INSPECT_REPORT_TYPE.ris:
                        return ris;
                    case INSPECT_REPORT_TYPE.ophthalmology:
                        return ophthalmology;
                    default:
                        return blank;
                }
            },

            // 展示检查状态
            getStatusName(status) {
                return {
                    [EXAMINATION_STATUS.WAIT]: '待检查',
                    [EXAMINATION_STATUS.WAIT_WRITE]: '待编辑',
                    [EXAMINATION_STATUS.WAIT_CHECK]: '待审核',
                    [EXAMINATION_STATUS.REFOUND]: '已退',
                    [EXAMINATION_STATUS.CHECKED]: '已审核',
                }[status - 0];
            },

            // 选择第一个检查项
            selectFirst() {
                this.handleSelectItem(
                    {
                        ...(this.quickList[0] || {}),
                    },
                );
            },

            // 拉取检查列表
            async fetchQuickList({
                isRefresh,
            } = {}) {
                let data;
                if (isRefresh) {
                    data = await this.$store.dispatch('refreshInspectQuickList');
                } else {
                    if (this.isLast) return false;
                    data = await this.$store.dispatch('fetchInspectQuickList');
                }

                this.isLast = data.isLast;

                this.totalCount = data.totalCount;

                const {
                    examinationTodoCountRsp: {
                        examDepartmentTodoCountViews = [],
                    } = {},
                } = data;

                this.departmentTodoList = this.createTodoList(
                    this.departmentList,
                    examDepartmentTodoCountViews,
                );
            },

            // 生成检查科室待办列表
            createTodoList(departmentList, todoCountList) {
                const res = departmentList.map((d) => {
                    const todo = todoCountList.find((t) => t.executeDepartmentId === d.id) || {
                        waitExamineCount: 0,
                        waitViewCount: 0,
                    };
                    return {
                        id: d.id,
                        name: d.name,
                        todo,
                    };
                });

                const all = {
                    id: null,
                    name: '全部',
                    todo: todoCountList.reduce((cur,next) => {
                        cur.waitExamineCount += next.waitExamineCount;
                        cur.waitViewCount += next.waitViewCount;
                        return cur;
                    },{
                        waitExamineCount: 0,
                        waitViewCount: 0,
                    }),
                };

                return [ all, ...res ];
            },

            handleSearch() {
                // 筛选防抖
                this.debounceFilterQuickList();
            },

            handleClear() {
                this.searchBarFilterParams.searchKey = '';
                this.handleSearch();
            },

            resetQlFilter(key) {
                if (key) {
                    if (key === 'datePickerValue') {
                        this.searchBarFilterParams.datePickerValue = [this._today, this._today];
                    } else {
                        this.searchBarFilterParams[key] = '';
                    }
                }
                this.handleSearch();
            },

            // 拉取合作机构列表
            async fetchCoClinicList() {
                try {
                    const data = await SettingAPI.areaInspectionCenter.getCoopStoreList({ pharmacyType: 20 });
                    this.coClinicList = (data.rows || []).map((r) => r.coopClinic);
                } catch (e) {
                    console.error(e);
                }
            },

            handleSocketEvent() {
                const { socket } = AbcSocket.getSocket();

                // ql-change事件，更新新增的检查单
                const inspectServiceCallback = {
                    onUpdate: () => {
                        this.debounceRefreshQuickList();
                    },
                };
                this.$abcPage.InspectService?.addCallback(inspectServiceCallback);

                // update事件，更新 ql 检查单的状态
                const callback = this.updateInspectSheetStatus.bind(this);
                socket.on('examination.sheet.update', callback);

                this.$on('hook:beforeDestroy', () => {
                    this.$abcPage.InspectService?.removeCallback(inspectServiceCallback);

                    socket.off('examination.sheet.update', callback);
                });
            },

            updateInspectSheetStatus({
                examSheetId, status,
            }) {
                this.$store.commit('update_inspect_sheet_status', {
                    id: examSheetId,
                    status,
                });
            },

            handleComplete() {
                this.$abcEventBus.$emit(INSPECT_EVENT_KEY.downloadDeviceCallbackData);
            },

            handleRefresh() {
                this.$abcEventBus.$emit(INSPECT_EVENT_KEY.refreshItemsValue);
            },

            addNew() {
                if (!AbcAccess.check()) return false;
                this.showAddInspectDialog = true;
            },

            onReadCard(response) {
                const { patient } = response;
                const { name } = patient || {};
                this.searchBarFilterParams.searchKey = name;
                this.handleSearch();
            },
        },
    };
</script>

<style lang="scss" scoped>
@import '@/styles/mixin.scss';

.inspect-diagnosis-quick-list-wrapper {
    height: 100%;
}

.quick-list-small {
    ::v-deep .quick-list-item-wrapper {
        .status {
            &.more-width {
                width: 50px;
                max-width: 50px;
            }
        }
    }
}

.hospital-device-dropdown-wrapper {
    top: -8px !important;

    .normal-wrapper {
        padding-top: 8px;

        .popover-card {
            max-height: 30vh;
            padding: 4px;
            overflow: auto;
            background-color: #ffffff;
            border: 1px solid #dadbe0;
            border-radius: var(--abc-border-radius-small);
            box-shadow: 0 3px 8px 0 rgba(0, 0, 0, 0.15);

            .hospital-device-dropdown {
                .hospital-dropdown-item {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    width: 200px;
                    height: 32px;
                    padding: 0 9px 0 12px;
                    cursor: pointer;
                    transition: all 0.2s;

                    &:hover {
                        background: $P5;
                        border-radius: var(--abc-border-radius-small);
                    }

                    .drop-device-name {
                        max-width: 118px;
                        overflow: hidden;
                        font-size: 14px;
                        font-weight: bold;
                        line-height: 20px;
                        color: #000000;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }

                    .device-done,
                    .split-line {
                        font-size: 14px;
                        font-weight: bold;
                        line-height: 20px;
                        color: #1ec761;
                    }

                    .split-line {
                        margin: 0 2px;
                    }

                    .device-todo {
                        font-size: 14px;
                        font-weight: bold;
                        line-height: 20px;
                        color: $B1;
                        letter-spacing: 0;
                    }

                    .todo-label {
                        color: $T2;
                    }
                }
            }

            .quick-list-select-empty-status {
                padding: 32px 16px 16px;

                .tip {
                    margin-top: 8px;
                    margin-bottom: 16px;
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 16px;
                    color: #aab4bf;
                    text-align: center;
                }
            }
        }
    }
}

.inspect-diagnosis_date-picker {
    .abc-date-picker {
        margin-top: -1px;
    }

    .date-picker {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 36px;
        margin-left: auto;

        .date-picker-reference {
            display: flex;
            align-items: center;
        }
    }

    .examination-date-picker {
        margin-left: 0;
    }

    .date-picker-str {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: $T2;
        text-align: right;
        cursor: pointer;

        &.is-select-other-day {
            color: $T1;
        }

        .iconfont {
            position: static;
            color: $P1;
            background-color: transparent;
            transform: translate3d(0, 0%, 0);

            &:hover {
                color: $T2;
            }
        }
    }
}
</style>

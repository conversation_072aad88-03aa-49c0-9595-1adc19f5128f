<template>
    <abc-container
        ref="containerRef"
        has-left-container
        content-align-center
        :is-support-drawer="viewDistributeConfig.isSupportDrawer"
        drawer-toggle-text="待检查列表"
        :has-right-container="!!selectedItem.id"
        :style="containerStyle"
    >
        <abc-container-left>
            <quick-list></quick-list>
        </abc-container-left>

        <abc-container-center class="content-container" data-cy="inspect-center-content">
            <router-view ref="content"></router-view>
        </abc-container-center>
    </abc-container>
</template>

<script>
    import { mapGetters } from 'vuex';
    import { createAbcPage } from '@/core/page/factory.js';
    import QuickList from './components/quick-list/quick-list.vue';
    import { VideoService } from '@/views-hospital/inspect-diagnosis/utils/media';
    import { INSPECT_TYPE } from '@/views-hospital/inspect-setting/utils/constant';
    import ResizeObserver from 'resize-observer-polyfill';
    import { debounce } from '@/utils/lodash';

    export default {
        name: 'HospitalInspectDiagnosis',
        components: {
            QuickList,
        },
        mixins: [
            createAbcPage({
                theme: 'pharmacy',
            }),
        ],

        data () {
            return {
                rightContainerWidth: 0,
            };
        },

        computed: {
            ...mapGetters([
                'inspect',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),

            selectedItem: {
                get() {
                    return this.inspect.selectedItem || { subType: '' };
                },
            },

            containerStyle() {
                return {
                    '--rightContainerWidth': `${this.rightContainerWidth}px`,
                };
            },
        },

        watch: {
            selectedItem() {
                this.getRightContainerWidth();
            },
        },

        mounted() {
            this.$store.dispatch('fetchAllDoctorsRegsFee', {
                allEmployee: 1,
            });
            // 拉取检查叫号规则配置
            this.$store.dispatch('inspect/fetchInspectCallNumberRule');

            VideoService.getInstance().start();

            const debounceHandleContainerSizeChange = debounce(this.handleContainerSizeChange, 250, true);

            this._resizeObserver = new ResizeObserver(debounceHandleContainerSizeChange);

            this._resizeObserver.observe(this.$refs.containerRef.$el);
        },
        beforeDestroy() {
            VideoService.getInstance().stop() ;
            this._resizeObserver && this._resizeObserver.disconnect();
        },

        methods: {
            getRightContainerWidth() {
                let rightWidth = 0;

                if (
                    [
                        INSPECT_TYPE.CT,
                        INSPECT_TYPE.DR,
                        INSPECT_TYPE.MR,
                        INSPECT_TYPE.MG,
                        INSPECT_TYPE.CDU,
                        INSPECT_TYPE.GASTROSCOPE,
                    ].includes(this.selectedItem.deviceType)
                ) {
                    if (!this.$refs.containerRef?.$el) {
                        rightWidth = 420;
                        return;
                    }
                    if (this.$refs.containerRef.$el.clientWidth <= 1620) {
                        rightWidth = 420;
                    } else {
                        rightWidth = 576;
                    }
                } else {
                    if (!this.$refs.containerRef?.$el) {
                        rightWidth = 255;
                        return;
                    }
                    const { clientWidth } = this.$refs.containerRef.$el;
                    if (clientWidth <= 1440) {
                        rightWidth = 255;
                    } else if (clientWidth <= 1600) {
                        rightWidth = 279;
                    } else {
                        rightWidth = 369;
                    }
                }

                this.rightContainerWidth = rightWidth;
            },

            handleContainerSizeChange() {
                this.getRightContainerWidth();
            },
        },
    };
</script>

<style lang="scss">
    @import './_index.scss';
</style>



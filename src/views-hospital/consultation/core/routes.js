import Index from '../index.vue';
import Blank from '../frames/blank.vue';
import { MODULE_ID_MAP } from 'utils/constants';

// 避免提前打包 以下路由异步加载
const PageAsync = () => import('./page.js');

const Main = () => import('../frames/main.vue');

// 外部跳转都应该用该字段
export const HospitalConsultationRouterNameKeys = {
    index: '@HospitalConsultation',
    blank: '@HospitalConsultationBlank',
    main: '@HospitalConsultationMain',
};

export default {
    path: 'consultation',
    name: HospitalConsultationRouterNameKeys.index,
    component: Index,
    meta: {
        name: '会诊',
        needAuth: true,
        pageAsyncClass: PageAsync,
        moduleId: MODULE_ID_MAP.hospitalDoctorConsultation,
    },
    children: [
        {
            path: '',
            component: Blank,
            name: HospitalConsultationRouterNameKeys.blank,
            meta: {
                name: '会诊空白页',
                needAuth: true,
                moduleId: MODULE_ID_MAP.hospitalDoctorConsultation,
            },
        },
        {
            path: ':id',
            component: Main,
            name: HospitalConsultationRouterNameKeys.main,
            meta: {
                name: '会诊主页',
                needAuth: true,
                moduleId: MODULE_ID_MAP.hospitalDoctorConsultation,
            },
        },
    ],
};

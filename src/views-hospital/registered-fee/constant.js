export const RegistrationCategory = {
    ORDINARY: 0, // 普通门诊
    SPECIALIST: 1, // 专家门诊
    CONVENIENCE: 2, // 便民门诊
};

export const RegistrationCategoryText = {
    [RegistrationCategory.ORDINARY]: '普通门诊',
    [RegistrationCategory.SPECIALIST]: '专家门诊',
    [RegistrationCategory.CONVENIENCE]: '便民门诊',
};

export const RegistrationCategoryText2 = {
    [RegistrationCategory.ORDINARY]: '普通号',
    [RegistrationCategory.SPECIALIST]: '专家号',
    [RegistrationCategory.CONVENIENCE]: '便民号',
};

export const RegistrationCategoryOptions = Object.freeze(
    [
        {
            label: '普通门诊', value: RegistrationCategory.ORDINARY, tip: '线上线下都可挂号', disabled: true,
        },
        {
            label: '专家门诊', value: RegistrationCategory.SPECIALIST, tip: '线上线下都可挂号', disabled: false,
        },
        {
            label: '便民门诊', value: RegistrationCategory.CONVENIENCE, tip: '只可线下挂号，不限号源', disabled: false,
        },
    ],
);

export const regTableHeaderConfig = Object.freeze([
    {
        label: '类型', width: 148, align: 'left',
    },
    {
        label: '费用项', align: 'left',
    },
    {
        label: '成本', width: 70, align: 'right',
    },
    {
        label: '售价', width: 70, align: 'right',
    },
]);

export const FEE_TYPE_ID_REGISTRATION = 5;
export const FEE_TYPE_ID_GENERAL_TREATMENT = 1003;
export const FEE_TYPE_ID_DIAGNOSTIC = 1001;

<template>
    <div
        class="main-content"
    >
        <business-toolbar
            :clinic-id-filter.sync="params.clinicId"
            :enable-features="toolbarFeatures"
            :patient-width="130"
            :dimension.sync="params.dimension"
            :dimension-options="dimensionOptions"
            :date-filter.sync="dateFilter"
            :handle-export="handleExport"
            :export-task-type="exportTaskType"
            @change-date="handleDateChange"
            @change-clinic="handleClinicChange"
            @change-dimension="handleDimensionChange"
        >
            <filter-select
                v-if="!showEmployeeTab"
                v-model="params.executorId"
                clearable
                placeholder="护士"
                :width="120"
                :options="doctorOptions"
                @change="getTableData"
            >
            </filter-select>
            <abc-select
                :key="feeTypeKey"
                v-model="feeTypeIdList"
                placeholder="费用分类"
                :width="120"
                clearable
                multiple
                multi-label-mode="text"
                :max-tag="1"
                style="margin-left: 2px;"
                @change="handleAdviceFeeTypeChange"
            >
                <abc-option
                    v-for="(item, index) in adviceFeeTypeOptions"
                    :key="index"
                    :value="item.feeTypeId"
                    :label="item.name"
                ></abc-option>
            </abc-select>
        </business-toolbar>
        <pro-statistics-table-fixed2
            :loading="loading"
            :data="tableData"
            :max-height="calcStatTableInfo && calcStatTableInfo.maxHeight"
            :min-height="calcStatTableInfo && calcStatTableInfo.minHeight"
            :header="tableRenderHeader"
            :pagination-params="params"
            :total-count="totalCount"
            @current-change="handlePageIndexChange"
        ></pro-statistics-table-fixed2>
    </div>
</template>

<script>
    import ClinicTypeJudger from 'views/statistics/statUtils/clinicTypeJudger';
    import PickerOptions from 'views/common/pickerOptions';
    import BusinessToolbar from '@/views-hospital/doctor-statistics/components/business-toolbar.vue';
    import {
        mapGetters,
    } from 'vuex';
    import ExportService from 'views/statistics/core/services/export/export-service';
    import {
        calcStatTablePageSize, HeaderHeightEnum,
    } from 'utils/statistic.js';
    import TableUtilsMixin from 'views/statistics/mixins/table-utils-mixin';
    import RevenueAPI from '@/views/statistics/core/api/revenue.js';
    import GoodsAPIV3 from '@/api/goods/index-v3';
    import { createGUID } from '@/utils';
    import { formatDate } from '@abc/utils-date';
    import { isEqual } from 'utils/lodash';
    import WardAreaAPI from 'api/hospital/setting/ward-area';
    import FilterSelect from 'views/layout/filter-select/index.vue';
    import ProStatisticsTableFixed2 from 'views/statistics/common/pro-statistics-table-fixed2/index.vue';

    export default {
        components: {
            ProStatisticsTableFixed2,
            BusinessToolbar,
            FilterSelect,
        },
        mixins: [ClinicTypeJudger, PickerOptions, TableUtilsMixin],
        data() {
            return {
                dateFilter: {
                    beginDate: formatDate(new Date()),
                    endDate: formatDate(new Date()),
                    dateRange: [formatDate(new Date()), formatDate(new Date())],
                },
                params: {
                    dimension: 'employee',
                    clinicId: '',
                    executorId: '',
                    pageIndex: 0,
                    pageSize: 12,
                    feeTypeId: '',
                },
                dimensionOptions: [
                    {
                        value: 'employee', label: '人员',
                    },
                    {
                        value: 'product', label: '项目',
                    },
                ],
                feeTypeIdList: [],
                loading: false,
                tableHeader: [],
                tableData: [],
                totalCount: 0,
                exportTaskType: 'his-nurse-station-charge',
                calcStatTableInfo: {},
                totalInfo: '',
                departmentsOptions: [],
                adviceFeeTypeOptions: [],
                feeTypeKey: '',
                doctorOptions: [],
            };
        },

        computed: {
            ...mapGetters(['isSingleStore']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            toolbarFeatures() {
                const features = [BusinessToolbar.Feature.DATE, BusinessToolbar.Feature.EXPORT, BusinessToolbar.Feature.CLINIC];
                return features;
            },
            tableRenderHeader() {
                return this.resolveHeader(this.tableHeader, {});
            },
            queryClinicId() {
                if (this.isChainAdmin) {
                    return this.params.clinicId;
                }
                return this.isSingleStore ? '' : this.currentClinic?.clinicId;
            },
            showEmployeeTab() {
                return this.params.dimension === 'employee';
            },
        },

        created() {
            this.exportService = new ExportService();
            this.fetchClinicInfoById();
            this.getAdviceTypeOptions();
            this.getAdviceTypeOptions();
            this.getTableData();
        },
        methods: {
            handlePageIndexChange(index) {
                this.params.pageIndex = index - 1;
                this.getTableData(false);
            },
            handleAdviceFeeTypeChange(list = []) {
                this.params.feeTypeId = list?.length ? list?.join(',') : '';
                this.getTableData();
            },
            async fetchClinicInfoById() {
                try {
                    const { data } = await WardAreaAPI.getWardArea(this.$route.params.wardId);
                    this.doctorOptions = data.employeeList || [];
                } catch (err) {
                    console.log(err);
                    this.doctorOptions = [];
                }
            },
            async getAdviceTypeOptions() {
                try {
                    const data = await GoodsAPIV3.getFeeTypeList();
                    this.adviceFeeTypeOptions = data?.rows || [];
                    this.feeTypeKey = createGUID();
                } catch (err) {
                    console.log(err);
                }
            },

            handleDateChange() {
                this.getAdviceTypeOptions();
                this.getTableData();
            },

            getTableParams() {
                const {
                    pageIndex,
                    pageSize,
                    feeTypeId,
                    executorId,
                } = this.params;
                const {
                    beginDate,
                    endDate,
                } = this.dateFilter;
                const offset = pageIndex * pageSize;
                const baseParams = {
                    beginDate,
                    clinicId: this.queryClinicId,
                    endDate,
                    offset,
                    limit: pageSize,
                    feeTypeId,
                    wardAreaId: this.$route.params.wardId,
                };
                if (this.showEmployeeTab) {
                    return baseParams;
                }
                return {
                    ...baseParams,
                    executorId,
                };
            },

            handleClinicChange() {
                this.getAdviceTypeOptions();
                this.getTableData();
            },
            async handleExport() {
                const params = this.getTableParams();
                delete params.limit;
                delete params.offset;

                try {
                    await this.exportService.startExport(this.exportTaskType, {
                        ...params,
                    });
                } catch (e) {
                    console.error(e);
                    return false;
                }
                return true;
            },

            handleDimensionChange(val) {
                this.params.dimension = val;
                this.getTableData();
            },

            async getTableData(resetPageParams = true) {
                await this.$nextTick();
                const defaultHeaderHeight = HeaderHeightEnum.ONE_ROW;
                this.calcStatTableInfo = calcStatTablePageSize(defaultHeaderHeight);

                this.params.pageSize = this.calcStatTableInfo.pageSize;

                if (resetPageParams) {
                    this.params.pageIndex = 0;
                    this.params.pageSize = this.calcStatTableInfo.pageSize;
                }

                const params = this.getTableParams();


                this.loading = true;
                if (this.showEmployeeTab) {
                    try {
                        const {
                            data,
                        } = await RevenueAPI.hospitalNurseRevenue.employee({
                            ...params,
                        });
                        if (isEqual(params, this.getTableParams())) {
                            this.setTableData(false, data, resetPageParams);
                        }
                    } catch (err) {
                        this.loading = false;
                        console.log(err);
                        this.setTableData(true);
                    } finally {
                        this.loading = false;
                    }
                } else {
                    try {
                        const {
                            data,
                        } = await RevenueAPI.hospitalNurseRevenue.product({
                            ...params,
                        });
                        if (isEqual(params, this.getTableParams())) {
                            this.setTableData(false, data, resetPageParams);
                        }
                    } catch (err) {
                        this.setTableData(true);
                        console.log(err);
                    } finally {
                        this.loading = false;
                    }
                }
            },
        },
    };
</script>

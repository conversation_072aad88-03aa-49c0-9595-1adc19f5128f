<template>
    <div class="main-content">
        <business-toolbar
            :clinic-id-filter.sync="params.clinicId"
            :enable-features="toolbarFeatures"
            product-placeholder="搜索医嘱"
            :product-id-filter.sync="params.adviceId"
            :patient-id-filter.sync="params.patientId"
            :patient-width="130"
            :date-filter.sync="dateFilter"
            :handle-export="handleExport"
            :export-task-type="exportTaskType"
            :dimension.sync="params.dimension"
            :dimension-options="dimensionOptions"
            @change-patient="getTableData"
            @change-product="getTableData"
            @searchGoods="onSearchGoods"
            @change-date="handleDateChange"
            @change-clinic="handleClinicChange"
            @change-dimension="handleDimensionChange"
        >
            <filter-select
                v-if="showEmployeeTab"
                v-model="params.managerDoctorId"
                clearable
                placeholder="主管医生"
                :width="120"
                :options="doctorOptions"
                @change="getTableData"
            >
            </filter-select>
            <filter-select
                v-if="!showStatTab"
                v-model="params.managerNurseId"
                clearable
                placeholder="主管护士"
                :width="120"
                :options="doctorOptions"
                @change="getTableData"
            >
            </filter-select>
            <filter-select
                v-if="showProductTab"
                v-model="params.checkNurseId"
                clearable
                placeholder="核对护士"
                :width="120"
                :options="doctorOptions"
                @change="getTableData"
            >
            </filter-select>
            <abc-select
                v-if="showProductTab"
                :key="feeTypeKey"
                v-model="feeTypeIdList"
                placeholder="费用类型"
                :width="120"
                clearable
                multiple
                multi-label-mode="text"
                :max-tag="1"
                style="margin-left: 2px;"
                @change="handleAdviceFeeTypeChange"
            >
                <abc-option
                    v-for="(item, index) in adviceFeeTypeOptions"
                    :key="index"
                    :value="item.feeTypeId"
                    :label="item.name"
                ></abc-option>
            </abc-select>
        </business-toolbar>
        <pro-statistics-table-fixed2
            :loading="loading"
            :summary-method="handleSummaries"
            :show-total="!showProductTab"
            :data="tableData"
            :max-height="calcStatTableInfo && calcStatTableInfo.maxHeight"
            :min-height="calcStatTableInfo && calcStatTableInfo.minHeight"
            :header="tableRenderHeader"
            :pagination-params="paginationParams"
            :total-count="totalCount"
            @current-change="handlePageIndexChange"
        ></pro-statistics-table-fixed2>
    </div>
</template>

<script>
    import ClinicTypeJudger from 'views/statistics/statUtils/clinicTypeJudger';
    import PickerOptions from 'views/common/pickerOptions';
    import BusinessToolbar from '@/views-hospital/doctor-statistics/components/business-toolbar.vue';
    import {
        mapGetters,
    } from 'vuex';
    import ExportService from 'views/statistics/core/services/export/export-service';
    import {
        calcStatTablePageSize, HeaderHeightEnum,
    } from 'utils/statistic.js';
    import FilterSelect from 'views/layout/filter-select/index.vue';
    import TableUtilsMixin from 'views/statistics/mixins/table-utils-mixin';
    import OperationStatAPI from '@/views/statistics/core/api/operation-stat.js';
    import { formatDate } from '@abc/utils-date';
    import { createGUID } from '@/utils';
    import GoodsAPIV3 from '@/api/goods/index-v3';
    import { isEqual } from 'utils/lodash';
    import WardAreaAPI from 'api/hospital/setting/ward-area';
    import ProStatisticsTableFixed2 from 'views/statistics/common/pro-statistics-table-fixed2/index.vue';

    export default {
        components: {
            ProStatisticsTableFixed2,
            BusinessToolbar,
            FilterSelect,
        },
        mixins: [ClinicTypeJudger, PickerOptions, TableUtilsMixin],
        data() {
            return {
                dateFilter: {
                    beginDate: formatDate(new Date()),
                    endDate: formatDate(new Date()),
                    dateRange: [formatDate(new Date()), formatDate(new Date())],
                },
                params: {
                    dimension: 'stat',
                    clinicId: '',
                    adviceId: '',
                    patientId: '',
                    managerDoctorId: '',
                    managerNurseId: '',
                    checkNurseId: '',
                    feeTypeId: '',
                    keyword: '',
                    pageIndex: 0,
                    pageSize: 12,
                },
                dimensionOptions: [
                    {
                        value: 'stat', label: '汇总',
                    },
                    {
                        value: 'employee', label: '患者',
                    },
                    {
                        value: 'product', label: '项目',
                    },
                ],
                loading: false,
                tableHeader: [],
                tableData: [],
                totalCount: 0,
                exportTaskType: 'nurse-station-ward-income',
                calcStatTableInfo: {},
                totalInfo: '',
                feeTypeKey: '',
                departmentsOptions: [],
                feeTypeIdList: [],
                adviceFeeTypeOptions: [],
                doctorOptions: [],
            };
        },

        computed: {
            ...mapGetters(['isSingleStore']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            ...mapGetters({
                clinicEmployeeList: 'employeeList',
            }),
            paginationParams() {
                if (this.showProductTab) {
                    return {
                        ...this.params,
                        pageSize: this.calcStatTableInfo.pageSize,
                    };
                }
                return {
                    ...this.params,
                    pageSize: 0,

                };
            },
            toolbarFeatures() {
                const features = [
                    BusinessToolbar.Feature.DATE,
                    BusinessToolbar.Feature.EXPORT,
                    BusinessToolbar.Feature.CLINIC,
                ];
                if (!this.showStatTab) {
                    features.push(BusinessToolbar.Feature.PATIENT);
                }

                if (this.showProductTab) {
                    features.push(BusinessToolbar.Feature.PRODUCT);
                }

                return features;
            },
            tableRenderHeader() {
                return this.resolveHeader(this.tableHeader, {});
            },
            queryClinicId() {
                if (this.isChainAdmin) {
                    return this.params.clinicId;
                }
                return this.isSingleStore ? '' : this.currentClinic?.clinicId;
            },
            showEmployeeTab() {
                return this.params.dimension === 'employee';
            },
            showStatTab() {
                return this.params.dimension === 'stat';
            },
            showProductTab() {
                return this.params.dimension === 'product';
            },
        },

        created() {
            this.exportService = new ExportService();
            this.getAdviceTypeOptions();
            this.fetchClinicInfoById();
            this.getTableData();
        },
        methods: {
            resetParams() {
                this.params.adviceId = '';
                this.params.patientId = '';
                this.params.managerDoctorId = '';
                this.params.managerNurseId = '';
                this.params.checkNurseId = '';
                this.params.keyWord = '';
            },
            onSearchGoods(keyword) {
                this.params.keyword = keyword;
                this.params.adviceId = '';
                this.getTableData();
            },
            async fetchClinicInfoById() {
                try {
                    const { data } = await WardAreaAPI.getWardArea(this.$route.params.wardId);
                    this.doctorOptions = data.employeeList || [];
                } catch (err) {
                    console.log(err);
                    this.doctorOptions = [];
                }
            },
            handleSummaries(data, col) {
                if (this.showProductTab) return null;
                return col.type === 'money' ? parseFloat(this.summaryData[col.prop] || 0).toFixed(2) : this.summaryData[col.prop];
            },
            async getAdviceTypeOptions() {
                try {
                    const data = await GoodsAPIV3.getFeeTypeList();
                    this.adviceFeeTypeOptions = data?.rows || [];
                    this.feeTypeKey = createGUID();
                } catch (err) {
                    console.log(err);
                }
            },
            handlePageIndexChange(index) {
                this.params.pageIndex = index - 1;
                this.getTableData(false);
            },
            handleAdviceFeeTypeChange(list = []) {
                this.params.feeTypeId = list?.length ? list?.join(',') : '';
                this.getTableData();
            },

            handleDateChange() {
                this.getTableData();
            },

            getTableParams() {
                const {
                    pageIndex,
                    pageSize,
                    feeTypeId,
                    managerDoctorId,
                    managerNurseId,
                    adviceId,
                    patientId,
                    checkNurseId,
                } = this.params;

                const {
                    beginDate,
                    endDate,
                } = this.dateFilter;
                const offset = pageIndex * pageSize;
                const baseParams = {
                    beginDate,
                    clinicId: this.queryClinicId,
                    endDate,
                    wardAreaId: this.$route.params.wardId,
                };
                if (this.showEmployeeTab) {
                    return {
                        ...baseParams,
                        managerDoctorId,
                        managerNurseId,
                        patientId,
                    };
                }
                if (this.showStatTab) {
                    return baseParams;
                }
                if (this.showProductTab) {
                    return {
                        ...baseParams,
                        patientId,
                        checkNurseId,
                        managerNurseId,
                        feeTypeId,
                        offset,
                        limit: pageSize,
                        adviceId,
                    };
                }
            },

            handleClinicChange() {
                this.getTableData();
            },
            async handleExport() {
                const {
                    patientId,
                } = this.params;
                const {
                    beginDate,
                    endDate,
                } = this.dateFilter;

                try {
                    await this.exportService.startExport(this.exportTaskType, {
                        patientId,
                        beginDate,
                        clinicId: this.queryClinicId,
                        endDate,
                        wardAreaId: this.$route.params.wardId,
                    });
                } catch (e) {
                    console.error(e);
                    return false;
                }
                return true;
            },

            handleDimensionChange(val) {
                this.resetParams();
                this.params.dimension = val;
                this.getTableData();
            },

            async getTableData(resetPageParams = true) {
                await this.$nextTick();
                const defaultHeaderHeight = HeaderHeightEnum.ONE_ROW;
                if (!this.showProductTab) {
                    this.calcStatTableInfo = calcStatTablePageSize(defaultHeaderHeight, true);
                } else {
                    this.calcStatTableInfo = calcStatTablePageSize(defaultHeaderHeight);
                }

                this.params.pageSize = this.calcStatTableInfo.pageSize;

                if (resetPageParams) {
                    this.params.pageIndex = 0;
                    this.params.pageSize = this.calcStatTableInfo.pageSize;
                }

                const params = this.getTableParams();
                this.loading = true;
                if (this.showStatTab) {
                    try {
                        const { data } = await OperationStatAPI.hospitalNurseWardIncome.stat({
                            ...params,
                        });
                        if (isEqual(params, this.getTableParams())) {
                            this.setTableData(false, data, resetPageParams);
                        }
                    } catch (err) {
                        this.loading = false;
                        console.log(err);
                        this.setTableData(true);
                    } finally {
                        this.loading = false;
                    }
                } else if (this.showEmployeeTab) {
                    try {
                        const { data } = await OperationStatAPI.hospitalNurseWardIncome.patient({
                            ...params,
                        });
                        if (isEqual(params, this.getTableParams())) {
                            this.setTableData(false, data, resetPageParams);
                        }
                    } catch (err) {
                        this.setTableData(true);
                        console.log(err);
                    } finally {
                        this.loading = false;
                    }
                } else {
                    try {
                        const { data } = await OperationStatAPI.hospitalNurseWardIncome.product({
                            ...params,
                        });
                        if (isEqual(params, this.getTableParams())) {
                            this.setTableData(false, data, resetPageParams);
                        }
                    } catch (err) {
                        this.setTableData(true);
                        console.log(err);
                    } finally {
                        this.loading = false;
                    }
                }
            },
        },
    };
</script>

import { SingleDosageUnitType } from '@/views-hospital/medical-prescription/utils/constants.js';
import {
    parseTime,
    dayDiffCount,
} from '@abc/utils-date';
import clone from 'utils/clone.js';
import { unique } from 'utils/lodash';

/**
 * 获取nid的每天执行时间
 *
 * @param {type} freq - the frequency for which to retrieve the execution timings
 * @return {type} the execution timings for the given frequency
 */
export function getNidExecuteTimings(freq, freqInfo) {
    const dailyTimings = [{
        timings: [],
    }];
    const dailyTimes = freq.match(/\d{1,}/g)?.[0] ? freq.match(/\d{1,}/g)?.[0] * 1 : 1;
    const {
        startExecuteTiming,
        endExecuteTiming,
    } = freqInfo;

    const transTimeToMin = (time) => {
        const times = time.split(':');
        return Number(times[0]) * 60 + Number(times[1]);
    };

    const transMinToTime = (min) => {
        const hour = Math.floor(min / 60);
        const minute = min % 60;
        return `${hour < 10 ? `0${hour}` : hour}:${minute < 10 ? `0${minute}` : minute}`;
    };

    if (startExecuteTiming && endExecuteTiming) {
        const startExecuteTimingMin = transTimeToMin(startExecuteTiming);
        const endExecuteTimingMin = transTimeToMin(endExecuteTiming);
        const intervalTimeMin = endExecuteTimingMin - startExecuteTimingMin;

        if (intervalTimeMin > 0) {
            // 第一次固定为开始时间
            dailyTimings[0].timings.push(startExecuteTimingMin);

            if (dailyTimes > 1) {
                const step = intervalTimeMin / (dailyTimes - 1);
                const times = dailyTimes - 2;
                // 均分中间的时间间隔，但分钟间隔必须为5的倍数，所以需要向上取5的倍数（3 -> 5, 7 -> 10）
                for (let i = 1; i <= times; i++) {
                    const v = (step * i) % 5;
                    const addTimeMin = v === 0 ? step * i : Math.floor(step * i) + 5 - Math.floor(v);
                    dailyTimings[0].timings.push(startExecuteTimingMin + addTimeMin);
                }
                // 最后一次固定为结束时间
                dailyTimings[0].timings.push(endExecuteTimingMin);
            }

            dailyTimings[0].timings = dailyTimings[0].timings.map(transMinToTime);
        }
    }

    return dailyTimings;
}

/**
 * @desc 根据选择的频率计算首日执行时间
 *       开始时间在今天之后，默认勾选上全部的首日执行
 * <AUTHOR>
 * @date
 * @params
 * @return
 */
export function getFirstExecuteTime(freq, freqInfo, startTime = '') {
    if (!freq) return [];
    if (!freqInfo) return [];
    const { dailyTimings } = freqInfo;
    if (!dailyTimings?.length) return [];
    const now = new Date();
    const tempStartTime = startTime ? new Date(startTime) : now;
    const diffDays = dayDiffCount(new Date(parseTime(now, 'y-m-d', true)), new Date(parseTime(tempStartTime, 'y-m-d', true)));

    let firstDayTimings = [];
    if (freq !== 'biw') {
        const { timings } = dailyTimings[0];
        if (timings?.length) {
            if (diffDays !== 0) {
                // 不是今天的医嘱，默认首日执行全部勾选上
                firstDayTimings = clone(timings);
            } else {
                if (timings.length === 1) {
                    firstDayTimings.push(timings[0]);
                } else {
                    timings.forEach((item) => {
                        let tempTime = parseTime(new Date(), 'y-m-d', true);
                        tempTime += ` ${item}`;
                        if (now.getTime() <= new Date(tempTime).getTime()) {
                            firstDayTimings.push(item);
                        }
                    });
                }
            }
        }
    } else {
        let nearDay = 1000;
        // 获取今天是周几
        const curDay = now.getDay() === 0 ? 7 : now.getDay();
        dailyTimings.forEach((day) => {
            if (diffDays !== 0) {
                firstDayTimings = [...day.timings];
            } else {
                const days = curDay - day.weekday;
                if (days < nearDay) {
                    nearDay = days;
                    firstDayTimings = [...day.timings];
                }
            }
        });
    }
    return firstDayTimings;
}

export function getFreqInfo(allExecuteTimeList, freq, isInitFirstExecuteTime = false, startTime = '') {
    let freqInfo = {
        code: '',
        description: '',
        dailyTimings: [],
        firstDayFrequency: 0,
        firstDayTimings: [],
        intervalTime: {
            day: 0,
            month: 0,
            week: 0,
        },
    };
    if (freq) {
        const executeTimeList = allExecuteTimeList || [];
        const qndFreqReg = /q(\d{1,2})d/;
        const qnwFreqReg = /q(\d{1,2})w/;
        const nidFreqReg = /(\d{1,2})id/;

        let intervalDay = 0;
        let intervalWeek = 0;

        const res = executeTimeList?.find((item) => {
            if (qndFreqReg.test(freq)) {
                if (item.code === 'qnd') {
                    intervalDay = RegExp.$1;
                    return item;
                }
            }
            if (qnwFreqReg.test(freq)) {
                if (item.code === 'qnw') {
                    intervalWeek = RegExp.$1;
                    return item;
                }
            }
            if (nidFreqReg.test(freq)) {
                if (item.code === 'nid') {
                    return item;
                }
            }
            return item.code.toLowerCase() === freq.toLowerCase();
        });

        if (res) {
            let dailyTimings = clone(res.executeTimingRule?.dailyTimings) || [];

            if (res.code === 'nid') {
                dailyTimings = getNidExecuteTimings(freq, res);
            }
            freqInfo = {
                code: res.code,
                description: res.description,
                dailyTimings,
                firstDayFrequency: 0,
                firstDayTimings: [],
                intervalTime: clone(res.executeTimingRule?.intervalTime) || {
                    day: intervalDay,
                    month: 0,
                    week: intervalWeek,
                },
                startExecuteTiming: res.startExecuteTiming,
                endExecuteTiming: res.endExecuteTiming,
            };
            freqInfo.firstDayTimings = isInitFirstExecuteTime ? getFirstExecuteTime(res.code, freqInfo, startTime) : [];
            freqInfo.firstDayFrequency = freqInfo.firstDayTimings?.length || 0;
        }
    }
    return freqInfo;
}

export function getFreqInfoByFirst(allExecuteTimeList, freq, isInitFirstExecuteTime = false, startTime = '') {
    const freqInfo = getFreqInfo(allExecuteTimeList, freq, isInitFirstExecuteTime, startTime);
    let {
        firstDayFrequency, firstDayTimings,
    } = freqInfo;
    if (!firstDayFrequency || !firstDayTimings.length) {
        const { dailyTimings } = freqInfo;
        if (!dailyTimings.length) return freqInfo;
        const lastDailyTimings = dailyTimings[dailyTimings.length - 1];
        const { timings } = lastDailyTimings;
        if (!timings.length) return freqInfo;
        const lastTiming = timings[timings.length - 1];

        firstDayFrequency = 1;
        firstDayTimings = [lastTiming];

        freqInfo.firstDayFrequency = firstDayFrequency;
        freqInfo.firstDayTimings = firstDayTimings;
    }
    return freqInfo;
}

export function getSingleDosageUnitType(singleDosageUnit, goods) {
    if (!goods || !singleDosageUnit) return null;
    const {
        pieceUnit, packageUnit, medicineDosageUnit, componentContentUnit,
    } = goods;
    switch (singleDosageUnit) {
        case packageUnit:
            return SingleDosageUnitType.PACKAGE;
        case pieceUnit:
            return SingleDosageUnitType.PIECE;
        case medicineDosageUnit:
            return SingleDosageUnitType.MEDICINE_DOSAGE;
        case componentContentUnit:
            return SingleDosageUnitType.COMPONENT_CONTENT;
        default:
            return null;
    }
}

export function getMedicalDosageUnitArray(goods) {
    const {
        componentContentUnit,
        medicineDosageUnit,
        pieceUnit,
        packageUnit,
    } = goods;
    let res = [componentContentUnit,medicineDosageUnit,pieceUnit,packageUnit];
    res = unique(res.filter((it) => it));
    return res;
}
// 获取单次计量的推荐值
export function getSuggestCountAndUnit(goods, dosageUnit, dosageCount) {
    const units = getMedicalDosageUnitArray(goods);
    let unit = dosageUnit;
    let count = dosageCount;
    if (!units.includes(unit)) {
        unit = '';
        count = '';
    }
    return {
        unit,
        count,
    };
}


/**
 * @description: 传入日期是否在当天之前
 * @author: ff
 * @date: 2024/1/25
 */
export function isBeforeToday(date) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const curDate = new Date(date);
    return curDate < today;
}

/**
 * 根据开始时间和结束时间计算天数,天数包括开始时间和结束时间那一天
 * @param startTime
 * @param stopTime
 * @return {number}
 */
export function calcDaysByTimeDifference(startTime, stopTime) {
    const startDate = new Date(startTime);
    const stopDate = new Date(stopTime);
    startDate.setHours(0, 0, 0, 0);
    stopDate.setHours(0, 0, 0, 0);
    const differenceTime = stopDate.getTime() - startDate.getTime();
    return Math.ceil(differenceTime / (1000 * 60 * 60 * 24)) + 1;
}

export function calcStopTime(startTime, days) {
    days = parseInt(days);
    if (!startTime || !days) {
        const nowDate = new Date();
        nowDate.setHours(23, 50);
        return parseTime(nowDate, 'y-m-d h:i', true);
    }
    const startDate = new Date(startTime);
    startDate.setDate(startDate.getDate() + days - 1);
    startDate.setHours(23, 50);
    return parseTime(startDate, 'y-m-d h:i', true);
}

import {
    MedicalAdviceTypeEnum,
    MedicalAdviceTypeStr,
    TreatmentTypeEnum,
    TreatmentTypeStr,
    MedicalAdviceStatusEnum,
    MedicalAdviceStatusStr,
} from '@/views-hospital/medical-prescription/utils/constants.js';
import { isNull } from '@/utils';

export const MedicalPrescriptionTypeList = [
    {
        value: MedicalAdviceTypeEnum.LONG_TIME,
        label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.LONG_TIME],
    },
    {
        value: MedicalAdviceTypeEnum.ONE_TIME,
        label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.ONE_TIME],
    },
    {
        value: MedicalAdviceTypeEnum.DISCHARGE_WITH_MEDICINE,
        label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.DISCHARGE_WITH_MEDICINE],
    },
];
export const DiagnosisTreatmentTypeList = [
    {
        value: TreatmentTypeEnum.MEDICINE,
        label: TreatmentTypeStr[TreatmentTypeEnum.MEDICINE],
    },
    {
        value: TreatmentTypeEnum.MATERIALS,
        label: TreatmentTypeStr[TreatmentTypeEnum.MATERIALS],
    },
    {
        value: TreatmentTypeEnum.INSPECTION,
        label: TreatmentTypeStr[TreatmentTypeEnum.INSPECTION],
    },
    {
        value: TreatmentTypeEnum.ASSAY,
        label: TreatmentTypeStr[TreatmentTypeEnum.ASSAY],
    },
    {
        value: TreatmentTypeEnum.NURSE,
        label: TreatmentTypeStr[TreatmentTypeEnum.NURSE],
    },
    {
        value: TreatmentTypeEnum.TRANSFER_DEPARTMENT,
        label: TreatmentTypeStr[TreatmentTypeEnum.TRANSFER_DEPARTMENT],
    },
    {
        value: TreatmentTypeEnum.DISCHARGE_WITH_MEDICINE,
        label: TreatmentTypeStr[TreatmentTypeEnum.DISCHARGE_WITH_MEDICINE],
    },
    {
        value: TreatmentTypeEnum.CONSULTATION,
        label: TreatmentTypeStr[TreatmentTypeEnum.CONSULTATION],
    },
    {
        value: TreatmentTypeEnum.SURGERY,
        label: TreatmentTypeStr[TreatmentTypeEnum.SURGERY],
    },
];

export const MedicalAdviceStatusList = [
    {
        value: MedicalAdviceStatusEnum.INIT,
        label: MedicalAdviceStatusStr[MedicalAdviceStatusEnum.INIT],
    },
    {
        value: MedicalAdviceStatusEnum.CHECKED,
        label: MedicalAdviceStatusStr[MedicalAdviceStatusEnum.CHECKED],
    },
    {
        value: MedicalAdviceStatusEnum.EXECUTED,
        label: MedicalAdviceStatusStr[MedicalAdviceStatusEnum.EXECUTED],
    },
    {
        value: MedicalAdviceStatusEnum.STOPPED,
        label: MedicalAdviceStatusStr[MedicalAdviceStatusEnum.STOPPED],
    },
    {
        value: MedicalAdviceStatusEnum.UNDONE,
        label: MedicalAdviceStatusStr[MedicalAdviceStatusEnum.UNDONE],
    },
];

export const DoctorAdviceStatusList = [
    {
        value: '',
        label: '全部',
    },
    {
        value: 0,
        label: '未停',
    },
    {
        value: 1,
        label: '已停',
    },
];

export const ModalDoctorAdviceTableHeader = [
    {
        prop: 'type',
        label: '类型',
        titleAlign: 'left',
        width: 50,
    },
    {
        prop: 'content',
        label: '医嘱内容',
        titleAlign: 'left',
        width: 289,
        render: (h, row) => {
            return (
                <div class="prescription-content">
                    {row.content}
                    <span class="specification">{row.specification}</span>
                    {row.showGroupLine ? <div class="group-line" style="height: 42px"></div> : ''}
                </div>
            );
        },
    },
    {
        prop: 'way',
        label: '用法',
        titleAlign: 'center',
        width: 77,
    },

    {
        prop: 'step',
        label: '频率',
        titleAlign: 'left',
        width: 52,
    },
    {
        prop: 'single',
        label: '单次',
        titleAlign: 'left',
        width: 81,
    },
    {
        prop: 'day',
        label: '天数',
        titleAlign: 'left',
        width: 146,
    },
    {
        prop: 'number',
        label: '总量',
        titleAlign: 'left',
        width: 83,
    },
    {
        prop: 'doneTime',
        label: '执行时间',
        titleAlign: 'left',
        width: 314,
    },
];

export class MPListTable {
    static checkbox = {
        label: '',
        style: {
            textAlign: 'left',
            width: '36px',
        },
    };
    static startTime = {
        label: '开始时间',
        style: {
            textAlign: 'left',
            width: '100px',
        },
    };

    static type = {
        label: '类型',
        style: {
            textAlign: 'left',
            width: '70px',
        },
    };

    static content = {
        label: '医嘱内容',
        style: {
            textAlign: 'left',
            flex: 1,
        },
    };

    static status = {
        label: '状态',
        style: {
            titleAlign: 'left',
            width: '52px',
        },
    };

    static remark = {
        label: '备注',
        style: {
            titleAlign: 'left',
            width: '200px',
            display: 'flex',
            'align-items': 'center',
        },
    };

    static startDoctor = {
        label: '开立医生',
        style: {
            titleAlign: 'left',
            width: '100px',
        },
    };

    static stopDoctor = {
        label: '停止医生',
        style: {
            titleAlign: 'left',
            width: '100px',
        },
    };

    static stopTime = {
        label: '停止时间',
        style: {
            titleAlign: 'left',
            width: '100px',
        },
    };
}

export function compareShebaoPayMode(shebaoPayMode, goodsShebaoPayMode) {
    if (isNull(goodsShebaoPayMode)) {
        return shebaoPayMode !== 0;
    }
    const formatGoodsShebaoPayMode = goodsShebaoPayMode === 2 ? 30 : goodsShebaoPayMode === 1 ? 10 : 0;
    return shebaoPayMode !== formatGoodsShebaoPayMode;
}

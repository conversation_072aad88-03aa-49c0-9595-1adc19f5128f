<template>
    <div id="hospital-medical-insurance-restriction-cover" class="hospital-medical-insurance-restriction-cover" @click="handleOutside">
        <abc-flex vertical class="hospital-medical-insurance-restriction-wrapper" :class="isOpened ? 'opened' : ''">
            <abc-flex align="center" justify="space-between" class="restriction-title">
                <abc-flex align="center" :gap="6">
                    <abc-text size="large" theme="black">
                        医保合规
                    </abc-text>
                    <abc-popover
                        theme="white"
                        trigger="hover"
                        :open-delay="50"
                        width="386px"
                        :visible-arrow="false"
                    >
                        <abc-icon
                            slot="reference"
                            icon="info_bold"
                            :size="14"
                            :color="'var(--abc-color-P10)'"
                        ></abc-icon>
                        <abc-flex vertical :gap="6">
                            <abc-text bold>
                                医保合规助手
                            </abc-text>
                            <abc-text theme="gray">
                                遵循医保目录、政策等考核要求，在疾病和药品大数据基础上，使用互联网技术为诊所提供的一套智能检查工具。
                            </abc-text>
                            <abc-text theme="gray">
                                在门诊及收费等多个环节，系统会根据医保目录，检查用药违规风险，如药品限定医疗机构等级、就医方式、患者特征、诊断、病种、医师级别等规则；也会根据医保考核经验，检查医疗行为违规风险，如就诊频次过高、医保支付金额过高等规则。
                            </abc-text>
                            <abc-text theme="gray">
                                开启合规助手，可以极大程度上帮助诊所降低医保合规风险，减少违规损失。同时，因为医保政策的复杂性和多变性，风控结果仅用于辅助参考，一切以医保实际结算为准。
                            </abc-text>
                        </abc-flex>
                    </abc-popover>
                </abc-flex>

                <abc-button
                    shape="square"
                    variant="ghost"
                    theme="default"
                    size="normal"
                    @click="$emit('close')"
                >
                    关闭
                </abc-button>
            </abc-flex>

            <abc-flex v-if="dangerWarnArr.length" vertical class="restriction-content">
                <template v-for="(it, idx) in dangerWarnArr">
                    <abc-flex
                        :key="`hospital-restriction-${idx}`"
                        align="center"
                        justify="space-between"
                        style="padding: 4px 0;"
                    >
                        <abc-flex
                            :class="{
                                'restriction-warn': it.level === 'WARN',
                                'restriction-danger': it.level === 'DANGER',
                                'restriction-done': !!it.isDeal,
                            }"
                        >
                            <abc-flex align="flex-start" class="restriction-status">
                                <template v-if="it.level === 'DANGER'">
                                    <abc-icon icon="Attention" size="14px" style="padding-top: 2px;"></abc-icon>
                                    <span style=" min-width: 30px; margin-left: 4px;">风险</span>
                                </template>
                                <template v-else-if="it.level === 'WARN'">
                                    <abc-icon icon="Attention" size="14px" style="padding-top: 2px;"></abc-icon>
                                    <span style="min-width: 30px; margin-left: 4px;">提醒</span>
                                </template>

                                <abc-flex class="restriction-abstract" style="margin-left: 8px;">
                                    {{ it.hint }}
                                </abc-flex>
                            </abc-flex>
                        </abc-flex>

                        <abc-button
                            v-if="it.name === 'overChineseUseRule'"
                            variant="text"
                            size="small"
                            :width="92"
                            :theme="it.isDeal ? 'default' : 'primary'"
                            @click="onClickSign(it)"
                        >
                            {{ it.isDeal ? '取消签字' : '确认签字' }}
                        </abc-button>
                        <template v-else-if="it.dealType">
                            <abc-select
                                v-if="it.dealWay === DealWayEnum.PAY_MODE || it.dealWay === DealWayEnum.All"
                                v-model="it.shebaoPayMode"
                                reference-mode="text"
                                reference-text-justify="end"
                                :width="92"
                                style="min-width: 92px;"
                                text-mode-plus
                                @change="onShebaoPayModeChange(it)"
                            >
                                <abc-option :value="0" label="优先统筹"></abc-option>
                                <abc-option :value="10" label="优先个账"></abc-option>
                                <abc-option :value="30" label="不过医保"></abc-option>
                            </abc-select>
                            <abc-button
                                v-if="it.dealWay === DealWayEnum.SIGNATURE || it.dealWay === DealWayEnum.All"
                                variant="text"
                                size="small"
                                :width="92"
                                :theme="it.isSignature ? 'default' : 'primary'"
                                @click="onClickThreeTimesSign(it)"
                            >
                                {{ it.isSignature ? '取消签字' : '确认签字' }}
                            </abc-button>
                        </template>
                    </abc-flex>

                    <abc-divider
                        v-if="idx < dangerWarnArr.length - 1"
                        :key="`hospital-restriction-divider-${idx}`"
                        size="normal"
                        theme="light"
                        variant="dashed"
                        margin="mini"
                    ></abc-divider>
                </template>
            </abc-flex>

            <abc-content-empty
                v-else
                icon-name="s-emptyIcon-compliance"
                value="暂无医保合规风险"
                top="200px"
                size="large"
                :icon-size="72"
            >
            </abc-content-empty>
        </abc-flex>
    </div>
</template>

<script>
    import Vue from 'vue';
    import {
        isNotNull, isNull,
    } from '@/utils';
    import { DealWayEnum } from '@/common/constants/shebao-restrict';

    export default {
        name: 'HospitalMedicalInsuranceRestriction',
        props: {
            hospitalVerifyRsp: {
                type: Object,
                required: true,
            },
            isOpened: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                DealWayEnum,
            };
        },
        computed: {
            behaviorVerifyLists() {
                return this.hospitalVerifyRsp.behaviorVerifyLists || [];
            },
            medicateVerifyLists() {
                return this.hospitalVerifyRsp.medicateVerifyLists || [];
            },
            arrDanger() {
                const arrDanger = [];
                this.medicateVerifyLists.concat(this.behaviorVerifyLists).forEach((item) => {
                    if (item.verifyDetails) {
                        item.verifyDetails.forEach((it) => {
                            if (it.level === 'DANGER') {
                                arrDanger.push({
                                    ...it,
                                    dealType: item.dealType,
                                });
                            }
                        });
                    }
                });
                return arrDanger;
            },
            arrWarn() {
                const arrWarn = [];
                this.medicateVerifyLists.concat(this.behaviorVerifyLists).forEach((item) => {
                    if (item.verifyDetails) {
                        item.verifyDetails.forEach((it) => {
                            if (it.level === 'WARN') {
                                arrWarn.push({
                                    ...it,
                                    dealType: item.dealType,
                                });
                            }
                        });
                    }
                });
                return arrWarn;
            },
            dangerWarnArr() {
                if (!this.$abcSocialSecurity.isOpenSocial) return [];
                return Vue.observable(this.arrDanger.concat(this.arrWarn));
            },
        },
        methods: {
            isNotNull,
            isNull,
            onShebaoPayModeChange(item) {
                this.$emit('changeShebaoPayMode', item);
            },
            onClickSign(item) {
                item.isDeal = !item.isDeal;
                this.$emit('shebaoRestrictSignature', item);
            },
            onClickThreeTimesSign(item) {
                item.isSignature = !item.isSignature;
                this.$emit('shebaoRestrictThreeTimesSignature', item);
            },
            handleOutside(event) {
                if (event.target.id === 'hospital-medical-insurance-restriction-cover') {
                    this.$emit('close');
                }
            },
        },
    };
</script>

<style lang="scss">
.hospital-medical-insurance-restriction-cover {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 99999;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: transparent;
}

.hospital-medical-insurance-restriction-wrapper {
    position: absolute;
    top: 0;
    right: -490px;
    width: 480px;
    height: 100%;
    background-color: var(--abc-color-LY4);
    border: 1px solid var(--abc-color-P8);
    border-radius: 6px 0 5px 6px;
    box-shadow: 0 3px 12px 2px rgba(0, 0, 0, 0.1);
    transition: right 0.5s ease-in-out;

    &.opened {
        right: 0;
    }

    .restriction-title {
        width: 100%;
        padding: 16px 20px;
        border-bottom: 1px solid var(--abc-color-P8);
    }

    .restriction-content {
        width: 100%;
        padding: 16px 20px;
    }

    .restriction-danger {
        color: var(--abc-color-R6);

        .iconfont {
            color: var(--abc-color-R6);
        }
    }

    .restriction-warn {
        color: var(--abc-color-Y2);

        .iconfont {
            color: var(--abc-color-Y2);
        }
    }

    .restriction-done {
        .restriction-status,
        .restriction-abstract,
        .cis-icon-Attention {
            color: var(--abc-color-T3);
            text-decoration: line-through;
        }
    }
}
</style>

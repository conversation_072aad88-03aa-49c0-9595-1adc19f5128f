<template>
    <div class="medical-prescription-detail-popover">
        <medical-prescription-detail
            :id="id"
            :key="id"
            :readonly="readonly"
            :business-type="businessType"
            :data-list="dataList"
            @close="$emit('input', false)"
            @copy="val => $emit('copy', val)"
            @success="$emit('success')"
        ></medical-prescription-detail>
    </div>
</template>

<script>
    import MedicalPrescriptionDetail from '@/views-hospital/medical-prescription/components/medical-prescription-detail/index.vue';
    export default {
        components: {
            MedicalPrescriptionDetail,
        },
        props: {
            id: {
                type: String,
                require: true,
                default: '',
            },
            businessType: {
                type: String,
                default: 'nurse',
            },
            readonly: {
                type: Boolean,
                default: false,
            },
            dataList: {
                type: Array,
                default: () => [],
            },
        },
    };
</script>

<style lang="scss">
    @import "src/styles/theme.scss";

    .medical-prescription-detail-popover {
        position: fixed;
        z-index: 11;
        width: 650px;
        height: 538px;
        background: #fffdec;
        border: 1px solid #e6e3c4;
        border-radius: var(--abc-border-radius-small);
        box-shadow: 0 3px 18px 0 rgba(0, 0, 0, 0.15);
    }
</style>


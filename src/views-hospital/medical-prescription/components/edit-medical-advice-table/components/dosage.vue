<template>
    <div class="advice__count-input-select-wrapper" :class="{ 'is-disabled': disabled }">
        <abc-form-item :required="required" style="margin-right: -1px;" :validate-event="validateNumberWithoutZero">
            <abc-input
                v-model="curDosageCount"
                v-abc-focus-selected
                :disabled="disabled"
                :placeholder="placeholder"
                size="small"
                class="count-center"
                :width="inputWidth"
                type="number"
                :config="countConfig(medicine)"
                @change="handleChangeCount"
                @enter="enterEvent"
            >
            </abc-input>
        </abc-form-item>

        <abc-form-item :required="required">
            <!--诊所药品-->
            <abc-select
                v-if="medicine.goodsId"
                v-model="curDosageUnit"
                :width="unitWidth"
                custom-class="prescription-select-options"
                :input-style="{ 'padding': '0 6px' }"
                :disabled="disabled"
                focus-show-options
                :tabindex="unitTabindex"
                @enter="enterEvent"
                @change="selectUnit"
            >
                <abc-option
                    v-for="it in unitArray(medicine)"
                    :key="it.name"
                    :label="it.name"
                    :value="it.name"
                >
                </abc-option>
            </abc-select>

            <!--系统药品：显示domain表的所有单位-->
            <select-usage
                v-else
                v-model="curDosageUnit"
                v-abc-focus-selected
                type="unit"
                :width="unitWidth"
                :disabled="disabled"
                focus-show-options
                :tabindex="unitTabindex"
                @enter="enterEvent"
                @change="selectUnit"
            >
            </select-usage>
        </abc-form-item>
    </div>
</template>

<script type="text/ecmascript-6">
    import {
        validateStock, validateNumberWithoutZero,
    } from 'utils/validate';
    import SelectUsage from '@/views/layout/select-group/index.vue';

    export default {
        name: 'Dosage',
        components: {
            SelectUsage,
        },
        props: {
            medicine: Object,
            dosageCount: [String, Number],
            dosageUnit: String,
            styleString: String,
            isDismounting: [Number,String],
            disabled: Boolean,
            needCheckStock: Boolean,
            placeholder: String,
            unitTabindex: {
                type: Number,
                default: 1,
            },
            inputWidth: {
                type: Number,
                default: 46,
            },
            unitWidth: {
                type: Number,
                default: 32,
            },
            required: {
                type: Boolean,
                default: true,
            },
            goodsDismounting: {
                type: Number,
                default: 1,
            },
        },
        data() {
            return {
                validateStock,
            };
        },
        computed: {
            curDosageCount: {
                get() {
                    return this.dosageCount;
                },
                set(v) {
                    this.$emit('update:dosageCount', v);
                },
            },
            curDosageUnit: {
                get() {
                    return this.dosageUnit;
                },
                set(v) {
                    this.$emit('update:dosageUnit', v);
                },
            },
            useDismounting: {
                get() {
                    return this.isDismounting;
                },
                set(v) {
                    this.$emit('update:isDismounting', v);
                },
            },
        },
        methods: {
            validateNumberWithoutZero,
            /**
             * @desc 开药量 是否允许输入 小数的判断
             * @desc 允许拆零的西药/中成药，当开药量单位选择 小单位 且 小单位是ml 时，可以输入 3位小数
             * <AUTHOR>
             * @date 2019/03/24 12:44:49
             */
            countConfig(item) {
                const result = {
                    max: 999999,
                    supportZero: false,
                    formatLength: 2,
                };

                if (!item.productInfo) {
                    return result;
                }
                const dismounting = !!item.productInfo.dismounting; // 是西药/中成药
                const isFormUnit = item.unit === item.productInfo.pieceUnit; // 开药量单位选择小单位
                const formUnitIsML = item.productInfo.pieceUnit === 'ml'; // 开药量单位选择小单位

                if (dismounting && isFormUnit && formUnitIsML) {
                    Object.assign(result, { formatLength: 3 });
                }

                return result;
            },

            /**
             * old: 医院强制拆零，只要大小单位不一致都会提供两个单位选择
             * new: 医院是否能够拆零也要看 goods 设置是否可以拆零销售
             */
            unitArray(wm) {
                const res = [];
                if (!wm) return res;

                const { pieceUnit } = wm;
                const { packageUnit } = wm;

                if (pieceUnit && packageUnit !== pieceUnit && this.goodsDismounting) {
                    res.push({ 'name': pieceUnit });
                }
                if (packageUnit) {
                    res.push({ 'name': packageUnit });
                }
                return res;
            },

            enterEvent(e) {
                this.$emit('enter', e);
            },

            handleChangeCount(val) {
                this.$emit('changeCount', val);

            },

            /**
             * @desc 选择小单位 dismounting  就是 1 大单位dismounting就是0
             * <AUTHOR>
             * @param unit 新值
             * @param index
             * @param oldVal 老值
             */
            selectUnit(unit, index, oldVal) {
                this.curDosageUnit = unit;
                this.useDismounting = +(this.medicine &&
                    unit === this.medicine.pieceUnit &&
                    unit !== this.medicine.packageUnit);
                if (unit === oldVal) return;
                this.$nextTick(() => {
                    this.$emit('changeUnit', this.useDismounting, oldVal);
                });
            },

        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
@import 'src/styles/theme.scss';

.advice__count-input-select-wrapper {
    display: flex;
    align-items: center;
    width: 76px;

    &:not(.is-disabled):hover .abc-form-item {
        &:first-child .abc-input__inner {
            z-index: 1;
        }

        &:last-child .abc-input__inner {
            background-color: #e9f2fe;
        }

        .abc-input__inner {
            border-color: $theme3;
        }
    }

    .abc-form-item {
        &:first-child {
            .abc-input__inner {
                padding: 3px 2px 3px 2px;
                text-align: right;

                &::placeholder {
                    font-size: 12px;
                }
            }
        }

        &:last-child {
            .abc-input__inner {
                padding-right: 0;
                padding-left: 8px;
                text-align: left;
            }

            .iconfont {
                display: none;
            }
        }

        &:focus,
        &:active {
            z-index: 2;
        }

        &.is-error {
            .count-center .abc-input__inner {
                border-color: $Y2;
            }
        }
    }
}
</style>

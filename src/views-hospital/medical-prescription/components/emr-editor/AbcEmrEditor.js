import { generatePromise } from 'utils/promise-task';

export class AbcEmrEditor {
    constructor() {
        this.editor = null;
        this.editorPromise = generatePromise();
    }

    async init(editor) {
        if (this.editor !== editor) {
            this.editorPromise = generatePromise();
        }
        this.editor = editor;
        this.editorPromise.resolve(editor);
        return this.editorPromise;
    }

    async getEditor() {
        return this.editorPromise.promise;
    }

    async toJSON() {
        await this.getEditor();
        return this.editor.state.doc.toJSON();
    }

    async setFieldContentByIndex(index, content, onSetFieldContentSuccess, animation) {
        await this.getEditor();
        return this.editor.commands.setFieldContentByIndex(index, content, onSetFieldContentSuccess, animation);
    }

    async setFieldValidate(validateFields) {
        await this.getEditor();
        return this.editor.commands.setFieldValidate(validateFields);
    }

    async setEnableFocusChange(enableFocusChange) {
        await this.getEditor();
        return this.editor.commands.setEnableFocusChange(enableFocusChange);
    }

    async clearAllFieldAnimation() {
        await this.getEditor();
        return this.editor.commands.clearAllFieldAnimation();
    }

    async insertText(text) {
        await this.getEditor();
        return this.editor.commands.insertText(text);
    }
}

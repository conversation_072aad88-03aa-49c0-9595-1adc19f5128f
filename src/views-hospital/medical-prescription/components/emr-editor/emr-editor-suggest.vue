<template>
    <div class="emr-editor-suggest">
        <div class="emr-editor-suggest_tabs">
            <abc-tabs
                v-model="currentTab"
                :option="tabOptions"
                size="middle"
                disable-indicator
            ></abc-tabs>
        </div>

        <div class="emr-editor-suggest_content">
            <component
                :is="currentComponent"
                ref="content"
                :template-id="templateId"
                :department-id="departmentId"
                :patient-order-id="patientOrderId"
                :business-type="businessType"
                :business-id="businessId"
                :patient-info="patientInfo"
                :patient-id="patientId"
                :medical-id="medicalId"
                :medical-doc-id="medicalDocId"
                :medical-name="medicalName"
                :template-setting="templateSetting"
                :emr-editor="emrEditor"
                :has-copy="true"
                :medical-document-type="medicalDocumentType"
                :disabled-operate="disabledOperate"
                :patient-inpatient-history="patientInpatientHistory"
                @save-personal-template="handleSavePersonalTemplate"
                @update-diff="handleUpdateDiff"
                @update-single-diff="handleUpdateSingleDiff"
                @set-selection="handleSetSelection"
                @scroll-to-field-by-index="handleScrollToFieldByIndex"
                @insert-record="handleInsertRecord"
                @quick-insert="handleQuickInsert"
            ></component>
        </div>
    </div>
</template>

<script>
    import EmrEditorSuggestTemplates from './suggest-components/emr-editor-suggest-templates.vue';
    import EmrEditorSuggestMedicals from './suggest-components/emr-editor-suggest-medicals.vue';
    import EmrEditorSuggestDiagnosis from './suggest-components/emr-editor-suggest-diagnosis.vue';
    import EmrEditorSuggestSigns from './suggest-components/emr-editor-suggest-signs.vue';
    import EmrEditorSuggestRecords from './suggest-components/emr-editor-suggest-records.vue';
    import EmrEditorSuggestPrescriptions
        from '@/views-hospital/medical-prescription/components/emr-editor/suggest-components/emr-editor-suggest-prescriptions.vue';
    import { MedicalDocumentBusinessType } from '@/views-hospital/nursing/common/constants.js';
    import PatientOutpatientHistory from './patient-outpatient-history/patient-outpatient-history.vue';
    import {
        MedicalDocumentRecordTypeEnum,
    } from '@/views-hospital/medical-prescription/components/emr-editor/common/constants';

    export default {
        name: 'EmrEditorSuggest',
        props: {
            templateId: {
                type: String,
                default: '',
            },
            departmentId: {
                type: String,
                default: '',
            },
            patientOrderId: {
                type: String,
                default: '',
            },
            businessType: {
                type: Number,
                default: MedicalDocumentBusinessType.HOSPITAL,
            },
            businessId: {
                type: String,
                default: '',
            },
            patientInfo: {
                type: Object,
                default: () => ({}),
            },
            medicalId: {
                type: String,
                default: '',
            },
            medicalDocId: {
                type: String,
                default: '',
            },
            medicalName: {
                type: String,
                default: '',
            },
            templateSetting: {
                type: Object,
                default: () => ({}),
            },
            emrEditor: {
                type: Object,
                default: () => ({}),
            },
            patientId: {
                type: String,
                default: '',
            },
            medicalDocumentType: {
                type: Number,
                default: 0,
            },
            patientInpatientHistory: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                tabOptions: [],
                currentTab: 0,
            };
        },
        computed: {
            currentComponent() {
                return this.tabOptions.find((tab) => tab.value === this.currentTab)?.component ?? null;
            },
            disabledOperate() {
                return this.medicalDocumentType === MedicalDocumentRecordTypeEnum.CareTableRecord;
            },
        },
        mounted() {
            this.initTabOptions();
        },
        methods: {
            handleSavePersonalTemplate(template) {
                this.$emit('save-personal-template', template);
            },
            initTabOptions() {
                const outpatientCommonTabOptions = [
                    {
                        label: '模板',
                        value: 0,
                        component: EmrEditorSuggestTemplates,
                    },
                    {
                        label: '病历',
                        value: 2,
                        component: EmrEditorSuggestMedicals,
                    }, {
                        label: '历史',
                        value: 3,
                        component: PatientOutpatientHistory,
                    }, {
                        label: '报告',
                        value: 4,
                        component: EmrEditorSuggestRecords,
                    },
                ];
                const hospitalCommonTabOptions = [
                    {
                        label: '模板',
                        value: 1,
                        component: EmrEditorSuggestTemplates,
                    },
                    {
                        label: '病历',
                        value: 2,
                        component: EmrEditorSuggestMedicals,
                    },
                    {
                        label: '医嘱',
                        value: 3,
                        component: EmrEditorSuggestPrescriptions,
                    },
                    {
                        label: '诊断',
                        value: 4,
                        component: EmrEditorSuggestDiagnosis,
                    },
                    {
                        label: '体征',
                        value: 5,
                        component: EmrEditorSuggestSigns,
                    },
                    {
                        label: '报告',
                        value: 6,
                        component: EmrEditorSuggestRecords,
                    },
                ];

                this.tabOptions = this.businessType === MedicalDocumentBusinessType.OUTPATIENT ?
                    outpatientCommonTabOptions :
                    hospitalCommonTabOptions;

                // 护理记录单默认选中体征
                this.currentTab = this.medicalDocumentType === MedicalDocumentRecordTypeEnum.CareTableRecord ? 5 : this.tabOptions[0].value;
            },
            handleUpdateDiff(...props) {
                this.$emit('update-diff', ...props);
            },
            handleUpdateSingleDiff(...props) {
                this.$emit('update-single-diff', ...props);
            },
            handleSetSelection(...props) {
                this.$emit('set-selection', ...props);
            },
            handleScrollToFieldByIndex(...props) {
                this.$emit('scroll-to-field-by-index', ...props);
            },
            handleInsertRecord(...props) {
                this.$emit('insert-record', ...props);
            },
            handleQuickInsert(...props) {
                this.$emit('quick-insert', ...props);
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";

.emr-editor-suggest {
    width: 400px;
    height: 100%;
    background: #ffffff;

    &_tabs {
        .abc-tabs {
            height: 44px;
            padding-left: 12px;
        }
    }

    &_content {
        height: calc(100% - 44px);
    }

    .emr-editor-suggest_content {
        position: relative;
    }
}
</style>

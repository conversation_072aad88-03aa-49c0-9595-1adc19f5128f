<template>
    <div class="emr-editor-suggest-records">
        <abc-tabs
            v-if="showTabs"
            v-model="curTab"
            :option="options"
            size="small"
            @change="handleChangeTab"
        >
        </abc-tabs>

        <div v-abc-loading="loading" class="emr-editor-suggest-records_content">
            <template v-if="curTab === EXAM_ENUM.Inspect">
                <div v-if="(!inspectList || !inspectList.length) && !loading" class="sidebar-no-data-wrapper">
                    暂无数据
                </div>
                <template v-else>
                    <inspect-item
                        v-for="(item, index) in inspectList"
                        :key="item.examinationSheetId"
                        :data="item"
                        :disabled-operate="disabledOperate"
                        :loading="item.loading"
                        @fetch-detail="fetchDetail(item, index)"
                        @quick-insert="handleQuickInsert"
                    ></inspect-item>
                </template>
            </template>
            <template v-else>
                <div v-if="(!examList || !examList.length) && !loading" class="sidebar-no-data-wrapper">
                    暂无数据
                </div>
                <template v-else>
                    <exam-item
                        v-for="(item, index) in examList"
                        :key="item.examinationSheetId"
                        :loading="item.loading"
                        :data="item"
                        :disabled-operate="disabledOperate"
                        @fetch-detail="fetchDetail(item,index)"
                        @quick-insert="handleQuickInsert"
                    ></exam-item>
                </template>
            </template>
        </div>
    </div>
</template>

<script>
    import ExamItem from '@/views-hospital/medical-prescription/components/emr-editor/exam-components/exam-item.vue';
    import InspectItem from '@/views-hospital/medical-prescription/components/emr-editor/exam-components/inspect-item.vue';

    import ExaminationAPI from 'api/hospital/examination/index.js';
    import { SampleExaminationAPI } from 'api/hospital/examination/sample-examination.js';
    import { inspectDetailAdapter } from '@/views-hospital/inspect-diagnosis/utils/adapter';
    import {
        dcm4cheeType,
    } from '@/views-hospital/inspect-setting/utils/constant';
    import { AbcMedicalImagingViewerService } from '@/service/abcMedicalImagingViewer';
    import Logger from 'utils/logger';
    import { mapGetters } from 'vuex';
    import InspectAPI from 'api/hospital/inspect';

    const EXAM_ENUM = {
        Inspect: 1,
        Exam: 2,
    };
    export default {
        name: 'EmrEditorSuggestRecords',
        components: {
            InspectItem,
            ExamItem,
        },
        props: {
            patientOrderId: {
                type: String,
                default: '',
            },
            // 指定tab后，不能切换，只查询该tab下的数据
            defaultTab: {
                type: Number,
                default: undefined,
            },
            // 禁止操作
            disabledOperate: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                EXAM_ENUM,
                curTab: 1,
                options: [
                    {
                        value: EXAM_ENUM.Inspect,
                        label: '检验',
                    },
                    {
                        value: EXAM_ENUM.Exam,
                        label: '检查',
                    },
                ],
                inspectList: null,
                examList: null,
                loading: false,
                examLoading: true,
            };
        },
        computed: {
            ...mapGetters(['isEnablePacsUpgrade']),
            showTabs() {
                return this.defaultTab === undefined;
            },
        },
        created() {
            if (this.defaultTab !== undefined) {
                this.curTab = this.defaultTab;
            }
            this.handleChangeTab(this.curTab);
        },
        methods: {
            async initInspectData(val) {
                try {
                    if (!this.inspectList?.length) {
                        this.loading = true;
                        if (!this.patientOrderId) return;
                        const data = await this.fetchData(val);
                        this.inspectList = data.rows;
                    }
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
            },
            async initExamData(val) {
                try {
                    if (!this.examList?.length) {
                        this.loading = true;
                        const data = await this.fetchData(val);
                        this.examList = data.rows.map((item) => ({
                            ...item,
                            examDetail: {},
                            study: [],
                        }));
                    }
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
            },
            async fetchData(type) {
                try {
                    const { data } = await ExaminationAPI.fetchExaminationList(this.patientOrderId, type);
                    return data;
                } catch (e) {
                    console.warn('获取列表失败', e);
                }
            },
            async handleChangeTab(val) {
                if (!this.patientOrderId) return;
                if (val === EXAM_ENUM.Inspect) {
                    await this.initInspectData(val);
                } else {
                    await this.initExamData(val);
                }
            },
            /**
             * @desc 获取检验单详情
             * <AUTHOR>
             * @date 2023-02-23 18:46:18
             */
            async fetchDetail(item, index) {
                this.$set(item, 'loading', true);
                try {
                    const { examinationSheetId } = item;
                    if (!examinationSheetId) return;
                    if (item?.detail) return;
                    const { data } = await SampleExaminationAPI.getSampleExaminationReport(examinationSheetId);
                    if (this.curTab === EXAM_ENUM.Inspect) {
                        this.$set(this.inspectList[index], 'examDetail', data);
                    } else {
                        if (dcm4cheeType.includes(data.deviceType)) {
                            const applySheetNo = data.examinationApplySheetNo || '';
                            if (applySheetNo) {
                                const study = await this.getStudy(applySheetNo);
                                data.study = study;
                            }
                        }

                        this.$set(this.examList[index], 'examDetail', inspectDetailAdapter(data));
                    }
                } catch (e) {
                    console.error(e);
                }
                this.$set(item, 'loading', false);
            },

            async getStudy(applyNo) {
                try {
                    await AbcMedicalImagingViewerService.getInstance().start();
                    let completed = false;
                    let study = [];
                    if (this.isEnablePacsUpgrade) {
                        const { data } = await InspectAPI.getStudyCompleteByAccessionNumber(applyNo);
                        if (data.rows && data.rows.length > 0) {
                            study = data.rows;
                            completed = true;
                        }
                    } else {
                        const {
                            completed: _completed, study: _study,
                        } = await AbcMedicalImagingViewerService
                            .getInstance()
                            .getBusinessService()
                            .getInstance()
                            .checkStudyCompleteByAccessionNumber(applyNo);
                        completed = _completed;
                        study = _study;
                    }

                    if (!completed) {
                        return [];
                    }

                    const promises = study.map((o) => {
                        return AbcMedicalImagingViewerService
                            .getInstance()
                            .getStudyByStudyInstanceUid(o.StudyInstanceUID);
                    });

                    const studies = await Promise.all(promises);
                    studies.forEach((o) => {
                        o.series.forEach((series) => {
                            series.thumbnail = series.instances[Math.floor(series.instances.length / 2)].imageId;
                        });
                    });
                    return studies;
                } catch (e) {
                    Logger.error({
                        scene: 'GET_STUDY_ERROR',
                        err: e,
                    });
                }
            },

            handleQuickInsert(value) {
                this.$emit('quick-insert', value);
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";

.emr-editor-suggest-records {
    display: flex;
    flex-direction: column;
    height: 100%;

    .abc-tabs {
        padding-left: 12px;
    }

    .emr-editor-suggest-records_content {
        position: relative;
        flex: 1;
        overflow-y: auto;
    }

    .sidebar-no-data-wrapper {
        position: absolute;
        top: 28%;
        width: 100%;
        height: auto;
        font-size: 14px;
        line-height: 1;
        color: #aab4bf;
        text-align: center;
    }
}
</style>

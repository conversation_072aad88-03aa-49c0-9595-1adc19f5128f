<template>
    <div class="emr-editor-suggest-diagnosis">
        <div v-if="!disabledOperate" style="padding: 10px 16px;">
            <abc-text theme="gray" size="small">
                点击快速插入
            </abc-text>
        </div>
        <abc-flex
            v-for="item in list"
            :key="item.id"
            class="diagnosis__item"
            :class="{
                'disabled': disabledOperate,
            }"
            align="center"
            justify="space-between"
            :gap="4"
            @click="!disabledOperate && handleQuickInsert(item)"
        >
            <abc-text style="flex: 1;" class="ellipsis">
                {{ item.diseaseName }}({{ item.diseaseCode }})
            </abc-text>
            <abc-flex :gap="8">
                <abc-text theme="gray" size="mini">
                    {{ DiagnosisTypeTextEnum[item.type] }}
                </abc-text>
                <abc-flex :gap="4">
                    <abc-text
                        theme="gray"
                        size="mini"
                        class="ellipsis"
                        style="width: 36px;"
                    >
                        {{ item.doctorName }}
                    </abc-text>
                    <abc-text theme="gray" size="mini">
                        {{ parseTime(item.diagnosedTime, 'm-d', true) }}
                    </abc-text>
                </abc-flex>
            </abc-flex>
        </abc-flex>
    </div>
</template>

<script>
    import MedicalPrescriptionAPI from 'api/hospital/medical-prescription/index.js';

    import { DiagnosisTypeTextEnum } from '@/views-hospital/medical-prescription/utils/constants.js';
    import { parseTime } from '@abc/utils-date';

    export default {
        name: 'EmrEditorSuggestDiagnosis',
        props: {
            patientOrderId: {
                type: String,
                required: true,
            },
            // 禁止操作
            disabledOperate: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                DiagnosisTypeTextEnum,
                list: [],
                loading: false,
            };
        },
        created() {
            this.fetchData();
        },
        methods: {
            parseTime,
            async fetchData() {
                try {
                    this.loading = true;
                    this.list = await MedicalPrescriptionAPI.getDiagnosisList({
                        patientOrderId: this.patientOrderId,
                    });
                } catch (e) {
                    console.warn('获取诊断列表失败',e);
                } finally {
                    this.loading = false;
                }
            },
            async handleQuickInsert(item) {
                const {
                    type, diseaseName, diseaseCode, doctorName, diagnosedTime,
                } = item;
                const typeStr = DiagnosisTypeTextEnum[type];
                const diagnosedTimeStr = parseTime(diagnosedTime, 'm-d', true);
                const resStr = `${diseaseName || ''}${diseaseCode ? `(${diseaseCode})` : ''} ${typeStr || ''} ${doctorName || ''} ${diagnosedTimeStr || ''}`;
                this.$emit('quick-insert', resStr);
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";

.emr-editor-suggest-diagnosis {
    height: 100%;
    overflow: auto;

    .diagnosis__item {
        height: 44px;
        padding: 0 16px;
        cursor: url("~assets/images/cursor-insert.svg"), pointer;

        &:hover {
            background: var(--abc-color-cp-grey4);
        }

        &.disabled {
            cursor: unset;
            background: unset;
        }
    }
}
</style>

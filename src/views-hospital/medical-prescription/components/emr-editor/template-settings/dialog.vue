<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        :title="''"
        full
        append-to-body
        class="emr-template-setting-dialog-wrapper"
        content-styles="padding: 0px; width: 90vw; height: 90vh;"
    >
        <div class="setting-dialog__content-wrapper">
            <div class="setting-dialog__left">
                <template-category :catalog-list="catalogList" :selected-node="currentSelectedNode" @change-node="handleChangeNode"></template-category>
            </div>
            <div v-abc-loading="templateLoading" class="setting-dialog__right">
                <div class="template-list__title">
                    <p>模板设置</p>
                    <abc-delete-icon size="huge" @delete="handleClose"></abc-delete-icon>
                </div>
                <div class="template-list__content">
                    <div class="emr-template-list__handler-bar">
                        <abc-button type="success" @click="handleClickCreateMedicalDocument">
                            新增模版
                        </abc-button>
                    </div>
                    <abc-form ref="form">
                        <template-setting
                            :owner-type-options="ownerTypeOptions"
                            :departments="departments"
                            :loading="templateLoading"
                            :medical-document-file-list="postData.emrTemplateViews"
                            @delete="handleDelete"
                            @edit="handleUpdateMedicalDocument"
                        ></template-setting>
                    </abc-form>
                    <div class="template-list__footer">
                        <abc-button @click="handleSave">
                            保存
                        </abc-button>
                    </div>
                </div>
            </div>
        </div>

        <emr-editor-design-dialog-v2
            v-if="showMedicalDocumentDesignDialog"
            v-model="showMedicalDocumentDesignDialog"
            :design-mode="DesignModeEnum.MedicalDocument"
            :on-submit="handleOnSubmitMedicalDocument"
            :default-post-data="currentMedicalDocumentContent"
            :departments="departments"
            :owner-type-options="ownerTypeOptions"
            :default-owner-id="currentMedicalDocumentOwnerId"
            :default-owner-type="currentMedicalDocumentOwnerType"
        ></emr-editor-design-dialog-v2>
    </abc-dialog>
</template>

<script>
    import TemplateCategory from './category.vue';
    import TemplateSetting from './template.vue';
    import EmrAPI from 'api/hospital/emr/index.js';
    import {
        CATALOGUE_FILE_OWNER_TYPE,
    } from 'utils/constants.js';
    import { GLOBAL_PAGE_LARGE_REDUCER } from '@/printer/config.js';
    import {
        DesignModeEnum, EditorPageOrientation,
    } from '@abc-emr-editor/constants';
    import SettingAPI from 'api/settings.js';
    import { mergeCommonTemplate } from '@/views-hospital/medical-prescription/components/emr-editor/common/tools.js';
    import { isEqual } from 'utils/lodash.js';
    import clone from 'utils/clone.js';
    import { OwnerTypeOptions } from '@/views-hospital/medical-prescription/components/emr-editor/common/constants';
    export default {
        name: 'EmrEditorTemplateSettingsDialog',
        components: {
            TemplateCategory,
            TemplateSetting,
            EmrEditorDesignDialogV2: () => import('@/views-hospital/medical-prescription/components/emr-editor/emr-editor-design-dialog-v2.vue'),
        },
        props: {
            value: {
                type: Boolean,
                required: true,
            },
            businessType: Number,
            businessId: String,
        },
        data() {
            return {
                DesignModeEnum,
                departments: [],
                catalogList: [],
                templateLoading: false,
                currentSelectedNode: {},
                postData: {
                    emrTemplateViews: [],
                },
                ownerTypeOptions: OwnerTypeOptions,
                showMedicalDocumentDesignDialog: false,
                currentUpdateMedicalDocument: null,
                currentMedicalDocumentContent: null,
                currentMedicalDocumentOwnerId: null,
                currentMedicalDocumentOwnerType: null,
                currentMedicalDocumentId: '',
            };
        },
        computed: {
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            disabled() {
                return isEqual(this.postData, this.cachePostData);
            },
        },
        created() {
            this.initData();
            this.fetchDepartments();
        },
        methods: {
            async fetchDepartments() {
                try {
                    const { data } = await SettingAPI.clinic.fetchClinicDepartments();
                    this.departments = data.data?.rows || [];
                } catch (e) {
                    this.departments = [];
                }
            },
            handleClose() {
                this.showDialog = false;
            },
            // 获取当前人能够查看的文书
            // 获取第一个节点的模板列表
            async initData() {
                await this.fetchMedicalDocumentList();
                this.currentSelectedNode = this.catalogList?.[0]?.children?.[0];
                await this.fetchMedicalTemplateDetail(this.currentSelectedNode?.id);
            },
            async fetchMedicalDocumentList() {
                try {
                    const { data: { emrCatalogueViews = [] } } = await EmrAPI.fetchMedicalDocumentListWithPermission(this.businessType);
                    this.catalogList = emrCatalogueViews.map((item) => {
                        const tempItem = item;
                        tempItem.children = item.medicalViews;
                        tempItem.isFolder = 1;
                        tempItem.expand = true;
                        delete tempItem.medicalViews;
                        return tempItem;
                    });
                } catch (e) {
                    this.catalogList = [];
                }
            },
            /**
             * @desc 获取文书的模板列表
             * <AUTHOR>
             * @date 2023-09-08 15:58:15
             */
            async fetchMedicalTemplateDetail(medicalId) {
                if (!medicalId) return;
                try {
                    this.templateLoading = true;
                    const { data } = await EmrAPI.fetchMedicalDocumentDetail(medicalId);
                    data.emrTemplateViews = data.emrTemplateViews.map((emrTemplate) => {
                        const { permissions } = emrTemplate;
                        let ownerType, ownerId;
                        // 全院公用
                        if (permissions.some((it) => it.ownerType === CATALOGUE_FILE_OWNER_TYPE.CLINIC)) {
                            ownerType = CATALOGUE_FILE_OWNER_TYPE.CLINIC;
                            ownerId = [];
                        }
                        // 个人专用
                        if (permissions.some((it) => it.ownerType === CATALOGUE_FILE_OWNER_TYPE.PERSONAL)) {
                            ownerType = CATALOGUE_FILE_OWNER_TYPE.PERSONAL;
                            ownerId = [];
                        }
                        // 科室公用
                        if (permissions.every((it) => it.ownerType === CATALOGUE_FILE_OWNER_TYPE.DEPARTMENT)) {
                            ownerType = CATALOGUE_FILE_OWNER_TYPE.DEPARTMENT;
                            ownerId = permissions.map((it) => it.ownerId);
                        }
                        return {
                            ...emrTemplate,
                            ownerType,
                            ownerId,
                        };
                    });
                    this.postData = data;
                    this.cachePostData = clone(this.postData);
                } catch (e) {
                    console.error('获取模板详情错误', e);
                } finally {
                    this.templateLoading = false;
                }
            },

            /**
             * @desc 切换不同文书类型的模板列表
             * <AUTHOR>
             * @date 2023-09-09 09:50:23
             */
            async handleChangeNode(node) {
                if (node.depth !== 1) {
                    this.currentSelectedNode = node;
                    await this.fetchMedicalTemplateDetail(node.id);
                }
            },
            createDefaultPostDataContent() {
                return {
                    templateSetting: {
                        pageSize: 'A4',
                        pageSizeReduce: GLOBAL_PAGE_LARGE_REDUCER,
                        pageOrientation: EditorPageOrientation.portrait,
                    },
                    templateContent: {},
                };
            },
            // 新建模板
            handleClickCreateMedicalDocument() {
                this.currentMedicalDocumentContent = {
                    name: this.postData.name,
                    content: this.postData.content ?? this.createDefaultPostDataContent(),
                };
                this.currentMedicalDocumentOwnerId = [];
                this.currentMedicalDocumentOwnerType = CATALOGUE_FILE_OWNER_TYPE.CLINIC;
                this.showMedicalDocumentDesignDialog = true;
                this.submitMode = 'create';
            },
            async handleDelete(id) {
                try {
                    await EmrAPI.deleteMedicalDocumentV2(id);
                    await this.fetchMedicalTemplateDetail(this.currentSelectedNode.id);
                } catch (e) {
                    console.error('删除模板失败', e);
                }
            },
            createUpdateTemplatePostData(postData, customProps) {
                return {
                    businessType: this.businessType,
                    medicalId: this.postData.id,
                    combineIds: postData.combineIds,
                    content: postData.content,
                    name: postData.name,
                    ...customProps,
                };
            },
            // 更新模板
            async handleUpdateMedicalDocument(currentUpdateMedicalDocument) {
                const {
                    id,
                    name,
                    ownerType,
                    ownerId,
                } = currentUpdateMedicalDocument;
                const { data } = await EmrAPI.fetchEmrTemplate(id);
                this.currentMedicalDocumentContent = {
                    name,
                    content: data.content ?? this.createDefaultPostDataContent(),
                };
                await mergeCommonTemplate(this.currentMedicalDocumentContent.content?.templateContent?.content);
                this.designMode = DesignModeEnum.MedicalDocument;
                this.showMedicalDocumentDesignDialog = true;
                this.submitMode = 'update';
                this.currentUpdateMedicalDocument = currentUpdateMedicalDocument;
                this.currentMedicalDocumentId = id;
                this.currentMedicalDocumentOwnerId = ownerId;
                this.currentMedicalDocumentOwnerType = ownerType;
            },
            async handleSave() {
                this.$refs.form.validate(async (valid) => {
                    if (!valid) {
                        return;
                    }
                    this.saveLoading = true;
                    try {
                        const postData = {
                            id: this.postData.id,
                            name: this.postData.name,
                            sharedPageId: this.postData.sharedPageId,
                            permissions: this.postData.permissions,
                            emrTemplates: this.postData.emrTemplateViews.map((fileItem) => {
                                return {
                                    ...fileItem,
                                    permissions: this.handleTemplatePermission(fileItem),
                                };
                            }),
                        };
                        await EmrAPI.updateMedicalDocumentDetail(postData);
                        await this.fetchMedicalTemplateDetail(this.currentSelectedCatalogNode);
                        this.$Toast({
                            type: 'success',
                            message: '保存成功',
                        });
                    } catch (e) {
                        console.error(e);
                        this.$Toast({
                            type: 'error',
                            message: '保存失败',
                        });
                    } finally {
                        this.saveLoading = false;
                    }
                });
            },
            handleTemplatePermission(templateItem) {
                let permissions = [];
                if (templateItem.ownerType === CATALOGUE_FILE_OWNER_TYPE.CLINIC) {
                    permissions = [{
                        ownerId: this.clinicId,
                        ownerType: templateItem.ownerType,
                    }];
                }
                if (templateItem.ownerType === CATALOGUE_FILE_OWNER_TYPE.PERSONAL) {
                    permissions = [{
                        ownerId: this.userId,
                        ownerType: templateItem.ownerType,
                    }];
                }
                if (templateItem.ownerType === CATALOGUE_FILE_OWNER_TYPE.DEPARTMENT) {
                    permissions = templateItem.ownerId.map((id) => ({
                        ownerId: id,
                        ownerType: templateItem.ownerType,
                    }));
                }
                return permissions;
            },
            // 更新模板
            async handleOnSubmitMedicalDocument(postData, onSuccess, onError) {
                try {
                    if (this.submitMode === 'create') {
                        await EmrAPI.createEmrTemplate(this.createUpdateTemplatePostData(postData, {
                            isDefault: 0,
                            permissions: this.handleTemplatePermission(postData),
                        }));
                    }
                    if (this.submitMode === 'update') {
                        this.currentUpdateMedicalDocument.ownerId = postData.ownerId;
                        this.currentUpdateMedicalDocument.ownerType = postData.ownerType;
                        await EmrAPI.updateEmrTemplate(this.createUpdateTemplatePostData(postData, {
                            isDefault: 0,
                            id: this.currentMedicalDocumentId,
                            permissions: this.handleTemplatePermission(this.currentUpdateMedicalDocument),
                        }));
                    }
                    onSuccess();
                    await this.fetchMedicalTemplateDetail(this.currentSelectedNode.id);
                    this.$emit('update');
                } catch (e) {
                    this.$Toast({
                        type: 'error',
                        message: this.submitMode === 'create' ? '创建模板失败' : '更新模板失败',
                    });
                    onError(e);
                    console.error(e);
                }
            },

            getRightContentIsScroll() {
                this.$nextTick(() => {
                    const isScroll = $('.setting-dialog__right').scrollTop();
                    return isScroll;
                });
            },

        },
    };
</script>
<style lang="scss">
@import "styles/theme.scss";
@import "styles/mixin.scss";

.emr-template-setting-dialog-wrapper {
    .setting-dialog__content-wrapper {
        display: flex;
        height: 100%;
        min-height: 440px;
    }

    .setting-dialog__left {
        flex: 0 0 360px;
        height: 100%;
        background: #f5f7fb;
        border-top-left-radius: 5px;
        border-bottom-left-radius: 5px;
    }

    .setting-dialog__right {
        display: flex;
        flex: 1;
        flex-direction: column;
        height: 100%;
    }

    .template-list__title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 56px;
        padding: 0 16px;
        border-bottom: 1px solid $P6;

        > p {
            font-size: 16px;
            font-weight: 500;
            line-height: 24px;
        }
    }

    .template-list__content {
        flex: 1;
        height: 0;
        padding: 16px 16px 0;
        overflow-y: auto;
        overflow-y: overlay;

        @include scrollBar;
    }

    .template-list__footer {
        position: sticky;
        bottom: 0;
        padding: 16px 0;
        margin-top: 16px;
        background-color: #ffffff;
        border-top: 1px solid $P6;
    }

    .emr-template-list__handler-bar {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 16px;
    }
}
</style>


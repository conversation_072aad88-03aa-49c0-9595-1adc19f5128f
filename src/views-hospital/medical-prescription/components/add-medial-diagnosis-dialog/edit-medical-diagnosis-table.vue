<template>
    <div class="edit-medical-diagnosis-table-wrapper">
        <abc-excel-table class="edit-medical-diagnosis-table">
            <diagnosis-table-header :header-config="DiagnosisTableHeaderConfig">
                <abc-checkbox
                    slot="th-selection"
                    :indeterminate="indeterminate"
                    :value="checkAll"
                    @change="handleCheckAll"
                ></abc-checkbox>
            </diagnosis-table-header>
            <div v-if="diagnosisList && diagnosisList.length" class="table-body">
                <div
                    v-for="(diagnosisItem, diagnosisItemIndex) in diagnosisList"
                    :key="diagnosisItemIndex"
                    class="edit-medical-diagnosis-item"
                >
                    <div
                        v-if="diagnosisItemIndex !== diagnosisList.length - 1"
                        style="width: 40px; text-align: center;"
                    >
                        <abc-checkbox
                            v-model="diagnosisItem.checked"
                            type="number"
                            @change="(val) => handleCheckChange(val, diagnosisItem)"
                        >
                        </abc-checkbox>
                    </div>
                    <div
                        v-if="diagnosisItemIndex === diagnosisList.length - 1"
                        style="width: 40px; text-align: center;"
                    ></div>
                    <div class="td">
                        <abc-select
                            v-model="diagnosisItem.type"
                            :disabled="diagnosisItem.disabled"
                            no-icon
                            :width="86"
                            @change="(val) => handleTypeChange(val, diagnosisItem)"
                        >
                            <abc-option
                                v-for="(item, index) in diagnosisTypeOptions"
                                :key="index"
                                :value="item.value"
                                :label="item.label"
                            >
                            </abc-option>
                        </abc-select>
                    </div>
                    <div class="td">
                        <abc-select
                            v-model="diagnosisItem.category"
                            :disabled="diagnosisItem.disabled"
                            no-icon
                            :width="54"
                            @change="(val) => handleCategoryChange(val, diagnosisItem)"
                        >
                            <abc-option
                                v-for="item in categoryOptions"
                                :key="item.value"
                                :value="item.value"
                                :label="item.label"
                            >
                            </abc-option>
                        </abc-select>
                    </div>
                    <div class="td" style="width: 283px;">
                        <template v-if="!diagnosisItem.category">
                            <abc-form-item
                                :validate-event="validateDiseaseName"
                                :validate-params="diagnosisItem"
                            >
                                <diagnosis-autocomplete
                                    :inner-width="400"
                                    :width="283"
                                    :icon-name="''"
                                    :type="1"
                                    :disabled="diagnosisItem.disabled"
                                    focus-show
                                    :default-keyword="diagnosisItem.diseaseName.slice(0, 16)"
                                    @select-diagnosis="(val) => handleEditDiseaseName(val, diagnosisItem)"
                                ></diagnosis-autocomplete>
                            </abc-form-item>
                        </template>
                        <template v-else>
                            <abc-form-item
                                :validate-event="validateDiseaseName"
                                :validate-params="diagnosisItem"
                            >
                                <diagnosis-autocomplete
                                    :inner-width="400"
                                    :width="203"
                                    :icon-name="''"
                                    focus-show
                                    :type="2"
                                    :disabled="diagnosisItem.disabled"
                                    :default-keyword="diagnosisItem.diseaseName.slice(0, 8)"
                                    @select-diagnosis="(val) => handleEditDiseaseName(val, diagnosisItem)"
                                ></diagnosis-autocomplete>
                            </abc-form-item>
                            <syndrome-type-autocomplete
                                focus-show
                                :inner-width="120"
                                :width="80"
                                :default-keyword="diagnosisItem.tcmSyndromeTypeInput"
                                :tcm-syndrome-type-text="diagnosisItem.tcmSyndromeTypeText"
                                :icon-name="''"
                                :type="3"
                                :disabled="diagnosisItem.disabled"
                                @select-diagnosis="(val) => handleEditSyndromeType(val, diagnosisItem)"
                            ></syndrome-type-autocomplete>
                        </template>
                    </div>
                    <div class="td">
                        <abc-form-item
                            style="margin: 0;"
                            :validate-event="validateIsPrimary"
                            :validate-params="diagnosisItem"
                        >
                            <abc-select
                                v-model="diagnosisItem.isPrimary"
                                no-icon
                                :input-style="{
                                    textAlign: 'center',
                                }"
                                :disabled="diagnosisItem.disabled"
                                :width="66"
                                @change="(val) => handleIsPrimaryChange(val, diagnosisItem)"
                            >
                                <abc-option
                                    v-for="item in diagnosisPrimaryOptions"
                                    :key="item.value"
                                    :value="item.value"
                                    :label="item.label"
                                >
                                </abc-option>
                            </abc-select>
                        </abc-form-item>
                    </div>
                    <div class="td">
                        <abc-select
                            v-model="diagnosisItem.diagnosedStatus"
                            :disabled="diagnosisItem.disabled"
                            no-icon
                            :width="54"
                            @change="(val) => handleDiagnosisStatusChange(val, diagnosisItem)"
                        >
                            <abc-option
                                v-for="item in diagnosisStatusOptions"
                                :key="item.value"
                                :value="item.value"
                                :label="item.label"
                            >
                            </abc-option>
                        </abc-select>
                    </div>
                    <div
                        class="td"
                        style="min-width: 100px; max-width: 100px; height: 40px; padding: 0 8px; line-height: 40px;"
                        :class="{
                            'is-disabled': diagnosisItem.disabled
                        }"
                    >
                        <div v-abc-title.ellipsis="diagnosisItem.diseaseCode"></div>
                    </div>
                    <div class="td">
                        <div v-if="diagnosisItem.disabled" class="disabled-time">
                            {{ parseTime(diagnosisItem.sickTime, "y-m-d", true) || "" }}
                        </div>
                        <abc-date-picker
                            v-else
                            v-model="diagnosisItem.sickTime"
                            :disabled="diagnosisItem.disabled"
                            :width="100"
                            :clearable="false"
                            placeholder="日期"
                            :picker-options="pickerOptions"
                            @change="(val) => handleSickTimeChange(val, diagnosisItem)"
                        >
                        </abc-date-picker>
                    </div>
                    <div class="td">
                        <div v-if="diagnosisItem.disabled" class="disabled-time">
                            {{ parseTime(diagnosisItem.diagnosedTime, "y-m-d", true) || "" }}
                        </div>
                        <abc-date-picker
                            v-else
                            v-model="diagnosisItem.diagnosedTime"
                            :disabled="diagnosisItem.disabled"
                            :width="100"
                            :clearable="false"
                            placeholder="诊断日期"
                            :picker-options="pickerOptions"
                            @change="(val) => handleSelectTime(val, diagnosisItem)"
                        >
                        </abc-date-picker>
                    </div>
                    <div
                        v-abc-title.ellipsis="diagnosisItem.doctorName"
                        class="td"
                        style="width: 64px; height: 40px; padding-left: 10px; border-right: 0;"
                        :class="{ 'is-disabled': diagnosisItem.disabled }"
                    ></div>
                </div>
            </div>
        </abc-excel-table>
    </div>
</template>
<script>
    import AbcExcelTable from 'components/excel-table/index.vue';
    import DiagnosisTableHeader from '@/views-hospital/medical-prescription/components/edit-medical-advice-table/components/table-header.vue';
    import {
        DiagnosisTableHeaderConfig,
        DiagnosisTypeOptionsEnum,
    } from '@/views-hospital/medical-prescription/model/medical-diagnosis.js';
    import {
        parseTime, createGUID,
    } from 'utils/index';
    import DiagnosisAutocomplete from './diagnosis-autocomplete.vue';
    import SyndromeTypeAutocomplete from './syndrome-type-autocomplete.vue';
    import { mapGetters } from 'vuex';
    export default {
        name: 'EditMedicalDiagnosisTable',
        components: {
            AbcExcelTable,
            DiagnosisTableHeader,
            DiagnosisAutocomplete,
            SyndromeTypeAutocomplete,
        },
        props: {
            list: {
                type: Array,
                default: () => [],
            },
            patientOrderId: {
                type: String,
                default: '',
            },
            doctor: {
                type: Object,
                default: () => ({}),
            },
            patient: {
                type: Object,
                default: () => ({}),
            },
            defaultDiagnosisType: {
                type: Number,
            },
        },
        data() {
            return {
                keyword: '',
                tcmSyndromeType: '',
                DiagnosisTableHeaderConfig,
                diagnosisList: [],
                DiagnosisTypeOptionsEnum,
                categoryOptions: [
                    {
                        label: '中医',
                        value: 10,
                    },
                    {
                        label: '西医',
                        value: 0,
                    },
                ],
                diagnosisStatusOptions: [
                    {
                        label: '确诊',
                        value: 0,
                    },
                    {
                        label: '疑似',
                        value: 10,
                    },
                ],
                diagnosisPrimaryOptions: [
                    {
                        label: '是',
                        value: 10,
                    },
                    {
                        label: '否',
                        value: 0,
                    },
                ],
                sickTime: parseTime(new Date(), 'y-m-d', true),
                diagnosedTime: parseTime(new Date(), 'y-m-d', true),
                pickerOptions: {
                    disabledDate(date) {
                        return date > new Date('2040-01-01');
                    },
                    shortcuts: [
                        {
                            text: '今天',
                            onClick(cb) {
                                const start = new Date();
                                cb(start);
                            },
                        },
                        {
                            text: '昨天',
                            onClick(cb) {
                                const start = new Date();
                                start.setTime(start.getTime() - 24 * 60 * 60 * 1000);
                                cb(start);
                            },
                        },
                    ],
                    yearRange: {
                        begin: 1930,
                        end: 2040,
                    },
                },
                diseaseNameKey: '',
            };
        },
        computed: {
            ...mapGetters(['userInfo']),
            diagnosisTypeOptions() {
                return DiagnosisTypeOptionsEnum.filter((item) => item.value !== 30);
            },
            /**
             * @desc 全选的状态
             */
            checkAll() {
                return (
                    this.selectedDiagnosisCount ===
                    this.diagnosisList?.filter((item) => item.diseaseName)?.length &&
                    this.diagnosisList?.filter((item) => item.diseaseName)?.length
                );
            },
            /**
             * @desc 半选的状态
             */
            indeterminate() {
                return (
                    !!this.selectedDiagnosisCount &&
                    this.selectedDiagnosisCount <
                    this.diagnosisList?.filter((item) => item.diseaseName)?.length
                );
            },
            selectedDiagnosisCount() {
                return this.diagnosisList?.filter((item) => item.checked && item.diseaseName)
                    ?.length;
            },
        },
        watch: {
            list: {
                handler(list) {
                    this.diagnosisList = list?.map((item) => {
                        return {
                            ...item,
                            diseaseName: this.$set(item, 'diseaseName', item.diseaseName),
                            tcmSyndromeTypeInput: item?.tcmSyndromeType?.map((it) => it.name)?.join(','),
                            key: createGUID(),
                        };
                    });
                },
                immediate: true,
                deep: true,
            },
            diseaseNameKey: {
                handler() {
                    this.addDiagnosisItem();
                },
            },
        },
        methods: {
            parseTime,
            diagnosisSearchType(category) {
                return category ? 2 : 1;
            },
            addDiagnosisItem() {
                if (this.diagnosisList.filter((item) => !item.diseaseName)?.length < 1) {
                    this.diagnosisList.push({
                        diseaseName: '',
                        diseaseCode: '',
                        checked: 0,
                        type: this.diagnosisList?.length ?
                            this.diagnosisList[this.diagnosisList.length - 1].type :
                            10,
                        category: this.diagnosisList?.length ?
                            this.diagnosisList[this.diagnosisList.length - 1].category :
                            0,
                        isPrimary: 0,
                        diagnosedStatus: 0,
                        sickTime: parseTime(new Date(), 'y-m-d', true),
                        diagnosedTime: parseTime(new Date(), 'y-m-d', true),
                        doctorName: this.userInfo.name,
                        doctorId: this.userInfo.id,
                        departmentId: this.$route.params.departmentId,
                        patientId: this.patient.id,
                        key: createGUID(),
                        patientOrderId: this.patientOrderId,
                    });
                }
                this.$emit('diagnosis-change', this.diagnosisList);
            },
            handleEditSyndromeType(val, diagnosisItem) {
                const arr = [];
                arr.push({
                    name: val.name,
                    code: val.code,
                });
                this.diagnosisList = this.diagnosisList.map((item) => {
                    if (item.key === diagnosisItem.key) {
                        return {
                            ...item,
                            tcmSyndromeType: arr,
                            // tcmSyndromeTypeText: arr?.map((it) => it.name)?.join(','),
                            // tcmSyndromeTypeInput: val.name,
                        };
                    }
                    return {
                        ...item,
                    };
                });
                this.$emit('diagnosis-change', this.diagnosisList);
            },
            handleEditDiseaseName(val, diagnosisItem) {
                this.diseaseNameKey = createGUID();
                this.diagnosisList = this.diagnosisList.map((item) => {
                    if (item.key === diagnosisItem.key) {
                        return {
                            ...item,
                            diseaseName: this.$set(item, 'diseaseName', val.name),
                            diseaseCode: val.code,
                        };
                    }
                    return {
                        ...item,
                    };
                });
                this.$emit('diagnosis-change', this.diagnosisList);
            },

            handleCheckAll(val) {
                this.diagnosisList.forEach((item) => {
                    if (item.diseaseName) {
                        item.checked = val;
                    }
                });
                this.$emit('diagnosis-change', this.diagnosisList);
            },
            handleCheckChange(val, diagnosis) {
                this.diagnosisList.forEach((item) => {
                    if (item.key === diagnosis.key) {
                        item.checked = val;
                    }
                });
                this.$emit('diagnosis-change', this.diagnosisList);
            },
            handleCategoryChange(val, diagnosis) {
                this.diagnosisList.forEach((item) => {
                    if (item.key === diagnosis.key) {
                        item.tcmSyndromeType = val === 10 ? diagnosis.tcmSyndromeType : null;
                        item.diseaseName = '';
                    }
                });
                this.$emit('diagnosis-change', this.diagnosisList);
            },
            handleTypeChange(val, diagnosis) {
                const currentDiagnosisTypeHasPrimary = this.diagnosisList
                    .filter((item) => item.type === val)
                    ?.find((it) => it.isPrimary);
                diagnosis.isPrimary = currentDiagnosisTypeHasPrimary ? 0 : 10;
                this.diagnosisList.forEach((item, index) => {
                    if (item.key === diagnosis.key) {
                        item.type = val;

                        if (index === this.diagnosisList.length - 2) {
                            this.$set(this.diagnosisList[index + 1], 'type', val);
                        }
                    }
                });
                this.$emit('diagnosis-change', this.diagnosisList);
            },

            handleSickTimeChange(val, item) {
                this.sickTime = val;
                const index = this.diagnosisList.findIndex(
                    (diagnosisItem) => diagnosisItem.key === item.key,
                );
                if (index !== -1) {
                    this.$set(this.diagnosisList[index], 'sickTime', val);
                }
                this.$emit('diagnosis-change', this.diagnosisList);
            },
            handleDiagnosisStatusChange(val, item) {
                const index = this.diagnosisList.findIndex(
                    (diagnosisItem) => diagnosisItem.key === item.key,
                );
                if (index !== -1) {
                    this.$set(this.diagnosisList[index], 'diagnosedStatus', val);
                }
                this.$emit('diagnosis-change', this.diagnosisList);
            },
            handleIsPrimaryChange(val, item) {
                const index = this.diagnosisList.findIndex(
                    (diagnosisItem) => diagnosisItem.key === item.key,
                );
                if (index !== -1) {
                    this.$set(this.diagnosisList[index], 'isPrimary', val);
                }
                this.$emit('diagnosis-change', this.diagnosisList);
            },

            handleSelectTime(val, item) {
                this.diagnosedTime = val;
                const index = this.diagnosisList.findIndex(
                    (diagnosisItem) => diagnosisItem.key === item.key,
                );
                if (index !== -1) {
                    this.$set(this.diagnosisList[index], 'diagnosedTime', val);
                }
                this.$emit('diagnosis-change', this.diagnosisList);
            },
            validateIsPrimary(val, callback, diagnosisItem) {
                const groupedData = this.diagnosisList.reduce((acc, item) => {
                    const existingGroup = acc.find((group) => group.type === item.type);
                    if (existingGroup) {
                        existingGroup.list.push(item);
                    } else {
                        acc.push({
                            type: item.type,
                            list: [item],
                        });
                    }
                    return acc;
                }, []);

                const current = groupedData.find((item) => item.type === diagnosisItem.type);

                const count = current.list.filter((item) => item.isPrimary === 10)?.length;
                if (count > 1 && val === '是') {
                    callback({
                        validate: false,
                        message: '同种类型不可有多个主诊断',
                    });
                }
            },
            validateDiseaseName(val, callback, diagnosisItem) {
                if (!val && diagnosisItem.diseaseCode) {
                    callback({
                        validate: false,
                        message: '不能为空',
                    });
                    return;
                }
                const groupedData = this.diagnosisList.reduce((acc, item) => {
                    const existingGroup = acc.find((group) => group.type === item.type);
                    if (existingGroup) {
                        existingGroup.list.push(item);
                    } else {
                        acc.push({
                            type: item.type,
                            list: [item],
                        });
                    }
                    return acc;
                }, []);

                const current = groupedData.find((item) => item.type === diagnosisItem.type);

                const count = current.list.filter((item) => item.diseaseName === val)?.length;
                if (count > 1) {
                    callback({
                        validate: false,
                        message: '同种类型,诊断不能重复',
                    });
                    return;
                }
                if (val !== diagnosisItem.diseaseName.slice(0, 16)) {
                    callback({
                        validate: false,
                        message: '需要填写标准诊断名称',
                    });
                }
            },
        },
    };
</script>
<style lang="scss">
@import "src/styles/theme.scss";
@import "src/styles/mixin.scss";

.edit-medical-diagnosis-table-wrapper {
    #abc-excel-table {
        max-height: 500px;
        overflow-x: hidden;
        overflow-y: auto;
        overflow-y: overlay;

        .table-header {
            .th {
                padding-left: 8px;

                &:first-child {
                    padding-left: 11px;
                }

                &:nth-child(2) {
                    padding-left: 2px;
                }
            }
        }

        .table-body {
            position: relative;

            .edit-medical-diagnosis-item {
                display: flex;
                align-items: center;
                border-bottom: 1px solid $P6;

                &:last-child {
                    border-bottom: none;
                }

                .td {
                    border-right: 1px dashed $P6;

                    &.is-disabled {
                        height: 100%;
                        background-color: #fafbfc;
                    }

                    .disabled-time {
                        width: 100px;
                        height: 40px;
                        padding-left: 8px;
                        line-height: 40px;
                        background-color: #fafbfc;
                    }
                }

                .abc-date-picker {
                    display: flex;
                    width: 100px;
                    height: 40px;

                    i {
                        display: none;
                    }

                    .abc-date-picker__display-value {
                        display: none;
                    }
                }

                .abc-input__inner {
                    padding: 0 8px;
                }
            }

            .add-medical-diagnosis {
                width: 100%;
            }
        }
    }
}
</style>

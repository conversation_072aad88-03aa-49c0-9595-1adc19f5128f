<template>
    <abc-dialog
        v-if="dialogVisible"
        v-model="dialogVisible"
        content-styles="width:1308px ;padding:0px;height: 584px;display: flex; overflow-y: overlay; border-radius: 5px;"
        :title="title"
        custom-class="add-medical-diagnosis__dialog"
        :append-to-body="true"
    >
        <div class="left">
            <div class="add-medical-diagnosis__dialog-toolbar">
                <abc-flex>
                    <abc-tips-card-v2
                        v-if="mustAtLeastOneWesternDisease && !hasWesternDiagnosis"
                        theme="warning"
                    >
                        医保结算要求必须开立至少一条西医诊断，若不使用医保结算请忽略此提示。
                    </abc-tips-card-v2>
                </abc-flex>
                <abc-space>
                    <abc-button :disabled="isDisabled" @click="handleSave">
                        完成
                    </abc-button>
                    <abc-button
                        type="blank"
                        :disabled="diagnosisList.length >= maxDiagnosisNum || selectedDiagnosisCount === 0"
                        :loading="copyLoading"
                        @click="handleCopyDiagnosis"
                    >
                        复制
                    </abc-button>
                    <abc-button
                        type="danger"
                        :disabled="!selectedDiagnosisCount"
                        :count="selectedDiagnosisCount"
                        @click="handleDelete"
                    >
                        删除
                    </abc-button>
                </abc-space>
            </div>
            <div class="medical-diagnosis__table-wrapper">
                <abc-form ref="medicalDiagnosisFormRef">
                    <edit-medical-diagnosis-table
                        :patient-order-id="patientOrderId"
                        :list="diagnosisList"
                        :patient="patient"
                        :default-diagnosis-type="defaultDiagnosisType"
                        @diagnosis-change="handleDiagnosisChange"
                    ></edit-medical-diagnosis-table>
                </abc-form>
            </div>
        </div>
        <div class="right">
            <diagnosis-history :diagnosis-history="diagnosisHistory" @copy-diagnosis="handleCopyHistory"></diagnosis-history>
        </div>
    </abc-dialog>
</template>

<script>
    import {
        parseTime, createGUID,
    } from 'utils/index';
    import DiagnosisHistory from '@/views-hospital/medical-prescription/components/add-medial-diagnosis-dialog/diagnosis-history/index.vue';
    import {
        DiagnosisTypeOptionsEnum, DiagnosisTypeEnum,
    } from '@/views-hospital/medical-prescription/utils/constants.js';
    import MedicalPrescriptionAPI from 'api/hospital/medical-prescription';
    import EditMedicalDiagnosisTable from './edit-medical-diagnosis-table.vue';
    import { mapGetters } from 'vuex';
    import { isEqual } from 'utils/lodash';
    import clone from 'utils/clone';
    export default {
        components: {
            DiagnosisHistory,
            EditMedicalDiagnosisTable,
        },
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            title: {
                type: String,
                default: '下诊断',
            },
            existedDiagnosisList: {
                type: Array,
                default: () => [],
            },
            patientOrderId: {
                type: String,
                default: '',
            },
            doctor: {
                type: Object,
                default: () => ({}),
            },
            patient: {
                type: Object,
                default: () => ({}),
            },
            defaultDiagnosisType: {
                type: Number,
            },
        },
        data() {
            return {
                diagnosisType: 1,
                DiagnosisTypeOptionsEnum,
                doctorType: 1,
                diagnosis: '',
                extendDiagnosisInfos: [],
                diagnosisHistory: [],
                categoryOptions: [
                    {
                        label: '中医',
                        value: 10,
                    },
                    {
                        label: '西医',
                        value: 0,
                    },

                ],
                isPrimaryDiagnosis: 0,
                status: 0,
                isPrimary: 0,
                diagnosedStatus: 0,
                diagnosisStatusOptions: [
                    {
                        label: '确诊',
                        value: 0,
                    },
                    {
                        label: '疑似',
                        value: 10,
                    },
                ],
                sickTime: parseTime(new Date(), 'y-m-d', true),
                diagnosedTime: parseTime(new Date(), 'y-m-d', true),
                pickerOptions: {
                    disabledDate(date) {
                        return date > new Date('2040-01-01');
                    },
                    shortcuts: [{
                        text: '今天',
                        onClick(cb) {
                            const start = new Date();
                            cb(start);
                        },
                    }, {
                        text: '昨天',
                        onClick(cb) {
                            const start = new Date();
                            start.setTime(start.getTime() - 24 * 60 * 60 * 1000);
                            cb(start);
                        },
                    }],
                    yearRange: {
                        begin: 1930,
                        end: 2040,
                    },
                },
                diagnosisList: [],
                cacheDiagnosisList: [],
                checkAll: 0,
                indeterminate: false,
                activeErrorRuleIndex: -1,
                copyLoading: false,
                maxDiagnosisNum: 100,
            };
        },

        computed: {
            ...mapGetters([ 'userInfo' ]),
            dialogVisible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            diagnosisTypeOptions() {
                return DiagnosisTypeOptionsEnum.filter((item) => item.value !== 30);
            },
            isDisabled() {
                return isEqual(this.diagnosisList, this.cacheDiagnosisList);
            },
            selectedDiagnosisCount() {
                return this.diagnosisList.filter((item) => item.checked)?.length;
            },
            mustAtLeastOneWesternDisease() {
                return this.$abcSocialSecurity.isOpenSocial && this.$abcSocialSecurity.config.isNeedHisDiagnosisWithICD10;
            },
            hasWesternDiagnosis() {
                return this.diagnosisList.some((x) => x.category === 0);
            },
        },
        created() {
            this.getDiagnosisHistoryOptions();
            this.diagnosisList = this.existedDiagnosisList.map((item) => {
                return {
                    ...item,
                    disabled: true,
                    checked: this.$set(item,'checked', false),
                };
            });
            this.cacheDiagnosisList = clone(this.diagnosisList);
            this.addDiagnosisItem();
        },
        methods: {
            addDiagnosisItem() {
                if (this.diagnosisList.filter((item) => !item.diseaseName)?.length < 1) {
                    this.diagnosisList.push({
                        diseaseName: '',
                        diseaseCode: '',
                        checked: 0,
                        type: this.defaultDiagnosisType || this.diagnosisList?.length && this.diagnosisList[this.diagnosisList.length - 1].type || DiagnosisTypeEnum.IN_HOSPITAL,
                        category: this.diagnosisList?.length ? this.diagnosisList[this.diagnosisList.length - 1].category : 0,
                        isPrimary: 0,
                        diagnosedStatus: 0,
                        sickTime: parseTime(new Date(), 'y-m-d', true),
                        diagnosedTime: parseTime(new Date(), 'y-m-d', true),
                        doctorName: this.userInfo.name,
                        doctorId: this.userInfo.id,
                        departmentId: this.$route.params.departmentId,
                        patientId: this.patient.id,
                        key: createGUID(),
                        patientOrderId: this.patientOrderId,
                    });
                }
            },
            handleSave() {
                this.diagnosisList.forEach((item) => {
                    item.sickTime = item.sickTime?.length > 10 ? parseTime(item.sickTime, 'y-m-d', true) : item.sickTime;
                    item.diagnosedTime = item.diagnosedTime?.length > 10 ? parseTime(item.diagnosedTime, 'y-m-d', true) : item.diagnosedTime;
                });
                this.$refs.medicalDiagnosisFormRef.validate(async (valid) => {
                    if (valid) {
                        try {
                            // 排序方式
                            // 1. 初步诊断 > 入院诊断 > 出院诊断
                            // 2. 同一种诊断主诊断在前面
                            const groups = this.diagnosisList.reduce((acc, item) => {
                                if (!acc[item.type]) {
                                    acc[item.type] = [];
                                }
                                acc[item.type].push(item);
                                return acc;
                            }, {});
                            const sortGroups = Object.keys(groups).map((key) => {
                                return groups[key].sort((a, b) => {
                                    if (a.isPrimary === b.isPrimary) {
                                        return a.type - b.type;
                                    }
                                    return b.isPrimary - a.isPrimary;
                                });
                            });
                            this.diagnosisList = sortGroups.flat().map((item, index) => {
                                return {
                                    ...item,
                                    sort: index,
                                };
                            });
                            const res = await MedicalPrescriptionAPI.createdDiagnosis({
                                items: this.diagnosisList.filter((item) => item.diseaseName),
                                patientOrderId: this.patientOrderId,
                            });
                            if (res) {
                                this.$Toast({
                                    type: 'success',
                                    message: '创建成功',
                                });
                                this.$emit('refresh');
                                this.dialogVisible = false;
                            }
                        } catch (err) {
                            console.log(err);
                        }
                    }
                });
            },
            async getDiagnosisHistoryOptions() {
                try {
                    const list = await MedicalPrescriptionAPI.getDiagnosisHistoryList(this.patient.id, this.patientOrderId);
                    if (list?.length) {
                        this.diagnosisHistory = list.map((item) => {
                            return {
                                ...item,
                                outpatientOrderId: item.outpatientOrderId,
                                diseaseName: item.diseaseName,
                                diagnosedTime: parseTime(item.diagnosedTime, 'y-m-d', true),
                            };
                        });
                    }

                } catch (err) {
                    console.log(err);
                }
            },
            handleDiagnosisChange(list) {
                this.diagnosisList = list;
            },
            handleDelete() {
                this.$modal({
                    type: 'warn',
                    preset: 'alert',
                    title: '删除诊断',
                    onConfirm: this.handleDeleteConfirm,
                    content: `诊断删除后不可恢复，是否确认删除选中的${this.selectedDiagnosisCount}条诊断?`,
                });
            },
            handleDeleteConfirm() {
                this.diagnosisList = this.diagnosisList?.filter((item) => !item.checked);
                this.$Toast({
                    message: '删除成功',
                    type: 'success',
                });
            },
            async handleCopyDiagnosis() {
                if (this.diagnosisList.length >= this.maxDiagnosisNum) {
                    this.$Toast({
                        message: '诊断数量不能超过100条',
                        type: 'error',
                    });
                    this.copyLoading = false;
                    return;
                }
                const selectedDiagnosisList = this.diagnosisList.filter((item) => item.checked);
                this.copyLoading = true;
                try {
                    if (selectedDiagnosisList?.length) {
                        await Promise.all(selectedDiagnosisList.map(async (item) => {
                            if (this.diagnosisList?.length < this.maxDiagnosisNum) {
                                await this.diagnosisList.splice(this.diagnosisList.length - 1, 0, {
                                    ...item,
                                    disabled: false,
                                    key: createGUID(),
                                    id: '',
                                });
                            } else {
                                this.$Toast({
                                    message: '诊断数量不能超过100条',
                                    type: 'error',
                                });
                            }
                        }));
                    }
                } catch (error) {
                    console.error(error);
                } finally {
                    this.copyLoading = false;
                }
            },
            handleCopyHistory(diagnosis) {
                const index = this.diagnosisList.findIndex((item) => !item.diseaseName);
                if (index !== -1) {
                    this.diagnosisList.splice(this.diagnosisList.length - 1, 0, {
                        ...diagnosis,
                        id: '',
                        key: createGUID(),
                        checked: 0,
                        patientOrderId: this.patientOrderId,
                        type: diagnosis.type === 30 ? 0 : diagnosis.type,
                    });
                }
            },
        },
    };
</script>

<style lang="scss" scoped>
    @import "src/styles/abc-common.scss";
    @import "src/styles/theme.scss";
    @import "src/styles/mixin.scss";

    .add-medical-diagnosis__dialog {
        display: flex;
        justify-content: space-between;

        .left {
            width: calc(100% - 310px);
            padding: 24px;
            padding-top: 0;

            .add-medical-diagnosis__dialog-toolbar {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 16px 0;
                padding-right: 0;
            }

            .medical-diagnosis__table-wrapper {
                .medical-diagnosis__list-table {
                    background: #ffffff;
                    border: 1px solid $P6;
                    border-radius: var(--abc-border-radius-small);

                    .table-header {
                        display: flex;
                        align-items: center;
                        height: 32px;
                        color: #8d9aa8;
                        background: #f5f7fb;
                        border-bottom: 1px solid $P6;
                        border-radius: var(--abc-border-radius-small) var(--abc-border-radius-small) 0 0;
                    }

                    .table-body {
                        position: relative;
                        min-height: 500px;

                        .edit-medical-diagnosis-item {
                            display: flex;
                            align-items: center;
                            height: 40px;
                            padding: 0;
                            line-height: 40px;
                            border-bottom: 1px solid $P6;

                            .td {
                                border-right: 1px dashed $P6;

                                &.is-disabled {
                                    height: 100%;
                                    background-color: $abcBgDisabled;
                                }
                            }

                            ::v-deep .abc-input__inner {
                                width: 100%;
                                height: 40px;
                                border: 0;
                                border-color: transparent;

                                &:focus {
                                    border: 1px solid $theme1;
                                }
                            }

                            .abc-date-picker {
                                display: flex;
                                width: 180px;
                                height: 40px;
                            }

                            .abc-input__inner {
                                padding: 0 8px;
                            }
                        }

                        .add-medical-diagnosis {
                            width: 100%;
                        }
                    }

                    .th {
                        padding: 0 12px;

                        @include ellipsis;
                    }
                    // .,td {
                    //     padding: 0 12px;

                    //     @include ellipsis;
                    // }

                    .small-font {
                        font-size: 12px;
                    }

                    .gray {
                        color: $T2;
                    }

                    .prescription-content {
                        position: relative;
                        padding: 0 8px;

                        .specification {
                            margin-left: 8px;
                            font-size: 12px;
                            color: $T2;
                        }

                        .group-line {
                            position: absolute;
                            top: 10px;
                            right: 10px;
                            z-index: 9;
                            display: block;
                            width: 7px;
                            content: '';
                            border-top: 2px solid #8f8f8f;
                            border-right: 2px solid #8f8f8f;
                            border-bottom: 2px solid #8f8f8f;
                        }
                    }
                }
            }
        }

        .right {
            width: 310px;
            height: 584px;
            background: #f9fafc;
            border-left: 1px solid $P6;
        }
    }
  </style>

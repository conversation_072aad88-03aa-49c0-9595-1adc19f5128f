<template>
    <div class="syndrome-type-autocomplete__wrapper">
        <abc-autocomplete
            ref="abcAutocomplete"
            v-model.trim="keyword"
            :placeholder="placeholder"
            :inner-width="innerWidth"
            :width="width"
            :delay-time="0"
            :async-fetch="true"
            :disabled="disabled"
            :fetch-suggestions="fetchData"
            :max-length="maxLength"
            :focus-show="focusShow"
            :auto-focus-first="false"
            :size="size"
            @enterEvent="handleSelect"
        >
            <div v-if="iconName" slot="prepend" class="search-icon">
                <i :class="['iconfont', iconName]"></i>
            </div>
            <template slot="suggestions" slot-scope="props">
                <div
                    class="suggestions-item"
                    :class="{ selected: props.index == props.currentIndex }"
                    @click="handleSelect(props.suggestion)"
                >
                    <div style="flex: 1;">
                        <span>{{ props.suggestion.name }}</span>
                    </div>
                </div>
            </template>
        </abc-autocomplete>
    </div>
</template>

<script>
    import SocialAPI from 'api/social.js';
    export default {
        props: {
            disabled: {
                // 是否禁用
                type: [Boolean, Number],
                default: false,
            },
            placeholder: {
                type: [String],
                default: '证型',
            },
            size: {
                // autocomplete 高度大小
                type: [String],
                default: '44px',
            },
            placement: {
                // popover层浮动位置
                type: [String],
                default: 'top-start',
            },
            defaultKeyword: {
                type: String,
                default: '',
            },
            type: {
                type: [String, Number],
                default: 3,
            },
            tcmSyndromeTypeText: {
                type: String,
                default: '',
            },
            innerWidth: {
                // 弹出框的宽度
                type: [Number, String],
                default: 200,
            },
            focusShow: {
                type: Boolean,
                default: false,
            },
            showPrice: {
                type: Boolean,
                default: true,
            },
            delayTime: {
                // 搜索debounce延时
                type: [Number],
                default: 10,
            },
            width: {
                type: [String, Number],
                default: 200,
            },
            iconName: {
                type: String,
                default: 'cis-icon-plus',
            },
            maxLength: {
                type: Number,
                default: 12,
            },

            clearIconName: {
                type: String,
                default: '',
            },
            splitByStock: {
                // 是否需要根据库存分割数据
                type: Boolean,
                default: false,
            },
            splitString: {
                type: String,
                default: '以下药品不在库存中',
            },
            // 选中项目后是否自动清除关键字
            autoClear: {
                type: Boolean,
                default: false,
            },

            // 默认选中第一个suggestion
            autoFocusFirst: {
                type: Boolean,
                default: true,
            },
            // 聚焦搜索
            focusSearch: {
                type: Boolean,
                default: false,
            },
        },
        data () {
            return {
                keyword: this.defaultKeyword,
                options: [],
            };
        },
        watch: {
            keyword: {
                handler (val) {
                    val = `${val},`;
                    console.log('defa', this.defaultKeyword);
                    if (!val.length) {
                        this.$emit('clear');
                    }
                    // this.keyword = val;
                },
            },
        },
        methods: {
            async fetchData(keyword, callback) {
                if (!keyword.length && this.focusSearch) {
                    callback([]);
                    return false;
                }
                if (!keyword.length && !this.focusShow) {
                    callback([]);
                    return false;
                }
                if (!keyword.length) {
                    callback([]);
                    return false;
                }

                try {
                    const { data } = await SocialAPI.hospitalSearchSocialDiagnosis({
                        keyword,
                        type: this.type,
                    });
                    if (data) {
                        callback(data.diagnosisInfos);
                    }

                } catch (err) {
                    console.log(err);
                }
            },
            handleSelect(diagnosis) {
                if (this.autoClear) {
                    this.keyword = '';
                } else {
                    this.keyword = diagnosis.name;
                }
                this.$emit('select-diagnosis', diagnosis);
            },
            clearKeyword() {
                this.keyword = '';
            },

        },
    };
</script>

<style lang="scss">
@import "~styles/theme.scss";

.syndrome-type-autocomplete__wrapper {
    position: relative;
    width: 100%;

    .prepend-input i,
    .abc-popover__reference i {
        color: $P1;
        outline: none;
    }

    .abc-input__inner {
        height: 40px;
        padding-left: 30px;
        border-radius: 0;
    }
}
</style>

import BasePageStore from '@/core/page/store.js';
import {
    GoodsTypeEnum, GoodsSubTypeEnum,
} from '@abc/constants';
import GoodsV3API from 'api/goods/index-v3';
import SettingAPI from 'api/settings';

/**
 * @desc 费用页面局部store，伴随page生命周期
 * <AUTHOR>
 * @date 2022-11-15 16:12:41
 */
export default class DoctorAdvicePageStore extends BasePageStore {
    constructor() {
        const namespace = '@HospitalMedicalPrescription';
        const state = {
            quickList: [],
            selectedQuickItem: {},
            currentPatient: {},
            commonAdviceList: [{
                key: 'examination', // 检验
                table: [],
            },
            {
                key: 'inspect', // 检查
                table: [],
            },
            {
                key: 'medicine', // 药品
                table: [],
            },
            {
                key: 'nurse', // 护理 + 手术
                table: [],
            },
            {
                key: 'other', // 其他 + 术后
                table: [],
            }],
            initCommonAdviceList: false,
            curPatientHospitalInfo: null,
            departments: [], // 科室
        };
        super(namespace, state);
    }

    get commonAdviceList() {
        return this.state.commonAdviceList;
    }

    async fetchCommonAdviceList(clinicId) {
        /**
         * 切换患者病区不同，对应的推荐项目的药房不同，需要重新获取
         */
        // if (this.initCommonAdviceList) {
        //     return false;
        // }
        try {
            await Promise.all([
                this.fetchCommonAdviceByType(clinicId,[{
                    type: GoodsTypeEnum.EXAMINATION, subType: [GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Inspect],
                }], 'examination'),
                this.fetchCommonAdviceByType(clinicId,[{
                    type: GoodsTypeEnum.EXAMINATION, subType: [GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Test],
                }], 'inspect'),
                this.fetchCommonAdviceByType(clinicId,[{
                    type: GoodsTypeEnum.MEDICINE, subType: [GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine],
                }], 'medicine'),
                this.fetchCommonAdviceByType(clinicId, [
                    { type: GoodsTypeEnum.NURSE },
                    { type: GoodsTypeEnum.TREATMENT },
                    {
                        type: GoodsTypeEnum.SURGERY,
                        subType: [GoodsSubTypeEnum[GoodsTypeEnum.SURGERY].SURGERY],
                    },
                ],'nurse'),
                this.fetchCommonAdviceByType(clinicId, [
                    { type: GoodsTypeEnum.LEAVE_HOSPITAL },
                    { type: GoodsTypeEnum.CONSULTATION },
                    {
                        type: GoodsTypeEnum.SURGERY,
                        subType: [GoodsSubTypeEnum[GoodsTypeEnum.SURGERY].AFTER_SURGERY],
                    },
                ],'other'),
            ]);
            this.initCommonAdviceList = true;
        } catch (e) {
            console.log(e);
        }

    }

    async fetchCommonAdviceByType(clinicId, jsonType ,name) {
        let resDate = [];
        try {
            const params = {
                clinicId,
                wardAreaId: this.currentPatientWardId,
                key: '',
                jsonType,
                cMSpec: '',
                sex: '',
                age: {},
                offset: 0,
                limit: 20,
                withDomainMedicine: 1,
            };
            const res = await GoodsV3API.searchGoods(params);
            resDate = res?.data?.list || [];
        } catch (e) {
            console.log(e);
        } finally {
            const commonAdviceItem = this.commonAdviceList.find((item) => {
                return item.key === name;
            });
            commonAdviceItem.table = resDate;
        }
    }


    get quickList() {
        return this.state.quickList || [];
    }

    get selectedQuickItem() {
        return this.state.selectedQuickItem || {};
    }

    get currentPatient() {
        return this.selectedQuickItem.patient || {};
    }

    get currentPatientWardId() {
        return this.selectedQuickItem.wardId || '';
    }

    init() {
        this.fetchDepartments();
    }

    setSelectedQuickItem(newSelectedItem) {
        this.state.selectedQuickItem = newSelectedItem;
    }

    setQuickList(data) {
        this.state.quickList = data;
    }

    setQuickListCurrentItem(data) {
        const index = this.state.quickList?.findIndex((item) => {
            return item?.id === this.state.selectedQuickItem?.id;
        });
        if (index !== -1 && this.state.quickList[index]?.patient) {
            this.state.quickList[index].patient.name = data.name ?? this.state.quickList[index].patient.name;
            this.state.quickList[index].patient.age = data.age ?? this.state.quickList[index].patient.age;
            this.state.quickList[index].patient.sex = data.sex ?? this.state.quickList[index].patient.sex;
        }
    }

    get curPatientHospitalInfo() {
        return this.state.curPatientHospitalInfo;
    }

    setCurPatientHospitalInfo(data) {
        this.state.curPatientHospitalInfo = data;
    }

    get curDepartments() {
        return this.state.departments;
    }

    setCurDepartments(data) {
        this.state.departments = data;
    }

    async fetchDepartments() {
        try {
            const res = await SettingAPI.clinic.fetchClinicDepartments();
            const { data } = res.data;
            this.state.departments = data?.rows || [];
        } catch (e) {
            console.error('科室信息获取错误\n', e);
        }
    }
}

<template>
    <div>
        <abc-container-center-top-head>
            <abc-tabs
                v-model="contentTab"
                :option="tabOptions"
                :border-style="{ borderBottom: 'none' }"
                size="huge"
            ></abc-tabs>
        </abc-container-center-top-head>
        <div class="main-content">
            <div v-abc-auto-blank class="blank-index">
                <abc-content-empty value="暂无患者"></abc-content-empty>
            </div>
        </div>
    </div>
</template>
<script type="text/ecmascript-6">
    export default {
        data() {
            return {
                contentTab: 0,
                tabOptions: [
                    {
                        label: '医嘱',
                        value: 0,
                    },
                    {
                        label: '病历',
                        value: 1,
                    },
                    {
                        label: '诊断',
                        value: 2,
                    },
                    {
                        label: '检查',
                        value: 3,
                    },
                    {
                        label: '检验',
                        value: 4,
                    },
                    {
                        label: '体征',
                        value: 5,
                    },
                    {
                        label: '费用',
                        value: 6,
                    },
                ],
            };
        },
        computed: {},
        methods: {},
    };
</script>

<template>
    <hospital-inventory-page-container v-abc-loading:page="loading">
        <inventory-goods-out
            :pharmacy-no="currentPharmacy.no"
            :pharmacy-type="currentPharmacy.type"
        ></inventory-goods-out>
    </hospital-inventory-page-container>
</template>

<script type="text/ecmascript-6">
    import { mapGetters } from 'vuex';

    import InventoryGoodsOut from '@/views/inventory/goods-out.vue';
    import HospitalInventoryPageContainer from '@/views-hospital/components/inventory-page-container/index.vue';

    export default {
        name: 'HospitalInventoryOtherStockMain',
        components: {
            HospitalInventoryPageContainer,
            InventoryGoodsOut,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        data() {
            return {
                loading: false,
            };
        },
        computed: {
            ...mapGetters(['currentPharmacy']),
        },
    };
</script>



<script>
    export default {
        name: 'DiopterPopover',
        data() {
            function createArr(start, end) {
                const _arr = [];
                while (start < end) {
                    start += 0.25;
                    _arr.push(start.toFixed(2));
                }
                return _arr;
            }
            return {
                tabValue: 0,
                tabOptions: [
                    {
                        label: '近视',
                        value: 0,
                    },
                    {
                        label: '远视',
                        value: 1,
                    },
                ],

                jinContents: [
                    {
                        label: '轻度',
                        list: createArr(0, 3),
                    },{
                        label: '中度',
                        list: createArr(3, 6),
                    },{
                        label: '重度',
                        list: createArr(6, 10),
                    },
                ],
                yuanContents: [
                    {
                        label: '轻度',
                        list: createArr(0, 3),
                    },{
                        label: '中度',
                        list: createArr(3, 5),
                    },{
                        label: '重度',
                        list: createArr(5, 10),
                    },
                ],
            };
        },
        methods: {
            handleChangeTab(index, item) {
                this.tabValue = item.value;
            },
            handleClick(it) {
                if (this.tabValue === 1) {
                    this.$emit('select', `远视+${it}D`);
                } else {
                    this.$emit('select', `近视-${it}D`);
                }
            },
        },
    };
</script>

<template>
    <div class="diopter-popover-wrapper" @mousedown.stop="" @click.stop="">
        <abc-tabs-v2
            v-model="tabValue"
            style="padding-left: 6px;"
            size="small"
            :option="tabOptions"
            @change="handleChangeTab"
        ></abc-tabs-v2>

        <div class="diopter-content-wrapper">
            <div v-if="tabValue === 1" class="diopter-content">
                <div v-for="item in yuanContents" :key="item.label" class="diopter-item">
                    <label>{{ item.label }}</label>
                    <div class="content">
                        <div v-for="it in item.list" :key="it" @click="handleClick(it)">
                            +{{ it }}
                        </div>
                    </div>
                </div>
            </div>
            <div v-else class="diopter-content">
                <div v-for="item in jinContents" :key="item.label" class="diopter-item">
                    <label>{{ item.label }}</label>
                    <div class="content">
                        <div v-for="it in item.list" :key="it" @click="handleClick(it)">
                            -{{ it }}
                        </div>
                    </div>
                </div>
            </div>
            <div class="diopter-unit">
                D
            </div>
        </div>
    </div>
</template>

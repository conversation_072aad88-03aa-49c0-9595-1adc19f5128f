<template>
    <div v-abc-click-outside="outside" class="histories-wrapper">
        <abc-edit-div
            ref="ref-target"
            v-model="currentValue"
            :class="{ 'is-focus': showSuggestions }"
            :disabled="disabled"
            :maxlength="1000"
            @click="handleClick"
            @tab="handleTab"
            @keydown.down.prevent="handleDown"
            @keydown.up.prevent="handleUp"
        >
        </abc-edit-div>

        <div
            v-if="showSuggestions"
            ref="popper-target"
            class="medical-record-suggestions-wrapper no-shadow"
            :class="{
                fixed: fixed
            }"
            :style="suggestionsStyle"
            data-cy="abc-mr-popover-家族史"
        >
            <biz-quick-options-panel
                :list="options"
                close-data-cy="abc-mr-家族史-close"
                @select="handleSelect"
                @close="showSuggestions = false"
            ></biz-quick-options-panel>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    // API
    import common from 'components/common/form';

    import popper from '@/views/outpatient/common/medical-record/popper';
    import { keepLastIndex } from 'utils/dom.js';
    import { BizQuickOptionsPanel } from '@/components-composite/biz-quick-options-panel/index';

    export default {

        components: {
            BizQuickOptionsPanel,
        },
        mixins: [common, popper],
        props: {
            value: String,
            disabled: Boolean,
            type: {
                type: String,
                default: 'general',
            },
        },

        data() {
            return {
                showSuggestions: false,
                selectedTab: 0,


                inherit1: ['白内障', '青光眼', '高度近视', '上睑下垂', '斜视', '白化病眼底', '脉络膜缺损', '先天性黄斑缺损'],
                inherit2: ['色盲', '视网膜色素变性', '视锥细胞营养不良', '卵黄样黄斑变性', '视网膜母细胞瘤'],

                inherit3: ['冠心病', '高血压', '糖尿病', '高血脂', '肥胖症', '消化性溃疡', '炎症性肠病', '肝硬化', '慢性肝炎'],
                inherit4: ['支气管哮喘', '脑梗死', '脑出血', '阿尔茨海默病', '白血病', '血友病', '地中海贫血', '精神疾病'],
                inherit5: ['肝癌', '肺癌', '胃癌', '结肠癌', '乳腺癌', '皮肤癌', '食管癌'],

                suggestionsStyle: {
                    top: '40px',
                },
            };
        },
        computed: {
            currentValue: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.updateStyle();
                    this.$emit('input', val);
                },
            },
            options() {
                // 格式化选项的通用函数
                const formatItems = (items, type = 'normal') => {
                    return items.map((item, index) => ({
                        dataCy: `abc-mr-家族史-${item}`,
                        label: item,
                        value: item,
                        isBreak: index === items.length - 1,
                        type,
                    }));
                };

                // 眼科相关遗传病
                const eyeItems = [
                    ...formatItems(this.inherit1),
                    ...formatItems(this.inherit2),
                ];

                // 全身相关遗传病
                const systemItems = [
                    ...formatItems(this.inherit3),
                    ...formatItems(this.inherit4),
                    ...formatItems(this.inherit5),
                ];

                return [
                    {
                        list: formatItems(['否认家族遗传病史']),
                    },
                    {
                        label: '家族有',
                        list: eyeItems,
                    },
                    {
                        list: systemItems,
                    },
                ];
            },
        },
        beforeDestroy() {
            this._timer && clearTimeout(this._timer);
        },

        methods: {
            handleSelect(item) {
                this.selectSymptom(item.value);
            },

            handleClick() {
                if (this.disabled) return false;
                this.updateStyle();
                this.showSuggestions = true;
            },
            handleTab() {
                this.showSuggestions = false;
            },

            // click outside 回调方法
            outside() {
                this.showSuggestions = false;
            },

            updateStyle() {
                if (this.$children[0]) {
                    this.suggestionsStyle = {
                        top: this.fixed ? '0' : `${this.$children[0].$refs.abcinput.offsetHeight + 4}px`,
                    };
                }
            },

            selectSymptom(symptom, info) {
                let _arr = [];

                if (this.currentValue) {
                    _arr = this.currentValue.split(/,|，/).filter((item) => { return item; });
                }

                if (info) {
                    const index = _arr.findIndex((item) => {
                        return item.indexOf(info.label) > -1;
                    });
                    const _str = info.label + symptom + info.unit;
                    if (index > -1) {
                        _arr[index] = _str;
                    } else {
                        _arr.push(_str);
                    }
                } else {
                    const index = _arr.findIndex((item) => {
                        return item.indexOf(symptom) > -1;
                    });
                    if (index === -1) {
                        _arr.push(symptom);
                    }
                }

                this.formItem && this.formItem.$emit('formFieldInput', _arr.join('，'));
                this.$emit('input', _arr.join('，'));

                this.updateStyle();
                this.focusInput();
            },

            focusInput() {
                this._timer = setTimeout(() => {
                    const { $el } = this.$refs['ref-target'];
                    $el.focus();
                    keepLastIndex($el);
                }, 250);
            },

            handleDown() {
                this.showSuggestions = false;
            },
            handleUp() {
                this.showSuggestions = false;
            },

        },
    };
</script>

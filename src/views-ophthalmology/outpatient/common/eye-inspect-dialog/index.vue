<template>
    <abc-dialog
        v-model="showDialog"
        title="眼部检查"
        append-to-body
        custom-class="eye-inspect-dialog"
    >
        <template #title-append>
            <setting-popover
                class="setting"
                :scope-id="scopeId"
            ></setting-popover>
        </template>

        <div class="eye-inspect-dialog-content">
            <div class="eye-inspect-item">
                <label></label>
                <abc-form-item class="right-line">
                    <div class="th">
                        右眼（OD）
                    </div>
                </abc-form-item>
                <abc-form-item>
                    <div class="th">
                        左眼（OS）
                    </div>
                </abc-form-item>
            </div>
            <div
                v-for="item in curEyeExamination.items"
                v-show="item.show"
                :key="item.key"
                class="eye-inspect-item"
            >
                <label>{{ item.name }}</label>
                <abc-form-item class="right-line">
                    <eye-inspect-common
                        v-model="item.rightEyeValue"
                        :eye-inspect-key="item.key"
                        fixed
                        :placement="placement(item.key)"
                    ></eye-inspect-common>
                </abc-form-item>
                <abc-form-item>
                    <eye-inspect-common
                        v-model="item.leftEyeValue"
                        :eye-inspect-key="item.key"
                        fixed
                        :placement="placement(item.key)"
                    ></eye-inspect-common>
                </abc-form-item>
            </div>
        </div>

        <div slot="footer" class="dialog-footer">
            <abc-button @click="complete">
                完成
            </abc-button>
            <abc-button type="blank" @click="showDialog = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import { mapGetters } from 'vuex';
    import EyeInspectCommon from './eye-inspect-common';
    import SettingPopover from './setting-popover';
    import Clone from 'utils/clone.js';

    export default {
        name: 'EyeInspectDialog',
        components: {
            SettingPopover,
            EyeInspectCommon,
        },
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            eyeExamination: {
                type: Object,
                required: true,
            },
            scopeId: String,
        },
        data() {
            return {
                curEyeExamination: {
                    items: [
                        {
                            key: 'eyeball',
                            name: '眼球',
                            rightEyeValue: '',
                            leftEyeValue: '',
                        },
                        {
                            key: 'eyelid',
                            name: '眼睑',
                            rightEyeValue: '',
                            leftEyeValue: '',
                        },
                        {
                            key: 'tearOrgan',
                            name: '泪器',
                            rightEyeValue: '',
                            leftEyeValue: '',
                        },
                        {
                            key: 'conjunctiva',
                            name: '结膜',
                            rightEyeValue: '',
                            leftEyeValue: '',
                        },
                        {
                            key: 'sclera',
                            name: '巩膜',
                            rightEyeValue: '',
                            leftEyeValue: '',
                        },
                        {
                            key: 'cornea',
                            name: '角膜',
                            rightEyeValue: '',
                            leftEyeValue: '',
                        },
                        {
                            key: 'iris',
                            name: '虹膜',
                            rightEyeValue: '',
                            leftEyeValue: '',
                        },
                        {
                            key: 'pupil',
                            name: '瞳孔',
                            rightEyeValue: '',
                            leftEyeValue: '',
                        },
                        {
                            key: 'atria',
                            name: '前房',
                            rightEyeValue: '',
                            leftEyeValue: '',
                        },
                        {
                            key: 'lens',
                            name: '晶状体',
                            rightEyeValue: '',
                            leftEyeValue: '',
                        },
                        {
                            key: 'vitreum',
                            name: '玻璃体',
                            rightEyeValue: '',
                            leftEyeValue: '',
                        },
                        {
                            key: 'fundus',
                            name: '眼底',
                            rightEyeValue: '',
                            leftEyeValue: '',
                        },
                    ],
                },
            };
        },
        computed: {
            ...mapGetters('outpatientConfig', [
                'outpatientEmployeeConfig',
                'departmentDoctorMedicalRecord',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            showDialog: {
                get() {
                    return this.value;
                },
                set(bool) {
                    this.$emit('input', bool);
                },
            },
            switchSetting() {
                const {
                    outpatientConfigScope,
                } = this.viewDistributeConfig.Outpatient;
                if (outpatientConfigScope === 'dep_emp') {
                    return this.departmentDoctorMedicalRecord.medicalRecord.ophthalmology.eyeExaminationProducts || [];
                }
                return this.outpatientEmployeeConfig.medicalRecordV2.ophthalmology.eyeExaminationProducts || [];
            },
        },
        watch: {
            switchSetting: {
                handler(val) {
                    if (val) {
                        const { items } = this.curEyeExamination;
                        items.forEach((item) => {
                            if (val.includes(item.key)) {
                                item.show = true;
                            } else {
                                item.show = false;
                            }
                        });
                    }
                },
                deep: true,
            },
        },
        created() {
            const { items } = this.curEyeExamination;

            // 提交项中有值的项
            let keys = [];
            const { items: postedItems } = Clone(this.eyeExamination) || {};
            if (postedItems) {
                keys = postedItems.filter((x) => x.rightEyeValue || x.leftEyeValue)
                    .map((x) => x.key);
            }

            items.forEach((item) => {
                if (keys.includes(item.key) || this.switchSetting.includes(item.key)) {
                    if (keys.includes(item.key)) {
                        const index = postedItems.findIndex((x) => x.key === item.key);
                        const {
                            leftEyeValue, rightEyeValue,
                        } = postedItems[index];
                        item.leftEyeValue = leftEyeValue;
                        item.rightEyeValue = rightEyeValue;
                    }
                    this.$set(item, 'show', true);
                } else {
                    this.$set(item, 'show', false);
                }
            });
        },
        methods: {
            placement(key) {
                if (key === 'fundus') return 'top';
                return 'bottom';
            },
            complete () {
                this.showDialog = false;
                const { items } = this.curEyeExamination;
                const postItems = items.filter((x) => {
                    return x.show;
                });
                this.$emit('update:eyeExamination', {
                    items: postItems,
                });
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";

.eye-inspect-dialog {
    width: 800px;

    .abc-dialog-body {
        min-height: 228px;
        max-height: 600px;
    }

    .setting {
        position: absolute;
        top: 0;
        right: 40px;

        .eye-instpect-setting-reference {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 38px;
            cursor: pointer;

            &:hover {
                background: $P5;
            }
        }
    }

    &-content {
        border: 1px solid $P6;

        .eye-inspect-item {
            display: flex;

            label {
                width: 100px;
                font-size: 14px;
                font-weight: bold;
                line-height: 36px;
                color: $T1;
                text-align: center;
                border-right: 1px solid $P6;
            }

            & + .eye-inspect-item {
                border-top: 1px solid $P6;
            }

            .abc-form-item {
                flex: 1;
                margin: 0;

                .abc-form-item-content {
                    height: 100%;

                    >div {
                        height: 100%;
                    }
                }
            }

            .abc-input__inner {
                height: 100%;
                min-height: 36px;
                padding: 7px 10px;
                font-size: 14px;
                line-height: 20px;
                word-break: break-all;
                border-color: transparent;
                border-radius: 0;

                &:focus {
                    position: relative;
                    z-index: 2;
                    border-color: $theme1;
                }
            }
        }

        .th {
            height: 36px;
            font-size: 14px;
            font-weight: bold;
            line-height: 36px;
            text-align: center;
        }

        .right-line {
            border-right: 1px solid $P6;
        }
    }
}
</style>

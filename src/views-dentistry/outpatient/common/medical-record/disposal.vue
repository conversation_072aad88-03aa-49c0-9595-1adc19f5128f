<template>
    <div v-abc-click-outside="outside" class="disposals-wrapper">
        <div v-for="(item, index) in currentValue" :key="index" class="medical-record-item-group">
            <tooth-selector
                v-model="item.toothNos"
                :class="{ 'is-hover': item.keyId === hoverKeyId }"
                :fixed="fixed"
                :disabled="disabled"
                :tooth-selector-width="toothSelectorWidth"
                :quick-select-mode="disabledSyncToothBtn ? '' : 'copy'"
                :copy-tooth-nos-info="copyToothNosInfo"
                :is-tooth-sync-mode="isToothSyncMode(item)"
                @click="outside"
                @change="handleToothNoChange(item)"
                @mouseenter="handleMouseenter(item)"
                @mouseleave="handleMouseleave"
            ></tooth-selector>

            <abc-edit-div
                ref="ref-target"
                v-model="item.value"
                :class="{ 'is-focus': showSuggestions && currentRefIndex === index }"
                :style="{
                    marginLeft: `${toothSelectorWidth + 16}px`
                }"
                :maxlength="1000"
                :disabled="disabled"
                @click="handleClick(index)"
                @tab="handleTab"
                @keydown.down.prevent="handleDown"
                @keydown.up.prevent="handleUp"
            >
            </abc-edit-div>

            <div v-if="!disabled && index === currentValue.length - 1" class="add-btn" @click="handleClickGroup">
                <i class="iconfont cis-icon-a-plus13px"></i>
            </div>

            <input
                ref="abcinput"
                type="text"
                style=" position: absolute; top: 0; left: 0; width: 0; opacity: 0;"
                tabindex="-1"
                :value="item.value"
            />

            <div v-if="!disabled && currentValue.length > 1" class="delete-item">
                <delete-icon @delete="deleteGroup(index)"></delete-icon>
            </div>
        </div>

        <dentistry-suggestions-popover
            v-if="showSuggestions"
            v-model="showSuggestions"
            :fixed="fixed"
            :ref-target="refTarget"
            :data-list="dataList"
            :disabled="disabled"
            :suggestions-style="suggestionsStyle"
            @select="handleSelect"
        ></dentistry-suggestions-popover>
    </div>
</template>

<script type="text/ecmascript-6">
    import operation from './dentistry-common.js';

    export default {
        mixins: [operation],
        data() {
            return {
                dataList: [
                    {
                        label: '牙体',
                        children: [
                            {
                                label: '常规',
                                children: [
                                    ['宣教：引导患者知晓正确的刷牙方法，合理选用牙膏，合理使用牙线等。'],
                                    ['向患者告知病情、治疗方案和费用、风险和预后等情况，患者及家属知情并同意治疗。'],
                                ],
                            },
                            {
                                label: '龋病',
                                children: [
                                    ['清洁隔湿，酸蚀，冲洗干燥，涂布封闭剂，固化。'],
                                    ['去净龋坏，备洞，隔湿干燥，消毒，垫底充填，调颌，抛光。'],
                                ],
                            },
                            {
                                label: '非龋',
                                children: [
                                    ['洁牙，磨除染色层，隔湿干燥，酸蚀，涂粘接剂，光照。'],
                                    ['洁牙，隔湿上橡皮障，漂白冲洗，脱敏。'],
                                    ['磨除着色，酸蚀，吹干，涂粘合剂，修复，抛光。'],
                                ],
                            },
                            {
                                label: '牙髓',
                                children: [
                                    ['局麻，去腐开髓，冲洗吸干，牙髓失活剂暂封。'],
                                    ['去暂封，去髓根扩，消毒，隔湿干燥，垫底充填，调颌，抛光。'],
                                ],
                            },
                            {
                                label: '根尖周',
                                children: [
                                    ['局麻，开髓，根管清理，测量根管，根管成形，根管消毒，根管充填。'],
                                    ['局麻，翻瓣，根尖刮治，根尖切除，粘骨膜瓣复位缝合。'],
                                ],
                            },
                        ],
                    },
                    {
                        label: '牙周',
                        children: [
                            {
                                label: '常规',
                                children: [
                                    ['宣教：引导患者正确刷牙，告知牙线牙签及牙间隙刷的正确操作方法。'],
                                    ['向患者告知病情、治疗方案和费用、风险和预后等情况，患者及家属知情并同意治疗。'],
                                ],
                            },
                            {
                                label: '牙周',
                                children: [
                                    ['全口洁治，牙周手术。'],
                                    ['全口超声波龈上洁治，抛光，冲洗，上药。'],
                                    ['局麻，分离牙龈，暴露牙周，根面刮治平整，冲洗，缝合。'],
                                    ['调整咬合，并固定松动牙。'],
                                    ['全身和口腔局部应用抗菌药物。'],
                                    ['牙周修复治疗，制作修复体冠边缘。'],
                                    ['牙周支持治疗，定期复查。'],
                                ],
                            },
                        ],
                    },
                    {
                        label: '外科',
                        children: [
                            {
                                label: '常规',
                                children: [
                                    ['宣教：告知患者拔牙后不舌舔触摸伤口，勿食辛辣食物，以半流质饮食及软食为宜。'],
                                    ['向患者告知拔牙术中风险以及拔牙后并发症，患者及家属知情并同意拔牙。'],
                                ],
                            },
                            {
                                label: '外科',
                                children: [
                                    ['铺巾，消毒手术野，并核对牙位。'],
                                    ['局部麻醉。'],
                                    ['分离牙齿与牙龈，将牙龈分离器紧贴牙面向下分离，达牙槽嵴顶，剥离牙龈。'],
                                    ['挺松牙齿。将牙挺插入到牙根与牙槽之间，撬动和转动等动作，使患牙松动。'],
                                    ['拔除牙齿。通过摇动、扭转及牵引，将牙拔除。'],
                                    ['检查拔牙创口内有无牙碎片、骨碎片、牙结石以及炎性肉芽组织等，并予以清理。'],
                                ],
                            },
                        ],
                    },{
                        label: '正畸',
                        children: [
                            {
                                label: '常规',
                                children: [
                                    ['宣教：引导患者正确刷牙漱口、合理饮食，教会患者摘带、清洗、清洗牙套与保持器。'],
                                    ['向患者说明病情及相关矫治方案，在患者同意的前提下签署《正畸治疗知情同意书》。'],
                                ],
                            },
                            {
                                label: '正畸',
                                children: [
                                    ['常规检查，拍片照相取模，制定方案。'],
                                    ['拔牙，分牙，粘接托槽及颊面管，结扎固定矫治弓丝。'],
                                    ['排整牙齿，整平牙弓，关闭间隙。精细调整上下牙的咬合关系。'],
                                    ['安装矫正器，嘱定期复诊。'],
                                    ['佩戴保持器，并定期复查。'],
                                ],
                            },
                        ],
                    },{
                        label: '修复',
                        children: [
                            {
                                label: '常规',
                                children: [
                                    ['宣教：引导患者正确刷牙漱口，告知牙线牙签及牙间隙刷的正确操作方法。'],
                                    ['告知患者备选材料的优缺点、费用、修复时间及并发症，患者及家属知情并同意治疗。'],
                                ],
                            },
                            {
                                label: '修复',
                                children: [
                                    ['常规检查，拍片，制定修复方案。'],
                                    ['牙体预备，调磨相关牙齿。'],
                                    ['排龈，取模，制作临时冠，暂粘。'],
                                    ['去除临时冠，清洁基牙与修复体，试戴牙冠。'],
                                    ['灌制石膏模型，制作个别托盘。'],
                                    ['备洞，窝洞消毒，窝洞充填，塑形立固，打磨牙齿。'],
                                ],
                            },
                        ],
                    },{
                        label: '种植',
                        children: [
                            {
                                label: '常规',
                                children: [
                                    ['宣教：引导患者正确刷牙漱口，告知牙线牙签及牙间隙刷的正确操作方法。'],
                                    ['医生已履行告知义务，患者完全知情同意并签署《种植牙手术知情同意书》。'],
                                ],
                            },
                            {
                                label: '种植',
                                children: [
                                    ['口腔检查，拍片，制定种植方案。'],
                                    ['植入种植体。'],
                                    ['安装临时愈合基台。'],
                                    ['取下愈合基台，安装永久基台。'],
                                    ['安装牙冠，定期复诊。'],
                                ],
                            },
                        ],
                    },
                ],
            };
        },
        computed: {
            refTarget() {
                const refTarget = this.$refs['ref-target'][this.currentRefIndex];
                return refTarget?.$el;
            },
        },
        methods: {
            handleSelect(symptom) {
                let val = [];
                const { value } = this.currentValue[this.currentRefIndex];
                if (this._lastEditRange && this._lastEditRange.startContainer.nodeName === '#text') {
                    const { endOffset } = this._lastEditRange;
                    const start = value.slice(0, endOffset);
                    const end = value.slice(endOffset);

                    if (!/[。]$/.test(start) && start) {
                        symptom = `，${symptom}`;
                    }
                    if (!/^[。]/.test(end) && end) {
                        symptom = `${symptom}，`;
                    }
                    val = (start + symptom + end).split(/[。]/).filter((item) => { return item; });
                } else {
                    if (value) {
                        val = value.split(/[。]/).filter((item) => { return item; });
                    }

                    if (val.indexOf(symptom.replace(/。$/,'')) === -1) {
                        val.push(symptom);
                    }
                }

                // 去重
                const setArr = new Set(val);
                val = new Array(...setArr);

                this.currentValue[this.currentRefIndex].value = val.join('。');
                this.$emit('input', this.currentValue);
                this.focusInput();
            },
        },
    };
</script>

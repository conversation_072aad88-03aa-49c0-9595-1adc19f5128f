<template>
    <div
        v-abc-click-outside="outside"
        class="doctor-advice-wrapper consult-plan-intro-wrap"
    >
        <abc-edit-div
            ref="ref-target"
            v-model="currentValue"
            style="display: inline-block;"
            size="large"
            auto-height
            :class="{ 'is-focus': showSuggestions }"
            class="scheme-text no-placeholder"
            :disabled="disabled"
            :maxlength="maxlength"
            :placeholder="placeholder"
            @click="handleClick"
            @tab="handleTab"
            @keydown.down.prevent="handleDown"
            @keydown.up.prevent="handleUp"
        >
        </abc-edit-div>

        <input
            ref="abcinput"
            type="text"
            style=" position: absolute; top: 0; left: 0; width: 0; opacity: 0;"
            :value="value"
            tabindex="-1"
        />

        <div
            v-if="showSuggestions"
            ref="popper-target"
            :class="{ fixed: fixed }"
            :style="suggestionsStyle"
            class="medical-record-suggestions-wrapper"
            data-cy="panel-方案描述"
        >
            <div
                v-if="suggestions.length"
                v-abc-loading="loading"
                class="item"
                style="padding-top: 0;"
            >
                <ul>
                    <li
                        v-for="(s, index) in suggestions"
                        :key="`${index }_${ s.sort}`"
                        :data-cy="`option-方案描述-${s.content}`"
                        @click="selectAdvice(s.content)"
                    >
                        <div>{{ s.content }}</div>
                    </li>
                </ul>
            </div>
            <div v-else class="item no-data">
                <ul>
                    <li>无方案描述模板</li>
                </ul>
            </div>
            <div class="close-suggestions" @click="showSuggestions = false">
                <i class="iconfont cis-icon-delete_circle"></i>关 闭

                <div class="setting-btn" @click="showSettingDialog">
                    <i class="iconfont cis-icon-set"></i>
                </div>
            </div>
        </div>

        <doctor-advice-dialog
            v-if="showDialog"
            v-model="showDialog"
            :max-item-length="1000"
            :scene-type="sceneType"
            :suggestions.sync="suggestions"
        ></doctor-advice-dialog>
    </div>
</template>

<script type="text/ecmascript-6">
    // API
    import DoctorTemplateAPI from 'api/doctor-template';

    const DoctorAdviceDialog = () => import('src/views/outpatient/common/doctor-advice-dialog/doctor-advice-dialog.vue');
    import popper from 'src/views/outpatient/common/medical-record/popper.js';

    // utils
    import common from 'components/common/form';
    import { QuickInputTemplateEnum } from 'src/views/outpatient/common/doctor-advice-dialog/constants.js';

    export default {
        name: 'ConsultPlanQuickInput',

        components: {
            DoctorAdviceDialog,
        },

        mixins: [ common, popper ],

        props: {
            value: String,
            disabled: Boolean,
            placeholder: String,
            sceneType: Number,
            maxlength: {
                type: Number,
                default: 500,
            },
        },
        data() {
            return {
                QuickInputTemplateEnum,
                showSuggestions: false,
                suggestions: [],
                loading: false,
                showDialog: false,

                suggestionsStyle: {
                    top: '40px',
                },
            };
        },
        computed: {
            currentValue: {
                get() {
                    return this.value || '';
                },
                set(val) {
                    this.updateStyle();
                    this.$emit('input', val);
                    this.formItem && this.formItem.$emit('formFieldInput', val);
                },
            },
        },
        created() {
        },
        mounted() {
        },
        methods: {
            async handleClick() {
                if (this.disabled) return false;
                this.updateStyle();
                await this.queryAdviceAsync();
            },
            handleTab() {
                this.showSuggestions = false;
            },

            // click outside 回调方法
            outside() {
                this.showSuggestions = false;
            },

            updateStyle() {
                if (this.$children[ 0 ]) {
                    this.suggestionsStyle = {
                        top: this.fixed ? '0' : `${this.$children[0].$refs.abcinput.offsetHeight + 4}px`,
                    };
                }
            },

            handleDown() {
                this.showSuggestions = false;
            },
            handleUp() {
                this.showSuggestions = false;
            },

            async queryAdviceAsync() {
                try {
                    const { data } = await DoctorTemplateAPI.fetchDoctorTemplates(this.sceneType);
                    this.suggestions = data.list || [];
                    this.showSuggestions = true;
                } catch (e) {
                    console.error(e);
                }
            },

            selectAdvice(advice) {
                if (this.currentValue.indexOf(advice) === -1) {
                    let val = this.currentValue;
                    if (val) {
                        val += `，${advice}`;
                    } else {
                        val += advice;
                    }
                    const num = 500;
                    if (val.length > num) {
                        val = val.substr(0, num);
                    }
                    this.currentValue = val;
                    this.$nextTick(() => {
                        this.updateStyle();
                    });
                    this.outside();
                }
            },
            showSettingDialog() {
                this.showSuggestions = false;
                this.showDialog = true;
            },
        },
    };
</script>

<style lang="scss">
    .consult-plan-intro-wrap {
        position: relative;
        width: 100%;

        .medical-record-suggestions-wrapper li {
            line-height: 14px;
        }
    }
</style>

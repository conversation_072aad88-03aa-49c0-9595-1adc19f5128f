<template>
    <div v-if="value">
        <abc-dialog
            v-if="value"
            v-model="value"
            custom-class="share-consult-dialog"
            content-styles="padding: 0;height: 700px;width: 375px;overflow: hidden;"
            confirm-text="分享给患者"
            :close-after-confirm="false"
            title="方案预览"
            append-to-body
            @close-dialog="closeDialog"
        >
            <div class="consult-dialog-wrapper">
                <div v-if="previewUrl" class="consult-dialog-wrapper_box">
                    <iframe
                        ref="previewFrame"
                        frameborder="none"
                        :height="`${previewFrameHeight}px`"
                        :width="`${previewFrameWidth}px`"
                        :style="{
                            transformOrigin: 'top',
                        }"
                        wmode="transparent"
                        class="decorate-preview__iframe"
                        :src="previewUrl"
                    ></iframe>
                </div>
            </div>

            <div slot="footer" class="footer">
                <abc-button type="primary" @click="confirmHandler">
                    分享给患者
                </abc-button>
                <abc-button type="blank" @click="closeDialog">
                    取消
                </abc-button>
            </div>
        </abc-dialog>

        <share-link-dialog
            v-if="showShareLinkDialog"
            v-model="showShareLinkDialog"
            :share-link-url="shareLinkUrl"
        ></share-link-dialog>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';
    import { formatAge } from '@/utils';
    import { formatDate } from '@abc/utils-date';
    import {
        CONSULT_PROJECT_STATUS,
        CONSULT_STATUS, CONSULT_TYPE_ENUM,
    } from '@/views-dentistry/consult/common/constant';
    import {
        IFRAME_SCALE_RATE,
        DEFAULT_IFRAME_HEIGHT,
    } from 'views/settings/micro-clinic/decoration/constant';
    import {
        filterProductTotalPrice,
    } from '@/views-dentistry/consult/common/utils';
    import ConsultApi from 'api/consult';
    import ShareLinkDialog from '@/views-dentistry/consult/components/share-link-dialog.vue';
    import QRCode from 'qrcode';
    import { getPreviewBaseUrl } from 'views/settings/micro-clinic/decoration/config';
    import CrmAPI from 'api/crm';
    export default {
        name: 'ShareConsultDialog',
        components: {
            ShareLinkDialog,
        },
        filters: {
            formatAge,
            formatDate,
        },
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            postData: {
                type: Object,
                default: () => {},
            },
            currentConsultId: String,
            patientId: String,
        },
        data() {
            return {
                CONSULT_TYPE_ENUM,
                showShareLinkDialog: false, // 分享二维码弹窗
                shareLinkUrl: '', // 二维码链接
                previewUrl: '',
                IFRAME_SCALE_RATE,
                previewFrameWidth: 380,
                previewFrameHeight: DEFAULT_IFRAME_HEIGHT,
            };
        },
        computed: {
            ...mapGetters(['clinicBasicConfig']),
            /**
             * 咨询师的名称
             */
            consultantName() {
                return this.postData.consultant?.name || '';
            },
            /**
             * 咨询单的治疗方案
             */
            medicalPlanSheets() {
                if (
                    !Array.isArray(this.postData.medicalPlanSheets) ||
                    !this.postData.medicalPlanSheets.length
                ) {
                    return [];
                }
                const list = this.postData.medicalPlanSheets;
                list.sort((a, b) => a.sort - b.sort);
                return list;
            },
            patientInfo() {
                return this.postData?.patient || {};
            },
        },
        watch: {
            async value(val) {
                if (!val) {
                    this.shareLinkUrl = '';
                    this.showShareLinkDialog = false;
                } else if (this.postData.id) {
                    const { data } = await CrmAPI.fetchQRCodeType(this.currentConsultId, 'medical_plan_share_patient_bind', this.patientId);
                    QRCode.toDataURL(data?.url, { margin: 0 }, (err, url) => {
                        if (err) {
                            console.warn('生成二维码失败\n', err);
                        } else {
                            this.shareLinkUrl = url;
                        }
                    });
                }
            },
        },
        async created() {
            this.previewUrl = `${getPreviewBaseUrl()}/consultant?consultantId=${this.currentConsultId}`;
        },
        methods: {
            filterProductTotalPrice,
            submit() {
                this.closeDialog();
            },
            closeDialog() {
                this.$emit('input', false);
            },
            treatmentTransToScheme(medicalPlanSheet) {
                const res = [];
                medicalPlanSheet.forms.forEach((form) => {
                    form.productFormItems.forEach((item) => {
                        res.push({
                            toothNos: item.toothNos,
                            value: [{ name: item.name }],
                        });
                    });
                });
                return res;
            },
            async fetchShareQrCode() {
                const { data } = await ConsultApi.shareQrCode({
                    medicalPlanId: this.postData.id,
                });
                return data.url || '';
            },
            async confirmHandler() {
                if (this.postData.draftId) {
                    this.$Toast.error('草稿单无法分享');
                    return;
                }
                if (this.shareLinkUrl) {
                    this.showShareLinkDialog = true;
                } else {
                    const { data } = await CrmAPI.fetchQRCodeType(this.currentConsultId, 'medical_plan_share_patient_bind', this.patientId);
                    QRCode.toDataURL(data?.url || '', { margin: 0 }, (err, url) => {
                        if (err) {
                            this.$Toast.error('生成二维码失败，请重试');
                            console.warn('生成二维码失败\n', err);
                        } else {
                            this.shareLinkUrl = url;
                            this.showShareLinkDialog = true;
                        }
                    });
                }
            },
            /**
             * 治疗方案的状态为关闭
             */
            isProjectClose(medicalPlanSheet) {
                return (
                    this.postData.status > CONSULT_STATUS.FOLLOWING_UP &&
                    medicalPlanSheet.status === CONSULT_PROJECT_STATUS.CLOSED
                );
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";

.share-consult-dialog {
    .abc-dialog-footer {
        padding-right: 0;
        padding-left: 0;
    }

    .abc-input__inner {
        border-color: transparent;
    }

    .abc-edit-div.abc-input__inner {
        min-height: 0;
        padding: 0;
    }

    .tooth-position {
        cursor: none;
    }

    .consult-dialog-wrapper {
        display: flex;
        align-items: center;
        justify-content: space-around;

        .dialog-title {
            width: 560px;
            margin-bottom: 24px;
            font-size: 16px;
            font-weight: 500;
            line-height: 24px;
            color: $T1;
            text-align: center;
        }

        .wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 375px;
            padding: 24px 0;
            background-color: #3155d2;
            border-radius: var(--abc-border-radius-small);

            .logo-image {
                width: 50px;
                height: 50px;
                margin-bottom: 12px;
                border-radius: 50%;
            }

            .clinic-title {
                max-width: 340px;
                font-family: SourceHanSansCN-Heavy;
                font-size: 28px;
                font-weight: 900;
                line-height: 32px;
                color: #ffffff;
                text-align: center;
            }

            .content-wrapper {
                display: flex;
                flex-direction: column;
                width: 360px;
                padding: 16px;
                background-color: #ffffff;
                border-radius: 20px;
            }

            .patient-info {
                margin-top: 24px;

                .info {
                    display: flex;
                    flex-direction: row;
                    align-items: center;

                    .avatar {
                        width: 34px;
                        height: 34px;
                        margin-right: 4px;
                        border-radius: var(--abc-border-radius-small);
                    }

                    .no-avatar {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 34px;
                        height: 34px;
                        margin-right: 4px;
                        border: 1px solid #eff3f6;
                        border-radius: var(--abc-border-radius-small);
                    }

                    .patient-detail {
                        margin: 0 8px;
                        font-size: 16px;
                        font-weight: 500;
                        line-height: 20px;
                        color: $T1;
                    }

                    .patient-divide {
                        width: 0;
                        height: 18px;
                        border: 1px solid $T1;
                    }
                }

                .divide-line {
                    width: 100%;
                    height: 0;
                    margin: 12px 0;
                    border: 0.5px solid #f0f0f0;
                }

                .doctor-info {
                    display: flex;
                    flex-direction: column;

                    .title {
                        font-size: 14px;
                        color: #777777;

                        &:not(:first-of-type) {
                            margin-top: 4px;
                        }
                    }
                }
            }

            .consult-project-wrapper {
                margin-top: 14px;

                .consult-type {
                    display: flex;
                    flex-direction: column;
                    align-items: center;

                    .type-title {
                        font-size: 24px;
                        font-weight: 600;
                        color: #3459d4;
                    }

                    .type-divide {
                        width: 160px;
                        height: auto;
                        margin-top: 2px;
                    }
                }

                .project-type {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    margin-top: 18px;

                    &:not(:nth-of-type(2)) {
                        margin-top: 40px;
                    }

                    .type-title-wrapper {
                        display: flex;
                        flex-direction: row;
                        padding: 3px 12px;
                        background: rgba(52, 89, 212, 0.08);
                        border-radius: var(--abc-border-radius-small);

                        .type-num {
                            font-size: 16px;
                            font-weight: 600;
                            color: #3459d4;
                        }

                        .type-money {
                            font-size: 16px;
                            font-weight: 500;
                            line-height: 16px;
                            color: #ff3333;
                        }
                    }

                    .type-wrapper {
                        display: flex;
                        flex-direction: column;
                        width: 100%;
                        margin-top: 12px;
                        overflow: hidden;
                        border: 0.5px solid #dedede;
                        border-radius: var(--abc-border-radius-small);
                    }

                    .type-project-wrapper {
                        .project-txt {
                            padding: 16px 12px;
                            background-color: rgba(52, 89, 212, 0.08);

                            .title {
                                font-size: 16px;
                                font-weight: 500;
                                color: $T1;
                            }

                            .content {
                                margin-top: 12px;
                                font-size: 14px;
                                color: #333333;
                                background-color: transparent;
                            }
                        }

                        .project-disposals {
                            padding: 0 8px;

                            .scheme-steps-wrapper {
                                padding: 12px 0;
                            }

                            .scheme-steps-wrapper:not(:first-of-type) {
                                border-top: 1px solid #f0f0f0 !important;
                            }

                            .abc-edit-div:empty::before {
                                color: transparent;
                            }

                            .abc-edit-div.abc-input__inner {
                                padding-right: 8px !important;
                                line-height: 20px;
                            }
                        }
                    }

                    .type-treatment-wrapper {
                        padding: 16px 12px;

                        .title {
                            font-size: 16px;
                            font-weight: 500;
                            color: $T1;
                        }
                    }
                }

                .type-attachment-wrapper {
                    display: flex;
                    flex-direction: column;
                    width: 100%;
                    padding: 16px 12px;
                    margin-top: 12px;
                    overflow: hidden;
                    border: 0.5px solid #dedede;
                    border-radius: var(--abc-border-radius-small);

                    .title {
                        font-size: 16px;
                        font-weight: 500;
                        color: $T1;
                    }

                    .attachment-content {
                        display: flex;
                        flex-direction: row;
                        flex-wrap: wrap;
                        gap: 8px;
                        width: 100%;
                        margin-top: 12px;

                        .attachment-image {
                            width: 84px;
                            height: 112px;
                            border: 1px solid #dedede;
                            border-radius: var(--abc-border-radius-small);
                        }
                    }
                }
            }
        }
    }

    .footer {
        padding-right: 24px;
        padding-left: 16px;
        text-align: right;
    }
}
</style>

<template>
    <abc-layout class="consult-container" has-sidebar>
        <template v-if="isLoading || (patientId && consultList.length)">
            <abc-layout-content :style="contentStyle">
                <div v-abc-loading:page="isLoading" class="main-content">
                    <abc-tips-card-v2
                        v-if="isDisabledByLock"
                        theme="warning"
                        class="pay-exception-tips-card"
                        align="center"
                        border-radius
                    >
                        {{ lockUserName || '有人' }}正在编辑此咨询单，编辑结束前其他人不可同时编辑
                    </abc-tips-card-v2>

                    <!--咨询记录-->
                    <consult-records
                        style="margin-bottom: 16px;"
                        :patient-id="patientId"
                    ></consult-records>

                    <!--治疗方案-->
                    <consult-form-content
                        ref="consult-form-content"
                        :post-data="postData"
                        :disabled="disabled"
                        :is-edit="isEdit"
                        :is-disabled-by-lock="isDisabledByLock"
                        :show-share-dialog.sync="showShareDialog"
                        :visit-source-option.sync="visitSourceOption"
                        :source-from-options="sourceFromOptions"
                        :patient-info="patientInfo"
                        :medical-plan-sheets="medicalPlanSheets"
                        :show-patient-suggest-popper="showPatientSuggestPopper"
                        :patient-suggest-popper-top="patientSuggestPopperTop"
                        :consult-list="consultList"
                        :current-consult-id="currentConsultId"
                        @saveConsultSheet="saveConsultSheet"
                        @cancelEdit="cancelEdit"
                        @submit="submit"
                        @editConsultSheet="editConsultSheet"
                        @loseConsult="loseConsult"
                        @deleteConsultSheet="deleteConsultSheet"
                        @changeConsultant="changeConsultant"
                        @handleVisitSourceEdit="handleVisitSourceEdit"
                        @deleteTreatmentProject="deleteTreatmentProject"
                        @addTreatmentProject="addTreatmentProject"
                        @outsidePatientSuggest="outsidePatientSuggest"
                        @showPatientSuggest="showPatientSuggest"
                        @calcPatientSuggestTop="calcPatientSuggestTop"
                        @selectPatientSuggest="selectPatientSuggest"
                        @changeConsult="selectedShowConsult"
                        @addConsult="addConsultHandle"
                    ></consult-form-content>
                </div>
            </abc-layout-content>

            <!-- 修改就诊推荐设置弹窗 -->
            <visit-source-dialog
                v-if="isShowVisitSourceDialog"
                :is-show.sync="isShowVisitSourceDialog"
                :patient-source-type="patientSourceType"
                @close="isShowVisitSourceDialog = false"
            ></visit-source-dialog>

            <!-- 开单弹窗 -->
            <confirm-dialog
                v-model="showConfirmDialog"
                :medical-plan-sheets="medicalPlanSheets"
                @submit="transactionConfirm"
            ></confirm-dialog>

            <!-- 丢单弹窗 -->
            <lose-dialog
                v-if="showLoseDialog"
                v-model="showLoseDialog"
                @submit="loseConsultConfirm"
            ></lose-dialog>

            <!-- 分享咨询方案弹窗 -->
            <share-consult-dialog
                v-if="showShareDialog"
                v-model="showShareDialog"
                :patient-id="patientId"
                :current-consult-id="currentConsultId"
                :post-data="previewPostData"
            ></share-consult-dialog>
        </template>

        <template v-else>
            <abc-layout-content v-abc-loading:page="isLoading" class="main-content" :style="contentStyle">
                <abc-content-empty value="暂无咨询单"></abc-content-empty>
            </abc-layout-content>
        </template>

        <!-- 跟进记录 -->
        <abc-layout-sidebar :style="sidebarStyle">
            <consult-sidebar
                :has-consult="hasConsult"
            ></consult-sidebar>
        </abc-layout-sidebar>
    </abc-layout>
</template>

<script>
    // lib
    import { mapGetters } from 'vuex';
    // components
    import ConsultFormContent from '@/views-dentistry/consult/components/consult-form-content.vue';
    import ConsultRecords from '@/views-dentistry/consult/components/consult-records.vue';
    import ConsultSidebar from '@/views-dentistry/consult/sidebar/index.vue';
    import ShareConsultDialog from '@/views-dentistry/consult/components/share-consult-dialog.vue';
    import VisitSourceDialog from 'views/registration/visit-source-dialog/index.vue';
    import ConfirmDialog from '@/views-dentistry/consult/components/confirm-dialog.vue';
    import LoseDialog from '@/views-dentistry/consult/components/lose-dialog.vue';
    // utils
    import Clone from 'utils/clone';
    import {
        createNewConsultData,
    } from '@/views-dentistry/consult/common/utils';
    import { formatDate } from '@abc/utils-date';
    import { RecommendService } from '@/service/recommend';
    import inputSelect from 'views/common/input-select';
    import {
        debounce, isEqual,
    } from 'utils/lodash';
    import { keepLastIndex } from 'utils/dom';
    import AbcSocket from 'views/common/single-socket';
    import LayoutManager from 'views/layout/abc-container/layout-manager';
    // api
    import ConsultApi from 'api/consult';
    import CrmAPI from 'api/crm';
    import OutpatientAPI from 'api/outpatient';
    // constants
    import {
        CONSULT_PROJECT_STATUS,
        CONSULT_STATUS,
        CONSULT_STATUS_ENUM,
        CONSULT_TYPE_ENUM,
        patientSuggestion,
        PROJECT_TYPE_LIST,
    } from '@/views-dentistry/consult/common/constant';
    import { BusinessTypeEnum } from 'views/layout/mobile-upload-dialog/config';
    import {
        ROLE_CONSULTANT_ID, ROLE_DOCTOR_ID,
    } from 'utils/constants';
    import { LockBusinessKeyEnum } from '@/common/constants/business-lock';
    import { SourceFormTypeEnum } from '@/service/charge/constants';
    import { isAllowAddByAntimicrobialDrugManagement } from 'views/outpatient/utils';
    import AntimicrobialDrugManagementModal from 'views/outpatient/common/antimicrobial-drug-limit-modal';

    export default {
        name: 'ConsultForm',
        components: {
            ShareConsultDialog,
            ConfirmDialog,
            LoseDialog,
            VisitSourceDialog,
            ConsultSidebar,
            ConsultFormContent,
            ConsultRecords,
        },
        mixins: [inputSelect],
        provide() {
            return {
                main: this,
            };
        },
        data() {
            return {
                PROJECT_TYPE_LIST,
                CONSULT_STATUS,
                BusinessTypeEnum,
                patientSuggestion,
                CONSULT_TYPE_ENUM,
                CONSULT_STATUS_ENUM,
                patientId: '', // 患者id
                patientInfo: null, // 患者信息
                consultList: [], // 咨询单列表,包括草稿单和云端咨询单
                consultListCloud: [], // 云端咨询单列表,和本地咨询单草稿列表区分
                postData: {}, // 咨询单详情
                editPostData: {}, // 暂存修改咨询单时的内容
                currentConsultId: '', // 选中的咨询单id
                currentHideConsultData: null, // 选中的隐藏列表的咨询单详情
                sourceFromOptions: [], // 就诊推荐列表
                patientSourceType: [], // 编辑就诊推荐类型
                visitSourceOption: [], // 就诊推荐
                isShowVisitSourceDialog: false, // 是否展示就诊推荐设置弹窗
                isEdit: false, // 当前是否在编辑咨询单
                isLoading: false, // 加载中
                showPatientSuggestPopper: false, // 控制患者意见popper
                patientSuggestPopperTop: 0, // 患者意见popper的top
                showConfirmDialog: false, // 控制开单二次确认弹窗
                showLoseDialog: false, // 控制丢单弹窗
                showShareDialog: false, // 控制分享弹窗
                hasConsult: false, // 是否有过咨询单
                showConsultListNumber: 4, // 直接显示的咨询单个数
                consultItemWidth: '185.6px',
                isHoverMoreConsult: false,
                isClickMoreConsult: false,
                lockIdentity: '',
                lockUserName: '',
                lockUserId: '',
                lockPatientOrderId: '',
                // 分页参数
                pageParams: {
                    limit: 99,
                    offset: 0,
                },
            };
        },
        computed: {
            ...mapGetters(['draftConsults', 'userInfo', 'isOpenMp', 'isSingleStore', 'isChainSubStore', 'isAdmin']),
            ...mapGetters('outpatientConfig', ['outpatientEmployeeConfig']),
            ...mapGetters('consult', ['consultantList']),
            // 连锁总部不能编辑咨询单
            canOperateConsult() {
                return this.isSingleStore || this.isChainSubStore;
            },
            /**
             * 筛选当前患者的草稿咨询单
             */
            consultListDraft() {
                // 如果当前用户不是医生或者咨询师,则无法看到草稿单
                if (!(this.isClinicAdmin || this.isConsultant || this.isDoctor)) return [];
                return this.draftConsults.filter(
                    (item) => item.patientId === this.patientId,
                );
            },
            isDisabledByLock() {
                return !!(this.postData.patientOrderId === this.lockPatientOrderId &&
                    this.postData.id &&
                    this.lockIdentity &&
                    this.lockUserId !== this.userInfo.id);
            },
            splitConsultList() {
                const directShowList = this.consultList.slice(0, this.showConsultListNumber);
                const hideList = this.consultList.slice(this.showConsultListNumber);
                return {
                    directShowList, hideList,
                };
            },
            /**
             * 获取诊疗项目需要显示的类别,同门诊
             */
            switchSetting() {
                const config = this.outpatientEmployeeConfig;
                const { diagnosisTreatment } = config;
                return { diagnosisTreatment };
            },
            /**
             * 判断当前登陆用户是否有管理员权限
             * 1--管理员 2--普通成员
             */
            isClinicAdmin() {
                return this.userInfo.roleId === 1;
            },
            /**
             * 当前咨询单是否能够编辑
             */
            disabled() {
                if (this.postData.draftId) return false;
                return !this.isEdit;
            },
            /**
             * 当前咨询单的治疗方案
             */
            medicalPlanSheets() {
                if (
                    !Array.isArray(this.postData.medicalPlanSheets) ||
                    !this.postData.medicalPlanSheets.length
                ) {
                    return [];
                }
                // 做一下保护
                this.postData.medicalPlanSheets.forEach((sheet) => {
                    if (!sheet.introduction) {
                        sheet.introduction = {
                            summary: '',
                            disposals: [
                                {
                                    toothNos: [],
                                    value: [
                                        {
                                            name: '',
                                        },
                                    ],
                                },
                            ],
                        };
                    }
                });
                const list = this.postData.medicalPlanSheets;
                // 按照方案序号排序
                list.sort((a, b) => a.sort - b.sort);
                // 关闭的方案需放到最后
                list.sort((a, b) => a.status - b.status);
                return list;
            },
            /**
             * 已收费
             */
            isCharged() {
                return this.postData.status === CONSULT_STATUS.CHARGED;
            },
            /**
             * 判断当前登陆用户是否是咨询师
             */
            isConsultant() {
                return this.userInfo.roleIds.includes(ROLE_CONSULTANT_ID);
            },
            /**
             * 判断当前登陆用户是否是咨询师
             */
            isDoctor() {
                return this.userInfo.roleIds.includes(ROLE_DOCTOR_ID);
            },
            previewPostData() {
                return Clone(this.postData);
            },
            contentStyle() {
                const {
                    rightContainerWidth,
                } = LayoutManager.state;

                return {
                    marginRight: `${rightContainerWidth - 10}px`,
                    height: '100%',
                    overflowY: 'scroll',
                    padding: 0,
                    paddingBottom: '24px',
                };
            },
            sidebarStyle() {
                const {
                    rightContainerWidth,
                    containerTopHeadHeight,
                    containerOffsetLeft,
                } = LayoutManager.state;

                return {
                    position: 'fixed',
                    right: `${containerOffsetLeft}px`,
                    top: `${containerTopHeadHeight}px`,
                    flex: `0 0 ${rightContainerWidth}px`,
                    maxWidth: `${rightContainerWidth}px`,
                    minWidth: `${rightContainerWidth}px`,
                    width: `${rightContainerWidth}px`,
                    height: `calc(100% - ${containerTopHeadHeight}px)`,
                    borderLeft: '1px solid #e0e2eb',
                    zIndex: 10,
                };
            },
        },
        watch: {
            /**
             * 通过路由的patientId监听QL切换患者
             */
            '$route.params.patientId': {
                async handler(id, oldId) {
                    if (!id) {
                        this.currentHideConsultData = null;
                        this.patientId = '';
                        return;
                    }
                    if (id && oldId && id !== oldId && this.postData.id) {
                        await this.sendUnlock();
                    }
                    this.isLoading = true;
                    this.currentHideConsultData = null;
                    this.patientId = id;
                    await this.fetchPatientInfo();
                    await this.fetchDetail();
                    // 切换患者拉一次锁状态
                    await this.getConsultLockStatus();
                    this.isLoading = false;
                },
                immediate: true,
            },
            /**
             * 监听切换咨询单
             */
            // async currentConsultId(id, oldId) {
            //     await this.watchCurrentConsultIdHandler(id, oldId);
            // },
            /**
             * 监听就诊推荐
             */
            patientSourceType: {
                handler(val) {
                    if (val) {
                        this.options = val.map((item) => {
                            if (item.name === '顾客推荐') {
                                return {
                                    ...item,
                                    slot: '新增患者档案',
                                };
                            }
                            return { ...item };
                        });
                    }
                },
                immediate: true,
                deep: true,
            },
            /**
             * 选择就诊推荐后将信息保存到咨询单postData中
             */
            visitSourceOption(val) {
                let sourceId, sourceFrom, sourceFromName;
                if (val.length) {
                    if (val.length > 2) {
                        sourceId = val[val.length - 2].value;
                        sourceFrom = val[val.length - 1] ?
                            val[val.length - 1].value :
                            null;
                        sourceFromName = val[val.length - 1] ?
                            val[val.length - 1].label :
                            null;
                    } else if (
                        ['顾客推荐', '员工推荐', '医生推荐', '转诊医生'].includes(
                            val[0].label,
                        )
                    ) {
                        sourceId = val[0].value;
                        sourceFrom = val[1] ? val[1].value : null;
                        sourceFromName = val[1] ? val[1].label : null;
                    } else {
                        sourceId = val[val.length - 1].value;
                        sourceFrom = null;
                    }
                } else {
                    sourceId = null;
                    sourceFrom = null;
                }
                this.postData.patientSource = {
                    ...this.postData.patientSource,
                    ...{
                        id: sourceId,
                        sourceFrom,
                        sourceFromName: sourceFromName || null,
                    },
                };
            },
        },
        async created() {
            const { socket } = AbcSocket.getSocket();
            this._socket = socket;
            // 监听手机附件上传回调
            this._socket.on('short-url.upload_attachment', this.handleImages);

            this._handleSocketLock = (data) => {
                this.onLockSocketHandler(data, 'lock');
            };
            this._handleSocketUnlock = (data) => {
                this.onLockSocketHandler(data, 'unlock');
            };
            // 加锁/解锁socket回调
            this._socket.on('outpatient.sheet_lock', this._handleSocketLock);
            this._socket.on('outpatient.sheet_unlock', this._handleSocketUnlock);

            this._sendLock = debounce(this.sendLock, 1000, true);
            this._compareUpdate = debounce(this.compareUpdate, 250, true);
            // 拉取就诊推荐列表
            await this.getListSource();
        },
        mounted() {
            this.screenChange();
            window.addEventListener('resize', this.screenChange);
        },
        beforeDestroy() {
            this.currentHideConsultData = null;
            window.removeEventListener('resize', this.screenChange);
            this._socket.off('short-url.upload_attachment', this.handleImages);
            this._socket.off('outpatient.sheet_lock', this._handleSocketLock);
            this._socket.off('outpatient.sheet_unlock', this._handleSocketUnlock);

            // 解锁咨询单
            this.sendUnlock();
        },
        methods: {
            formatDate,
            /**
             * 监听屏幕宽度变化
             */
            screenChange() {
                const { innerWidth } = window;
                if (innerWidth < 1366) {
                    this.showConsultListNumber = 2;
                    this.consultItemWidth = '175px';
                    return;
                }
                if (innerWidth < 1440) {
                    this.showConsultListNumber = 2;
                    this.consultItemWidth = '181.5px';
                    return;
                }
                if (innerWidth < 1660) {
                    this.showConsultListNumber = 2;
                    this.consultItemWidth = '188px';
                    return;
                }
                if (innerWidth < 1920) {
                    this.showConsultListNumber = 3;
                    this.consultItemWidth = '176.2px';
                    return;
                }
                this.showConsultListNumber = 3;
                this.consultItemWidth = '185.6px';
            },
            /**
             * 对咨询单进行排序
             */
            sortConsultListHandler() {
                const sortConsultList = Clone(this.consultList);
                // 定义排序原则
                const statusOrder = [
                    CONSULT_STATUS.FOLLOWING_UP,
                    CONSULT_STATUS.BE_CHARGED,
                    CONSULT_STATUS.CHARGED,
                    CONSULT_STATUS.REFUND,
                    CONSULT_STATUS.LOSING,
                ];
                sortConsultList.sort((a, b) => {
                    // 草稿单排在最前面
                    if (a.draftId && b.draftId) return 0;
                    if (a.draftId) return -1;

                    const statusA = a.status;
                    const statusB = b.status;
                    return statusOrder.indexOf(statusA) - statusOrder.indexOf(statusB);
                });
                this.consultList = sortConsultList;
            },
            /**
             * socket上锁/解锁回调
             */
            onLockSocketHandler ({
                key, employeeId, employeeName, identity, businessKey,
            }, type) {
                if (businessKey !== LockBusinessKeyEnum.CONSULT) return;
                const { id } = this.userInfo || {};
                if (key === this.postData.patientOrderId) {
                    if (type === 'lock') {
                        this.lockIdentity = identity;
                        this.lockUserName = employeeName;
                        this.lockUserId = employeeId;
                        this.lockPatientOrderId = this.postData.patientOrderId;
                    } else {
                        this.lockIdentity = '';
                        this.lockUserName = '';
                        this.lockUserId = '';
                        this.lockPatientOrderId = '';
                        if (employeeId !== id) {
                            if (this.isEdit) {
                                this.postData = Clone(this.editPostData);
                                this.isEdit = false;
                            }
                            this.watchCurrentConsultIdHandler(
                                this.postData.id,
                                this.currentConsultId,
                            );
                        }
                    }
                }
            },
            /**
             * 咨询单解锁
             */
            async sendUnlock() {
                try {
                    if (!this.lockIdentity || !this.postData.patientOrderId) return;
                    const { id } = this.userInfo || {};
                    if (this.lockUserId !== id) return;
                    await OutpatientAPI.unlockOutpatient(this.postData.patientOrderId, {
                        businessKey: LockBusinessKeyEnum.CONSULT,
                        identity: this.lockIdentity,
                    });
                    this.lockIdentity = '';
                    this.lockUserId = '';
                    this.lockUserName = '';
                    this.lockPatientOrderId = '';
                    this.clearLockTimeOut();
                } catch (err) {
                    console.warn('咨询单解锁失败\n', err);
                    this.lockIdentity = '';
                    this.lockUserId = '';
                    this.lockUserName = '';
                    this.lockPatientOrderId = '';
                    this.clearLockTimeOut();
                }
            },
            /**
             * 咨询单上锁
             */
            async sendLock() {
                if (this.lockIdentity || !this.postData.patientOrderId) return;
                try {
                    const resp = await OutpatientAPI.lockOutpatient(this.postData.patientOrderId, LockBusinessKeyEnum.CONSULT);
                    const {
                        result, identity, key,
                    } = resp.data || {};
                    if (result === 1) {
                        this.lockIdentity = identity;
                        this.lockUserId = identity.split(':')[0];
                        this.lockPatientOrderId = this.postData.patientOrderId;
                        this.continueLock(this.sendLockRenew, key);
                    }
                } catch (err) {
                    console.warn('咨询单上锁失败\n', err);
                    this.clearLockTimeOut();
                }
            },
            /**
             * 获取咨询单锁单状态
             */
            async getConsultLockStatus() {
                if (!this.postData.patientOrderId) return;
                try {
                    const resp = await OutpatientAPI.getOutpatientLock(this.postData.patientOrderId, {
                        businessKey: LockBusinessKeyEnum.CONSULT,
                    });
                    const { id } = this.userInfo || {};
                    const {
                        result, identity, employeeId, employeeName,
                    } = resp.data || {};
                    if (result === 1) {
                        // 获取到业务锁
                        if (identity) {
                            this.lockIdentity = identity;
                            this.lockUserId = employeeId;
                            this.lockUserName = employeeName;
                            this.lockPatientOrderId = this.postData.patientOrderId;
                            if (employeeId === id) {
                                await this.sendLockRenew();
                            }
                        } else {
                            this.lockIdentity = '';
                            this.lockUserId = '';
                            this.lockUserName = '';
                            this.lockPatientOrderId = '';
                        }
                    }
                } catch (err) {
                    console.warn('获取咨询单锁单状态失败\n', err);
                }
            },
            /**
             * 锁单续期
             */
            async sendLockRenew(key = this.postData.patientOrderId) {
                if (!this.lockIdentity || !this.postData.patientOrderId) {
                    this.clearLockTimeOut();
                    return;
                }
                if (key !== this.postData.patientOrderId) {
                    this.clearLockTimeOut();
                    return;
                }
                try {
                    const resp = await OutpatientAPI.lockOutpatientRenew(this.postData.patientOrderId, {
                        businessKey: LockBusinessKeyEnum.CONSULT,
                        identity: this.lockIdentity,
                    });
                    const { result } = resp.data || {};
                    if (result === 1) {
                        this.continueLock(this.sendLockRenew, key);
                    } else {
                        this.clearLockTimeOut();
                    }
                } catch (err) {
                    console.warn('咨询单锁单续期失败\n', err);
                    this.clearLockTimeOut();
                }
            },
            /**
             * 锁单续期重新计时
             */
            continueLock(callback, key, timeout = 1000 * 50) {
                this.clearLockTimeOut();
                this._lockTimeoutId = setTimeout(() => {
                    callback(key);
                }, timeout);
            },
            /**
             * 清除锁单定时器
             */
            clearLockTimeOut() {
                if (this._lockTimeoutId) {
                    clearTimeout(this._lockTimeoutId);
                }
            },
            handleImages(data) {
                const {
                    attachments = [], businessType,
                } = data;

                if (
                    businessType === BusinessTypeEnum.CONSULT &&
                    attachments &&
                    attachments.length
                ) {
                    this.postData.attachments =
                        this.postData.attachments.concat(attachments);
                }
            },
            async changeConsultSelector(newId) {
                // 切换咨询单,将之前的咨询单解锁
                await this.sendUnlock();

                await this.watchCurrentConsultIdHandler(
                    newId,
                    this.currentConsultId,
                );

                // 切换咨询单拉一次锁状态
                await this.getConsultLockStatus();
            },
            /**
             * 拉取咨询相关所有信息
             */
            async fetchDetail(id) {
                this.isEdit = false;
                await this.fetchConsultList(id);
            },
            /**
             * 根据patientId请求咨询单列表
             * 有且只会有一个草稿
             */
            async fetchConsultList(id) {
                try {
                    // 请求咨询单列表
                    const { data: res } =
                        await ConsultApi.fetchConsultListByPatientId(
                            this.patientId,
                            this.pageParams,
                        );
                    this.consultListCloud = res?.rows || [];
                    this.hasConsult = !!this.consultListCloud.length;

                    // 既没有云端咨询单也没有草稿咨询单,登陆用户同时是咨询师或医生,创建一个咨询单草稿
                    if (
                        (this.isClinicAdmin || this.isConsultant || this.isDoctor) &&
                        !this.consultListCloud.length &&
                        !this.consultListDraft.length
                    ) {
                        this.createNewConsultData();
                    }
                } catch (e) {
                    this.consultListCloud = [];
                    this.hasConsult = false;
                    console.warn('请求咨询单列表失败\n', e);
                } finally {
                    this.consultList = this.consultListDraft.concat(
                        this.consultListCloud,
                    );
                    // 对咨询单排序
                    this.sortConsultListHandler();
                    if (!this.consultList.length) {
                        await this.watchCurrentConsultIdHandler('', '');
                    } else {
                        let flag;
                        if (id) {
                            flag = this.consultList.find(
                                (consultItem) => consultItem.id === id,
                            );
                        }
                        if (flag) {
                            await this.watchCurrentConsultIdHandler(
                                id,
                                this.currentConsultId,
                            );
                        } else {
                            // 选中第一个咨询单
                            await this.watchCurrentConsultIdHandler(
                                this.consultList[0].draftId || this.consultList[0].id,
                                this.currentConsultId,
                            );
                        }
                    }
                }
            },
            /**
             * 创建咨询单草稿
             */
            createNewConsultData() {
                const newPostData = createNewConsultData();
                // 当前登陆用户是否是咨询师,如果是,直接带入
                if (this.isConsultant) {
                    newPostData.consultant = {
                        countryCode: this.userInfo.countryCode,
                        id: this.userInfo.id,
                        mobile: this.userInfo.mobile,
                        name: this.userInfo.name,
                    };
                }
                if (this.patientInfo?.patientSource) {
                    newPostData.patientSource = {
                        id: this.patientInfo.patientSource.id,
                        sourceFrom: this.patientInfo.patientSource.sourceFrom,
                        sourceFromName: this.patientInfo.patientSource.sourceFromName || null,
                    };
                }
                // 保存草稿单到localStorage,通过patientId来区分草稿单是否属于当前选择的患者
                this.$store.dispatch('SetDraft', {
                    key: 'consult',
                    record: {
                        ...newPostData,
                        patientId: this.patientId,
                        patient: this.patientInfo,
                    },
                });
            },
            /**
             * 新建咨询单按钮回调函数
             */
            async addConsult() {
                // 一个患者最多只能有一个草稿
                if (this.consultListDraft.length) {
                    this.$Toast.error('患者已有一个草稿方案，请先处理');
                    await this.watchCurrentConsultIdHandler(
                        this.consultList[0].draftId || this.consultList[0].id,
                        this.currentConsultId,
                    );
                    return;
                }
                this.isLoading = true;
                this.createNewConsultData();
                this.consultList = this.consultListDraft.concat(
                    this.consultListCloud,
                );
                // 对咨询单排序
                this.sortConsultListHandler();
                await this.watchCurrentConsultIdHandler(
                    this.consultList[0].draftId || this.consultList[0].id,
                    this.currentConsultId,
                );
            },
            /**
             * 选择一个咨询单
             */
            async selectOne(consultData) {
                if (!consultData) return;
                // destroy 前一个草稿监听
                this.unRegisterDraftListener();
                let result;
                if (consultData.draftId) {
                    result = Clone(consultData);
                } else {
                    const { data } = await ConsultApi.fetchConsultDetail(
                        consultData.id,
                    );
                    result = data;
                }
                // 如果选中的咨询单在隐藏的列表里,需要设置 currentHideConsultData
                if (this.splitConsultList.hideList.findIndex((item) => item.id === consultData.id) > -1) {
                    this.currentHideConsultData = Clone(consultData);
                } else {
                    // 如果不在隐藏的列表里,则需要将 currentHideConsultData 置为 null
                    this.currentHideConsultData = null;
                }
                result.referralPatientOrder = result.referralPatientOrder || {};
                this.postData = Clone(result);
                this._postDataDraft = Clone(result);
                // 注册草稿监听
                if (this.postData.draftId) {
                    this._registerDraftListenerTimer = setTimeout(() => {
                        this.registerDraftListener();
                    }, 500);
                }
                this.transPatientSourceHandler(result.patientSource);
            },
            /**
             * 根据需求改变咨询单列表展示文案
             */
            filterConsultSelectorTitle(item) {
                if (!item) return '';
                if (item.draftId) {
                    return `${CONSULT_TYPE_ENUM[item.type]}  ${formatDate(item.created, 'YYYY-MM-DD')}`;
                }
                return `${CONSULT_TYPE_ENUM[item.type]}  ${
                    CONSULT_STATUS_ENUM[item.status]
                }  ${formatDate(item.created, 'YYYY-MM-DD')}`;
            },
            /**
             * 选择咨询师
             */
            changeConsultant(consultantId) {
                const selectedConsultant = this.consultantList.find(
                    (item) => item.employeeId === consultantId,
                );
                if (selectedConsultant) {
                    this.postData.consultant = {
                        countryCode: selectedConsultant.countryCode,
                        id: selectedConsultant.employeeId,
                        mobile: selectedConsultant.mobile,
                        name: selectedConsultant.employeeName,
                    };
                }
            },
            /**
             * 拉取就诊推荐列表
             */
            async getListSource() {
                if (!RecommendService.getInstance().originOptions.length) {
                    await RecommendService.getInstance().structureOriginOptions();
                }
                this.patientSourceType = this.sourceFromOptions =
                    RecommendService.getInstance().cascaderOptions;
            },
            async fetchAllDoctor() {
                await RecommendService.getInstance().structureOriginOptions();
                this.patientSourceType = this.sourceFromOptions =
                    RecommendService.getInstance().cascaderOptions;
            },
            /**
             * 打开就诊推荐设置
             */
            handleVisitSourceEdit() {
                this.$refs['visit-source-cascader'].outside();
                this.isShowVisitSourceDialog = true;
            },
            /**
             * 提交前预检测抗菌用药是否符合规范
             * @return {boolean}
             */
            validateAntimicrobial(medicalPlanSheets = []) {
                const { doctorId } = this.postData;
                const {
                    antimicrobialDrugManagementData, employeeListByPractice,
                } = this.$store.getters;

                const filterFormItems = [];
                (medicalPlanSheets || []).forEach((sheet) => {
                    (sheet.medicalPlanForms || []).forEach((form) => {
                        if (form.sourceFormType === SourceFormTypeEnum.COMPOSE) {
                            // 套餐
                            (form.medicalPlanFormItems || []).forEach((item) => {
                                (item.composeChildren || []).forEach((children) => {
                                    const isSuccess = isAllowAddByAntimicrobialDrugManagement(children, doctorId, antimicrobialDrugManagementData, employeeListByPractice);
                                    if (!isSuccess) {
                                        filterFormItems.push(children);
                                    }
                                });
                            });
                        } else if ([SourceFormTypeEnum.PRESCRIPTION_WESTERN, SourceFormTypeEnum.PRESCRIPTION_INFUSION, SourceFormTypeEnum.PRESCRIPTION_EXTERNAL].includes(form.sourceFormType)) {
                            // 中西成药
                            (form.medicalPlanFormItems || []).forEach((item) => {
                                const isSuccess = isAllowAddByAntimicrobialDrugManagement(item, doctorId, antimicrobialDrugManagementData, employeeListByPractice);
                                if (!isSuccess) {
                                    filterFormItems.push(item);
                                }
                            });
                        }
                    });
                });
                if (filterFormItems.length) {
                    // eslint-disable-next-line abc/no-timer-id
                    setTimeout(() => {
                        new AntimicrobialDrugManagementModal({ list: filterFormItems }).generateDialogAsync({ parent: this });
                    }, 100);
                    return false;
                }
                return true;
            },
            /**
             * 保存咨询单
             */
            async saveConsultSheet() {
                return new Promise((resolve) => {
                    this.$refs['consult-form-content'].$refs['consult-form'].validate(async (valid) => {
                        if (valid) {
                            try {
                                this.isLoading = true;

                                if (!this.validateAntimicrobial((this.postData.medicalPlanSheets || []).filter((sheet) => sheet.status === CONSULT_PROJECT_STATUS.NORMAL))) {
                                    this.isLoading = false;
                                    resolve(false);
                                    return false;
                                }

                                // 如果存在空的方案,在保存前需要删除
                                if (this.postData.medicalPlanSheets.length > 1) {
                                    const cacheMedicalPlanSheets = [];
                                    this.postData.medicalPlanSheets.forEach((item) => {
                                        if (!this.isTreatmentEmpty(item)) {
                                            cacheMedicalPlanSheets.push(Clone(item));
                                        }
                                    });
                                    if (!cacheMedicalPlanSheets.length) {
                                        cacheMedicalPlanSheets.push(this.postData.medicalPlanSheets[0]);
                                    }
                                    this.postData.medicalPlanSheets = cacheMedicalPlanSheets;
                                }
                                // 咨询单草稿
                                if (this.postData.draftId) {
                                    await ConsultApi.createConsultSheet(this.postData);
                                    await this.$store.dispatch('ClearDraft', {
                                        key: 'consult',
                                        draftId: this.postData.draftId,
                                    });
                                    await this.fetchDetail();
                                    this.isLoading = false;
                                } else {
                                    // 云端咨询单
                                    await ConsultApi.editConsultSheet(this.postData);
                                    this.isEdit = false;
                                    await this.fetchDetail(this.postData.id);

                                    // 咨询单解锁
                                    await this.sendUnlock();
                                }
                                // 草稿单要更新下状态
                                this.$emit('update-crm-consultant-flag');
                                resolve(true);
                            } catch (e) {
                                console.warn('咨询单保存失败\n', e);
                                this.isLoading = false;
                                resolve(false);
                            }
                        }
                        resolve(false);
                    });
                });
            },
            /**
             * 删除咨询单
             */
            async deleteConsultSheet() {
                const callback = async () => {
                    this.isLoading = true;
                    this.currentHideConsultData = null;
                    // 咨询单草稿
                    if (this.postData.draftId) {
                        // 如果只有一个草稿单,删除后立即新建
                        if (!this.consultListCloud.length) {
                            const newPostData = createNewConsultData();
                            // 当前登陆用户是否是咨询师,如果是,直接带入
                            if (this.isConsultant) {
                                newPostData.consultant = {
                                    countryCode: this.userInfo.countryCode,
                                    id: this.userInfo.id,
                                    mobile: this.userInfo.mobile,
                                    name: this.userInfo.name,
                                };
                            }
                            if (this.patientInfo?.patientSource) {
                                newPostData.patientSource = {
                                    id: this.patientInfo.patientSource.id,
                                    sourceFrom: this.patientInfo.patientSource.sourceFrom,
                                    sourceFromName: this.patientInfo.patientSource.sourceFromName || null,
                                };
                            }
                            newPostData.draftId = this.postData.draftId;
                            newPostData.id = '';
                            this.postData = newPostData;
                            this.isLoading = false;
                            return;
                        }
                        await this.$store.dispatch('ClearDraft', {
                            key: 'consult',
                            draftId: this.postData.draftId,
                        });
                        this.consultList = this.consultListDraft.concat(
                            this.consultListCloud,
                        );
                        // 对咨询单排序
                        this.sortConsultListHandler();
                        await this.watchCurrentConsultIdHandler(
                            this.consultList[0].draftId || this.consultList[0].id,
                            this.currentConsultId,
                        );
                    } else {
                        // 云端咨询单
                        try {
                            await ConsultApi.deleteConsultSheet(this.postData.id);
                            // 解锁咨询单
                            await this.sendUnlock();
                            await this.fetchDetail();
                            // 删除要更新下状态
                            this.$emit('update-crm-consultant-flag');
                        } catch (e) {
                            console.warn('删除咨询单失败\n', e);
                        } finally {
                            this.isLoading = false;
                        }
                    }
                };

                this.$confirm({
                    type: 'warn',
                    title: '删除确认',
                    content: '删除后不能恢复。确定删除该治疗方案？',
                    confirmText: '确定',
                    cancelText: '取消',
                    confirmLoading: this.isLoading,
                    onConfirm: callback,
                });
            },
            /**
             * 编辑咨询单
             */
            async editConsultSheet() {
                await this.getConsultLockStatus();
                if (!this.lockIdentity) {
                    this._sendLock();
                }
                // 如果咨询单被锁,则不能再编辑
                if (this.isDisabledByLock) return;

                this.editPostData = Clone(this.postData);
                this.isEdit = true;
            },
            /**
             * 拉取患者详情
             */
            async fetchPatientInfo() {
                try {
                    const params = {
                        wx: 1,
                    };
                    const { data } = await CrmAPI.fetchPatientOverview(
                        this.patientId,
                        params,
                    );
                    if (data.id === this.patientId) {
                        this.patientInfo = data;
                    }
                } catch (e) {
                    this.patientInfo = {};
                    console.warn('获取患者信息失败\n', e);
                }
            },
            /**
             * 取消编辑咨询单
             */
            async cancelEdit() {
                await this.sendUnlock();

                this.postData = Clone(this.editPostData);
                this.isEdit = false;
            },
            /**
             * 添加咨询方案
             */
            addTreatmentProject() {
                const lastSort =
                    this.medicalPlanSheets[this.medicalPlanSheets.length - 1]
                        .sort || 1;
                this.postData.medicalPlanSheets.push({
                    addFlag: 1,
                    id: Date.now(),
                    forms: [],
                    medicalPlanForms: [],
                    introduction: {
                        summary: '',
                        disposals: [
                            {
                                toothNos: [],
                                value: [
                                    {
                                        name: '',
                                    },
                                ],
                            },
                        ],
                    },
                    sort: lastSort + 1,
                    status: 0,
                });
            },
            /**
             * 删除咨询方案
             */
            deleteTreatmentProject(treatmentProjectId) {
                this.postData.medicalPlanSheets =
                    this.postData.medicalPlanSheets.filter(
                        (item) => item.id !== treatmentProjectId,
                    );
            },
            /**
             * 切换咨询单回调
             */
            async watchCurrentConsultIdHandler(id, oldId) {
                this.isLoading = true;
                this.isEdit = false;
                try {
                    if (!id) return;
                    this.currentConsultId = id;
                    const draftItem = this.consultListDraft.find(
                        (item) => item.draftId === id,
                    );
                    if (draftItem) {
                        await this.selectOne(draftItem);
                        return;
                    }
                    const cloudItem = this.consultListCloud.find(
                        (item) => item.id === id,
                    );
                    if (cloudItem) {
                        await this.selectOne(cloudItem);
                    }
                } catch (e) {
                    this.currentConsultId = oldId;
                    console.warn('切换咨询单失败\n', e);
                } finally {
                    this.isLoading = false;
                }
            },
            /**
             * 判断方案是否没有内容
             */
            isTreatmentEmpty(medicalPlanSheet) {
                if (medicalPlanSheet.medicalPlanForms.length > 0) {
                    return false;
                }
                if (medicalPlanSheet.introduction.summary !== '') {
                    return false;
                }
                for (const disposal of medicalPlanSheet.introduction.disposals) {
                    if (disposal.toothNos.length > 0) {
                        return false;
                    }
                    for (const valueObj of disposal.value) {
                        if (valueObj.name !== '') {
                            return false;
                        }
                    }
                }
                return true;
            },
            /**
             * 将接口返回的就诊推荐格式转换为前端显示的就诊推荐数据格式
             */
            transPatientSourceHandler(val) {
                if (!val) {
                    this.visitSourceOption = [];
                    return;
                }
                this.visitSourceOption =
                    RecommendService.getInstance().initCascaderValue({
                        visitSourceId: val.id,
                        visitSourceName: null,
                        visitSourceFrom: val.sourceFrom || null,
                        visitSourceFromName: val.sourceFromName || null,
                    });
            },
            /**
             * 比较当前内容是否有变更,如有变更则更新草稿
             */
            compareUpdate() {
                if (
                    this.postData.draftId &&
                    !isEqual(this.postData, this._postDataDraft)
                ) {
                    const cachePostData = Clone(this.postData);
                    this.$store.dispatch('SetDraft', {
                        key: 'consult',
                        record: {
                            ...cachePostData,
                            patientId: this.patientId,
                            patient: this.patientInfo,
                        },
                    });
                    this._postDataDraft = cachePostData;
                }
            },
            /**
             * 注册草稿监听事件
             */
            registerDraftListener() {
                this._unRegisterDraftListener = this.$watch(
                    'postData',
                    () => {
                        this._compareUpdate();
                    },
                    { deep: true },
                );
            },
            /**
             * 销毁草稿监听事件
             */
            unRegisterDraftListener() {
                this._registerDraftListenerTimer &&
                    clearTimeout(this._registerDraftListenerTimer);
                typeof this._unRegisterDraftListener === 'function' &&
                    this._unRegisterDraftListener();
            },
            /**
             * 展示患者意见popper
             */
            showPatientSuggest() {
                if (this.disabled) return;
                this.calcPatientSuggestTop();
                this.showPatientSuggestPopper = true;
            },
            /**
             * 选择患者意见option
             */
            selectPatientSuggest(val) {
                if (this.postData.patientFeedback) {
                    this.postData.patientFeedback += `，${val}`;
                } else {
                    this.postData.patientFeedback += val;
                }
                this.$nextTick(() => {
                    this.calcPatientSuggestTop();
                    const patientSuggestEditDiv = document.querySelector(
                        '.patient-suggest-edit-div',
                    );
                    patientSuggestEditDiv.focus();
                    keepLastIndex(patientSuggestEditDiv);
                });
            },
            /**
             * 计算患者意见popper的top
             */
            calcPatientSuggestTop() {
                const patientSuggestEditDiv = document.querySelector(
                    '.patient-suggest-edit-div',
                );
                this.patientSuggestPopperTop = `${
                    parseFloat(
                        window.getComputedStyle(patientSuggestEditDiv).height,
                    ) + 4
                }px`;
            },
            /**
             * 关闭患者意见的回调函数
             */
            outsidePatientSuggest() {
                this.showPatientSuggestPopper = false;
            },
            /**
             * 开单按钮回调
             */
            async submit() {
                // 保存咨询单是否成功
                let isSaveSuccess = true;
                // 如果是草稿单,先完成,再开单
                // 如果是非草稿单但处于编辑中,先完成,再开单
                if (this.postData.draftId || (this.postData.id && this.isEdit)) {
                    isSaveSuccess = await this.saveConsultSheet();
                }
                // 如果保存失败,则无法开单
                if (!isSaveSuccess) return;
                if (this.postData.draftId || (this.postData.id && this.isEdit)) return;

                // 如果只有一个方案，且没填写任何内容或者没有开项目
                if (
                    this.postData.medicalPlanSheets.length === 1 &&
                    (this.isTreatmentEmpty(this.postData.medicalPlanSheets[0]) || this.isMedicalPlanSheetFormsEmpty(this.postData.medicalPlanSheets[0]))
                ) {
                    this.$modal({
                        title: '方案开单',
                        content: '确定后治疗方案状态将改为已收费',
                        confirmText: '确定',
                        cancelText: '取消',
                        onConfirm: async () => {
                            await this.transactionConfirm();
                        },
                        confirmLoading: this.isLoading,
                    });
                    return;
                }

                this.showConfirmDialog = true;
            },
            /**
             * 丢单按钮回调
             */
            async loseConsult() {
                // 保存咨询单是否成功
                let isSaveSuccess = true;
                // 如果是草稿单,先完成,再开单
                // 如果是非草稿单但处于编辑中,先完成,再开单
                if (this.postData.draftId || (this.postData.id && this.isEdit)) {
                    isSaveSuccess = await this.saveConsultSheet();
                }
                // 如果保存失败,则无法丢单
                if (!isSaveSuccess) return;
                if (this.postData.draftId || (this.postData.id && this.isEdit)) {return;}
                this.showLoseDialog = true;
            },
            /**
             * 收费前校验抗菌等级限制是否合规
             * @return {boolean}
             */
            preCheckCharged(medicalPlanSheetId) {
                let medicalPlanSheet = this.postData.medicalPlanSheets[0];
                if (medicalPlanSheetId) {
                    const cacheMedicalPlanSheet = this.postData.medicalPlanSheets.find((sheet) => sheet.id === medicalPlanSheetId);
                    if (cacheMedicalPlanSheet) {
                        medicalPlanSheet = cacheMedicalPlanSheet;
                    }
                }
                return this.validateAntimicrobial([medicalPlanSheet]);
            },
            /**
             * 开单
             */
            async transactionConfirm(medicalPlanSheetId) {
                try {
                    this.isLoading = true;

                    if (!this.preCheckCharged(medicalPlanSheetId)) {
                        this.isLoading = false;
                        return false;
                    }

                    await ConsultApi.transactionConfirm({
                        medicalPlanId: this.postData.id,
                        medicalPlanSheetId: medicalPlanSheetId ?
                            medicalPlanSheetId :
                            this.postData.medicalPlanSheets[0].id,
                    });
                    await this.fetchDetail(this.postData.id);
                    // 收费后要更新下状态
                    this.$emit('update-crm-consultant-flag');
                    this.$Toast.success('开单成功');
                } catch (e) {
                    console.warn('开单失败\n', e);
                } finally {
                    this.isLoading = false;
                }
            },
            /**
             * 确认丢单回调
             */
            async loseConsultConfirm(reason = '') {
                try {
                    this.isLoading = true;
                    await ConsultApi.loseConsult({
                        medicalPlanId: this.postData.id,
                        reason,
                    });
                    await this.fetchDetail(this.postData.id);
                    this.$Toast.success('丢单成功');
                    // 丢单要更新下状态
                    this.$emit('update-crm-consultant-flag');
                    // 丢单会新增一条跟进记录,需要刷新跟进记录列表
                    this.$abcEventBus.$emit('refresh-list');
                } catch (e) {
                    this.isLoading = false;
                    console.warn('丢单失败\n', e);
                }
            },
            /**
             * 治疗方案的状态为关闭
             */
            isProjectClose(medicalPlanSheet) {
                return (
                    this.postData.status > CONSULT_STATUS.FOLLOWING_UP &&
                    medicalPlanSheet.status === CONSULT_PROJECT_STATUS.CLOSED
                );
            },
            /**
             * 判断方案没开项目
             */
            isMedicalPlanSheetFormsEmpty(medicalPlanSheet) {
                return medicalPlanSheet.medicalPlanForms <= 0;
            },
            async selectedShowConsult(consultId) {
                if (this.currentConsultId === consultId) return;
                this.currentHideConsultData = null;
                await this.changeConsultSelector(consultId);
            },
            async selectedHideConsult(selectedConsult) {
                const id = selectedConsult.draftId || selectedConsult.id;
                this.currentHideConsultData = Clone(selectedConsult);
                await this.changeConsultSelector(id);
            },
            async addConsultHandle() {
                if (!this.isClinicAdmin && !this.isDoctor && !this.isConsultant) {
                    this.$Toast.error('只有医生和咨询师才能新增');
                    return;
                }
                this.currentHideConsultData = null;
                await this.addConsult();
            },
            outsideMore() {
                this.isClickMoreConsult = false;
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/abc-common.scss";

.consult-container {
    height: 100%;

    .abc-layout-content {
        @include scrollBar();
    }

    .pay-exception-tips-card {
        margin-bottom: 16px;
    }
}

.hide-consult-list {
    .item-info {
        font-size: 14px;
        line-height: 22px;

        &:not(:first-child) {
            margin-left: 8px;
        }
    }

    .item-date {
        color: $T2;
    }
}

.consult-item-status-name {
    margin-left: 8px;
}

.consult-item-created {
    margin-left: 8px;
    color: $T2;
}

.consult-list-popover {
    background: #ffffff;
    border: 1px solid #e0e2eb;
    border-radius: var(--abc-border-radius-small);
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.15);

    .consult-list-option-wrapper {
        width: 230px;
        height: auto;
        max-height: 392px;
        border-radius: var(--abc-border-radius-small);

        .option {
            display: flex;
            align-items: center;
            width: 100%;
            height: 32px;
            padding: 0 8px;
            color: $T1;
            cursor: pointer;
            background-color: #ffffff;

            &:first-of-type {
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }

            &:last-of-type {
                border-bottom-right-radius: 4px;
                border-bottom-left-radius: 4px;
            }

            &:hover {
                background-color: #e5f2ff;
            }

            &.is-selected {
                align-items: center;
                background-color: #e5f2ff;
            }
        }
    }
}
</style>

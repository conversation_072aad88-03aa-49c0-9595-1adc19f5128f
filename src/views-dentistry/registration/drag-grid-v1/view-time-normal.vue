<template>
    <div class="time-mode">
        <drag-grid
            ref="dragGrid"
            :row-height="rowHeight"
            :row-span="rowSpan"
            :y-axis="yAxis"
            :x-axis="xAxis"
            :col-width="colWidth"
            :mode="Dragger.DRAGGER_MODE_TIME"
            :disable-x="disableX"
            :x-axis-height="hideHeader ? 0 : xAxisHeight"
            :hide-header="hideHeader"
        >
            <template slot="xAxis">
                <template v-if="isBoardWeekViewMode">
                    <div class="prep-button normal-cursor"></div>
                </template>
                <template v-else-if="!isSingleDoctor">
                    <div class="prep-button" :class="{ 'disabled': !hasPrev }" @click="handleClickPrevBtn">
                        <abc-icon icon="Arrow_Left"></abc-icon>
                    </div>
                    <div class="append-button" :class="{ 'disabled': !hasNext }" @click="handleClickNextBtn">
                        <abc-icon icon="Arrow_Rgiht"></abc-icon>
                    </div>
                </template>

                <div
                    class="xAxis-items"
                    :style="{
                        'text-align': isSingleDoctor && !isBoardWeekViewMode ? 'center' : 'left'
                    }"
                >
                    <template v-if="!isBoardWeekViewMode">
                        <div
                            v-for="it in xAxisContent"
                            :key="it.id"
                            class="xAxis-item"
                            :class="{ active: it.isToday }"
                        >
                            <span>
                                {{ it.label }}
                                <template v-if="it.notRefundedCount">
                                    ({{
                                        it.notRefundedCount
                                    }})
                                </template>
                            </span>
                        </div>
                    </template>

                    <template v-else>
                        <div
                            v-for="it in xAxisContent"
                            :key="it.id"
                            class="xAxis-item week-item"
                            :class="{ active: it.isToday }"
                        >
                            <span>{{ it.week }}</span>
                            <span>{{ it.day }}</span>
                        </div>
                    </template>
                </div>
            </template>
            <template slot="yAxis">
                <div
                    class="yAxis-items"
                    :style="{
                        height: `${rowHeight * rowSpan * (yAxisContent.length - 1) }px`, position: 'relative'
                    }"
                >
                    <div
                        v-for="(it, yIndex) in yAxisContent"
                        :key="it.time"
                        class="yAxis-item"
                        :style="{
                            top: yIndex === 0 ? 0 : `${rowHeight * rowSpan * yIndex - 11 }px`,
                            position: 'absolute',
                            right: '8px'
                        }"
                    >
                        {{ it.time }}
                    </div>
                </div>
            </template>

            <div ref="dataGrid" class="drag-grid" :style="{ minHeight: toPx(rowHeight * yAxis) }">
                <disable-tips
                    v-if="disableTips"
                    class="global-disable-tips"
                    :text="disableTips.text"
                    :style="{
                        width: toPx(colWidth),
                        top: toPx(disableTips.top),
                        left: toPx(disableTips.left),
                    }"
                ></disable-tips>

                <div
                    class="yAxis-items-current-time-line"
                    :style="{
                        top: `${currentTimeTop + 9}px`, width: currentTimeWidth
                    }"
                ></div>
                <div
                    v-for="(row, rowIndex) in yAxis"
                    :key="`row-${row}`"
                    class="drag-grid-row"
                    :class="{ 'drag-grid-row-line': (rowIndex + 1) % rowSpan === 0 }"
                    :style="{ height: toPx(rowHeight) }"
                >
                </div>
                <div
                    v-for="(col, colIndex) in xAxis"
                    :key="`col-${col}`"
                    class="drag-grid-col"
                    :class="{
                        'drag-grid-col-last': col === xAxis, 'disabled': disableX.includes(colIndex)
                    }"
                    :style="{
                        width: toPx(colWidth), left: toPx(colWidth * colIndex)
                    }"
                ></div>

                <div
                    v-for="item in schedulesWithPosition"
                    :key="`${item.top}-${item.left}-${item.width}-${item.height}`"
                    class="drag-grid-background-block"
                    :class="{
                        'drag-grid-background-block_disable': !isCanModifyRegistrationInfo || !isSupportModifyRegistrationTimeInBoardView
                    }"
                    :style="{
                        top: toPx(item.top),
                        left: toPx(item.left),
                        height: toPx(item.height),
                        width: toPx(item.width),
                    }"
                >
                </div>

                <data-wrapper
                    v-for="item in dataListWithPosition"
                    :id="item.isNewRegistration ? 'new-registration' : ''"
                    ref="dragGridItem"
                    :key="createData(item).registrationId"
                    class="drag-grid-item dynamic-drag-grid-item"
                    :style="{
                        top: toPx(item.top),
                        left: toPx(item.left),
                        height: toPx(item.height),
                        width: toPx(item.width),
                        padding: '1px'
                    }"
                    :dragger-stretch-origin-height="toPx(item.height)"
                    :dragger-stretch-origin-start="item.registrationFormItem.reserveStart"
                    :class="{ [Dragger.DRAGGER_STRETCH_SINGLE_LINE_CLASS]: item.y[1] - item.y[0] === 1 }"
                    :data="createData(item)"
                    :disabled="!isCanModifyRegistrationInfo || createData(item).disabled"
                >
                    <div
                        class="drag-card drag-card-layout2 drag-card-stretch-mask"
                        :class="[
                            `drag-card-${statusV2Color[item.registrationFormItem.statusV2]}`,
                            {
                                'waiting-sign-in-confirm': item.registrationFormItem.statusV2 === StatusV2.WAITING_SIGN_IN_CONFIRM
                            }
                        ]"
                    >
                        <div class="top-container">
                            <span class="drag-card-name" style="word-break: break-all;">{{ item.patient.name || '新预约' }}</span>
                            <abc-icon 
                                v-if="item.registrationFormItem && item.registrationFormItem.referralFlag && item.registrationFormItem.referralFlag !== ReferralFlagEnum.REVISIT_IN_THREE_DAYS"
                                icon="s-change-tag-color" 
                                :size="14" 
                                style="margin-left: 4px;"
                            ></abc-icon>
                            <abc-tooltip
                                v-if="item.registrationFormItem && item.registrationFormItem.referralFlag === ReferralFlagEnum.REVISIT_IN_THREE_DAYS"
                                placement="top"
                                content="3日内复诊"
                            >
                                <abc-icon icon="s-followup-tag-color" :size="14" style="margin-left: 4px;"></abc-icon>
                            </abc-tooltip>
                            <img
                                v-if="item.registrationFormItem && item.registrationFormItem.registrationCategory === RegistrationCategory.CONVENIENCE"
                                class="registration-category-tag"
                                src="~assets/images/icon-convenience.png"
                                alt=""
                            />
                        </div>
                        <div class="bottom-container">
                            <span
                                :key="item.registrationFormItem.reserveStart"
                                class="drag-card-time"
                                :data-time="item.registrationFormItem.reserveStart"
                            >{{
                                item.registrationFormItem.reserveStart
                            }}</span>
                            <span>{{ showRegistrationProducts(item.registrationFormItem) }}</span>
                        </div>
                        <div v-if="showIrreducible(item.registrationFormItem)" class="absolute-top-right">
                            <img src="~assets/images/registration/pin.png" />
                        </div>
                        <disable-tips></disable-tips>
                    </div>
                </data-wrapper>

                <data-wrapper
                    v-for="(rangeTimeGroup, rangeTimeGroupIndex) in dataListRangeTimeGroupWithPosition"
                    :key="rangeTimeGroupIndex"
                    ref="rangeTimeGroup"
                    class="range-time-group-wrapper"
                    :style="{
                        position: 'absolute',
                        top: toPx(rangeTimeGroup.top),
                        left: toPx(rangeTimeGroup.left),
                        zIndex: 1,
                    }"
                    :data="rangeTimeGroup"
                >
                    {{ rangeTimeGroup.list.length }}
                    <abc-icon icon="Arrow_Rgiht" size="8"></abc-icon>
                </data-wrapper>

                <div
                    v-if="currentExpandRangeTimeGroup.list.length"
                    ref="rangeTimeGroupCard"
                    class="range-time-group-card"
                    :style="{
                        position: 'absolute',
                        top: toPx(currentExpandRangeTimeGroup.columnTop),
                        left: toPx(currentExpandRangeTimeGroup.columnLeft),
                        height: toPx(rowHeight * rowSpan),
                        width: toPx(Math.floor((xAxis + 1) / 2) * colWidth),
                        zIndex: 2,
                    }"
                >
                    <data-wrapper
                        v-for="item in currentExpandRangeTimeGroup.list"
                        :key="createData(item).registrationId"
                        ref="rangeTimeGroupCardItem"
                        class="drag-grid-item dynamic-drag-grid-item"
                        :style="{
                            top: toPx(item.top),
                            left: toPx(item.left),
                            height: toPx(item.height),
                            width: toPx(item.width),
                            padding: '1px',
                        }"
                        :class="[item.className]"
                        :disabled="true"
                        :data="createData(item)"
                    >
                        <div
                            class="drag-card drag-card-layout2 drag-card-stretch-mask"
                            :class="[
                                `drag-card-${statusV2Color[item.registrationFormItem.statusV2]}`,
                                {
                                    'waiting-sign-in-confirm': item.registrationFormItem.statusV2 === StatusV2.WAITING_SIGN_IN_CONFIRM
                                }
                            ]"
                        >
                            <div class="top-container">
                                <span class="drag-card-name" style="word-break: break-all;">{{ item.patient.name }}</span>
                            </div>
                            <div class="bottom-container">
                                <span
                                    :key="item.registrationFormItem.reserveStart"
                                    :data-time="item.registrationFormItem.reserveStart"
                                    class="drag-card-time"
                                >{{
                                    item.registrationFormItem.reserveStart
                                }}</span>
                                <span>{{ showRegistrationProducts(item.registrationFormItem) }}</span>
                            </div>
                            <div v-if="showIrreducible(item.registrationFormItem)" class="absolute-top-right">
                                <img src="~assets/images/registration/pin.png" />
                            </div>
                        </div>
                    </data-wrapper>
                </div>
            </div>
        </drag-grid>
    </div>
</template>

<script>
    import {
        getTimeByIndex,
        getTimeIndex,
        getWeekDaysByDate,
        isFutureDate,
        isPastDate,
        layoutDayEvents,
        ROW_MERGE_COUNT,
        START_MINUTES,
        SUPPORT_HOURS,
        TIMES,
        toPx,
        getTextBoundingBox,
    } from './common.js';
    import { parseTime } from '@/utils/index.js';
    import TimeModeDataHandler from '@/views-dentistry/registration/drag-grid-v1/time-mode-data-handler';
    import Dragger from './dragger.js';
    import CommonMixin from '@/views-dentistry/registration/drag-grid-v1/common-mixin';
    import { sleep } from 'utils/delay';
    import Clone from 'utils/clone';
    import { mapGetters } from 'vuex';
    import { StatusV2 } from 'views/registration/common/constants';
    import { ReferralFlagEnum } from '@/common/constants/registration';

    export default {
        name: 'ViewTimeNormal',
        mixins: [CommonMixin],
        data() {
            return {
                ReferralFlagEnum,
                Dragger,
                StatusV2,
                xAxisHeight: 40,
                rowHeight: 30,
                rowSpan: ROW_MERGE_COUNT,
                yAxis: SUPPORT_HOURS * ROW_MERGE_COUNT,
                xAxis: 5,
                yAxisContent: TIMES,
                dataList: [],
                schedules: [],
                xAxisContent: [],
                currentTime: null,
                currentTimeTop: null,
                currentTimeWidth: '100%',
                hasPrev: false,
                hasNext: false,
                isNormalComponent: true,
                // 距离右侧宽度
                rowSpanRightGap: 22,
                // 卡片边距
                cardGap: 2,
                // 当前展开的分组
                currentExpandRangeTimeGroup: {
                    list: [],
                },
                // 禁用组件位置
                disableTips: null,
            };
        },
        computed: {
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            schedulesWithPosition() {
                return this.schedules.map((it) => {
                    return {
                        height: (it.y[1] - it.y[0]) * this.rowHeight,
                        top: it.y[0] * this.rowHeight,
                        width: this.colWidth,
                        left: it.x * this.colWidth,
                    };
                });
            },
            groupByX() {
                const groupX = [];
                this.dataList.forEach((dataItem) => {
                    const fined = groupX.find((group) => group.some((groupItem) => groupItem.x === dataItem.x));
                    const currentGroup = fined || [];
                    if (!fined) {
                        groupX.push(currentGroup);
                    }
                    currentGroup.push(dataItem);
                });
                return groupX;
            },
            dataListRangeTimeGroupWithPosition() {
                const groupWithPosition = Clone(this.groupByX).map((group) => {
                    const groupByStartTime = [];
                    // 按照跨度最大的排序
                    group.sort((a, b) => b.y[1] - b.y[0] - (a.y[1] - a.y[0]));
                    group.forEach((it) => {
                        const startHour = Math.floor(it.y[0] / ROW_MERGE_COUNT);
                        const endHour = Math.ceil(it.y[1] / ROW_MERGE_COUNT);
                        for (let i = startHour; i < endHour; i++) {
                            const timeIndex = `${i}`;
                            const fined = groupByStartTime.find((item) => item.timeIndex === timeIndex);
                            const currentGroup = fined || {
                                timeIndex, list: [],
                            };
                            if (!fined) {
                                groupByStartTime.push(currentGroup);
                            }

                            // 新预约卡片不参与计算
                            if (!it.isNewRegistration) {
                                currentGroup.list.push(Clone(it));
                            }
                            currentGroup.left = (it.x + 1) * this.colWidth - 4 - 32;
                            currentGroup.top = (i + 1) * this.rowHeight * ROW_MERGE_COUNT - 4 - 18;
                            currentGroup.columnTop = i * this.rowHeight * ROW_MERGE_COUNT;
                            currentGroup.columnLeft = (it.x > this.xAxis / 2 ? it.x - Math.floor(this.xAxis / 2) : it.x) * this.colWidth;
                        }
                    });

                    return groupByStartTime;
                });

                return groupWithPosition.reduce((prev, curr) => {
                    return prev.concat(curr);
                }, []).filter((it) => it.list.length > 4).map((it) => {
                    const cardWidth = 96;
                    const rangeTimeGroupHeight = this.rowHeight - 8;
                    // 处理一个组的卡片位置和宽高
                    it.list = it.list.map((card, cardIndex) => {
                        const y0 = Math.max(card.y[0], it.timeIndex * ROW_MERGE_COUNT);
                        const y1 = Math.min(card.y[1], (it.timeIndex + 1) * ROW_MERGE_COUNT);
                        card.width = cardWidth;
                        const height = (y1 - y0) * rangeTimeGroupHeight;
                        card.height = Math.max(height, 28);
                        card.top = (y0 % ROW_MERGE_COUNT) * rangeTimeGroupHeight;
                        card.left = cardIndex * cardWidth;
                        if (y1 - y0 === 1) {
                            card.className = Dragger.DRAGGER_STRETCH_SINGLE_LINE_CLASS;
                        }
                        return card;
                    });
                    return it;
                });
            },
            dataListWithPosition() {
                if (this.mode === Dragger.DRAGGER_MODE_FREE) {
                    return [];
                }
                const newDataList = [];
                // 处理每一组元素
                Clone(this.groupByX).forEach((group) => {
                    // 按起始时间排序
                    group.sort((a, b) => a.y[0] - b.y[0]);

                    // 按时间分组
                    const groupWithTime = [];
                    group.forEach((data) => {
                        const lastGroup = groupWithTime[groupWithTime.length - 1];
                        if (!lastGroup) {
                            groupWithTime.push([data]);
                        } else {
                            if (lastGroup[lastGroup.length - 1].y[0] === data.y[0]) {
                                lastGroup.push(data);
                            } else {
                                groupWithTime.push([data]);
                            }
                        }
                    });

                    groupWithTime.forEach((group) => group.sort((a, b) => (b.y[1] - b.y[0]) - (a.y[1] - a.y[0])));

                    // 平铺
                    group = groupWithTime.reduce((total, current) => [...total, ...current], []);

                    // 根据最大值进行分组
                    let maxEnd = -1;
                    const groupList = [];
                    group.forEach((data) => {
                        // 新建一组
                        if (data.y[0] >= maxEnd) {
                            groupList.push([data]);
                        } else {
                            groupList[groupList.length - 1].push(data);
                        }
                        maxEnd = Math.max(data.y[1], maxEnd);
                    });

                    groupList.forEach((groups) => {
                        layoutDayEvents(groups).forEach((it) => {
                            const width = (this.colWidth - this.rowSpanRightGap) / it._columnCnt;
                            it.left = it.x * this.colWidth + (it._column - 1) * width;
                            it.width = width * it._columnSpan;
                            newDataList.push(it);
                        });
                    });
                });

                return newDataList.map((it) => {
                    let paddingTop = 0;
                    let paddingBottom = 0;
                    let height = (it.y[1] - it.y[0]) * this.rowHeight;
                    if (it.y[0] % this.rowSpan === 0) {
                        // 第一个元素
                        paddingTop = this.cardGap;

                    } else if (it.y[0] % this.rowSpan === this.rowSpan - 1) {
                        // 最后一个元素
                        paddingTop = this.cardGap / 2;
                    } else {
                        paddingTop = this.cardGap / 2;
                    }

                    if (it.y[1] % this.rowSpan === 1) {
                        // 第一个元素
                        paddingBottom = this.cardGap / 2;
                    } else if (it.y[1] % this.rowSpan === 0) {
                        // 最后一个元素
                        paddingBottom = this.cardGap + 1;
                        height -= 1;
                    } else {
                        paddingBottom = this.cardGap / 2;
                    }

                    return {
                        ...it,
                        height,
                        top: it.y[0] * this.rowHeight,
                        paddingTop,
                        paddingBottom,
                        paddingLeft: this.cardGap,
                    };
                });
            },

            hideHeader() {
                return this.hideSingleDoctorHeader && this.isSingleDoctor && !this.isBoardWeekViewMode;
            },
        },
        watch: {
            isBoardWeekViewMode: {
                handler(newValue) {
                    this.xAxisHeight = newValue ? 56 : 40;
                },
                immediate: true,
            },
            dataList: {
                handler() {
                    this.queryItemWidthAndSetStyle();
                },
                immediate: true,
                deep: true,
            },
            currentExpandRangeTimeGroup: {
                handler() {
                    this.queryItemWidthAndSetStyle();
                },
                immediate: true,
                deep: true,
            },
        },
        methods: {
            toPx,
            async queryItemWidthAndSetStyle() {
                await this.$nextTick();
                const dynamicDragGridItems = this.$el?.querySelectorAll('.dynamic-drag-grid-item');
                if (dynamicDragGridItems) {
                    dynamicDragGridItems.forEach((el) => {
                        const $nameContainer = el.querySelector('.drag-card-name');
                        const $timeContainer = el.querySelector('.drag-card-time');
                        const $dragCard = el.querySelector('.drag-card');
                        const $bottomContainer = el.querySelector('.bottom-container');
                        const $topContainer = el.querySelector('.top-container');
                        const isSingleLine = el.classList.contains('drag-grid-item-stretch-single-line');
                        const nameWidth = getTextBoundingBox($nameContainer.textContent).width;
                        const timeWidth = getTextBoundingBox($timeContainer.textContent).width;
                        const elWidth = parseFloat(el.style.width);
                        const contentWidth = elWidth - 12;
                        const isOutsizeWidth = nameWidth > contentWidth || timeWidth > contentWidth;
                        const isSingleLineOutsizeWidth = isSingleLine && nameWidth + timeWidth > contentWidth;
                        const isTooSmall = elWidth < 18 + 12;
                        $bottomContainer.style.display = (isOutsizeWidth || isSingleLineOutsizeWidth) ? 'none' : '';
                        $topContainer.style.height = isOutsizeWidth ? 'auto' : '';
                        $topContainer.style.lineHeight = isOutsizeWidth ? '1.1' : '';
                        $dragCard.style.padding = isTooSmall ? '4px 0' : '';
                        $topContainer.style.textAlign = isTooSmall ? 'center' : '';
                        $topContainer.style.display = (elWidth < 18 && $nameContainer.textContent !== '新预约') ? 'none' : '';
                    });
                }
            },
            async initAndActiveSelectedRegistrationId(reset = true) {
                this.initDataHandler();
                // 每次更新需要重置数据
                // 避免面板切换时更新不及时
                // 在popper弹窗中视图更新变化不大 不需要重置数据
                reset && !this.isInPopper && Object.assign(this, this.isBoardWeekViewMode ? TimeModeDataHandler.getWeekViewDefaultData(this.timeRange) : TimeModeDataHandler.getDayViewDefaultData(this.doctorList));
                this.initColWidth();
                await this.initAll();
                this.activeSelectedRegistrationId();
            },
            initLayout() {
                this.initCurrentTime();
                this.scrollToCurrentTime();
            },
            initCurrentTime() {
                const now = new Date();
                this.currentTime = parseTime(now, 'h:i', true);
                this.currentTimeTop = (Math.floor(getTimeIndex(START_MINUTES, now) / this.rowSpan) + now.getMinutes() / 60) * this.rowHeight * this.rowSpan - 10;
                if (this.isBoardWeekViewMode) {
                    const index = getWeekDaysByDate(this.timeRange[0]).findIndex((it) => it.toDateString() === now.toDateString());
                    if (index !== -1) {
                        this.currentTimeWidth = `${(7 - index) * 100 / 7}%`;
                    } else {
                        this.currentTimeWidth = '100%';
                    }
                } else {
                    this.currentTimeWidth = '100%';
                }
            },
            scrollToCurrentTime() {
                this.$el.querySelector('.drag-grid-content-wrapper').scrollTop = this.currentTimeTop;
            },
            initDataHandler() {
                this._dataHandler = new TimeModeDataHandler(this.doctorList, this.timeRange, this.businessType);
            },
            async fetchData() {
                await this._dataHandler.fetchData();
                Object.assign(this, this.isBoardWeekViewMode ? this._dataHandler.getWeekView() : this._dataHandler.getDayView());
            },

            onContinueDragEnd({ coord }) {
                const isSchedulesDisabled = this.checkSchedulesDisabled(coord.x, coord.y[0], coord.y[1]);
                if (this.isSupportModifyRegistrationTimeInBoardView) {
                    return true;
                }
                if (isSchedulesDisabled) {
                    this.$Toast.error('该时段未排班');
                }
                return !isSchedulesDisabled;
            },

            checkSchedulesDisabled(x, y0) {
                const currentSchedules = this.schedules.filter((it) => it.x === x);
                return currentSchedules.some(({ y }) => y0 > y[0] && y0 < y[1]);
            },

            async onClickInactiveElement({
                coord, destroy, e, relativePosition,
            }) {
                // 点击分组元素
                const clickRangeTimeGroupElement = (this.$refs.rangeTimeGroup ?? []).find((vm) => vm.$el.contains(e.target));
                if (clickRangeTimeGroupElement) {
                    this.currentExpandRangeTimeGroup = clickRangeTimeGroupElement.$el._data;
                    return;
                }

                // 点击分组元素
                if (this.$refs?.rangeTimeGroupCard?.contains(e.target)) {
                    // 激活卡片元素
                    const activeCard = this.$refs.rangeTimeGroupCardItem?.find((it) => it.$el.contains(e.target));
                    if (activeCard) {
                        Dragger.getActive().$activeEl = activeCard.$el;
                        Dragger.getActive()._isIdle = false;
                        Dragger.getActive().onDragEnd({
                            destroy: () => {
                                destroy();
                            },
                            coord: {
                                x: undefined,
                                y: [
                                    getTimeIndex(START_MINUTES, activeCard.$el._data.timeRange[0]),
                                    getTimeIndex(START_MINUTES, activeCard.$el._data.timeRange[1]),
                                ],
                            },
                        });
                    }
                    this.disableTips = null;
                    return;
                }

                // 关闭分组元素
                if (this.currentExpandRangeTimeGroup.list.length > 0) {
                    this.currentExpandRangeTimeGroup = {
                        list: [],
                    };
                    return;
                }

                const y0 = coord.y[0];
                // 不能溢出超过最大值handleDragEndData
                const y1 = Math.min(y0 + 2, this.yAxis);
                let doctorId, doctorName, reserveDate;
                if (this.isBoardWeekViewMode) {
                    doctorId = this._dataHandler._doctorList[0].id;
                    doctorName = this._dataHandler._doctorList[0].name;
                    reserveDate = this.xAxisContent[coord.x].date;
                } else {
                    const {
                        doctor: {
                            id, name,
                        } = {},
                    } = this.xAxisContent[coord.x] || {};
                    doctorId = id;
                    doctorName = name;
                    reserveDate = this._dataHandler._timeRange[0];
                }

                if (isPastDate(reserveDate)) {
                    if (relativePosition) {
                        this.disableTips = {
                            top: relativePosition.top,
                            left: Math.floor(relativePosition.left / this.colWidth) * this.colWidth,
                        };
                    }
                    return;
                }

                if (!this.isCanModifyRegistrationInfo) {
                    if (relativePosition) {
                        this.disableTips = {
                            top: relativePosition.top,
                            left: Math.floor(relativePosition.left / this.colWidth) * this.colWidth,
                            text: '暂无挂号权限',
                        };
                    }
                    return;
                }

                // 检查排班状态
                if (!this.isSupportModifyRegistrationTimeInBoardView && this.checkSchedulesDisabled(coord.x, y0)) {
                    if (relativePosition) {
                        this.disableTips = {
                            top: relativePosition.top,
                            left: Math.floor(relativePosition.left / this.colWidth) * this.colWidth,
                            text: '未排班不可预约',
                        };
                    }
                    return;
                }

                this.disableTips = null;

                const newRegistrationDataItem = {
                    id: '',
                    patient: {
                        name: '',
                    },
                    registrationFormItem: {
                        reserveStart: getTimeByIndex(y0, new Date(), 'h:i'),
                        reserveEnd: getTimeByIndex(y1, new Date(), 'h:i'),
                        doctorId,
                        doctorName,
                    },
                    dailyView: {
                        reserveDate,
                    },
                    employee: {},
                    x: coord.x,
                    y: [y0, y1],
                    isNewRegistration: true,
                };

                this.dataList.push(newRegistrationDataItem);

                await sleep(0);
                // TODO 更优的DOM查找方式
                Dragger.getActive().$activeEl = document.querySelector('#new-registration');
                Dragger.getActive()._isIdle = false;
                Dragger.getActive().onDragEnd({
                    destroy: () => {
                        this.dataList.splice(this.dataList.indexOf(newRegistrationDataItem), 1);
                        destroy();
                    },
                    coord: {
                        x: coord.x,
                        y: [y0, y1],
                    },
                    data: newRegistrationDataItem,
                    isBlockDragEnd: true,
                });
            },

            onClickOutsideContentContainer() {
                this.currentExpandRangeTimeGroup = {
                    list: [],
                };

                this.disableTips = null;
            },

            handleDragEndData(dragEndEvent) {
                this.disableTips = null;

                const {
                    activeEl, coord,
                } = dragEndEvent;
                const oldData = activeEl?._data;
                const newData = {
                    ...oldData,
                    timeRange: [
                        getTimeByIndex(coord.y[0], new Date(), 'h:i'),
                        getTimeByIndex(coord.y[1], new Date(), 'h:i'),
                    ],
                };

                if (coord.x !== undefined && !isNaN(coord.x)) {
                    // 日视图全部医生
                    if (!this.isBoardWeekViewMode && !this.isSingleDoctor) {
                        newData.doctorId = this.xAxisContent[coord.x]?.doctor?.id;
                        newData.doctorName = this.xAxisContent[coord.x]?.doctor?.name;
                    }

                    // 周视图单个医生
                    if (this.isBoardWeekViewMode) {
                        newData.date = this.xAxisContent[coord.x]?.date;
                    }
                }

                // 查看
                const isView = (getTimeIndex(START_MINUTES, oldData.timeRange[0]) === getTimeIndex(START_MINUTES, newData.timeRange[0])) &&
                    (getTimeIndex(START_MINUTES, oldData.timeRange[1]) === getTimeIndex(START_MINUTES, newData.timeRange[1])) &&
                    (oldData.doctorId === newData.doctorId) &&
                    (oldData.date === newData.date);
                newData.registrationId = isView ? oldData.registrationId : null;
                newData.oldRegistrationId = isView ? null : oldData.registrationId;
                newData.payStatusV2 = isView ? null : oldData.payStatusV2;

                if (!oldData.isReserved) {
                    newData.isReserved = +isFutureDate(newData.date);
                }
                return {
                    ...newData,
                    isBlockHandle: !isView,
                };
            },

            onDragTimeModeShadowUpdate(shadowEl, coord, draggerType) {
                const start = getTimeByIndex(coord.y[0], new Date(), 'h:i');
                const end = getTimeByIndex(coord.y[1], new Date(), 'h:i');
                shadowEl.querySelector('[data-time]').innerHTML = draggerType === Dragger.DRAGGER_TYPE.STRETCH ? `${start} ~ ${end}` : start;
            },
        },
    };
</script>

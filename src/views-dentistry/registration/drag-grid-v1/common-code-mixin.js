export default {
    methods: {
        initTimeOfDayScrollWatch() {
            if (!this.$refs.dragGrid) {
                console.error('initTimeOfDayScrollWatch Error');
                return;
            }
            const $scrollWrapper = this.$refs.dragGrid.$el.querySelector('.drag-grid-content-wrapper');
            const $timeBlockTexts = this.$refs.dragGrid.$el.querySelectorAll('.time-block-text span');
            const { top } = $scrollWrapper.getBoundingClientRect();
            const timeBlockTextsTop = [...$timeBlockTexts].map((el) => {
                const top = el.getBoundingClientRect().top - $scrollWrapper.getBoundingClientRect().top;
                return {
                    top,
                    left: el.getBoundingClientRect().left,
                    parentTop: el.parentElement.getBoundingClientRect().top - $scrollWrapper.getBoundingClientRect().top,
                    el,
                };
            });
            $scrollWrapper.addEventListener('scroll', (e) => {
                const { scrollTop } = e.target;
                timeBlockTextsTop.forEach(({ el }) => {
                    el.setAttribute('style', '');
                });
                for (let i = timeBlockTextsTop.length - 1; i >= 0; i--) {
                    if (scrollTop > timeBlockTextsTop[i].top && scrollTop < timeBlockTextsTop[i + 1]?.parentTop - 28) {
                        timeBlockTextsTop[i].el.setAttribute('style', `position: fixed;left:${timeBlockTextsTop[i].left}px;top:${top + 12}px`);
                    }
                }
            });
        },
    },
};

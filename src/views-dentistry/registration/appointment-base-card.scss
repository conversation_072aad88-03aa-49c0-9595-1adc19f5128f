@import "src/styles/theme.scss";
@import 'src/styles/abc-common.scss';

.appointment-base-card-wrapper {
    .horizontal-split-line {
        height: 1px;
        background-color: $P4;
    }

    .display-status-wrapper {
        width: 400px;

        .abc-loading-wrapper {
            .cover-wrap {
                border-radius: var(--abc-dialog-border-radius);
            }
        }

        .patient-info-wrapper {
            display: flex;
            align-items: center;
            height: 64px;
            padding-left: 16px;
            line-height: 20px;
            background-image: linear-gradient(117deg, #ffd99a 1%, #fff9f0 100%);
            border-radius: var(--abc-dialog-border-radius) var(--abc-dialog-border-radius) 0 0;

            &.other-color {
                background-image: linear-gradient(117deg, #9bd2fa 1%, #ceebff 100%);
            }

            .title {
                width: 40px;
                font-size: 14px;
                font-weight: 400;
                color: #bc7500;
                text-align: left;

                &.title2 {
                    color: $theme1;
                }
            }

            .patient-info {
                display: inline-flex;
                flex: 1;
                align-items: center;
                font-size: 14px;
                font-weight: bold;
                color: $S1;

                .name {
                    max-width: 70px;
                }

                span {
                    font-size: 14px;

                    & + span {
                        margin-left: 8px;
                    }
                }
            }
        }

        .registration-content {
            padding: 16px;

            .row-line {
                display: flex;
                align-content: flex-start;
                font-size: 14px;
                font-weight: 400;
                line-height: 20px;

                & + .row-line {
                    margin-top: 6px;
                }

                &.created-info {
                    margin-top: 6px;
                    margin-bottom: 0;
                    font-size: 14px;
                    font-weight: 400;

                    .row-content {
                        color: $T2;
                    }
                }

                label {
                    display: inline-block;
                    width: 40px;
                    color: $T2;
                    text-align: left;
                }

                .row-content {
                    flex: 1;
                    color: $S1;

                    &.time-status {
                        display: flex;
                        justify-content: space-between;
                        width: 100%;

                        .status {
                            .waiting-sign-in {
                                color: $Y1;
                            }

                            .waiting-diagnose,
                            .continue-diagnose {
                                color: $theme1;
                            }

                            .diagnosed {
                                color: #556d8b;
                            }
                        }
                    }

                    &.pay-status {
                        display: flex;

                        .price {
                            margin-right: 8px;
                        }

                        .not-received {
                            color: $Y2;
                        }

                        .received {
                            color: $G2;
                        }

                        .part-paid {
                            color: $T2;

                            .part-pay {
                                color: #ff9933;
                            }
                        }

                        .fee-detail-info {
                            margin-left: 4px;
                        }
                    }
                }
            }
        }

        .pay-status {
            display: flex;
            align-self: center;
            height: 18px;
            font-size: 14px;
            font-weight: 400;
            line-height: 18px;
            color: $T2;

            .description {
                margin-left: 4px;
            }

            .part-paid {
                display: inline-flex;
                align-self: center;

                .price {
                    margin-left: 4px;
                    font-size: 14px;
                    font-weight: 400;
                    color: #ff9933;
                }
            }

            .fee-detail-info {
                margin-left: 4px;
                cursor: pointer;
            }
        }

        .footer-btn-wrapper {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            height: 44px;
            padding-right: 16px;
        }
    }

    .edit-status-wrapper {
        position: relative;
        width: 504px;
        background-image: linear-gradient(#fddba1, #ffffff 16%, #ffffff);
        border-radius: var(--abc-dialog-border-radius);

        .content-wrapper {
            padding: var(--abc-paddingTB-xxl) var(--abc-paddingLR-xxl);
        }

        .appointment-base-card-form {
            min-height: 408px;
            margin-top: var(--abc-space-xxxl);

            &.show-reserve-product {
                min-height: 464px;
            }

            .form-content {
                margin-top: var(--abc-space-xxxl);

                .item {
                    display: flex;
                    align-items: center;

                    & + .item {
                        margin-top: 16px;
                    }

                    .category-select,
                    .revisit-status-select,
                    .products-list-select,
                    .visit-source-cascader,
                    .registration-remark {
                        .abc-input__inner {
                            padding-left: var(--abc-paddingLR-xl) !important;
                        }
                    }

                    &.time-item-wrapper {
                        .date-wrapper,
                        .fixed-select-wrapper,
                        .date-select-wrapper,
                        .order-no-and-time-wrapper,
                        .flexible-date-wrapper,
                        .flexible-select-wrapper,
                        .time-range-picker,
                        .order-no-select-section-wrapper {
                            .abc-input__inner {
                                padding-left: var(--abc-paddingLR-xl) !important;
                                font-weight: 600;

                                &::placeholder {
                                    font-weight: normal;
                                }
                            }
                        }

                        .order-no-and-time-wrapper {
                            &.show-description {
                                .abc-input__inner {
                                    padding-left: 48px !important;
                                }
                            }
                        }

                        .flexible-date-wrapper {
                            &.has-shortcut {
                                .abc-date-picker__display-value {
                                    font-weight: 600;
                                }

                                .abc-input__inner {
                                    padding-left: 48px !important;
                                }
                            }
                        }

                        .time-range-picker {
                            .abc-time-range-picker__display-value {
                                padding-left: var(--abc-paddingLR-xl) !important;
                                font-weight: 600;
                            }
                        }
                    }

                    &.fee-item {
                        .fee-item-value {
                            display: inline-flex;
                            font-size: 0;

                            .abc-input-wrapper {
                                .abc-input__inner {
                                    margin-right: -1px;
                                    border-right-color: transparent;
                                    border-top-right-radius: 0;
                                    border-bottom-right-radius: 0;
                                }
                            }

                            .discount-wrapper {
                                position: relative;
                                display: flex;
                                align-items: center;
                                width: 246px;
                                height: 40px;
                                font-weight: 400;
                                cursor: pointer;
                                border: 1px solid var(--abc-color-P7);
                                border-left-color: transparent;
                                border-radius: 0 var(--abc-border-radius-small) var(--abc-border-radius-small) 0;

                                &:not(.disabled):hover {
                                    border-color: $theme3;
                                }

                                &.disabled {
                                    background-color: $abcBgDisabled;
                                }

                                &.active {
                                    border-color: #0270c9;
                                    box-shadow: 0 0 0 2px #c3e0fe;
                                }

                                .discount-popover-wrapper {
                                    width: 100%;
                                    height: 100%;
                                }

                                .discount-info-wrapper {
                                    display: flex;
                                    align-items: center;
                                    justify-content: flex-end;
                                    width: 100%;
                                    height: 100%;
                                    padding-right: 10px;

                                    .discount-money {
                                        line-height: 1;
                                        color: $T2;

                                        > span {
                                            font-size: 14px;
                                        }

                                        .money {
                                            color: #ff3333;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    .mr-info {
                        &.abc-input__inner {
                            display: inline-flex;
                            padding-left: var(--abc-paddingLR-xl) !important;
                        }
                    }

                    .abc-form-item {
                        .abc-form-item-content {
                            .date-select-wrapper {
                                .abc-input__inner {
                                    padding-left: 4px;
                                }
                            }

                            .not-exist-products {
                                .abc-input__inner {
                                    border-color: $Y2;
                                }
                            }
                        }
                    }
                }

                .additional-info {
                    margin-top: var(--abc-space-xxxl);
                }
            }

            .patient-section-wrapper {
                .patient-section__form-wrapper {
                    .patient-mobile-autocomplete {
                        .abc-input__inner {
                            padding-left: 7px !important;
                        }
                    }

                    .patient-display-mobile {
                        width: 172px !important;

                        &.no-card-btn-wrapper {
                            width: 208px !important;
                        }
                    }
                }

                .add-patient-wrapper_flex {
                    &--left {
                        border-right: 1px solid var(--abc-color-P7) !important;
                        border-radius: var(--abc-border-radius-small) !important;

                        &:hover {
                            border-right: 1px solid rgba(2, 10, 26, 0.04) !important;
                        }

                        &:active {
                            border-right: 1px solid rgba(2, 10, 26, 0.08) !important;
                        }
                    }
                }
            }
        }

        .footer-btn {
            position: relative;
            height: 64px;
            padding: 0 var(--abc-space-xxl);
        }

        .form-cover {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: 9;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.01);
        }
    }

    .medical-record-dialog {
        .abc-form {
            width: 100%;
        }
    }

    .abc-cascader-box .abc-cascader-icon {
        z-index: 9;
    }

    .dropdown-list-wrapper {
        z-index: 1992;
    }
}

.custom-part-paid-popper-style {
    width: 220px;
    padding: 0;

    .popover-content {
        min-height: 125px;
        max-height: 300px;

        .popover-item {
            display: flex;
            align-self: center;
            justify-content: space-between;
            font-size: 12px;
            font-weight: 400;

            &.label {
                color: $T2;
            }
        }

        .split-line {
            width: 100%;
            height: 1px;
            margin: 8px 0;
            background: $P4;
        }

        .charge-sheet-list {
            line-height: 16px;

            .charge-sheet-actions {
                font-size: 12px;
                font-weight: 400;
                color: $T2;

                .charge-item {
                    display: flex;
                    align-self: center;
                    justify-content: space-between;

                    .person {
                        margin-left: 8px;
                    }

                    .price {
                        color: $S1;
                    }
                }
            }
        }
    }
}

.custom-pay-fee-popover {
    padding: var(--abc-paddingTB-xl) var(--abc-paddingLR-xl);

    .price-wrapper {
        padding: var(--abc-paddingTB-xl) 0 var(--abc-paddingTB-m);

        .price-item {
            & + .price-item {
                margin-top: var(--abc-paddingTB-m);
            }
        }
    }
}

.discount-popover {
    .same-time-charge-card {
        // 挂号折扣部分信息
        .registration-discount {
            width: 100%;

            &.discount-table-wrapper {
                margin: 0 !important;
                border: none !important;

                .promotion-name > img {
                    display: none;
                }

                .table-header {
                    .totalPrice {
                        padding-right: 0;
                    }
                }

                .table-body {
                    .discount-tr {
                        display: flex;
                        align-items: center;
                        min-height: 40px;
                        padding: 4px;

                        .title-icon {
                            align-self: center;
                            padding: 2px 4px;
                            margin-right: 6px;
                            line-height: 1;
                            border-radius: 9px;
                        }

                        &.is-simple .title-icon {
                            min-width: 40px;
                        }

                        .discount-info,
                        .discount-info-item {
                            line-height: 18px;

                            > button {
                                height: 18px;
                                line-height: 18px;
                            }
                        }

                        .discount-total-price {
                            align-self: center !important;
                        }
                    }

                    .td.name {
                        align-items: center;
                        padding-right: 0;

                        .title {
                            min-width: 20px;
                            padding: 2px;
                            margin-top: 2px;
                            margin-right: 8px;
                            border-radius: 2px;
                        }

                        .opt-button-wrapper {
                            min-width: 34px;
                            padding-left: 0;
                        }

                        .promotion-detail-content {
                            padding-left: 0;
                        }

                        .label {
                            max-width: 140px;
                            margin-right: 4px;

                            > img {
                                display: none;
                            }
                        }

                        .promotion-item .promotion-name {
                            max-width: 140px;
                        }

                        > img {
                            display: none;
                        }

                        > .abc-button-text {
                            margin-left: auto;
                        }

                        .member-info {
                            max-width: 140px;
                        }
                    }

                    .td.discount-detail {
                        padding-right: 0;

                        &.total-price {
                            width: 50px !important;
                            min-width: 50px !important;
                            max-width: 50px !important;
                        }

                        .iconfont {
                            margin-right: 4px;
                        }
                    }

                    .abc-button-text {
                        min-width: 30px;
                    }
                }
            }
        }
    }
}

.patient-sex-options {
    .option-item-wrapper {
        .abc-option-item {
            padding: 6px;
        }
    }
}

.order-no-select {
    .empty-shift {
        height: 40px;
        font-size: 14px;
        line-height: 40px;
        color: $T3;
        text-align: center;
    }
}

.appointment-order-no-select-popper {
    left: -248px !important;
}

.describe-stop-work {
    color: var(--abc-color-Y2) !important;
}

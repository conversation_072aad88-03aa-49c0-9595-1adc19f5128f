<template>
    <div data-cy="ql-footer-wrapper" class="quick-footer-wrapper">
        <div class="quick-footer-title">
            <abc-tabs
                v-model="tabValue"
                size="small"
                :custom-gap="16"
                disable-indicator
                :border-style="{ borderBottom: 'none' }"
                :option="quickFooterTabsOption"
                @change="changeFooterTab"
            ></abc-tabs>
        </div>
        <div class="quick-footer-content">
            <template v-if="tabValue === 0">
                <call-number
                    v-show="visibleCallControl"
                    v-model="openCtrlView"
                    placement="right-end"
                    :doctor-id="showAllDoctor ? '' : userInfo?.id"
                    @active-call="(id) => (activeCallId = id)"
                >
                    <template #btn>
                        <abc-check-access>
                            <div
                                class="entry-item"
                                @click="openCtrlView = !openCtrlView"
                            >
                                <img src="~assets/images/icon/<EMAIL>" alt="" />
                                <div class="content">
                                    叫号
                                </div>
                                <div class="describe">
                                    下一位 {{ nextCallingText }}
                                    <abc-icon icon="Arrow_Rgiht"></abc-icon>
                                </div>
                            </div>
                        </abc-check-access>
                    </template>
                </call-number>

                <div class="entry-item" @click="handleClickEntryItem('work')">
                    <img src="~assets/images/icon/<EMAIL>" alt="" />
                    <div class="content">
                        门诊看板
                    </div>
                    <div class="describe">
                        医生工作汇总
                        <abc-icon icon="Arrow_Rgiht"></abc-icon>
                    </div>
                </div>

                <div v-if="isSupportNewRegistrationBoard" class="entry-item" @click="handleClickEntryItem('board')">
                    <img src="~assets/images/icon/<EMAIL>" alt="" />
                    <div class="content">
                        预约看板
                    </div>
                    <div class="describe">
                        挂号预约情况
                        <abc-icon icon="Arrow_Rgiht"></abc-icon>
                    </div>
                </div>

                <div v-else class="entry-item" @click="handleClickEntryItem('date')">
                    <img src="~assets/images/icon/<EMAIL>" alt="" />
                    <div class="content">
                        号源看板
                    </div>
                    <div class="describe">
                        挂号预约情况
                        <abc-icon icon="Arrow_Rgiht"></abc-icon>
                    </div>
                </div>

                <div class="entry-item" @click="handleClickEntryItem('pill')">
                    <img src="~assets/images/icon/<EMAIL>" alt="" />
                    <div class="content">
                        用药助手
                    </div>
                    <div class="describe">
                        安全合理用药
                        <abc-icon icon="Arrow_Rgiht"></abc-icon>
                    </div>
                </div>
            </template>

            <template v-if="hasOutpatientHistory">
                <outpatient-history
                    v-show="tabValue === 1"
                    :id="selectedPatient ? selectedPatient.id : ''"
                    from-module="outpatient"
                    has-copy
                    has-copy-mr
                    @copy="copyHandler"
                    @history-list-change="onHistoryListChange"
                ></outpatient-history>
            </template>
        </div>

        <outpatient-work-kanban-dialog v-if="showSummeryDialog" v-model="showSummeryDialog"></outpatient-work-kanban-dialog>
        <appointment-kanban-dialog v-if="showBulletinBoard" v-model="showBulletinBoard"></appointment-kanban-dialog>
        <registration-board-dialog v-if="showRegistrationBoard" v-model="showRegistrationBoard"></registration-board-dialog>
    </div>
</template>

<script>
    import {
        mapGetters,
        mapState,
    } from 'vuex';
    // utils
    import LocalStorage from 'utils/localStorage-handler';
    import AbcAccess from '@/access/utils.js';
    import ModulePermission from 'views/permission/module-permission';
    import { RESERVATION_MODE_TYPE } from 'views/settings/registered-reservation/constant';
    import MedicineInstructionsDialog from 'views/layout/medicine-instructions';
    // views
    const CallNumber = () => import('views/layout/call-number');
    import AppointmentKanbanDialog from 'views/outpatient/common/appointment-kanban-dialog/index.vue';
    import RegistrationBoardDialog from 'views/outpatient/common/registration-board-dialog';
    import OutpatientWorkKanbanDialog from 'views/outpatient/common/outpatient-work-summary-dialog/index.vue';
    import OutpatientHistory from 'views/layout/patient/outpatient-history/patient-outpatient-history.vue';

    export default {
        name: 'QuickFooter',
        components: {
            CallNumber,
            AppointmentKanbanDialog,
            RegistrationBoardDialog,
            OutpatientWorkKanbanDialog,
            OutpatientHistory,
        },
        mixins: [ ModulePermission ],
        data() {
            const tabValue = LocalStorage.get('outpatient_ql_tab', true) || 0;
            return {
                tabValue: +tabValue,
                openCtrlView: false,
                activeCallId: '', // 主动呼叫id

                showSummeryDialog: false,
                showBulletinBoard: false,
                showRegistrationBoard: false,
            };
        },
        computed: {
            ...mapState('call', [
                'callingId',
                'nextCalling',
            ]),
            ...mapGetters([
                'isOpenCall',
                'userInfo',
                'registrationsConfig',
                'clientWidth',
            ]),
            ...mapGetters('viewDistribute',[
                'viewDistributeConfig',
            ]),
            quickFooterTabsOption() {
                const _arr = [
                    {
                        label: '小工具',
                        value: 0,
                    },
                ];
                if (this.hasOutpatientHistory) {
                    _arr.push({
                        label: '就诊历史',
                        value: 1,
                        statisticsNumber: this.patientHistoryCount,
                    });
                }
                return _arr;
            },
            // 叫号器显示与否
            visibleCallControl() {
                if (!AbcAccess.getPurchasedByKey(AbcAccess.accessMap.CALLING_NUM)) return false;
                if (!this.isOpenCall) return false;
                // 门店管理员、医生权限、医助  均显示叫号器
                return this.isClinicAdmin || this.canDiagnosis || this.permissionHasAssistant;
            },
            // 展示排班列表，显示全部医生
            showAllDoctor() {
                if (this.isClinicAdmin || this.hasDoctorHelperModule) {
                    return true;
                }
                return false;
            },
            nextCallingText() {
                const {
                    patientName,
                    orderNo,
                    timeOfDay,
                    isAdditional,
                } = this.nextCalling || {};
                if (!patientName) {
                    return '无';
                }
                let orderNoStr = '';
                if (isAdditional) {
                    orderNoStr = `+${orderNo}`;
                } else {
                    orderNoStr = orderNo > 9 ? orderNo : `0${orderNo}`;
                }
                if (this.isFixOrderMode) {
                    return `${timeOfDay}${orderNoStr}号 ${patientName}`;
                }
                return patientName;
            },
            isFixOrderMode() {
                // modeType 0: 固定号源模式 1: 灵活时间模式
                return this.registrationsConfig.modeType === RESERVATION_MODE_TYPE.FIXED_NUMBER;
            },
            isSupportNewRegistrationBoard() {
                return this.viewDistributeConfig?.Outpatient?.supportNewRegistrationBoard;
            },
            hasOutpatientHistory () {
                return this.clientWidth <= 1096;
            },
        },
        methods: {
            changeFooterTab() {
                LocalStorage.set('outpatient_ql_tab', this.tabValue);
            },
            handleClickEntryItem(type) {
                if (type === 'work') {
                    this.showSummeryDialog = true;
                } else if (type === 'date') {
                    this.showBulletinBoard = true;
                } else if (type === 'pill') {
                    new MedicineInstructionsDialog({
                        store: this.$store,
                        value: true,
                    }).generateDialog({ parent: this });
                } else if (type === 'board') {
                    this.showRegistrationBoard = true;
                }
            },
        },
    };
</script>

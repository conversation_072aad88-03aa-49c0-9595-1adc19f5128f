<template>
    <abc-container-center class="content-container abc-dentistry__content-container">
        <abc-content-empty :value="text"></abc-content-empty>
    </abc-container-center>
</template>

<script>
    import { CrmQlTabsEnum } from '@/views-dentistry/crm/constants';

    export default {
        data() {
            return {
                text: '',
            };
        },
        watch: {
            '$route.query': {
                async handler(query) {
                    const { tab } = query;
                    if (tab === CrmQlTabsEnum.OUTPATIENT) {
                        this.text = '暂无门诊单';
                    } else if (tab === CrmQlTabsEnum.CONSULT) {
                        this.text = '暂无咨询单';
                    } else {
                        this.text = '暂无';
                    }
                },
                immediate: true,
            },
        },
    };
</script>

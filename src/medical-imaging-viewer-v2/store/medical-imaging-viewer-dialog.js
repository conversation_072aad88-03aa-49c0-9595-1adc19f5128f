import Vue from 'vue';
import {
    getPluginUrl, isSupportWebContents, openWindowInMatchedDisplay,
} from 'utils/electron';
import propertyAPI from 'api/property';
import { setIsEnableZoom$ } from 'views/layout/app-zoom-btn/app-zoom-service';
import { PROTOCOL } from 'utils/constants';
import cookieService from '@/cookie-service';
import {
    ABCYUN_TOKEN, ABCYUN_TOKEN_STORAGE,
} from 'utils/local-storage-key';

export const medicalImagingViewerDialogStore = Vue.observable({
    visible: false,
    studyInstanceUid: '',
    index: 0,
    lastWindowPreferences: {},
    isLoadingPlugin: false,
    enableDownload: false,
    patientId: '',
});

let globalMedicalViewerWindow = null;

function reset() {
    medicalImagingViewerDialogStore.studyInstanceUid = '';
    medicalImagingViewerDialogStore.index = 0;
}

async function openDialog({
    studyInstanceUid = '',
    seriesInstanceUid = '',
    enableDownload = false,
    patientId = '',
    isEnablePacsUpgrade = false,
} = {}) {
    if (!studyInstanceUid) {
        console.error('studyInstanceUid required');
        return;
    }
    // 避免反复点击加载插件
    if (medicalImagingViewerDialogStore.isLoadingPlugin) {
        return;
    }
    medicalImagingViewerDialogStore.studyInstanceUid = studyInstanceUid;
    medicalImagingViewerDialogStore.isLoadingPlugin = true;
    medicalImagingViewerDialogStore.enableDownload = enableDownload;
    medicalImagingViewerDialogStore.patientId = patientId;
    const AbcMedicalImagingViewerPluginUrl = await getPluginUrl('abc-medical-imaging-viewer-plugin');
    medicalImagingViewerDialogStore.isLoadingPlugin = false;

    // 支持插件
    if (AbcMedicalImagingViewerPluginUrl) {
        window.electron.remote.app.registerFileSchemeForAutoFillAbcyunCookie?.(AbcMedicalImagingViewerPluginUrl);
        const canWebContents = isSupportWebContents(); //客户端1.8.0及以上才支持window.remote.app.mainWindow.webContents
        enableDownload = canWebContents && enableDownload;
        const windowOptions = {
            resizable: true,
            backgroundColor: '#000',
        };
        const {
            position,
            isMaximized,
        } = medicalImagingViewerDialogStore.lastWindowPreferences;
        if (position) {
            windowOptions.x = position[0];
            windowOptions.y = position[1];
        }
        if (!globalMedicalViewerWindow) {
            const updateWindowState = () => {
                if (!globalMedicalViewerWindow) return;
                const position = globalMedicalViewerWindow.getPosition();
                const isMaximized = globalMedicalViewerWindow.isMaximized();
                medicalImagingViewerDialogStore.lastWindowPreferences = {
                    position,
                    isMaximized,
                };
            };
            const { data } = await propertyAPI.getV3('examination.settings.inspect.pacsSettings', 'clinic');
            const {
                interfacePort, interfaceIp,
            } = data;
            const dicomServer = `https://${interfaceIp}:${interfacePort}`;
            const dicomPluginServer = location.origin.replace(PROTOCOL.ABCYUN, PROTOCOL.HTTPS);
            const finalToken = await cookieService.get(ABCYUN_TOKEN) || window.localStorage.getItem(ABCYUN_TOKEN_STORAGE);
            globalMedicalViewerWindow = openWindowInMatchedDisplay(`${AbcMedicalImagingViewerPluginUrl}#/?studyInstanceUid=${studyInstanceUid}&seriesInstanceUid=${seriesInstanceUid}&dicomServer=${dicomServer}&dicomPluginServer=${dicomPluginServer}&enableDownload=${enableDownload}&patientId=${patientId}&isEnablePacsUpgrade=${isEnablePacsUpgrade}&token=${finalToken}`, windowOptions);
            ['resized', 'moved', 'maximize', 'unmaximize'].forEach((eventName) => {
                globalMedicalViewerWindow.on(eventName, () => {
                    updateWindowState();
                });
            });
            globalMedicalViewerWindow.on('close', () => {
                globalMedicalViewerWindow = null;
            });
        } else {
            if (!globalMedicalViewerWindow) return;
            window.remote.BrowserWindow.fromId(globalMedicalViewerWindow.id).webContents.send('abc-medical-viewer-plugin:switch-id', {
                studyInstanceUid,
                seriesInstanceUid,
                enableDownload,
                patientId,
            });
        }

        if (isMaximized && globalMedicalViewerWindow) {
            globalMedicalViewerWindow.maximize();
        }
    } else {
        medicalImagingViewerDialogStore.visible = true;
    }
    setIsEnableZoom$(false);
}

function closeDialog() {
    reset();
    medicalImagingViewerDialogStore.visible = false;
    setIsEnableZoom$(true);
    if (typeof medicalImagingViewerDialogStore.beforeClose === 'function') {
        medicalImagingViewerDialogStore.beforeClose();
        medicalImagingViewerDialogStore.beforeClose = undefined;
    }
}

export const medicalImagingViewerDialogService = {
    openDialog,
    closeDialog,
    reset,
};

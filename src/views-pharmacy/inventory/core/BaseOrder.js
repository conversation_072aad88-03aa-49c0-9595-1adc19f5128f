import Model from './orderModel';

export default class BaseOrder {
    /**
     * @param {import('./order.d.ts').IOptions} [options]
     */
    constructor(options) {
        const {
            orderId,
            goodsId,
            currentClinic,
            userInfo,
            clinicConfig,
            goodsConfig,
            multiPharmacyCanUse,
            isAdmin,
            isChain,
            isChainAdmin,
            isSingleStore,
            isChainSubStore,
        } = options || {};

        this.orderId = orderId;
        this.goodsId = goodsId;

        this.store = {
            currentClinic,
            userInfo,
            clinicConfig,
            goodsConfig,
            multiPharmacyCanUse,
            isAdmin,
            isChain,
            isChainAdmin,
            isSingleStore,
            isChainSubStore,
        };

        // 单据基础数据
        this.model = new Model();
    }

    get order() {
        return this.model.getOrder();
    }

    // 数据初始化
    initOrder(order) {
        this.model = new Model(order);
    }

    sortOrderList(order) {
        if (!this.goodsId) return;

        order.list?.sort((a, b) => {
            return (b.goodsId === this.goodsId) - (a.goodsId === this.goodsId);
        });
    }

    //<editor-fold desc="抽象方法">
    // 创建单据
    createOrder() {}
    // 修改单据
    updateOrder() {}
    // 单据详情
    fetchOrderDetail() {}
    // 单据审批信息
    fetchOrderGspDetail() {}
    // 删除单据
    deleteOrder() {}
    // 审核通过
    resolveOrder() {}
    // 审核驳回
    rejectOrder() {}
    // 撤回单据
    revokeOrder() {}
    // 确认单据
    confirmOrder() {}
    // 打印单据
    printOrder() {}
    //</editor-fold>

}

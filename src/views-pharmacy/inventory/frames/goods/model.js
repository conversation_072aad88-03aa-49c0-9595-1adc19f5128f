// 负责数据逻辑和状态管理

export default class Model {
    constructor(obj = {}) {
        this.searchKey = '';
        this.selectedTypes = [];
        // 选中的预警条件
        this.warningKey = '';
        this.warningKeys = [];
        this.fetchParams = {
            keyword: '',
            // 门店id
            clinicId: '',
            goodsId: '',
            // !利润分类
            profitCategoryType: '',
            profitCategoryTypeList: [],
            // 毛利率范围
            minProfitRat: '',
            maxProfitRat: '',
            // 查询的药品类型
            type: '1,2,7,24',
            typeId: [], // 多选一级分类参数
            customTypeId: [], // 多选二级分类参数
            orderBy: '',
            orderType: '',
            offset: 0,
            limit: 50,// 默认50
            sbOverPrice: 0, // 超过限价
            sbGoingOverPrice: 0, // 即将超过限价
            sbNotOverPrice: 0, // 未超限

            sbGoingExpired: 0, // 即将失效
            sbNotExpired: 0, // 未失效
            sbExpired: 0, // 已失效

            // 市码
            sbNotMatched: 0, // 未对码
            sbMatched: 0, // 已对码
            sbNotPermit: 0, // 不允许社保支付

            // 省码
            sbProMatched: 0, // 未对码
            sbProNotMatched: 0,
            sbProNotPermit: 0,

            // 国家码
            sbNationalMatched: 0, // 已对码
            sbNationalNotMatched: 0, // 未对码
            sbNationalNotPermit: 0, // 不允许医保支付

            // 医保等级：1.甲类 2.乙类 3.丙类 100.其他
            medicalFeeGrade: [],

            // 医保支付：0 正常支付  1 个账支付 2 禁止医保支付
            shebaoPayMode: [],

            onlyStock: 0, // 库存大于0
            disable: undefined, // 含停用药品
            pharmacyType: obj.pharmacyType ?? '', // 药房类型
            pharmacyNo: obj.pharmacyNo ?? '', // 药房编号

            // goods扩展查询参数
            otcType: [], // 处方药/OTC
            businessScopeId: undefined, // 经营范围
            baseMedicineType: [], // 基药
            maintainType: [], // 养护分类
            storage: undefined, // 存储条件
            hasBarCode: undefined, // 是否有条码
            erpCode: undefined, // erp编码
            remark: undefined, // 备注
            // 眼镜参数
            isSpu: '',
            spuGoodsCondition: {
                brandName: '',
                refractiveIndex: '',
                customType: '',
                addLight: '',
                wearCycle: '',
                material: '',
                spherical: '',
                focalLength: '',
                lenticular: '',
                spec: '',
                color: '',
            },
            tagId: [],
            unPrintPriceWarn: '',
            queryType: '',
        };
        this.panelData = {
            goodsHeader: [],
            rows: [],
            count: 0,
            saleCount: 0,
            totalCostPrice: 0,
            totalPrice: 0,
            expiredWarnCount: 0,
            profitRatWarnCount: 0,
            stockWarnCount: 0,
            stockWarnShortageCount: 0,//这个接口还是返回的stockWarnCount
            stockWarnTurnoverDays: 0,
            willExpiredDate: '',
            shebaoNationalMatchCount: 0, // 国 医保数
            medicalFeeGradeACount: 0, // 医保甲类总数
            medicalFeeGradeBCount: 0, // 医保乙类总数
            medicalFeeGradeCCount: 0, // 医保丙类总数
            medicalFeeGradeCount: 0, // 医保总数
        };
    }

    setState(key, val) {
        this[key] = val;
    }

    setParams(key, val) {
        this.fetchParams[key] = val;
    }
}

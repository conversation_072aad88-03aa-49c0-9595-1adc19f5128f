import { FunctionalDialog } from '@/views/common/functional-dialog.js';
const template = () => import('../form.vue');

export default class PriceAdjustmentDialog extends FunctionalDialog {
    constructor(props) {
        super(props, template, 'price-adjustment-dialog', 'showDialog');
    }

    generateDialog(extendProps = {}) {
        super.generateDialog(extendProps);
        this.instance.$on('input', (val) => {
            if (!val) {
                this.instance?.dom?.parentNode && this.instance.dom.parentNode.removeChild(this.instance.dom);
                this.destroyDialog();
            }
        });
    }
}

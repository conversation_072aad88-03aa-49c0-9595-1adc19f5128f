<template>
    <abc-layout preset="page-table">
        <abc-layout-header>
            <abc-flex justify="space-between">
                <abc-space>
                    <abc-date-picker
                        v-model="selectDate"
                        :picker-options="pickerOptions"
                        placeholder="选择提交日期范围"
                        type="daterange"
                        value-format="YYYY-MM-DD"
                        @change="changeDate"
                    >
                    </abc-date-picker>

                    <abc-select
                        v-if="!isReview"
                        v-model="fetchParams.status"
                        :width="100"
                        clearable
                        placeholder="状态"
                        @change="initOffset"
                    >
                        <abc-option
                            v-for="item in _statusOptions"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                        ></abc-option>
                    </abc-select>
                    <abc-select
                        v-else
                        v-model="fetchParams.status"
                        :width="100"
                        clearable
                        placeholder="状态"
                        @change="initOffset"
                        @close="showTotalCount = true"
                        @open="showTotalCount = false"
                    >
                        <abc-option
                            v-for="item in _statusOptions"
                            :key="item.id"
                            :label="item.label"
                            :statistics-number="tableData.todoCount && (item.value === 0 || item.value === '') ? tableData.todoCount : undefined"
                            :value="item.value"
                        >
                            {{ item.label }}
                        </abc-option>
                    </abc-select>

                    <!--                    <abc-select-->
                    <!--                        v-model="fetchParams.dateField"-->
                    <!--                        :width="90"-->
                    <!--                        @change="initOffset"-->
                    <!--                    >-->
                    <!--                        <abc-option :value="'createdDate'" label="提交日期"></abc-option>-->
                    <!--                        <abc-option :value="'reviewDate'" label="审核日期"></abc-option>-->
                    <!--                    </abc-select>-->



                    <abc-select
                        v-model="fetchParams.supplierId"
                        :fetch-suggestions="_filterSuppliers"
                        :max-width="240"
                        :width="150"
                        custom-class="supplierWrapper"
                        with-search
                        clearable
                        placeholder="供应商"
                        @change="initOffset"
                    >
                        <abc-option
                            v-for="it in currentSuppliers"
                            :key="`${it.id }`"
                            :label="it.name"
                            :value="it.id"
                        ></abc-option>
                    </abc-select>

                    <clinic-select
                        v-if="isChainAdmin"
                        v-model="fetchParams.clinicId"
                        :width="150"
                        placeholder="总部/门店"
                        :show-all-clinic="false"
                        @change="initOffset"
                    ></clinic-select>
                </abc-space>

                <abc-check-access v-if="!isReview">
                    <abc-button
                        theme="success"
                        icon="s-b-add-line-medium"
                        @click="addOrder"
                    >
                        新增付款单
                    </abc-button>
                </abc-check-access>
            </abc-flex>
        </abc-layout-header>
        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                ref="table-settlement"
                :data-list="tableData.rows"
                :empty-opt="{ label: '暂无数据' }"
                :loading="loading"
                :render-config="tableConfig"
                @handleClickTr="openDetailDialog"
            >
                <template #orderNo="{ trData: item }">
                    <abc-table-cell class="ellipsis" style="color: var(--abc-color-C2);">
                        <template v-if="item.status === SETTLEMENT_STATUS_ENUM.DRAFT">
                            <span
                                v-abc-title.ellipsis="`最后修改：${ item.lastModifiedDate ? formatCacheTime(item.lastModifiedDate) : '-'}`"
                            ></span>
                        </template>
                        <span v-else v-abc-title="item.orderNo"></span>
                    </abc-table-cell>
                </template>
                <template #status="{ trData: item }">
                    <abc-table-cell>
                        <abc-tag-v2 v-bind="statusConfig(item)">
                            {{ formatStatusName(item.status) }}
                        </abc-tag-v2>
                    </abc-table-cell>
                </template>
                <template #supplierName="{ trData: item }">
                    <abc-table-cell>
                        <span v-abc-title.ellipsis="item.supplierObj && item.supplierObj.name">
                            {{ item.supplierObj && item.supplierObj.name }}
                        </span>
                    </abc-table-cell>
                </template>

                <template #clinicName="{ trData: item }">
                    <abc-table-cell>
                        <abc-tooltip :disabled="!item.orderClinicList">
                            <span>
                                {{ item.orderClinicList?.map(formatClinicName)?.join('、') ?? '' }}
                            </span>
                            <template slot="content">
                                <abc-flex vertical>
                                    <abc-text v-for="organ in (item.orderClinicList || [])" :key="organ.id">
                                        {{ formatClinicName(organ) }}
                                    </abc-text>
                                </abc-flex>
                            </template>
                        </abc-tooltip>
                    </abc-table-cell>
                </template>
                <template #amount="{ trData: item }">
                    <abc-table-cell>
                        {{ item.amount | formatMoney }}
                    </abc-table-cell>
                </template>
                <template #amountExcludingTax="{ trData: item }">
                    <abc-table-cell>
                        {{ item.amountExcludingTax | formatMoney }}
                    </abc-table-cell>
                </template>
                <template #tax="{ trData: item }">
                    <abc-table-cell>
                        {{ item.tax | formatMoney }}
                    </abc-table-cell>
                </template>
                <template #createdDate="{ trData: item }">
                    <abc-table-cell>
                        {{ item.createdDate ? parseTime(item.createdDate, 'y-m-d', true) : '-' }}
                    </abc-table-cell>
                </template>
                <template #createdUser="{ trData: item }">
                    <abc-table-cell>
                        {{ item.createdUser && item.createdUser.name }}
                    </abc-table-cell>
                </template>
                <template #reviewDate="{ trData: item }">
                    <abc-table-cell>
                        {{ item.reviewDate ? parseTime(item.reviewDate, 'y-m-d', true) : '-' }}
                    </abc-table-cell>
                </template>
                <template #reviewUser="{ trData: item }">
                    <abc-table-cell>
                        {{ (item.reviewUser && item.reviewUser.name) || '-' }}
                    </abc-table-cell>
                </template>
            </abc-table>
        </abc-layout-content>

        <abc-layout-footer>
            <abc-pagination
                :count="tableData.count"
                :pagination-params="pageParams"
                :show-total-page="false"
                @current-change="pageTo"
            >
                <ul v-if="tableData.count > 0" slot="tipsContent">
                    <li>
                        共 <span>{{ tableData.count }}</span> 个结算单， 价税合计
                        <span>{{ tableData.amount | formatMoney }}</span>
                    </li>
                </ul>
            </abc-pagination>
        </abc-layout-footer>

        <goods-settlement-application-form
            v-if="showForm"
            :id="detailId"
            v-model="showForm"
            :gsp-inst-id="gspInstId"
            @update="updateList"
        ></goods-settlement-application-form>
        <goods-settlement-review-form
            v-if="showDetail"
            :id="detailId"
            v-model="showDetail"
            :gsp-inst-id="gspInstId"
            @update="updateList"
        ></goods-settlement-review-form>
    </abc-layout>
</template>

<script>
    import GoodsSettlementApplicationForm from './application-form.vue';
    import GoodsSettlementReviewForm from './review-form.vue';

    import PickerOptions from 'views/common/pickerOptions';
    import GoodsCommon from './common/mixin.js';
    import {
        SETTLEMENT_TABLE_CONFIG, STATUS_OPTION, formatStatusName, SETTLEMENT_STATUS_ENUM,
    } from './common.js';
    import {
        formatCacheTime, parseTime,
    } from '@/utils';


    import SupplierAPI from 'api/goods/supplier';
    import SettlementAPI from 'api/goods/settlement';
    import { CHECK_IN_SUPPLIER_ID } from 'views/inventory/constant.js';
    import { debounce } from '@abc/utils';
    import { TagHelper } from 'utils/tag-helper';
    import ClinicSelect from 'views/layout/clinic-select/clinic-select.vue';
    import { mapGetters } from 'vuex';
    export default {
        name: 'GoodsSettlement',
        components: {
            ClinicSelect,
            GoodsSettlementApplicationForm,
            GoodsSettlementReviewForm,
        },
        mixins: [PickerOptions, GoodsCommon],
        props: {
            pharmacyNo: Number,
            // 结算审核
            isSettlementReview: Boolean,
        },
        data() {
            return {
                SETTLEMENT_STATUS_ENUM,
                CHECK_IN_SUPPLIER_ID,
                selectDate: '',
                tableData: {
                    rows: [],
                    count: 0,
                    todoCount: '',
                    amount: '',
                },
                loading: false,
                fetchParams: {
                    status: '',
                    dateField: 'createdDate',
                    begDate: '',
                    endDate: '',
                    clinicId: '',
                    offset: 0,
                    limit: 10,
                    supplierId: '',
                },
                currentSuppliers: [],
                showForm: false, // 新增结算
                showDetail: false,
                detailId: '',
                showTotalCount: false,

                // gsp审批单id
                gspInstId: '',
            };
        },
        computed: {
            ...mapGetters(['isChainAdmin','currentClinic']),
            pageParams() {
                const {
                    limit: pageSize, offset,
                } = this.fetchParams;
                const pageIndex = Math.ceil(offset / pageSize);
                return {
                    pageIndex,
                    pageSize,
                };
            },
            isReview() {
                return this.isSettlementReview;
            },
        },
        created() {
            this.tableConfig = SETTLEMENT_TABLE_CONFIG;
            this._statusOptions = STATUS_OPTION;
            this.showTotalCount = this.isReview;
            this._filterSuppliers = debounce(this.filterSuppliers, 1000, true);
            this._filterSuppliers();
        },
        methods: {
            parseTime,
            formatStatusName,
            formatCacheTime,
            handleMounted(data) {
                this.fetchParams.limit = data.paginationLimit;
                this.fetchData();
            },
            formatClinicName(item) {
                if (item.clinicId === this.currentClinic?.chainId) {
                    return '总部';
                }
                return item.shortName || item.name;
            },
            statusConfig(item) {
                const configMap = {
                    [SETTLEMENT_STATUS_ENUM.WAIT_REVIEW]: this.isReview ? TagHelper.TODO_TAG : TagHelper.ING_TAG,
                    [SETTLEMENT_STATUS_ENUM.SETTLED]: TagHelper.COMPLETE_TAG,
                    [SETTLEMENT_STATUS_ENUM.REVIEW_REFUSE]: TagHelper.REFUSE_TAG,
                    [SETTLEMENT_STATUS_ENUM.CANCEL]: TagHelper.CANCEL_TAG,
                    [SETTLEMENT_STATUS_ENUM.DRAFT]: TagHelper.DRAFT_TAG,
                };
                return configMap[item.status] || TagHelper.CANCEL_TAG;
            },
            changeDate(picker) {
                if (picker.length === 2) {
                    this.fetchParams.begDate = picker[0];
                    this.fetchParams.endDate = picker[1];
                } else {
                    this.fetchParams.begDate = '';
                    this.fetchParams.endDate = '';
                }
                this.fetchParams.offset = 0;
                this.fetchData();
            },
            initOffset() {
                this.fetchParams.offset = 0;
                this.fetchData();
            },
            async updateList(type = '') {
                if (type === 'add') {
                    this.fetchParams.offset = 0;
                }
                this.$store.dispatch('fetchInventoryTodo', this.pharmacyNo);
                await this.fetchData();
            },
            async fetchData() {
                try {
                    this.loading = true;
                    const { data } = await SettlementAPI.fetchSettlementList(this.fetchParams);
                    this.tableData.count = data.count;
                    this.tableData.amount = data.amount;
                    this.tableData.rows = data.rows || [];
                    this.tableData.todoCount = data.todoCount;
                } catch (e) {
                    console.error('fetchDataError', e);
                    // 防止重复提示
                    if (!e.alerted) {
                        this.$Toast({
                            message: e.message,
                            type: 'error',
                        });
                    }
                } finally {
                    this.loading = false;
                }
            },
            async filterSuppliers(keyword = '') {
                try {
                    keyword = keyword.trim();
                    const params = {
                        keyword,
                        clinicId: this.clinicId,
                        status: '',
                    };
                    const { data } = await SupplierAPI.searchSupplier(params);
                    this.currentSuppliers =
                        (data &&
                            data.rows &&
                            data.rows.filter((item) => {
                                return !!item && item.id !== this.CHECK_IN_SUPPLIER_ID;
                            })) ||
                        [];
                } catch (err) {
                    this.currentSuppliers = [];
                }
            },
            async pageTo(page) {
                this.fetchParams.offset = (page - 1) * this.fetchParams.limit;
                await this.fetchData();
            },
            addOrder() {
                this.detailId = '';
                this.gspInstId = '';
                this.showForm = true;
            },
            openDetailDialog(item) {
                if (item.version === 1) {
                    this.showForm = true;
                } else {
                    this.showDetail = true;
                }
                this.detailId = item.id;
                this.gspInstId = item.gspInstId || '';
            },
        },
    };
</script>

// eslint-disable-next-line max-classes-per-file
import BaseClinicTypeTable from 'views/layout/tables/base-clinic-type-table';

export class PurchaseTableConfig extends BaseClinicTypeTable {
    constructor(clinic) {
        super(clinic);
        this.chainTableConfig = {
            'hasInnerBorder': false,
            'list': [{
                'key': 'orderNo',
                'label': '单号',
                'pinned': true,
                'slot': true,
                'style': {
                    'flex': '0','width': '148px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'testValue': 'CG2024060300001',
            },{
                'key': 'comment',
                'label': '',
                'pinned': true,
                'style': {
                    'flex': '0','width': '20px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            },{
                'key': 'status',
                'label': '状态',
                'pinned': true,
                'slot': true,
                'style': {
                    'flex': '0','width': '100px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
                'testValue': ' 已申请',
            },{
                'key': 'kindCount',
                'label': '品种',
                'style': {
                    'flex': '0','width': '100px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'right',
                },
                'testValue': '100',
            },{
                'key': 'inOrderTotalCost',
                'label': '入库金额',
                'colType': 'money',
                'style': {
                    'flex': '0','width': '100px','maxWidth': '','paddingLeft': '','paddingRight': '16px','textAlign': 'right',
                },
                'testValue': '100.00',
            },{
                'key': 'applicantName',
                'label': '采购人',
                'style': {
                    'flex': '0','width': '148px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'testValue': '张无忌',
            },{
                'key': 'supplierName',
                'label': '供应商',
                'style': {
                    'flex': '1','width': '0px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'key': 'applicantOrganName',
                'label': '采购门店',
                'style': {
                    'flex': '1','width': '','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'hidden': false,
            },{
                'key': 'purchaseOrderDate',
                'label': '采购日期',
                'colType': 'date',
                'style': {
                    'flex': '0','width': '104px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'testValue': '2023-12-31',
            }],
        };
        this.chainSubTableConfig = {
            'hasInnerBorder': false,
            'list': [{
                'key': 'orderNo',
                'label': '单号',
                'pinned': true,
                'slot': true,
                'style': {
                    'flex': '0','width': '148px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'testValue': 'CG2024060300001',
            },{
                'key': 'comment',
                'label': '',
                'pinned': true,
                'style': {
                    'flex': '0','width': '20px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            },{
                'key': 'status',
                'label': '状态',
                'pinned': true,
                'slot': true,
                'style': {
                    'flex': '0','width': '100px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
                'testValue': ' 已申请',
            },{
                'key': 'kindCount',
                'label': '品种',
                'style': {
                    'flex': '0','width': '100px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'right',
                },
                'testValue': '100',
            },{
                'key': 'inOrderTotalCost',
                'label': '入库金额',
                'colType': 'money',
                'style': {
                    'flex': '0','width': '100px','maxWidth': '','paddingLeft': '','paddingRight': '16px','textAlign': 'right',
                },
                'testValue': '100.00',
            },{
                'key': 'applicantName',
                'label': '采购人',
                'style': {
                    'flex': '0','width': '148px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'testValue': '张无忌',
            },{
                'key': 'supplierName',
                'label': '供应商',
                'style': {
                    'flex': '1','width': '0px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'key': 'applicantOrganName',
                'label': '采购门店',
                'style': {
                    'flex': '1','width': '','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'hidden': false,
            },{
                'key': 'purchaseOrderDate',
                'label': '采购日期',
                'colType': 'date',
                'style': {
                    'flex': '0','width': '104px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'testValue': '2023-12-31',
            }],
        };
        this.singleTableConfig = {
            'hasInnerBorder': false,
            'list': [{
                'key': 'orderNo',
                'label': '单号',
                'pinned': true,
                'slot': true,
                'style': {
                    'flex': '0','width': '148px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'testValue': 'CG2024060300001',
            },{
                'key': 'comment',
                'label': '',
                'pinned': true,
                'style': {
                    'flex': '0','width': '20px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            },{
                'key': 'status',
                'label': '状态',
                'pinned': true,
                'slot': true,
                'style': {
                    'flex': '0','width': '100px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
                'testValue': ' 已申请',
            },{
                'key': 'kindCount',
                'label': '品种',
                'style': {
                    'flex': '0','width': '100px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'right',
                },
                'testValue': '100',
            },{
                'key': 'inOrderTotalCost',
                'label': '入库金额',
                'colType': 'money',
                'style': {
                    'flex': '0','width': '100px','maxWidth': '','paddingLeft': '','paddingRight': '16px','textAlign': 'right',
                },
                'testValue': '100.00',
            },{
                'key': 'applicantName',
                'label': '采购人',
                'style': {
                    'flex': '0','width': '148px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'testValue': '张无忌',
            },{
                'key': 'supplierName',
                'label': '供应商',
                'style': {
                    'flex': '1','width': '0px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'key': 'purchaseOrderDate',
                'label': '采购日期',
                'colType': 'date',
                'style': {
                    'flex': '0','width': '104px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'testValue': '2023-12-31',
            }],
        };
    }
}

export class ClaimTableConfig extends BaseClinicTypeTable {
    constructor(clinic) {
        super(clinic);
        this.chainTableConfig = {
            'hasInnerBorder': false,
            'list': [{
                'key': 'orderNo',
                'label': '单号',
                'pinned': true,
                'slot': true,
                'style': {
                    'flex': '0','width': '148px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'testValue': 'YH2024052100002',
            },{
                'key': 'comment',
                'label': '',
                'pinned': true,
                'style': {
                    'flex': '0','width': '20px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            },{
                'key': 'status',
                'label': '状态',
                'pinned': true,
                'slot': true,
                'style': {
                    'flex': '0','width': '100px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
                'testValue': '未审批',
            },{
                'key': 'kindCount',
                'label': '品种',
                'style': {
                    'flex': '0','width': '100px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'right',
                },
                'testValue': '10000',
            },{
                'key': 'inOrderTotalCost',
                'label': '入库金额',
                'colType': 'money',
                'style': {
                    'flex': '0','width': '100px','maxWidth': '','paddingLeft': '','paddingRight': '16px','textAlign': 'right',
                },
                'testValue': '0.23',
            },{
                'key': 'applicantName',
                'label': '要货人',
                'style': {
                    'flex': '0','width': '148px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'testValue': '张无忌',
            },{
                'key': 'applicantOrganName',
                'label': '要货门店',
                'style': {
                    'flex': '1','width': '','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'hidden': false,
                'testValue': '神奇康正云上雅居店',
            },{
                'key': 'purchaseOrderDate',
                'label': '要货日期',
                'colType': 'date',
                'style': {
                    'flex': '0','width': '104px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'testValue': '2024-12-31',
            }],
        };
        this.chainSubTableConfig = {
            'hasInnerBorder': false,
            'list': [{
                'key': 'orderNo',
                'label': '单号',
                'pinned': true,
                'slot': true,
                'style': {
                    'flex': '0','width': '148px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'testValue': 'YH2024052100002',
            },{
                'key': 'comment',
                'label': '',
                'pinned': true,
                'style': {
                    'flex': '0','width': '20px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            },{
                'key': 'status',
                'label': '状态',
                'pinned': true,
                'slot': true,
                'style': {
                    'flex': '0','width': '100px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
                'testValue': '未审批',
            },{
                'key': 'kindCount',
                'label': '品种',
                'style': {
                    'flex': '0','width': '100px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'right',
                },
                'testValue': '10000',
            },{
                'key': 'inOrderTotalCost',
                'label': '入库金额',
                'colType': 'money',
                'style': {
                    'flex': '0','width': '100px','maxWidth': '','paddingLeft': '','paddingRight': '16px','textAlign': 'right',
                },
                'testValue': '0.23',
            },{
                'key': 'applicantName',
                'label': '要货人',
                'style': {
                    'flex': '0','width': '148px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'testValue': '张无忌',
            },{
                'key': 'applicantOrganName',
                'label': '要货门店',
                'style': {
                    'flex': '1','width': '','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'hidden': false,
                'testValue': '神奇康正云上雅居店',
            },{
                'key': 'purchaseOrderDate',
                'label': '要货日期',
                'colType': 'date',
                'style': {
                    'flex': '0','width': '104px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'testValue': '2024-12-31',
            }],
        };
    }
}

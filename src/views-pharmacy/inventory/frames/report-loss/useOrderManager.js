import {
    ref, reactive,
} from 'vue';
import GoodsOutOrder from 'views/inventory/goods-out/GoodsOutOrder';

export function useOrderManager(options = {}) {
    const { orderId } = options;

    const goodsOutOrder = new GoodsOutOrder();

    // 可以直接orderRef.value重新赋值，它也是响应式的
    const orderRef = ref(goodsOutOrder.order);

    // orderRef会解包，order.name=xxx等于orderRef.value.name===xxx
    const order = reactive(orderRef);

    if (orderId) {
        // 这样调用this的指向是goodsOutOrder实例，会导致更新的数据没有响应式，所以需要再vue文件中去调用
        // goodsOutOrder.fetchOrderDetail(orderId);
    }

    return {
        order,
        fetchOrderDetail: goodsOutOrder.fetchOrderDetail,
    };
}

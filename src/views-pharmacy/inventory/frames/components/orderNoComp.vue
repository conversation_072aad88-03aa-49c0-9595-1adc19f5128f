<template>
    <abc-flex :gap="4">
        <span
            :style="{
                color: disabled ? 'var(--abc-color-T3)' : 'var(--abc-color-theme1)',
                cursor: disabled ? 'default' : 'pointer'
            }"
            :title="orders[0].orderNo"
            @click="handleClick(orders[0])"
        >
            {{ orders[0].orderNo }}
        </span>
        <abc-popover
            v-if="orders.length > 1"
            placement="bottom-end"
            trigger="hover"
            theme="white"
        >
            <abc-tag-v2
                slot="reference"
                size="mini"
                shape="round"
                theme="primary"
                variant="outline"
            >
                +{{ orders.length - 1 }}
            </abc-tag-v2>
            <div
                v-for="item in orders.slice(1)"
                :key="item.id"
                style="color: #005ed9; cursor: pointer;"
                @click="handleClick(item)"
            >
                {{ item.orderNo }}
            </div>
        </abc-popover>
    </abc-flex>
</template>

<script>
    import TakeDeliveryOrderDialog from '@/views-pharmacy/inventory/frames/purchase/take-delivery/dialog';
    import AcceptanceCheckOrderDialog from '@/views-pharmacy/inventory/frames/purchase/acceptance-check/dialog';
    import GoodsInOrderDialog from '@/views-pharmacy/inventory/frames/purchase/goods-in/dialog';
    import GoodsPurchaseOrderDialog from '@/views-pharmacy/inventory/frames/purchase/require-goods/dialog';
    import GoodsReturnDetailDialog from '@/views-pharmacy/inventory/frames/purchase/return-goods/detail-dialog';
    import { RelatedOrderType } from '@/views-pharmacy/inventory/constant';
    import {
        mapGetters,
    } from 'vuex';

    export default {
        name: 'OrderNoComp',
        props: {
            orders: {
                type: Array,
                default: () => ([]),
            },
            onlyView: {
                type: Boolean,
                default: false,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
        },
        computed: {
            ...mapGetters(['modulePermission']),
            hasStockInModule() {
                return !!this.modulePermission?.hasPharmacyStockInModule;
            },
            hasStockReturnModule() {
                return !!this.modulePermission?.hasPharmacyStockReturnModule;
            },
        },
        methods: {
            handleClick(order) {
                console.log(order);
                if (this.disabled) return;

                const dialogMap = {
                    [RelatedOrderType.RECEIPT]: TakeDeliveryOrderDialog,
                    [RelatedOrderType.INSPECT]: AcceptanceCheckOrderDialog,
                    [RelatedOrderType.GOODS_IN]: GoodsInOrderDialog,
                    [RelatedOrderType.PURCHASE]: GoodsPurchaseOrderDialog,
                    [RelatedOrderType.RETURN]: GoodsReturnDetailDialog,
                };
                if ((order.type === RelatedOrderType.RETURN && !this.hasStockReturnModule) || (order.type === RelatedOrderType.GOODS_IN && !this.hasStockInModule)) {
                    this.$alert({
                        type: 'warn',
                        title: '暂无访问权限',
                        content: '您暂时没有功能访问权限，请联系管理员开通',
                    });
                    return;
                }
                const Dialog = dialogMap[order.type];
                new Dialog({
                    orderId: order.id,
                    onlyView: this.onlyView,
                    disabled: this.onlyView,
                }).generateDialogAsync({
                    parent: this,
                });
            },
        },
    };
</script>

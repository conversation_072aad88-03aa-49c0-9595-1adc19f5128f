<template>
    <div
        v-abc-loading.small.coverOpaque="loading"
        class="popper-wrap"
        :style="{
            'padding': '12px',
        }"
    >
        <div v-for="it in detail" :key="it.orderId" class="box">
            <abc-space>
                <div class="box-status">
                    {{ PurchaseOrderStatusName[it.status] || '-' }}
                </div>
                <div class="box-no">
                    {{ it.orderNo || '-' }}
                </div>
                <div>
                    {{ `${it.packageCount || '0'} ${it.unit || '-'}` || '-' }}
                </div>
            </abc-space>
        </div>
        <abc-content-empty
            v-if="!detail.length"
            top="50%"
            :show-icon="false"
            value="暂无数据"
        ></abc-content-empty>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';
    import GoodsAPIV3 from 'api/goods/index-v3';
    import {
        PurchaseOrderStatusName,
    } from '@/views-pharmacy/inventory/constant';

    export default {
        name: 'PopoverStockCard',
        props: {
            row: {
                type: Object,
                required: true,
            },
            rowKey: {
                type: String,
                default: 'id',
            },
            currentHoverId: {
                type: String,
                default: '',
            },
            fetchParams: {
                type: Object,
                required: true,
            },
        },
        data() {
            return {
                PurchaseOrderStatusName,
                loading: false,
                detail: [],
            };
        },
        computed: {
            ...mapGetters(['isChainAdmin']),
        },
        watch: {
            currentHoverId: {
                handler(id) {
                    if (this.row[this.rowKey] === id) {
                        this.fetchData();
                    }
                },
                immediate: true,
            },
        },
        methods: {
            async fetchData() {
                try {
                    this.loading = true;
                    const params = {
                        ...this.fetchParams,
                    };
                    const { data } = await GoodsAPIV3.getPurchaseOrderByGoodsId(params);
                    this.detail = data.rows || [];
                } catch (e) {
                    console.log(e);
                } finally {
                    this.loading = false;
                }
            },
        },
    };
</script>

<style lang="scss" scoped>
.popper-wrap {
    position: relative;
    display: flex;
    flex-direction: column;

    .row {
        display: flex;
        align-items: center;
        height: 16px;
        padding: 0 10px;
        margin-bottom: 2px;
        font-size: 12px;
        line-height: 16px;
        color: #000000;
        white-space: nowrap;

        .item {
            display: inline-flex;
            flex: 1;
            align-items: center;
            justify-content: space-between;

            &:first-child {
                margin-right: 40px;
            }

            .label {
                margin-right: 24px;
            }

            .value {
                color: $T2;
            }
        }
    }

    .box {
        display: flex;
        align-items: baseline;
        order: 1;
        height: 22px;
        margin-bottom: 2px;
        font-size: 14px;
        line-height: 22px;
        color: #000000;

        &-status {
            min-width: 42px;
        }

        &-no {
            min-width: 122px;
        }
    }
}
</style>

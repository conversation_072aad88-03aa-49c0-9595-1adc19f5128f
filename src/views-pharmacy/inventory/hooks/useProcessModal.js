
export default function useProcessModal() {
    let timer = null;

    function stopProcess() {
        clearInterval(timer);
    }

    async function pollingRequest(pollingAPI, progressCallback) {
        const res = await pollingAPI();
        progressCallback && progressCallback(res, stopProcess);
    }

    async function startProcess({
        pollingInterval = 1000,
        pollingAPI,
        progressCallback,
    }) {

        // 开始轮询
        timer = setInterval(() => {
            pollingRequest(pollingAPI, progressCallback);
        }, pollingInterval);

    }

    return {
        startProcess,
        stopProcess,
    };
}

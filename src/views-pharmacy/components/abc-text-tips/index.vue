<template>
    <abc-tooltip
        :placement="placement"
        :disabled="disabled"
    >
        <span slot="content" :style="tipStyle" v-html="content"></span>
        <span ref="tips-text" class="abc-text-tips" :style="{ width: `${width}px` }">
            <span v-html="content"></span>
        </span>
    </abc-tooltip>
</template>

<script>
    export default {
        props: {
            content: {
                type: String,
                required: true,
                default: '',
            },
            width: {
                type: Number,
                required: true,
            },
            placement: {
                type: String,
                default: 'top-start',
            },
            maxTipsWidth: {
                type: Number,
                default: 360,
            },
        },
        data() {
            return {
                disabled: true,
            };
        },
        computed: {
            tipStyle() {
                const style = {
                    'display': 'inline-block',
                    'line-height': '16px',
                    'max-width': `${this.maxTipsWidth}px`,
                };
                return style;
            },
        },
        watch: {
            content: {
                async handler() {
                    await this.$nextTick();
                    const tipsText = this.$refs['tips-text'];
                    this.disabled = tipsText.scrollWidth <= tipsText.offsetWidth;
                },
                immediate: true,
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/mixin.scss";

.abc-text-tips {
    display: inline-block;

    @include ellipsis;
}
</style>

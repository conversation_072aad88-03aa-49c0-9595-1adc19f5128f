<template>
    <abc-layout preset="page-table" class="abc-pharmacy-gsp-frames-storage-environment-container">
        <abc-layout-header>
            <abc-flex justify="space-between">
                <abc-space>
                    <abc-date-picker
                        v-model="toolsParams.dateRange"
                        :picker-options="pickerOptions"
                        type="daterange"
                        clearable
                    ></abc-date-picker>
                    <clinic-select
                        v-if="isChainAdmin"
                        v-model="toolsParams.clinicId"
                        :show-all-clinic="false"
                        placeholder="总部/门店"
                        :clearable="true"
                        :width="160"
                    ></clinic-select>
                </abc-space>
                <abc-space>
                    <abc-button
                        v-if="!(gspEnvironmentAddButton && isChainAdmin)"
                        theme="success"
                        icon="s-b-add-line-medium"
                        @click="onClickCreate"
                    >
                        新增记录
                    </abc-button>
                    <abc-button
                        icon="n-upload-line"
                        variant="ghost"
                        :disabled="dataList.length === 0"
                        @click="exportExcelCommon(GSPExportEnum.STORAGE.ENVIRONMENT,
                                                  createParams(),
                                                  '陈列环境检查')"
                    >
                        导出
                    </abc-button>
                </abc-space>
            </abc-flex>
        </abc-layout-header>
        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                :render-config="renderConfig"
                :loading="loading"
                :data-list="dataList"
                tr-clickable
                @handleClickTr="onClickRow"
            ></abc-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :show-total-page="true"
                :pagination-params="pageParams"
                :count="count"
                @current-change="onChangePage"
            ></abc-pagination>
        </abc-layout-footer>
    </abc-layout>
</template>

<script>
    import GspAPI from '@/api/pharmacy/gsp';
    import pickerOptions from 'views/common/pickerOptions';
    import mixinTable from '@/views-pharmacy/common/mixin-table';
    import ClinicSelect from 'views/layout/clinic-select/clinic-select';
    const DialogEnvironmentDetail = () => import('./dialog-environment-detail/index.vue');

    import { mapGetters } from 'vuex';
    import { isEqual } from '@abc/utils';
    import * as tools from '@/views-pharmacy/common/tools';
    import { PurchaseTableConfig } from './table-config';
    import {
        exportExcelCommon, GSPExportEnum,
    } from '@/views-pharmacy/gsp/utils/gspExportUtil';
    export default {
        components: {
            ClinicSelect,
        },
        mixins: [
            pickerOptions,
            mixinTable,
        ],
        data() {
            return {
                GSPExportEnum,
                toolsParams: {
                    dateRange: this.createDateRangeCurMonth(), // 日期范围
                    clinicId: '', // 门店ID
                },
                tableHeader: [],
                pageParams: {
                    pageIndex: 0,
                    pageSize: 10,
                },
                loading: false,
                originData: null,
            };
        },
        computed: {
            ...mapGetters([
                'isChainAdmin',
                'currentClinic',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            gspEnvironmentAddButton() {
                return this.viewDistributeConfig.Gsp.storage.environment.gspEnvironmentAddButton;
            },
            // 当前门店id
            clinicId() {
                return this.currentClinic?.clinicId;
            },
            /// 表格数据
            dataList() {
                return this.originData?.rows || [];
            },
            // 总条数
            count() {
                return this.originData?.total || 0;
            },
            renderConfig() {
                const tableConfig = new PurchaseTableConfig(this.currentClinic);
                return tableConfig.extendConfig({
                    clinicName: {
                        dataFormatter: (_,row) => (row.clinicId === this.clinicId ? '总部' : row.clinicName),
                    },
                    checkTime: {
                        dataFormatter: (checkTime) => {
                            if (!checkTime) {
                                return '';
                            }
                            return tools.getDatetimeFormat(checkTime);
                        },
                    },
                    checker: {
                        dataFormatter: (checker) => checker?.name,
                    },
                    surroundingEnvironmentResult: {
                        dataFormatter: (surroundingEnvironmentResult) => tools.getCheckResultWording(surroundingEnvironmentResult),
                    },
                    indoorEnvironmentResult: {
                        dataFormatter: (indoorEnvironmentResult) => tools.getCheckResultWording(indoorEnvironmentResult),
                    },
                    storageCheckResult: {
                        dataFormatter: (storageCheckResult) => tools.getCheckResultWording(storageCheckResult),
                    },
                    sanitationCheckResult: {
                        dataFormatter: (sanitationCheckResult) => tools.getCheckResultWording(sanitationCheckResult),
                    },
                });
            },
        },
        watch: {
            toolsParams: {
                handler() {
                    this.initPageIndex();
                    this.fetchDataList();
                },
                deep: true,
            },
        },
        mounted() {
            this.setPageSizeWithTableHeight();
            // this.fetchDataList();
        },
        methods: {
            exportExcelCommon,
            handleMounted(data) {
                this.pageParams.pageSize = data.paginationLimit;
                this.fetchDataList();
            },
            /**
             * 创建查询参数
             * <AUTHOR>
             * @date 2023-12-28
             * @returns {Object}
             */
            createParams() {
                const {
                    dateRange, // 日期范围
                    clinicId, // 门店ID
                } = this.toolsParams;
                const {
                    pageIndex,
                    pageSize,
                } = this.pageParams;
                const params = {
                    clinicId, // 门店ID
                    checkDateStart: dateRange[0], // 检查开始日期 yyyy-MM-dd
                    checkDateEnd: dateRange[1], // 检查结束日期 yyyy-MM-dd
                    offset: pageIndex * pageSize, // 分页
                    limit: pageSize, // 每页条数
                };
                return params;
            },
            /**
             * 查询记录数据
             * <AUTHOR>
             * @date 2023-12-28
             */
            async fetchDataList() {
                this.loading = true;
                const params = this.createParams();
                const fetchResponse = await GspAPI.fetchDisplayEnvironmentOrderList(params);
                if (!isEqual(params, this.createParams())) {
                    return fetchResponse;
                }
                this.loading = false;
                if (fetchResponse.status === false) {
                    return fetchResponse;
                }
                this.originData = fetchResponse.data;
            },
            /**
             * 当点击新增记录
             * <AUTHOR>
             * @date 2024-01-03
             */
            async onClickCreate() {
                const openResponse = await tools.openDialog({
                    component: DialogEnvironmentDetail,
                });
                if (openResponse.status === false) {
                    return openResponse;
                }
                this.initPageIndex();
                this.fetchDataList();
            },
            /**
             * 当点击一行触发
             * <AUTHOR>
             * @date 2023-12-25
             * @param {Object} row
             */
            onClickRow(row) {
                console.log('row=', row);
                tools.openDialog({
                    propsData: {
                        id: row.id, // 温湿度记录ID
                    },
                    component: DialogEnvironmentDetail,
                });
            },
            /**
             * 当切换页码时触发
             * <AUTHOR>
             * @date 2023-12-25
             * @param {Number} pageIndex
             */
            onChangePage(pageIndex) {
                this.pageParams.pageIndex = pageIndex - 1;
                this.fetchDataList();
            },
            /**
             * 初始化页码
             * <AUTHOR>
             * @date 2023-12-29
             */
            initPageIndex() {
                this.pageParams.pageIndex = 0;
            },
        },
    };
</script>

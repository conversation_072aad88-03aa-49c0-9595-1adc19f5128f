<template>
    <abc-dialog
        v-model="showDialog"
        class="auto-conserve-config_dialog"
        content-styles="width: 365px; padding: 24px;"
    >
        <abc-layout>
            <abc-section>
                <abc-input v-model="searchKey" :width="317">
                    <abc-icon slot="prepend" icon="n-search-line"></abc-icon>
                </abc-input>
            </abc-section>
            <abc-section style="margin-top: 8px;">
                <div class="auto-conserve-config_dialog-body">
                    <abc-space direction="vertical" align="start">
                        <abc-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleChangeAll">
                            <abc-title :bold="false">
                                全部（{{ handleDataList.length }}）
                            </abc-title>
                        </abc-checkbox>
                        <abc-checkbox-group v-model="checkboxValue">
                            <abc-space direction="vertical" align="start">
                                <abc-checkbox v-for="(item, index) in handleDataList" :key="index" :label="item.goodsId">
                                    <abc-space>
                                        <abc-title :bold="false">
                                            {{ item.displayName }}
                                        </abc-title><abc-p gray>
                                            <template v-if="item.manufacturer">
                                                {{ item.manufacturer }}
                                            </template>({{ item.number }})
                                        </abc-p>
                                    </abc-space>
                                </abc-checkbox>
                            </abc-space>
                        </abc-checkbox-group>
                    </abc-space>
                </div>
            </abc-section>
            <abc-section style="margin-top: 8px;">
                <abc-flex justify="space-between">
                    <abc-button
                        variant="outline"
                        theme="default"
                        icon="n-referral-line"
                        @click="reset"
                    >
                        重置
                    </abc-button>
                    <abc-space>
                        <abc-button @click="sureFilter">
                            确定
                        </abc-button>
                        <abc-button variant="ghost" @click="showDialog = false">
                            取消
                        </abc-button>
                    </abc-space>
                </abc-flex>
            </abc-section>
        </abc-layout>
    </abc-dialog>
</template>

<script>
    import clone from 'utils/clone';
    export default {
        name: 'Index',
        props: {
            value: Boolean,
            dataList: {
                type: Array,
                default: () => {
                    return [];
                },
            },
        },
        data() {
            return {
                searchKey: '',
                checkboxValue: [],
                checkboxCacheValue: [],
                checkAll: false,
                isIndeterminate: false,
            };
        },
        computed: {
            handleDataList() {
                const handleList = [];
                let list = this.dataList.filter((i) => {
                    return !(i.batchGroup && !i.group);
                }).map((i) => {
                    return {
                        displayName: i.displayName,
                        manufacturer: i.manufacturer || '',
                        goodsId: i.goodsId,
                        number: 0,
                    };
                }) || [];
                if (this.searchKey) {
                    list = list.filter((item) => {
                        return item.displayName.indexOf(this.searchKey) !== -1 || item.manufacturer.indexOf(this.searchKey) !== -1;
                    });
                }
                list.forEach((item) => {
                    if (!handleList.find((i) => {return i.goodsId === item.goodsId;})) {
                        handleList.push({
                            ...item,
                            number: list.filter((i) => {return i.goodsId === item.goodsId;}).length,
                        });
                    }
                });
                return handleList;
            },
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
        },
        watch: {
            checkboxValue: {
                handler(val) {
                    const checkAll = !!this.handleDataList.every((option) => val.includes(option.goodsId));
                    this.checkAll = checkAll;
                    if (!checkAll) {
                        const isIndeterminate = !!this.handleDataList.some((option) => val.includes(option.goodsId));
                        this.isIndeterminate = isIndeterminate;
                    }
                },
                deep: true,
            },
        },
        created() {
            this.dataList?.filter((i) => {
                return i.isShow;
            })?.forEach((item) => {
                if (!this.checkboxValue.find((i) => {return i === item.goodsId;})) {
                    this.checkboxValue.push(item.goodsId);
                }
            });
            this.checkboxCacheValue = clone(this.checkboxValue);
        },
        methods: {
            reset() {
                // this.checkboxValue = clone(this.checkboxCacheValue);
                this.checkboxValue = this.handleDataList.map((o) => {
                    return o.goodsId;
                });
            },
            handleChangeAll(val) {
                if (val) {
                    this.checkboxValue = this.handleDataList.map((i) => {
                        return i.goodsId;
                    });
                } else {
                    this.checkboxValue = [];
                }
            },
            sureFilter() {
                this.$emit('sure-filter', this.checkboxValue);
                this.showDialog = false;
            },
        },
    };
</script>
<style lang="scss">
@import 'styles/abc-common.scss';

.auto-conserve-config_dialog {
    &-body {
        height: 300px;
        padding: 12px 2px 12px 12px;
        overflow-y: scroll;
        border: 1px solid var(--abc-color-P8);
        border-radius: var(--abc-border-radius-small);
    }
}
</style>

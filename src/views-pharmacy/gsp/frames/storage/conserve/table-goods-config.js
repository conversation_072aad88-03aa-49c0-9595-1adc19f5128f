import BaseClinicTypeTable from 'views/layout/tables/base-clinic-type-table';

export class PurchaseGoodsTableConfig extends BaseClinicTypeTable {
    constructor(clinic) {
        super(clinic);
        this.chainTableConfig = {
            'hasInnerBorder': false,
            'list': [{
                'label': '单号',
                'key': 'orderNo',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '状态',
                'key': 'status',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            },{
                'label': '门店名称',
                'key': 'clinicName',
                'show': true,
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '养护时间',
                'key': 'maintenanceTime',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            },{
                'label': '养护类型',
                'key': 'maintenanceType',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '养护人',
                'key': 'maintainer',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '商品名称',
                'key': 'goods',
                'style': {
                    'flex': '1','width': '','maxWidth': '','minWidth': '188px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'testValue': '阿莫西林胶囊（阿莫仙）',
            },{
                'label': '生产批号',
                'key': 'batchNo',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '养护数量',
                'key': 'goodsInventoryCountWording',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '16px','textAlign': 'right',
                },
            },{
                'label': '外观包装',
                'key': 'appearancePackageCondition',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '质量状况',
                'key': 'qualityCondition',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '养护措施',
                'key': 'measure',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '处理结果',
                'key': 'result',
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '备注',
                'key': 'remark',
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '70px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            }],
        };
        this.chainSubTableConfig = {
            'hasInnerBorder': false,
            'list': [{
                'label': '单号',
                'key': 'orderNo',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '状态',
                'key': 'status',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            },{
                'label': '养护时间',
                'key': 'maintenanceTime',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            },{
                'label': '养护类型',
                'key': 'maintenanceType',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '养护人',
                'key': 'maintainer',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '商品名称',
                'key': 'goods',
                'style': {
                    'flex': '1','width': '','maxWidth': '','minWidth': '188px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'testValue': '阿莫西林胶囊（阿莫仙）',
            },{
                'label': '生产批号',
                'key': 'batchNo',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '养护数量',
                'key': 'goodsInventoryCountWording',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '16px','textAlign': 'right',
                },
            },{
                'label': '外观包装',
                'key': 'appearancePackageCondition',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '质量状况',
                'key': 'qualityCondition',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '养护措施',
                'key': 'measure',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '处理结果',
                'key': 'result',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '备注',
                'key': 'remark',
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '70px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            }],
        };
        this.singleTableConfig = {
            'hasInnerBorder': false,
            'list': [{
                'label': '单号',
                'key': 'orderNo',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '状态',
                'key': 'status',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            },{
                'label': '养护时间',
                'key': 'maintenanceTime',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            },{
                'label': '养护类型',
                'key': 'maintenanceType',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '养护人',
                'key': 'maintainer',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '商品名称',
                'key': 'goods',
                'style': {
                    'flex': '1','width': '','maxWidth': '','minWidth': '188px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'testValue': '阿莫西林胶囊（阿莫仙）',
            },{
                'label': '生产批号',
                'key': 'batchNo',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '养护数量',
                'key': 'goodsInventoryCountWording',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '16px','textAlign': 'right',
                },
            },{
                'label': '外观包装',
                'key': 'appearancePackageCondition',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '质量状况',
                'key': 'qualityCondition',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '养护措施',
                'key': 'measure',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '处理结果',
                'key': 'result',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '备注',
                'key': 'remark',
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '70px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            }],
        };
    }
}

<template>
    <abc-dialog
        class="pharmacy__after-sales__recover__dialog"
        title="追回"
        :value="true"
        :auto-focus="false"
        size="large"
        content-styles="height: 552px;"
        @input="(val) => $emit('input', val)"
    >
        <abc-form
            v-if="!!formData"
            ref="formData"
            v-abc-loading="loading"
            item-no-margin
        >
            <abc-form-item-group is-excel>
                <abc-descriptions
                    :column="2"
                    :label-width="88"
                    size="large"
                    grid
                    class="base-info"
                    background
                >
                    <abc-descriptions-item label="门店" :span="2">
                        <div v-abc-title.ellipsis="formData.clinicName"></div>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="登记人" :span="1" content-padding="0">
                        <abc-form-item required>
                            <abc-select
                                v-model="formData.registrant"
                                :inner-width="208"
                                :fetch-suggestions="(searchKey) => searchKeyRegistrant = searchKey"
                                with-search
                                size="large"
                            >
                                <abc-option
                                    v-for="item in registrantOptions"
                                    :key="item.value"
                                    :value="item.value"
                                    :label="item.label"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="登记时间" :span="1" content-padding="0">
                        <abc-form-item required>
                            <abc-date-time-picker
                                v-model="formData.registrationTime"
                                :picker-options="pickerOptions"
                                :clearable="false"
                                :show-icon="false"
                                size="large"
                            ></abc-date-time-picker>
                        </abc-form-item>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="备注" :span="2" content-padding="0">
                        <abc-form-item>
                            <abc-input
                                v-model="formData.remark"
                                :max-length="300"
                                placeholder="请输入备注"
                                size="large"
                            ></abc-input>
                        </abc-form-item>
                    </abc-descriptions-item>
                </abc-descriptions>
            </abc-form-item-group>
            <div class="goods-info">
                <abc-form-item label="商品信息" class="goods-info-form-item" required>
                    <div
                        v-if="formData && formData.goodsId"
                        class="goods-info-wrapper"
                    >
                        <abc-flex class="display-goods-info" align="center" justify="space-between">
                            <span class="name">
                                {{ formData.goodsName || '' }}
                            </span>
                            <abc-flex class="delete-icon-wrapper" align="center" justify="center">
                                <abc-delete-icon data-cy="删除" @delete="handleClearGoodsInfo"></abc-delete-icon>
                            </abc-flex>
                        </abc-flex>
                    </div>
                    <goods-auto-complete
                        v-else
                        only-stock
                        :search.sync="fetchParams.keyword"
                        :focus-show="true"
                        :type-arr="fetchParams.type"
                        :sub-type-arr="fetchParams.subType"
                        :c-m-spec="fetchParams.cMSpec"
                        :width="592"
                        need-filter-disable
                        is-type-arr
                        :format-search-result-fn="formatSearchResultFn"
                        :inorder-config="0"
                        :clinic-id="clinicId"
                        clearable
                        placeholder="扫码 / 商品名 / 首字母 / 条码"
                        @selectGoods="onSelectGoods"
                    ></goods-auto-complete>
                </abc-form-item>
                <abc-descriptions
                    v-if="formData && formData.goodsId"
                    class="goods-detail-info"
                    :column="2"
                    :label-width="56"
                    size="small"
                    ellipsis
                >
                    <abc-descriptions-item label="规格" :span="2">
                        {{ formData.spec || '' }}
                    </abc-descriptions-item>
                    <abc-descriptions-item label="生产厂家" :span="2">
                        {{ formData.manufacturer || '' }}
                    </abc-descriptions-item>
                    <abc-descriptions-item label="剂型" :span="1">
                        {{ formData.dosageFormType || '' }}
                    </abc-descriptions-item>
                    <abc-descriptions-item label="批准文号" :span="1">
                        {{ formData.approvalNumber || '' }}
                    </abc-descriptions-item>
                </abc-descriptions>
                <template v-if="formData && formData.goodsId">
                    <abc-form-item label="批次信息" class="batch-info-form-item" required>
                        <abc-autocomplete
                            v-model.trim="formData.batchNo"
                            custom-class="batch-customer-autocomplete-suggestions-popper"
                            inner-width="592"
                            :width="592"
                            :delay-time="0"
                            :async-fetch="true"
                            :fetch-suggestions="fetchBatchListData"
                            :max-length="20"
                            focus-show
                            :auto-focus-first="false"
                            clearable
                            readonly
                            @clear="()=> { handleSetBatchInfo(null) }"
                            @enterEvent="handleSetBatchInfo"
                        >
                            <template #suggestion-header>
                                <abc-flex class="suggestion-title" align="center">
                                    <div class="batch">
                                        批次
                                    </div>
                                    <div class="batch-no">
                                        生产批号
                                    </div>
                                    <div class="in-date">
                                        入库时间
                                    </div>
                                    <div class="expiry-date">
                                        效期
                                    </div>
                                    <div class="cost-price">
                                        进价
                                    </div>
                                    <div class="current-count">
                                        账面数量
                                    </div>
                                </abc-flex>
                            </template>
                            <template slot="suggestions" slot-scope="props">
                                <dt
                                    class="suggestions-item"
                                    :class="{ selected: props.index == props.currentIndex }"
                                    @click="handleSetBatchInfo(props.suggestion)"
                                >
                                    <div class="batch">
                                        {{ props.suggestion.batchId }}
                                    </div>
                                    <div class="batch-no">
                                        {{ props.suggestion.batchNo }}
                                    </div>
                                    <div class="in-date">
                                        {{ props.suggestion.inDate | parseTime('y-m-d') }}
                                    </div>
                                    <div class="expiry-date">
                                        {{ props.suggestion.expiryDate }}
                                    </div>
                                    <div class="cost-price">
                                        {{ props.suggestion.packageCostPrice | formatMoney(false) }}
                                    </div>
                                    <div class="current-count">
                                        {{ props.suggestion.dispOutGoodsCount }}
                                    </div>
                                </dt>
                            </template>
                        </abc-autocomplete>
                    </abc-form-item>
                    <abc-descriptions
                        v-if="batchList && batchList.length && formData.batchId"
                        class="goods-detail-info"
                        :column="2"
                        :label-width="56"
                        size="small"
                        ellipsis
                    >
                        <abc-descriptions-item label="供应商" :span="2">
                            {{ formData.supplierName || '' }}
                        </abc-descriptions-item>
                        <abc-descriptions-item label="生产日期" :span="1">
                            {{ formData.productionDate || '' }}
                        </abc-descriptions-item>
                        <abc-descriptions-item label="有效日期" :span="1">
                            {{ formData.expiryDate || '' }}
                        </abc-descriptions-item>
                        <!--                    <abc-descriptions-item label="入库数" :span="1">-->
                        <!--                        {{ getStockInPackageCount(formData.batchId, batchList) || 0 }}-->
                        <!--                    </abc-descriptions-item>-->
                        <!--                    <abc-descriptions-item label="销售数" :span="1">-->
                        <!--                        {{ getSalePackageCount(formData.batchId, batchList) || 0 }}-->
                        <!--                    </abc-descriptions-item>-->
                    </abc-descriptions>
                </template>
                <abc-flex gap="large" style="width: 100%; margin-top: 16px;" justify="space-between">
                    <abc-form-item label="追回级别">
                        <abc-select
                            v-model="formData.recoverLevel"
                            :width="288"
                            clearable
                        >
                            <abc-option
                                v-for="item in options.recoverLevelOptions"
                                :key="item.value"
                                :value="item.value"
                                :label="item.label"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>
                    <abc-form-item label="追回数量">
                        <div>
                            <abc-space is-compact compact-block :border-style="false">
                                <abc-form-item :required="needRequiredPackageCount">
                                    <abc-input
                                        v-if="packageCountWidth"
                                        v-model="formData.recoverPackageCount"
                                        :width="packageCountWidth"
                                        type="number"
                                        :config="{
                                            max: 99999999,
                                            min: 0,
                                            supportZero: true,
                                            formatLength: 2,
                                        }"
                                        :input-custom-style="{
                                            textAlign: 'center'
                                        }"
                                    >
                                        <span v-if="formData.packageUnit" slot="appendInner">{{ formData.packageUnit }}</span>
                                    </abc-input>
                                </abc-form-item>
                                <abc-form-item v-if="pieceCountWidth" :required="needRequiredPieceCount">
                                    <abc-input
                                        v-model="formData.recoverPieceCount"
                                        :width="pieceCountWidth"
                                        type="number"
                                        :config="{
                                            max: 99999999,
                                            min: 0,
                                            supportZero: true,
                                            formatLength: 2,
                                        }"
                                        :input-custom-style="{
                                            textAlign: 'center'
                                        }"
                                    >
                                        <span v-if="formData.pieceUnit" slot="appendInner">{{ formData.pieceUnit }}</span>
                                    </abc-input>
                                </abc-form-item>
                            </abc-space>
                        </div>
                    </abc-form-item>
                </abc-flex>

                <abc-form-item label="追回原因">
                    <abc-textarea
                        v-model="formData.recoverReason"
                        :width="592"
                        :height="56"
                        :maxlength="100"
                    ></abc-textarea>
                </abc-form-item>

                <abc-form-item label="附件">
                    <external-file
                        v-model="formData.attachments"
                        :business-type="BusinessTypeEnum.RECOVER"
                        oss-filepath="pharmacy-gsp-recover-record"
                        :max-upload-count="9"
                        :hidden-left="true"
                        business-desc="上传追回记录附件"
                        width="96px"
                        height="96px"
                        :accept="['.jpg', '.JPG', '.jpeg', '.JPEG', '.png', '.PNG', '.pdf', '.PDF']"
                        upload-description="追回记录附件支持图片、PDF格式"
                    ></external-file>
                </abc-form-item>
            </div>
        </abc-form>
        <div slot="footer" class="dialog-footer">
            <abc-button
                :loading="loadingSubmit"
                :disabled="disabledSubmit"
                @click="onClickSubmit"
            >
                提交
            </abc-button>
            <abc-button
                type="blank"
                @click="onClickCancel"
            >
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import GspAPI from '@/api/pharmacy/gsp';
    import pickerOptions from 'views/common/pickerOptions';

    const GoodsAutoComplete = () => import('src/views/inventory/common/goods-auto-complete');

    import { BusinessTypeEnum } from '@/views/layout/mobile-upload-dialog/config';
    import clone from 'utils/clone';
    import { pick } from 'utils/index';
    import { red } from 'utils/math';
    import { mapGetters } from 'vuex';
    import { isEqual } from '@abc/utils';
    import * as constants from '@/views-pharmacy/common/constants';
    import * as options from '@/views-pharmacy/common/options';
    import * as tools from '@/views-pharmacy/common/tools';
    import ExternalFile from 'views/layout/external-file/index.vue';
    import AbcSocket from 'views/common/single-socket';
    export default {
        components: {
            GoodsAutoComplete,
            ExternalFile,
        },
        mixins: [
            pickerOptions,
        ],
        props: {
            id: {
                type: String,
                default: '',
            },
            isUpdate: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                options,
                BusinessTypeEnum,
                pickerOptions: null,
                searchKeyRegistrant: '', // 搜索关键词 - 登记人
                loading: false,
                originData: null,
                formData: null,
                formDataCache: null,
                loadingSubmit: false,
                fetchParams: {
                    keyword: '',
                    typeId: '',
                    typeLabel: '全部类型',
                    type: [],
                    subType: [],
                    cMSpec: '',
                },
                batchList: [],
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'employeeList',
                'userInfo',
                'westernMedicineConfig',
            ]),
            packageCountWidth() {
                if (this.isChinese) {
                    return 0;
                }
                let width = 288;
                if (this.allowDismounting) {
                    width = width - 144;
                }
                return width;
            },
            pieceCountWidth() {
                if (!(this.isChinese || this.allowDismounting)) {
                    return 0;
                }
                let width = 288;
                if (!this.isChinese) {
                    width = width - 144;
                }
                return width;
            },
            needRequiredPackageCount() {
                if (!this.pieceCountWidth) {
                    return true;
                }
                return !this.formData?.recoverPackageCount && !this.formData?.recoverPieceCount;

            },
            needRequiredPieceCount() {
                if (!this.packageCountWidth) {
                    return true;
                }
                return !this.formData?.recoverPackageCount && !this.formData?.recoverPieceCount;
            },
            allowDismounting() {
                return this.formData?.dismounting || 0;
            },
            isChinese() {
                return this.formData?.type === 1 && this.formData?.subType === 2;
            },
            // 登记人员选项
            registrantOptions() {
                let employeeList = this.employeeList || [];
                if (this.searchKeyRegistrant) {
                    // 有关键词时，过滤一下
                    employeeList = tools.filterEmployeeBySearchKey(employeeList, this.searchKeyRegistrant);
                }
                return employeeList.map((item) => ({
                    value: item.employeeId,
                    label: item.employeeName,
                }));
            },
            // 当前门店id
            clinicId() {
                return this.currentClinic?.clinicId;
            },
            // 是否新增
            isCreate() {
                if (!this.id) {
                    // 没有ID的时候，是新增
                    return true;
                }
                return false;
            },
            // 是否禁用提交按钮
            disabledSubmit() {
                if (this.isCreate) {
                    return false;
                }
                if (this.isUpdate) {
                    return isEqual(this.formData, this.formDataCache);
                }
                return false;
            },
        },
        created() {
            this.formData = this.createFormData();
            this.pickerOptions = this.createPickerOptions();
            const { socket } = AbcSocket.getSocket();
            this._socket = socket;
            this._socket.on('short-url.upload_attachment', this.handleImages);
        },
        beforeDestroy() {
            this._socket?.off('short-url.upload_attachment', this.handleImages);
        },
        async mounted () {
            this.loading = true;
            await this.fetchRecoverRecordInfo();
            await this.fetchBatchListByOriginData();
            this.loading = false;

            this.formData = this.createFormData();
            if (this.originData) {
                this.fetchParams.keyword = this.originData.goods?.displayName;
            }
            this.formDataCache = clone(this.formData);
        },
        methods: {
            formatSearchResultFn(goodsList) {
                return goodsList?.filter((item) => {
                    return item.packageCount > 0 || item.pieceCount > 0;
                });
            },
            handleImages(data) {
                const {
                    attachments = [],
                    businessType,
                } = data;
                if (businessType === BusinessTypeEnum.RECOVER && attachments && attachments.length) {
                    this.formData.attachments = this.formData.attachments.concat(attachments.map((item) => {
                        return {
                            fileName: item.fileName,
                            fileSize: item.fileSize,
                            sort: item.sort,
                            url: item.url,
                        };
                    }));
                    this.formData.attachments = this.formData.attachments.map((item, index) => {
                        return {
                            ...item,
                            sort: index,
                        };
                    });
                }
            },
            getStockInPackageCount(batchId, list) {
                if (!batchId || !list?.length) {
                    return 0;
                }
                return list.find((item) => {
                    return batchId === item.batchId;
                })?.stockInPackageCount;
            },
            getSalePackageCount(batchId, list) {
                if (!batchId || !list?.length) {
                    return 0;
                }

                const item = list.find((item) => {
                    return batchId === item.batchId;
                }) || {};
                const {
                    stockInPackageCount = 0, stockPackageCount = 0,
                } = item;
                return stockInPackageCount - stockPackageCount;
            },
            /**
             * 查询追回记录单详情
             * <AUTHOR>
             * @date 2024-01-03
             */
            async fetchRecoverRecordInfo() {
                if (!this.id) {
                    return;
                }
                const fetchResponse = await GspAPI.fetchRecoverRecordInfo(this.id);
                if (fetchResponse.status === true) {
                    this.originData = fetchResponse.data;
                }
            },
            /**
             * 查询批次流水号 - 通过复用信息
             * <AUTHOR>
             * @date 2024-01-24
             */
            async fetchBatchListByOriginData() {
                if (!this.originData) {
                    return;
                }
                const fetchResponse = await this.fetchBatchList(this.originData.goodsId);
                if (fetchResponse.status === false) {
                    return fetchResponse;
                }
                this.batchList = fetchResponse.data?.rows?.map((item) => {
                    return {
                        ...item,
                        batchNo: item.batchNo || '-',
                    };
                }) || [];
            },
            /**
             * 创建表单数据
             * <AUTHOR>
             * @date 2024-01-03
             * @returns {Object}
             */
            createFormData() {
                let formData = {
                    clinicName: this.currentClinic?.clinicName || '', // 门店名称
                    approvalNumber: '', // 批准文号
                    batchNo: '', // 商品生产批次号
                    batchId: '',
                    causeQualityAccident: constants.causeQualityAccidentConst.FALSE, // 是否造成质量事故: (0:否，1:是)
                    dosageFormType: '', // 剂型
                    expiryDate: '', // 效期
                    goodsId: '', // 商品ID
                    goodsName: '', // 商品名称
                    id: '', // 主键ID
                    manufacturer: '', // 生产厂家
                    notRecoverReason: '', // 未追回原因
                    notRecoverPackageCount: '', // 未追回数量
                    packageCount: '', // 整包数量
                    pieceCount: '', // 小包装数量
                    pieceNum: '', // 制剂数量
                    productionDate: '', // 生产日期
                    recoverLevel: '', // 追回级别: 1:一级，2:二级，3:三级
                    recoverMethod: '', // 追回方式: 1:门店追回、2:厂家追回
                    recoverPackageCount: '', // 追回整包数量
                    packageUnit: '盒', // 整包单位
                    recoverPieceCount: '', // 追回小包装数量
                    recoverPieceNum: '', // 追回制剂数量
                    recoverReason: '', // 追回原因
                    attachments: [],
                    registrant: (() => {
                        // 登记人，默认当前人员，没有就选第一个
                        const target = this.registrantOptions.find((item) => item.value === this.userInfo?.id) || this.registrantOptions[0];
                        return target?.value || '';
                    })(), // 登记人
                    registrationTime: tools.getDatetimeFormat(), // 登记时间
                    remark: '',
                    spec: '', // 规格
                    supplierId: '', // 供应商ID
                    supplierName: '', // 供应商名称
                    unit: '', // 单位
                    pieceUnit: '', // 单位
                    dismounting: 0,
                    type: 0,
                    subType: 0,
                };
                if (this.originData) {
                    const {
                        goods, // goods信息
                        goodsId, // 商品ID
                        batchNo, // 商品生产批次号
                        batchId,
                        registrantInfo, // 记录员信息
                        registrationTime, // 登记时间
                        remark,
                        causeQualityAccident, // 是否造成质量事故: (0:否，1:是)
                        notRecoverReason, // 未追回原因
                        notRecoverPackageCount, // 未追回数量
                        packageCount, // 整包数量
                        pieceCount, // 小包装数量
                        recoverLevel, // 追回级别: 1:一级，2:二级，3:三级
                        recoverMethod, // 追回方式: 1:门店追回、2:厂家追回
                        recoverPackageCount, // 追回整包数量
                        recoverPieceCount, // 追回小包装数量
                        recoverPieceNum, // 追回制剂数量
                        recoverReason, // 追回原因
                        attachments = [],
                    } = clone(this.originData);
                    Object.assign(formData, {
                        approvalNumber: '', // 批准文号 √
                        batchNo, // 商品生产批次号
                        batchId,
                        causeQualityAccident, // 是否造成质量事故: (0:否，1:是)
                        dosageFormType: '', // 剂型 √
                        expiryDate: '', // 效期 √
                        goodsId, // 商品ID
                        goodsName: goods?.name || '', // 商品名称 √
                        manufacturer: '', // 生产厂家
                        notRecoverReason, // 未追回原因
                        notRecoverPackageCount, // 未追回数量
                        packageCount, // 整包数量
                        pieceCount, // 小包装数量
                        pieceNum: '', // 制剂数量 √
                        productionDate: '', // 生产日期 √
                        recoverLevel, // 追回级别: 1:一级，2:二级，3:三级
                        recoverMethod, // 追回方式: 1:门店追回、2:厂家追回
                        recoverPackageCount, // 追回整包数量
                        recoverPieceCount, // 追回小包装数量
                        recoverPieceNum, // 追回制剂数量
                        recoverReason, // 追回原因
                        attachments,
                        registrant: registrantInfo?.id, // 登记人
                        registrationTime: tools.getDatetimeFormat(registrationTime), // 登记时间
                        remark,
                        spec: '', // 规格 √
                        supplierId: '', // 供应商ID √
                        supplierName: '', // 供应商名称 √
                        unit: '', // 单位 √
                    });
                    formData = {
                        ...formData,
                        approvalNumber: goods.medicineNmpn, // 批准文号
                        dosageFormType: tools.getDosageFormTypeWording(goods.dosageFormType), // 剂型
                        goodsId: goods.goodsId, // 商品ID
                        goodsName: goods.displayName, // 商品名称
                        manufacturer: goods.manufacturer, // 生产厂家
                        pieceCount: '', // 小包装数量 - 销售数量
                        pieceNum: goods.pieceNum, // 制剂数量
                        spec: goods.displaySpec, // 规格
                        unit: goods.packageUnit, // 单位
                        packageUnit: goods.packageUnit, // 单位
                        pieceUnit: goods.pieceUnit, // 单位
                        dismounting: goods.dismounting,
                        type: goods.type,
                        subType: goods.subType,
                    };
                    const batchInfo = this.batchList.find((item) => item.batchId === formData.batchId) || this.batchList[0];
                    if (batchInfo) {
                        formData = {
                            ...formData,
                            batchNo: batchInfo.batchId ? batchInfo.batchNo || '-' : batchInfo.batchNo, // 商品生产批次号
                            batchId: batchInfo.batchId,
                            expiryDate: batchInfo.expiryDate, // 效期
                            productionDate: batchInfo.productDate, // 生产日期
                            supplierId: batchInfo.supplierId, // 供应商ID
                            supplierName: batchInfo.supplierName, // 供应商名称
                        };
                    }
                }
                return formData;
            },
            /**
             * 当选择一个商品时
             * <AUTHOR>
             * @date 2024-01-03
             * @param {Object} goods
             */
            async onSelectGoods(goods) {
                this.batchList = [];
                this.fetchParams.keyword = goods.displayName;

                // 查询批次列表
                const fetchResponse = await this.fetchBatchList(goods.goodsId);
                if (fetchResponse.status === false) {
                    return fetchResponse;
                }
                this.batchList = fetchResponse.data?.rows?.map((item) => {
                    return {
                        ...item,
                        batchNo: item.batchNo || '-',
                    };
                }) || [];

                this.formData = {
                    ...this.formData,
                    approvalNumber: goods.medicineNmpn, // 批准文号
                    dosageFormType: tools.getDosageFormTypeWording(goods.dosageFormType), // 剂型
                    goodsId: goods.goodsId, // 商品ID
                    goodsName: goods.displayName, // 商品名称
                    manufacturer: goods.manufacturer, // 生产厂家
                    pieceCount: '', // 小包装数量 - 销售数量
                    pieceNum: goods.pieceNum, // 制剂数量
                    spec: goods.displaySpec, // 规格
                    unit: goods.packageUnit, // 单位
                    packageUnit: goods.packageUnit, // 单位
                    pieceUnit: goods.pieceUnit, // 单位
                    dismounting: goods.dismounting,
                    type: goods.type,
                    subType: goods.subType,
                };
                this.setBatchInfo(this.formData);
            },
            /**
             * 设置goods信息
             * <AUTHOR>
             * @date 2024-01-29
             * @param {Object} formData
             * @param {Object} goods
             */
            setGoodsInfo(formData, goods) {
                Object.assign(formData, {
                    approvalNumber: goods.medicineNmpn, // 批准文号
                    dosageFormType: tools.getDosageFormTypeWording(goods.dosageFormType), // 剂型
                    goodsId: goods.goodsId, // 商品ID
                    goodsName: goods.displayName, // 商品名称
                    manufacturer: goods.manufacturer, // 生产厂家
                    pieceCount: '', // 小包装数量 - 销售数量
                    pieceNum: goods.pieceNum, // 制剂数量
                    spec: goods.displaySpec, // 规格
                    unit: goods.packageUnit, // 单位
                    packageUnit: goods.packageUnit, // 单位
                    pieceUnit: goods.pieceUnit, // 单位
                    dismounting: goods.dismounting,
                    type: goods.type,
                    subType: goods.subType,
                });
            },
            /**
             * 设置批次信息
             * <AUTHOR>
             * @date 2024-01-24
             */
            setBatchInfo() {
                const batchInfo = this.batchList.find((item) => item.batchId === this.formData.batchId) || this.batchList[0];
                if (!batchInfo) {
                    return;
                }
                Object.assign(this.formData, {
                    batchNo: batchInfo.batchNo, // 商品生产批次号
                    batchId: batchInfo.batchId,
                    expiryDate: batchInfo.expiryDate, // 效期
                    productionDate: batchInfo.productDate, // 生产日期
                    supplierId: batchInfo.supplierId, // 供应商ID
                    supplierName: batchInfo.supplierName, // 供应商名称
                });
            },
            // 删除商品信息需要情况对应的参数
            handleClearGoodsInfo() {
                Object.assign(this.formData, {
                    approvalNumber: '', // 批准文号
                    dosageFormType: '', // 剂型
                    goodsId: '', // 商品ID
                    goodsName: '', // 商品名称
                    manufacturer: '', // 生产厂家
                    pieceCount: '', // 小包装数量 - 销售数量
                    pieceNum: '', // 制剂数量
                    productionDate: '', // 生产日期
                    spec: '', // 规格
                    unit: '', // 单位
                    batchNo: '', // 商品生产批次号
                    batchId: '',
                    expiryDate: '', // 效期
                    supplierId: '', // 供应商ID
                    supplierName: '', // 供应商ID
                    packageCount: 0, // 销售数
                    packageUnit: '',
                    pieceUnit: '', // 单位
                    dismounting: 0,
                    type: 0,
                    subType: 0,
                });
                this.batchList = [];
            },
            /**
             * 当改变数量时，自动计算未召回数量
             * <AUTHOR>
             * @date 2024-01-24
             */
            onChangeCount() {
                const {
                    packageCount, // 销售数量
                    recoverPackageCount, // 召回数量
                } = this.formData;
                if (packageCount && recoverPackageCount) {
                    const notRecoverPackageCount = red(packageCount, recoverPackageCount);
                    if (isNaN(notRecoverPackageCount)) {
                        return;
                    }
                    this.formData.notRecoverPackageCount = notRecoverPackageCount; // 未追回整包数量
                }
            },
            /**
             * 获取批次列表
             * <AUTHOR>
             * @date 2024-01-24
             * @param {String} goodsId
             * @returns {Promise<Response>}
             */
            async fetchBatchList(goodsId) {
                const params = {
                    clinicId: this.clinicId,
                    batchNo: '',
                    goodsId,
                    supplierId: '',
                    offset: 0,
                    limit: 99,
                    pharmacyNo: '',
                    batchViewMode: '',
                    expiredWarn: '',
                    costPriceWarn: '',
                    all: 1, // 查询近3个月的批次，库存为0的也返回
                };
                const fetchResponse = await GspAPI.fetchBatchList(params);
                if (fetchResponse.status === false) {
                    return fetchResponse;
                }
                return fetchResponse;
            },
            /**
             * 创建提交数据
             * <AUTHOR>
             * @date 2024-01-03
             * @returns {Object}
             */
            createPostData() {
                const formData = clone(this.formData);
                formData.registrationTime += ':00';
                const postData = {
                    approvalNumber: '', // 批准文号
                    batchNo: '', // 商品生产批次号
                    batchId: '',
                    causeQualityAccident: '', // 是否造成质量事故: (0:否，1:是)
                    dosageFormType: '', // 剂型
                    expiryDate: '', // 效期
                    goodsId: '', // 商品ID
                    goodsName: '', // 商品名称
                    id: '', // 主键ID
                    manufacturer: '', // 生产厂家
                    notRecoverReason: '', // 未追回原因
                    notRecoverPackageCount: '', // 未追回数量
                    packageCount: '', // 整包数量
                    pieceCount: '', // 小包装数量
                    pieceNum: '', // 制剂数量
                    productionDate: '', // 生产日期
                    recoverLevel: '', // 追回级别: 1:一级，2:二级，3:三级
                    recoverMethod: '', // 追回方式: 1:门店追回、2:厂家追回
                    recoverPackageCount: '', // 追回整包数量
                    recoverPieceCount: '', // 追回小包装数量
                    recoverPieceNum: '', // 追回制剂数量
                    recoverReason: '', // 追回原因
                    attachments: [],
                    registrant: '', // 登记人
                    registrationTime: '', // 登记时间
                    remark: '',
                    spec: '', // 规格
                    supplierId: '', // 供应商
                    unit: '', // 单位
                };
                return pick(postData, formData);
            },
            /**
             * 当点击提交时
             * <AUTHOR>
             * @date 2023-12-25
             */
            async onClickSubmit() {
                if (this.loadingSubmit) {
                    return;
                }
                this.$refs.formData.validate(async (valid) => {
                    if (!valid) {
                        return;
                    }
                    this.loadingSubmit = true;
                    const postData = this.createPostData();
                    if (postData.batchNo === '-') {
                        postData.batchNo = '';
                    }
                    const createResponse = await (this.isCreate ? GspAPI.createRecoverRecord(postData) : GspAPI.updateRecoverRecord(this.id, postData));
                    this.loadingSubmit = false;
                    if (createResponse.status === false) {
                        this.$Toast({
                            type: 'error',
                            message: createResponse?.message || '操作失败',
                        });
                        return createResponse;
                    }
                    this.$Toast({
                        type: 'success',
                        message: '操作成功',
                    });
                    this.$emit('confirm');
                });
            },
            /**
             * 当点击取消时
             * <AUTHOR>
             * @date 2023-12-25
             */
            onClickCancel() {
                this.$emit('cancel');
            },
            fetchBatchListData(key, callback) {
                return callback(this.batchList);
            },
            handleSetBatchInfo(data) {
                Object.assign(this.formData,{
                    batchNo: data?.batchNo || '', // 商品生产批次号
                    batchId: data?.batchId || '',
                    expiryDate: data?.expiryDate || '', // 效期
                    productionDate: data?.productDate || '', // 生产日期
                    supplierId: data?.supplierId || '', // 供应商ID
                    supplierName: data?.supplierName || '', // 供应商ID
                    packageCount: data?.packageCount || 0, // 销售数
                });
            },
        },
    };
</script>

<style lang="scss">
    .pharmacy__after-sales__recover__dialog {
        .base-info {
            width: 592px;
        }

        .goods-info {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: flex-start;
            margin-top: 16px;

            .abc-form-item {
                margin: 0 0 16px 0;

                &.goods-info-form-item,
                &:last-child {
                    margin-bottom: 0;
                }

                &.batch-info-form-item {
                    margin: 16px 0 0 0;
                }

                .goods-info-wrapper {
                    display: inline-flex;
                    flex-shrink: 0;
                    align-items: center;
                    justify-content: space-between;
                    width: 592px;
                    height: 32px;
                    padding: 0 8px;
                    cursor: pointer;
                    border: 1px solid $P7;
                    border-radius: var(--abc-border-radius-small);

                    &:hover {
                        border-color: $theme3;
                        box-shadow: 0 0 0 2px #c6e2ff;

                        .delete-icon-wrapper {
                            visibility: visible;
                        }
                    }

                    .display-goods-info {
                        width: 100%;

                        .name {
                            font-size: 14px;
                        }

                        .desc,
                        .approval-number {
                            font-size: 12px;
                            color: $T2;
                        }
                    }

                    .delete-icon-wrapper {
                        width: 20px;
                        height: 20px;
                        visibility: hidden;
                    }
                }
            }

            .goods-detail-info {
                width: 592px;
                margin-top: 8px;
                background: #f9fafc;
                border: 1px dashed $P7;
            }
        }
    }

    .batch-customer-autocomplete-suggestions-popper {
        .suggestion-title {
            display: flex;
            align-items: center;
            padding: 0 12px;
        }

        .batch {
            width: 120px;
            min-width: 120px;
            max-width: 120px;
        }

        .cost-price,
        .current-count {
            width: 80px;
            min-width: 80px;
            max-width: 80px;
            padding-left: 8px;
            text-align: right;
        }

        .batch-no,
        .in-date,
        .expiry-date {
            width: 90px;
            min-width: 90px;
            max-width: 90px;
            padding-left: 8px;
        }
    }
</style>

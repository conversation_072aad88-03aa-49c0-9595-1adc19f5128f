import BaseClinicTypeTable from 'views/layout/tables/base-clinic-type-table';

export class RecallTableConfig extends BaseClinicTypeTable {
    constructor(props) {
        super(props);

        this.chainTableConfig = {
            'list': [{
                'label': '门店名称',
                'style': {
                    'flex': 1,'width': '248px','maxWidth': '','minWidth': '248px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'key': 'clinicName',
                'hidden': false,
            },{
                'label': '商品编码',
                'key': 'shortId',
                'style': {
                    'flex': '','width': '100px','maxWidth': '120px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '品名',
                'key': 'name',
                'style': {
                    'flex': 1,'width': '248px','maxWidth': '','minWidth': '248px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '状态',
                'key': 'status',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
                'show': true,
            },{
                'label': '规格',
                'key': 'spec',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '剂型',
                'key': 'dosageFormType',
                'style': {
                    'flex': '','width': '80px','maxWidth': '80px','minWidth': '80px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '生产厂家',
                'key': 'manufacturer',
                'style': {
                    'flex': 1,'width': '248px','maxWidth': '','minWidth': '248px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '批准文号',
                'key': 'approvalNumber',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '生产批号',
                'key': 'batchNo',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '生产日期',
                'key': 'productionDate',
                'style': {
                    'flex': '','width': '104px','maxWidth': '104px','minWidth': '104px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '有效日期',
                'key': 'expiryDate',
                'style': {
                    'flex': '','width': '104px','maxWidth': '104px','minWidth': '104px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '供应商',
                'key': 'supplier',
                'style': {
                    'flex': 1,'width': '248px','maxWidth': '','minWidth': '248px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '召回级别',
                'key': 'recallLevel',
                'style': {
                    'flex': '','width': '80px','maxWidth': '80px','minWidth': '80px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '召回数量',
                'key': 'recallPackageCount',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '16px','textAlign': 'right',
                },
            },{
                'label': '召回原因',
                'key': 'recallReason',
                'style': {
                    'flex': '1','width': '248px','maxWidth': '','minWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '登记时间',
                'key': 'registrationTime',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '登记人',
                'key': 'registrantInfo',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '备注',
                'key': 'remark',
                'style': {
                    'flex': 1,'width': '140px','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '操作',
                'key': 'handlers',
                'pinned': 'right',
                'style': {
                    'flex': '','width': '120px','maxWidth': '120px','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            }],
        };

        this.chainSubTableConfig = {
            'list': [{
                'label': '商品编码',
                'key': 'shortId',
                'style': {
                    'flex': '','width': '100px','maxWidth': '120px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '品名',
                'key': 'name',
                'style': {
                    'flex': 1,'width': '248px','maxWidth': '','minWidth': '248px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '状态',
                'key': 'status',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
                'show': true,
            },{
                'label': '规格',
                'key': 'spec',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '剂型',
                'key': 'dosageFormType',
                'style': {
                    'flex': '','width': '80px','maxWidth': '80px','minWidth': '80px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '生产厂家',
                'key': 'manufacturer',
                'style': {
                    'flex': 1,'width': '248px','maxWidth': '','minWidth': '248px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '批准文号',
                'key': 'approvalNumber',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '生产批号',
                'key': 'batchNo',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '生产日期',
                'key': 'productionDate',
                'style': {
                    'flex': '','width': '104px','maxWidth': '104px','minWidth': '104px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '有效日期',
                'key': 'expiryDate',
                'style': {
                    'flex': '','width': '104px','maxWidth': '104px','minWidth': '104px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '供应商',
                'key': 'supplier',
                'style': {
                    'flex': 1,'width': '248px','maxWidth': '','minWidth': '248px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '召回级别',
                'key': 'recallLevel',
                'style': {
                    'flex': '','width': '80px','maxWidth': '80px','minWidth': '80px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '召回数量',
                'key': 'recallPackageCount',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '16px','textAlign': 'right',
                },
            },{
                'label': '召回原因',
                'key': 'recallReason',
                'style': {
                    'flex': '1','width': '248px','maxWidth': '','minWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '登记时间',
                'key': 'registrationTime',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '登记人',
                'key': 'registrantInfo',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '备注',
                'key': 'remark',
                'style': {
                    'flex': 1,'width': '140px','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '操作',
                'key': 'handlers',
                'pinned': 'right',
                'style': {
                    'flex': '','width': '120px','maxWidth': '120px','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            }],
        };

        this.singleTableConfig = {
            'list': [{
                'label': '商品编码',
                'key': 'shortId',
                'style': {
                    'flex': '','width': '100px','maxWidth': '120px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '品名',
                'key': 'name',
                'style': {
                    'flex': 1,'width': '248px','maxWidth': '','minWidth': '248px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '状态',
                'key': 'status',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
                'show': true,
            },{
                'label': '规格',
                'key': 'spec',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '剂型',
                'key': 'dosageFormType',
                'style': {
                    'flex': '','width': '80px','maxWidth': '80px','minWidth': '80px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '生产厂家',
                'key': 'manufacturer',
                'style': {
                    'flex': 1,'width': '248px','maxWidth': '','minWidth': '248px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '批准文号',
                'key': 'approvalNumber',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '生产批号',
                'key': 'batchNo',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '生产日期',
                'key': 'productionDate',
                'style': {
                    'flex': '','width': '104px','maxWidth': '104px','minWidth': '104px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '有效日期',
                'key': 'expiryDate',
                'style': {
                    'flex': '','width': '104px','maxWidth': '104px','minWidth': '104px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '供应商',
                'key': 'supplier',
                'style': {
                    'flex': 1,'width': '248px','maxWidth': '','minWidth': '248px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '召回级别',
                'key': 'recallLevel',
                'style': {
                    'flex': '','width': '80px','maxWidth': '80px','minWidth': '80px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '召回数量',
                'key': 'recallPackageCount',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '16px','textAlign': 'right',
                },
            },{
                'label': '召回原因',
                'key': 'recallReason',
                'style': {
                    'flex': '1','width': '248px','maxWidth': '','minWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '登记时间',
                'key': 'registrationTime',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '登记人',
                'key': 'registrantInfo',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '备注',
                'key': 'remark',
                'style': {
                    'flex': 1,'width': '140px','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '操作',
                'key': 'handlers',
                'pinned': 'right',
                'style': {
                    'flex': '','width': '120px','maxWidth': '120px','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            }],
        };
    }

    extendConfig(configMap) {
        const tableConfig = super.extendConfig(configMap);
        tableConfig.list = tableConfig.list.filter((item) => item.show !== false);
        return tableConfig;
    }
}

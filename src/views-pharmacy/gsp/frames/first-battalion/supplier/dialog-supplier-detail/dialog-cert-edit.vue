<template>
    <abc-dialog
        class="pharmacy__first-battalion__supplier__dialog-doc-edit"
        :title="title"
        :value="true"
        :type="dialogType"
        no-animation
        append-to-body
        content-styles="width: 480px; height: 176px; padding: 24px;"
        @input="(val) => $emit('input', val)"
    >
        <section>
            <div class="attachment-left">
                <external-file
                    v-model="formData.pictureUrls"
                    :business-type="BusinessTypeEnum.PHARMACY_SUPPLIER_CERTIFICATE"
                    oss-filepath="supplier-license"
                    business-desc="资质证件"
                    width="128px"
                    file-text="上传图片"
                    height="128px"
                    :max-upload-count="1"
                    is-limit-max-count
                    :accept="['.jpg', '.JPG', '.jpeg', '.JPEG', '.png', '.PNG', '.pdf', '.PDF']"
                    upload-description="附件支持图片、PDF格式"
                ></external-file>
            </div>
            <abc-form
                ref="formData"
                label-position="left"
                :label-width="88"
                item-block
            >
                <abc-form-item label="证照名称" required>
                    <abc-select
                        v-model="formData.type"
                        :width="192"
                        @change="onChangeType"
                    >
                        <abc-option
                            v-for="one in options.certTypeOptions"
                            :key="one.value"
                            :value="one.value"
                            :label="one.label"
                        ></abc-option>
                    </abc-select>
                </abc-form-item>
                <abc-form-item label="证照编号">
                    <abc-input v-model="formData.no" :width="192" :max-length="30"></abc-input>
                </abc-form-item>
                <abc-form-item
                    label="有效日期"
                    :help="helpText"
                    :help-theme="helpTheme"
                >
                    <abc-date-picker
                        v-model="formData.validTo"
                        value-format="YYYY-MM-DD"
                        :width="192"
                    ></abc-date-picker>
                </abc-form-item>
            </abc-form>
        </section>
        <div slot="footer" class="dialog-footer">
            <abc-button
                @click="onClickConfirm"
            >
                确认
            </abc-button>
            <abc-button
                type="blank"
                @click="onClickCancel"
            >
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import ExternalFile from 'views/layout/external-file/index.vue';

    import clone from 'utils/clone';
    import { isEqual } from 'utils/lodash';
    import { BusinessTypeEnum } from '@/views/layout/mobile-upload-dialog/config';

    import * as constants from '@/views-pharmacy/common/constants';
    import * as options from '@/views-pharmacy/common/options';
    import * as tools from '@/views-pharmacy/common/tools';
    import AbcSocket from 'views/common/single-socket';
    import { nextMonth } from '@abc/utils-date';

    export default {
        name: 'DialogAttachment',
        components: {
            ExternalFile,
        },
        props: {
            dialogType: {
                type: String,
                required: true,
            },
            title: {
                type: String,
                default: '上传资质证照',
            },
            item: {
                type: Object,
                default: () => {},
            },
        },
        data() {
            return {
                constants,
                options,
                tools,
                BusinessTypeEnum,
                formData: null,
            };
        },
        computed: {
            // 是否有更新
            isUpdated() {
                return !isEqual(this.formData, this.createFormData());
            },
            // 未上传附件不允许提交
            isDisabledBtn() {
                return (this.formData.pictureUrls || []).length === 0;
            },
            currentTime() {
                if (!this.formData.validTo) {
                    return '';
                }
                const validToDate = new Date(this.formData.validTo);
                // 格式化为 YYYY-MM-DD 23:59:59
                const year = validToDate.getFullYear();
                const month = String(validToDate.getMonth() + 1).padStart(2, '0');
                const day = String(validToDate.getDate()).padStart(2, '0');
                return `${year}-${month}-${day} 23:59:59`;
            },
            // 是否已到期
            isExpired() {
                const date1 = new Date(this.currentTime);
                const date2 = new Date();
                return date1.getTime() - date2.getTime() < 0;
            },
            helpText() {
                return this.isExpired ? '证照已过期，请尽快换证' : this.adventDays > -1 ? `${this.adventDays} 天后到期，请及时换证` : '';
            },
            helpTheme() {
                return this.isExpired ? 'danger' : 'warning';
            },
            // 临期时间
            adventDays() {
                if (!this.currentTime) {
                    return -1;
                }
                const date1 = new Date(this.currentTime);
                const date2 = new Date(this.getThreeMonthDay());
                const date3 = new Date();
                const flag = date1.getTime() <= date2.getTime();
                if (flag && !this.isExpired) {
                    // 计算时间差并转换为天数
                    const diffTime = date1.getTime() - date3.getTime();
                    const diffDays = Math.floor(diffTime / (24 * 3600 * 1000));
                    return diffDays;
                }
                return -1;
            },
        },
        created() {
            this.formData = this.createFormData();

            const { socket } = AbcSocket.getSocket();
            this._socket = socket;
            this._socket.on('short-url.upload_attachment', this.handleImages);
        },
        beforeDestroy() {
            this._socket.off('short-url.upload_attachment', this.handleImages);
        },
        methods: {
            getThreeMonthDay() {
                // 产品要求计算3个自然月后的时间
                const threeMonthDays = new Date(nextMonth(new Date(nextMonth(new Date(nextMonth(new Date()))))));
                const validToDate = new Date(threeMonthDays);
                const year = validToDate.getFullYear();
                const month = String(validToDate.getMonth() + 1).padStart(2, '0');
                const day = String(validToDate.getDate()).padStart(2, '0');
                return `${year}-${month}-${day} 00:00:00`;
            },
            handleImages(data) {
                const {
                    attachments = [],
                    businessType,
                } = data;

                if (+businessType === BusinessTypeEnum.PHARMACY_SUPPLIER_CERTIFICATE && attachments?.length) {
                    attachments.forEach((item) => {
                        const isExist = this.formData.pictureUrls?.find((it) => it.id === item.id);
                        if (!isExist) {
                            this.formData.pictureUrls.push(item);
                        }
                    });
                }

            },
            /**
             * 创建表单数据
             * <AUTHOR>
             * @date 2024-01-19
             * @returns {Object}
             */
            createFormData() {
                const formData = {
                    pictureUrls: [], // 图片
                    type: '', // 证书类型
                    name: '', // 证书名称
                    no: '', // 证书编码
                    validFrom: '', // 有效期 - 开始
                    validTo: '', // 有效期 - 截止
                };
                if (this.item) {
                    const item = clone(this.item);
                    Object.assign(formData, item);
                }
                return formData;
            },
            /**
             * 当改变证照类型时
             * <AUTHOR>
             * @date 2024-01-19
             */
            onChangeType() {
                this.formData.name = tools.getCertTypeWording(this.formData.type);
            },
            /**
             * 当点击确认
             * <AUTHOR>
             * @date 2024-01-19
             */
            onClickConfirm() {
                if (this.isDisabledBtn) {
                    this.$Toast.warning('请上传资质证照图片');
                    return;
                }
                this.$refs.formData.validate((val) => {
                    if (val) {
                        this.$emit('confirm', this.formData);
                    }
                });
            },
            /**
             * 当点击取消
             * <AUTHOR>
             * @date 2024-01-19
             */
            onClickCancel() {
                this.$emit('input', false);
            },
        },
    };
</script>

<style lang="scss">
    @import 'styles/abc-common.scss';

    .pharmacy__first-battalion__supplier__dialog-doc-edit {
        section {
            width: 100%;
            height: 100%;

            @include flex(row, center, flex-start);

            .attachment-left {
                margin-right: 24px;

                .external-files-wrapper {
                    margin: 0;

                    > .item {
                        margin: 0;
                    }
                }

                .file-add {
                    margin: 0;
                }
            }

            .abc-form-item {
                margin-bottom: 16px;
            }
        }
    }
</style>

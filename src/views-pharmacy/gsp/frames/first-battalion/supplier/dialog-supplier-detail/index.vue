<template>
    <dialog-approval-box
        :title="title"
        :loading.sync="loading"
        :editing.sync="editing"
        :inst-id="instId"
        :fetch-sync="getGspDetail"
    >
        <template v-if="editing">
            <view-form
                v-if="!!supplierDetail"
                ref="view-form"
                :supplier-id="supplierId"
                :supplier-detail="supplierDetail"
            ></view-form>
            <div
                slot="footer"
                class="dialog-footer"
            >
                <abc-button
                    :loading="loadingSubmit"
                    @click="onClickSubmit"
                >
                    提交
                </abc-button>
                <abc-button
                    type="blank"
                    variant="outline"
                    @click="onClickCancel"
                >
                    取消
                </abc-button>
            </div>
        </template>
        <view-info
            v-else
            :supplier-id="supplierId"
            :supplier-detail="supplierDetail"
            :gsp-detail="gspDetail"
            :is-approval-form="isApprovalForm"
        ></view-info>
    </dialog-approval-box>
</template>

<script>
    import SupplierApi from 'api/goods/supplier';
    import Response from 'utils/Response';

    import ViewForm from './view-form.vue';
    import ViewInfo from './view-info.vue';
    import DialogApprovalBox from '@/views-pharmacy/components/dialog-approval-box/index.vue';
    import { businessTypeConst } from '@/views-pharmacy/common/constants';

    export default {
        components: {
            ViewForm,
            ViewInfo,
            DialogApprovalBox,
        },
        props: {
            // 供应商id
            supplierId: {
                type: String,
                required: true,
            },
            // 审核任务id
            gspInstId: {
                type: String,
                default: '',
            },
            // 审批单号
            gspOrderNo: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                loading: false,
                editing: false, // 是否编辑信息
                instId: '', // 审批ID
                supplierDetail: null, // 供应商详情
                gspDetail: null, // 审批详情
                isApprovalForm: false,// 是否是修改审批单
                loadingSubmit: false,
                titlePrefix: '供应商首营申请',
            };
        },
        computed: {
            // 标题
            title() {
                let tit = this.titlePrefix;
                if (!this.editing) {
                    // 非编辑状态，显示单号
                    tit += ` ${this.gspOrderNo || ''}`;
                }
                return tit;
            },
            // 是否首次GSP提交
            isStartGsp() {
                return !this.gspInstId;
            },
            // 是否再次GSP提交
            isAgainGsp() {
                return !!this.gspInstId;
            },
        },
        created() {
            if (this.isStartGsp) {
                this.editing = true;
            }
            if (this.isAgainGsp) {
                this.instId = this.gspInstId;
            }
        },
        async mounted() {
            this.loading = true;
            const fetchResponse = await this.fetchSupplierDetail();
            if (fetchResponse.status === true) {
                this.supplierDetail = fetchResponse.data;
            }
            this.loading = false;
        },
        methods: {
            // 获取gsp审批详情
            getGspDetail(detail) {
                if (detail?.businessType === businessTypeConst.goodsSupplierModify) {
                    this.titlePrefix = detail?.businessName || '供应商修改申请';
                    this.isApprovalForm = true;
                }
                this.gspDetail = detail;
            },
            /**
             * 获取供应商详情
             * <AUTHOR>
             * @date 2023-12-27
             * @returns {Promise<Response>}
             */
            async fetchSupplierDetail() {
                let response = null;
                try {
                    const params = {
                        withDeleted: 1, // 即使删除，也能查出信息
                    };
                    const res = await SupplierApi.fetchSupplierDetail(this.supplierId, params);
                    response = Response.success(res.data);
                    this.supplierDetail = res?.data;
                } catch (error) {
                    console.log('fetchSupplierDetail error', error);
                    response = Response.error(error?.message || '接口异常', error);
                }
                return response;
            },
            /**
             * 更新supplier信息并提交首营
             * <AUTHOR>
             * @date 2024-01-16
             * @param {any} ...args
             * @returns {Promise<Response>}
             */
            async updateSupplier(...args) {
                let response = null;
                try {
                    const res = await SupplierApi.updateSupplier(...args);
                    response = Response.success(res.data.data);
                } catch (error) {
                    console.log('updateSupplier error', error);
                    response = Response.error(error?.message || '接口异常', error);
                }
                return response;
            },
            /**
             * 当点击提交按钮时
             * <AUTHOR>
             * @date 2024-01-15
             */
            async onClickSubmit() {
                const viewForm = this.$refs['view-form'];
                // 表单校验
                const isPass = await viewForm.validateFormData();
                if (!isPass) {
                    return;
                }
                // 提交申请
                this.loadingSubmit = true;
                const postData = viewForm.createPostData();
                const updateResponse = await this.updateSupplier(this.supplierId, postData);
                if (updateResponse.status === false) {
                    this.loadingSubmit = false;
                    return updateResponse;
                }
                // 拉取详情
                const fetchResponse = await this.fetchSupplierDetail();
                if (fetchResponse.status === false) {
                    this.loadingSubmit = false;
                    return updateResponse;
                }
                this.supplierDetail = fetchResponse.data;
                this.loadingSubmit = false;
                this.$Toast({
                    type: 'success',
                    message: '提交首营成功',
                });
                if (this.isStartGsp) {
                    // 首次GSP提交 - 直接关闭弹窗
                    this.$emit('confirm');
                    return;
                }
                if (this.isAgainGsp) {
                    // 再次GSP提交 - 展开审批详情
                    this.instId = this.supplierDetail.gsp.gspInstId;
                    this.editing = false;
                    this.$emit('fetch-data-list');
                    return;
                }
                return updateResponse;
            },
            /**
             * 当点击取消按钮时
             * <AUTHOR>
             * @date 2024-01-15
             */
            onClickCancel() {
                if (this.isAgainGsp) {
                    // 再次GSP提交 - 此时是取消编辑
                    this.editing = false;
                    return;
                }
                this.$emit('input', false);
            },
        },
    };
</script>

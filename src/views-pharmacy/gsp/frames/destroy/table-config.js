// eslint-disable-next-line max-classes-per-file
import BaseClinicTypeTable from 'views/layout/tables/base-clinic-type-table';

export class DestroyApplyTableConfig extends BaseClinicTypeTable {
    constructor(props) {
        super(props);
        this.chainTableConfig = {
            'hasInnerBorder': false,
            'list': [{
                'label': '申请单号',
                'key': 'recordNo',
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '状态',
                'key': 'status',
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            },{
                'label': '商品',
                'key': 'destroyItems',
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '248px','paddingLeft': '80px','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '申请人',
                'key': 'submitterName',
                'style': {
                    'flex': 1,'width': '','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '申请时间',
                'key': 'submitterTime',
                'style': {
                    'flex': 1,'width': '','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            }],
        };

        this.singleTableConfig = {
            'hasInnerBorder': false,
            'list': [{
                'label': '申请单号',
                'key': 'recordNo',
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '状态',
                'key': 'status',
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            },{
                'label': '商品',
                'key': 'destroyItems',
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '248px','paddingLeft': '80px','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '申请人',
                'key': 'submitterName',
                'style': {
                    'flex': 1,'width': '','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '申请时间',
                'key': 'submitterTime',
                'style': {
                    'flex': 1,'width': '','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            }],
        };
    }
}

export class DestroyApplySearchTableConfig extends BaseClinicTypeTable {
    constructor(props) {
        super(props);
        this.chainTableConfig = {
            'hasInnerBorder': false,
            'list': [{
                'label': '申请单号',
                'key': 'recordNo',
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '状态',
                'key': 'approvalStatus',
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            },{
                'label': '商品',
                'key': 'goods',
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '248px','paddingLeft': '80px','paddingRight': '','textAlign': 'left',
                },
                'pinned': true,
            },{
                'label': '数量',
                'key': 'num',
                'pinned': true,
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                },
            },{
                'label': '金额',
                'key': 'amount',
                'pinned': true,
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '16px','textAlign': 'right',
                },
            },{
                'label': '申请人',
                'key': 'submitterName',
                'pinned': true,
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '申请时间',
                'key': 'submitterTime',
                'pinned': true,
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            }],
        };

        this.singleTableConfig = {
            'hasInnerBorder': false,
            'list': [{
                'label': '申请单号',
                'key': 'recordNo',
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '状态',
                'key': 'approvalStatus',
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            },{
                'label': '商品',
                'key': 'goods',
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '248px','paddingLeft': '80px','paddingRight': '','textAlign': 'left',
                },
                'pinned': true,
            },{
                'label': '数量',
                'key': 'num',
                'pinned': true,
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                },
            },{
                'label': '金额',
                'key': 'amount',
                'pinned': true,
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '16px','textAlign': 'right',
                },
            },{
                'label': '申请人',
                'key': 'submitterName',
                'pinned': true,
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '申请时间',
                'key': 'submitterTime',
                'pinned': true,
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            }],
        };
    }
}

export class DestroyDoneTableConfig extends BaseClinicTypeTable {
    constructor(props) {
        super(props);
        this.chainTableConfig = {
            'hasInnerBorder': false,
            'list': [{
                'label': '销毁单号',
                'key': 'processNo',
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '状态',
                'key': 'status',
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            },{
                'label': '商品',
                'key': 'destroyItems',
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '248px','paddingLeft': '80px','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '销毁方式',
                'key': 'destroyMode',
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '销毁人',
                'key': 'destroyPersonnel',
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '监督人',
                'key': 'supervisoryPersonnel',
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '销毁时间',
                'key': 'destroyTime',
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            }],
        };

        this.singleTableConfig = {
            'hasInnerBorder': false,
            'list': [{
                'label': '销毁单号',
                'key': 'processNo',
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '状态',
                'key': 'status',
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            },{
                'label': '商品',
                'key': 'destroyItems',
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '248px','paddingLeft': '80px','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '销毁方式',
                'key': 'destroyMode',
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '销毁人',
                'key': 'destroyPersonnel',
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '监督人',
                'key': 'supervisoryPersonnel',
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '销毁时间',
                'key': 'destroyTime',
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            }],
        };
    }
}

export class DestroyDoneSearchTableConfig extends BaseClinicTypeTable {
    constructor(props) {
        super(props);
        this.chainTableConfig = {
            'hasInnerBorder': false,
            'list': [{
                'label': '销毁单号',
                'key': 'processNo',
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '状态',
                'key': 'status',
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            },{
                'label': '商品',
                'key': 'goods',
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '248px','paddingLeft': '80px','paddingRight': '','textAlign': 'left',
                },
                'pinned': true,
            },{
                'label': '数量',
                'key': 'num',
                'pinned': true,
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                },
            },{
                'label': '金额',
                'key': 'totalPrice',
                'pinned': true,
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '16px','textAlign': 'right',
                },
            },{
                'label': '申请人',
                'key': 'submitterName',
                'pinned': true,
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '申请时间',
                'key': 'submitterTime',
                'pinned': true,
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            }],
        };

        this.singleTableConfig = {
            'hasInnerBorder': false,
            'list': [{
                'label': '销毁单号',
                'key': 'processNo',
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '状态',
                'key': 'status',
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            },{
                'label': '商品',
                'key': 'goods',
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '248px','paddingLeft': '80px','paddingRight': '','textAlign': 'left',
                },
                'pinned': true,
            },{
                'label': '数量',
                'key': 'num',
                'pinned': true,
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                },
            },{
                'label': '金额',
                'key': 'totalPrice',
                'pinned': true,
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '16px','textAlign': 'right',
                },
            },{
                'label': '申请人',
                'key': 'submitterName',
                'pinned': true,
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '申请时间',
                'key': 'submitterTime',
                'pinned': true,
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            }],
        };
    }
}

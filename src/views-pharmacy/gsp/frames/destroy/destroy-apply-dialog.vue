<template>
    <frame-dialog
        class="pharmacy__destroy-apply--dialog"
        title="销毁申请"
        :value="showDialog"
        :auto-focus="false"
        :right-width="rightWidth"
        @input="(val) => $emit('input', val)"
    >
        <abc-layout v-abc-loading="tableLoading">
            <abc-form ref="formData" item-no-margin>
                <abc-section>
                    <abc-form-item-group is-excel>
                        <abc-descriptions
                            :column="3"
                            :label-width="100"
                            size="large"
                            grid
                            background
                        >
                            <abc-descriptions-item label="申请时间" :span="1" content-padding="0">
                                <abc-form-item required>
                                    <abc-date-time-picker
                                        v-model="formData.reportDate"
                                        :disabled="disabled"
                                        :date-width="172"
                                        :time-width="126"
                                        :time-step="1"
                                        :time-row="20"
                                        :picker-options="pickerOptions"
                                    ></abc-date-time-picker>
                                </abc-form-item>
                            </abc-descriptions-item>
                            <abc-descriptions-item label="申请人" :span="1" content-padding="0">
                                <abc-form-item required>
                                    <abc-select
                                        v-model="formData.submitterId"
                                        :fetch-suggestions="(searchKey) => searchKeyRegistrant = searchKey"
                                        :disabled="disabled"
                                        :no-icon="disabled"
                                        with-search
                                        size="large"
                                    >
                                        <abc-option
                                            v-for="item in registrantOptions"
                                            :key="item.value"
                                            :value="item.value"
                                            :label="item.label"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                            </abc-descriptions-item>
                            <abc-descriptions-item label="备注" :span="1" content-padding="0">
                                <abc-form-item>
                                    <abc-input v-model="formData.remark" :disabled="disabled"></abc-input>
                                </abc-form-item>
                            </abc-descriptions-item>
                        </abc-descriptions>
                    </abc-form-item-group>
                </abc-section>
                <abc-section>
                    <abc-table
                        ref="tableRef"
                        type="excel"
                        class="focus-table"
                        :render-config="renderConfig"
                        :data-list="dataList"
                        empty-size="small"
                        :auto-height="true"
                        fill-height
                        :fill-reference-el="dialogBody"
                        :fill-bottom-offset="fillBottomOffset"
                        :support-delete-tr="!disabled"
                        @delete-tr="handleDeleteTr"
                    >
                        <template v-if="isAdd" #footer>
                            <abc-flex justify="flex-end" style="padding: 0 8px;">
                                <abc-button variant="text" @click="destroyApplyChoiceDialogVisible = true">
                                    选择销毁商品
                                </abc-button>
                            </abc-flex>
                        </template>
                    </abc-table>
                </abc-section>
            </abc-form>
        </abc-layout>
        <template #right>
            <abc-layout>
                <approval-flow v-if="!!approvalViewModel.approvalDetail" :inst-detail="approvalViewModel.approvalDetail"></approval-flow>
            </abc-layout>
        </template>
        <template #footer>
            <abc-flex justify="flex-end">
                <template v-if="canApprove">
                    <abc-button
                        :loading="approvalViewModel.loadingAgree"
                        :disabled="approvalViewModel.loadingReject"
                        @click="handleApprovalAgree"
                    >
                        同意
                    </abc-button>
                    <abc-button
                        theme="danger"
                        variant="ghost"
                        :loading="approvalViewModel.loadingReject"
                        :disabled="approvalViewModel.loadingAgree"
                        @click="handleApprovalReject"
                    >
                        驳回
                    </abc-button>
                </template>
                <template v-else>
                    <abc-button v-if="isAdd" :disabled="!dataList.length" @click="createdDestroyApply">
                        确定
                    </abc-button>
                    <abc-button variant="ghost" @click="showDialog = false">
                        {{ isAdd ? '取消' : '关闭' }}
                    </abc-button>
                </template>
            </abc-flex>
        </template>
        <destroy-apply-choice-dialog
            v-if="destroyApplyChoiceDialogVisible"
            v-model="destroyApplyChoiceDialogVisible"
            :current-goods="dataList"
            @choiceGoodsList="choiceGoodsList"
        ></destroy-apply-choice-dialog>
    </frame-dialog>
</template>

<script>
    import GspAPI from '@/api/pharmacy/gsp';
    import FrameDialog from '@/views-pharmacy/inventory/frames/components/order-frame-dialog.vue';
    import ApprovalFlow from '@/views-pharmacy/components/approval-flow/index.vue';
    import useApprovalOrder from '@/views-pharmacy/common/hooks/approval-order';
    import { useFillHeightInDialog } from '@/views-pharmacy/inventory/hooks/useFillHeightInDialog';
    import * as tools from '@/views-pharmacy/common/tools';
    import { mapGetters } from 'vuex';
    import DestroyApplyChoiceDialog from '@/views-pharmacy/gsp/frames/destroy/destroy-apply-choice-dialog.vue';
    import { formatDate } from '@abc/utils-date';
    import {
        createGUID, paddingMoney,
    } from '@/utils';
    export default {
        name: 'DestroyApplyDialog',
        components: {
            ApprovalFlow,
            FrameDialog,
            DestroyApplyChoiceDialog,
        },
        props: {
            value: Boolean,
            gspInstId: {
                type: String,
                default: '',
            },
            id: {
                type: String,
                default: '',
            },
        },
        setup() {
            const {
                viewModel,
                canApprove,
                fetchApprovalDetail,
                approvalAgree,
                approvalReject,
            } = useApprovalOrder();
            const {
                dialogBody,
                tableRef,
                fillBottomOffset,
                logs,
                setLogs,
                openDialog,
            } = useFillHeightInDialog();
            return {
                dialogBody,
                tableRef,
                fillBottomOffset,
                logs,
                setLogs,
                openDialog,
                approvalViewModel: viewModel,
                canApprove,
                fetchApprovalDetail,
                approvalAgree,
                approvalReject,
            };
        },
        data() {
            const pickerOptions = {
                disabledDate(date) {
                    return date > new Date() || date < new Date('2001-11-01');
                },
            };
            return {
                pickerOptions,
                tableLoading: false,
                dataList: [],
                formData: {
                    submitterId: '',
                    remark: '',
                    reportDate: tools.getDatetimeFormat(),
                    clinicName: '',
                },
                searchKeyRegistrant: '',
                destroyApplyChoiceDialogVisible: false,
            };
        },
        created() {
            if (this.isAdd) {
                this.formData.clinicName = this.currentClinic?.clinicName || '';
                const target = this.registrantOptions.find((item) => item.value === this.userInfo?.id) || this.registrantOptions[0];
                this.formData.submitterId = target?.value || '';
            } else {
                this.fetchDestroyDetail(this.id);
            }
            if (this.gspInstId) {
                this.fetchApprovalDetail(this.gspInstId);
            }
        },
        computed: {
            ...mapGetters([
                'isChainAdmin',
                'currentClinic',
                'userInfo',
                'employeeList',
            ]),
            isAdd() {
                return !this.id;
            },
            disabled() {
                return !this.isAdd;
            },
            registrantOptions() {
                let employeeList = this.employeeList || [];
                if (this.searchKeyRegistrant) {
                    // 有关键词时，过滤一下
                    employeeList = tools.filterEmployeeBySearchKey(employeeList, this.searchKeyRegistrant);
                }
                return employeeList.map((item) => ({
                    value: item.employeeId,
                    label: item.employeeName,
                }));
            },
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            rightWidth() {
                if (this.gspInstId) return 320;
                return 0;
            },
            renderConfig() {
                return {
                    list: [
                        {
                            label: '不合格品编号',
                            key: 'unqualifiedNo',
                            style: {
                                minWidth: '120px',
                                maxWidth: '120px',
                                width: '120px',
                                paddingLeft: '',
                                paddingRight: '',
                            },
                        },
                        {
                            label: '商品编码',
                            key: 'shortId',
                            style: {
                                width: '100px',
                                minWidth: '100px',
                                paddingLeft: '',
                                paddingRight: '',
                            },
                        },
                        {
                            label: '商品名称',
                            key: 'goods',
                            style: {
                                minWidth: '140px',
                                flex: 1,
                                paddingLeft: '',
                                paddingRight: '',
                            },
                            customRender: (h, row) => {
                                return (<abc-table-cell>
                                    <abc-flex vertical align="flex-start" style="width:100%;">
                                        <div className="ellipsis" style="width:100%;line-height: 22px;color: #000000;font-size:14px;font-weight: 500;overflow: hidden; text-overflow: ellipsis;word-break: keep-all;white-space: nowrap;" title={row?.goodsName || ''}>{row?.goodsName || ''}</div>
                                        <div className="ellipsis" style="width:100%;font-size: 12px;color: #7A8794;line-height: 16px;overflow: hidden; text-overflow: ellipsis;word-break: keep-all;white-space: nowrap;">
                                            {row?.goodsSpec || ''} {row?.manufacturer || ''}
                                        </div>
                                    </abc-flex>
                                </abc-table-cell>);
                            },
                        },
                        {
                            label: '剂型',
                            key: 'dosageFormType',
                            style: {
                                minWidth: '80px',
                                maxWidth: '80px',
                                paddingLeft: '',
                                paddingRight: '',
                            },
                            dataFormatter: (_,row) => {
                                const dosageFormTypeName = row?.dosageFormTypeName;
                                return dosageFormTypeName === '未知' ? '' : dosageFormTypeName;
                            },
                        },
                        {
                            label: '批准文号',
                            key: 'medicineNmpn',
                            style: {
                                width: '80px',
                                minWidth: '80px',
                                paddingLeft: '',
                                paddingRight: '',
                            },
                        },
                        {
                            label: '生产批号',
                            key: 'batchNo',
                            style: {
                                minWidth: '100px',
                                maxWidth: '100px',
                                paddingLeft: '',
                                paddingRight: '',
                            },
                        },
                        {
                            label: '有效日期',
                            key: 'expiryDate',
                            dataFormatter: (expiryDate) => {
                                if (!expiryDate) {
                                    return '';
                                }
                                return tools.getDateFormat(expiryDate);
                            },
                            style: {
                                minWidth: '110px',
                                maxWidth: '110px',
                            },
                        },
                        {
                            label: '入库日期',
                            key: 'inDate',
                            dataFormatter: (inDate) => {
                                if (!inDate) {
                                    return '';
                                }
                                return tools.getDateFormat(inDate);
                            },
                            style: {
                                minWidth: '110px',
                                maxWidth: '110px',
                            },
                        },
                        {
                            label: '供应商',
                            key: 'supplier',
                            style: {
                                minWidth: '100px',
                                maxWidth: '100px',
                                paddingLeft: '',
                                paddingRight: '',
                            },
                        },
                        {
                            label: '数量',
                            key: 'num',
                            style: {
                                minWidth: '80px',
                                maxWidth: '80px',
                                paddingLeft: '',
                                paddingRight: '',
                            },
                            dataFormatter: (_, row) => {
                                const {
                                    packageUnit = '盒',
                                    packageCount = 0,
                                    pieceUnit = '包',
                                    pieceCount = 0,
                                } = row;
                                if (packageCount) {
                                    let text = `${packageCount}${packageUnit}`;
                                    if (pieceCount) {
                                        text = `${text}${pieceCount}${pieceUnit}`;
                                    }
                                    return text;
                                }
                                return `${pieceCount || 0}${pieceUnit}`;
                            },
                        },
                        {
                            label: '金额',
                            key: 'amount',
                            style: {
                                minWidth: '80px',
                                maxWidth: '80px',
                                paddingLeft: '',
                                paddingRight: '',
                            },
                            dataFormatter: (amount) => {
                                return paddingMoney(amount,true);
                            },
                        },
                        {
                            label: '不合格原因',
                            key: 'unqualifiedReason',
                            style: {
                                minWidth: '120px',
                                maxWidth: '120px',
                                paddingLeft: '',
                                paddingRight: '',
                            },
                        },
                        {
                            label: '来源门店',
                            key: 'clinicName',
                            style: {
                                minWidth: '120px',
                                maxWidth: '120px',
                                paddingLeft: '',
                                paddingRight: '',
                            },
                            hidden: !this.isChainAdmin,
                        },
                    ],
                };
            },
        },
        methods: {
            handleDeleteTr(index) {
                this.dataList.splice(index, 1);
            },
            async fetchDestroyDetail(recordId) {
                this.tableLoading = true;
                try {
                    const { data } = await GspAPI.fetchDestroyDetail(recordId);
                    this.dataList = data?.destroyItems?.map((i) => {
                        return {
                            ...i,
                            keyId: createGUID(),
                        };
                    }) || [];
                    this.formData = {
                        submitterId: data?.submitterId || '',
                        remark: data?.remark || '',
                        reportDate: data?.submitterTime ? formatDate(data.submitterTime, 'YYYY-MM-DD HH:mm') : '',
                        clinicName: data?.clinicName || '',
                    };
                } catch (e) {
                    console.log(e);
                } finally {
                    this.tableLoading = false;
                }
            },
            choiceGoodsList(goodsList) {
                this.dataList = goodsList?.map((i) => {
                    return {
                        ...i,
                        checked: false,
                        keyId: createGUID(),
                    };
                });
            },
            async createdDestroyApply() {
                this.$refs.formData.validate(async (valid) => {
                    if (!valid) {
                        return;
                    }
                    const {
                        reportDate = '',submitterId = '',remark = '',
                    } = this.formData;
                    const params = {
                        clinicId: this.currentClinic.clinicId,
                        reportDate: `${reportDate}:00`,
                        submitterId,
                        remark,
                        destroyItems: this.dataList.map((item) => {
                            return {
                                unqualifiedNo: item.unqualifiedNo,
                                clinicId: item.clinicId,
                                relatedType: item.relatedType,
                                relatedId: item.relatedId,
                                goodsId: item.goodsId,
                                batchId: item.batchId,
                                batchNo: item.batchNo,
                                packageCount: item.packageCount,
                                pieceCount: item.pieceCount,
                                amount: item.amount,
                                unqualifiedReason: item.unqualifiedReason,
                            };
                        }),
                    };
                    try {
                        const { data } = await GspAPI.createdDestroy(params);
                        if (data === 200) {
                            this.$Toast({
                                type: 'success',
                                message: '操作成功',
                            });
                            this.showDialog = false;
                            this.$emit('refresh');
                        }
                    } catch (e) {
                        this.$Toast({
                            type: 'error',
                            message: '操作失败',
                        });
                        console.log(e);
                    }
                });
            },
            async handleApprovalAgree() {
                const response = await this.approvalAgree({
                    gspInstId: this.gspInstId,
                });
                if (response.status === false) {
                    return;
                }
                this.showDialog = false;
                this.$emit('confirm');
                this.$emit('refresh');
            },

            async handleApprovalReject() {
                const response = await this.approvalReject(this.gspInstId);
                if (response.status === false) {
                    return;
                }
                this.showDialog = false;
                this.$emit('confirm');
                this.$emit('refresh');
            },
        },
    };
</script>

<style lang="scss">
.pharmacy__destroy-apply--dialog {
    width: 100%;
}
</style>

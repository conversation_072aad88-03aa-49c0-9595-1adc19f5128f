<template>
    <abc-dialog
        v-model="dialogVis"
        title="选择资质证照类型"
        content-styles="width: 560px;padding: 24px"
    >
        <abc-flex vertical :gap="16">
            <abc-flex
                v-for="(t,idx) in bizTypeList"
                :key="idx"
                align="center"
                justify="center"
                class="license-type-item"
                @click="handleSelectType(t)"
            >
                {{ t.name }}
            </abc-flex>
        </abc-flex>
    </abc-dialog>
</template>

<script>
    export default {
        name: 'SelectLicenseType',

        props: {
            value: Boolean,

            bizTypeList: {
                type: Array,
                default: () => ([]),
            },
        },

        computed: {
            dialogVis: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
        },

        methods: {
            handleSelectType(t) {
                this.$emit('select-type', t);
                this.dialogVis = false;
            },
        },
    };
</script>

<style lang="scss" scoped>
.license-type-item {
    width: 100%;
    height: 40px;
    cursor: pointer;
    border: 1px solid $P6;
    border-radius: var(--abc-border-radius-small);

    &:hover {
        border-color: $theme3;
    }
}
</style>

<template>
    <abc-layout preset="page-table" class="abc-pharmacy-gsp-frames-first-battalion-goods-container">
        <abc-layout-header>
            <abc-space>
                <abc-date-picker
                    v-model="toolsParams.dateRange"
                    :picker-options="pickerOptions"
                    type="daterange"
                    clearable
                ></abc-date-picker>
                <abc-input
                    v-model="toolsParams.keyword"
                    :width="260"
                    placeholder="审批名称"
                    clearable
                >
                    <abc-search-icon slot="prepend"></abc-search-icon>
                </abc-input>
                <abc-select
                    v-if="!isMineApplied"
                    v-model="toolsParams.submitterId"
                    :width="160"
                    :fetch-suggestions="(searchKey) => searchKeySubmitter = searchKey"
                    with-search
                    clearable
                    placeholder="提交人"
                >
                    <abc-option
                        v-for="item in submitterOptions"
                        :key="item.value"
                        :value="item.value"
                        :label="item.label"
                    ></abc-option>
                </abc-select>
            </abc-space>
        </abc-layout-header>
        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                ref="abcTable"
                :render-config="renderConfig"
                :loading="loading"
                :custom-tr-key="customTrKey"
                :data-list="dataList"
                tr-clickable
                @handleClickTr="onClickRow"
            ></abc-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :show-total-page="true"
                :pagination-params="pageParams"
                :count="count"
                @current-change="onChangePage"
            ></abc-pagination>
        </abc-layout-footer>
    </abc-layout>
</template>

<script>
    import ApprovalAPI from '@/api/pharmacy/approval';
    import pickerOptions from 'views/common/pickerOptions';
    import mixinTable from '@/views-pharmacy/common/mixin-table';
    const DialogGoodsDetail = () => import('@/views-pharmacy/gsp/frames/first-battalion/goods/dialog-goods-detail/index.vue');
    const DialogSuppliersDetail = () => import('@/views-pharmacy/gsp/frames/first-battalion/supplier/dialog-supplier-detail/index.vue');
    const DialogPriceAdjustmentDetail = () => import('@/views-pharmacy/inventory/frames/price-adjustment/detail.vue');
    const DialogRequireGoodsDetail = () => import('@/views-pharmacy/inventory/frames/purchase/require-goods/order-dialog.vue');
    const DialogLossGoodsDetail = () => import('views/inventory/goods-out/detail.vue');
    const DialogGoodsCheckDetail = () => import('views/inventory/goods-check/detail.vue');
    const DialogReturnGoodsDetail = () => import('@/views-pharmacy/inventory/frames/purchase/return-goods/detail.vue');
    const DialogSettlementDetail = () => import('@/views-pharmacy/inventory/frames/purchase/goods-settlement/review-form.vue');
    const DialogSettlementDetailV2 = () => import('@/views-pharmacy/inventory/frames/purchase/goods-settlement/application-form.vue');
    const SuspiciousQualityDialog = () => import('@/views-pharmacy/gsp/frames/suspicious-quality/suspicious-quality-dialog.vue');
    const DestroyApplyDialog = () => import('@/views-pharmacy/gsp/frames/destroy/destroy-apply-dialog.vue');
    import { mapGetters } from 'vuex';
    import { isEqual } from '@abc/utils';
    import * as constants from '@/views-pharmacy/common/constants';
    import * as options from '@/views-pharmacy/common/options';
    import * as tools from '@/views-pharmacy/common/tools';
    import { PharmacyApproveRouterNameKeys } from '@/views-pharmacy/approval/core/routes';
    import { ApprovalTableConfig } from '@/views-pharmacy/approval/frames/table-config';
    import SettlementAPI from 'api/goods/settlement';

    export default {
        name: 'PharmacyGspMineApproved',
        mixins: [
            pickerOptions,
            mixinTable,
        ],
        data() {
            return {
                constants,
                options,
                tools,
                searchKeySubmitter: '', // 搜索关键词 - 人员选项
                toolsParams: {
                    dateRange: this.$route.name === PharmacyApproveRouterNameKeys.mineApprovedPending ? '' : this.createDateRangeCurMonth(), // 时间范围
                    keyword: '', // 搜索关键词
                    submitterId: '', // 提交人ID
                },
                tableHeader: [],
                pageParams: {
                    pageIndex: 0,
                    pageSize: 10,
                },
                loading: false,
                originData: null,
            };
        },
        computed: {
            ...mapGetters([
                'employeeList',
                'currentClinic',
            ]),
            // 是否待处理
            isPending() {
                return this.$route.name === PharmacyApproveRouterNameKeys.mineApprovedPending;
            },
            // 是否已处理
            isCompleted() {
                return this.$route.name === PharmacyApproveRouterNameKeys.mineApprovedCompleted;
            },
            // 是否我审核的
            isMineApproved() {
                return this.isPending || this.isCompleted;
            },
            // 是否我发起的
            isMineApplied() {
                return this.$route.name === PharmacyApproveRouterNameKeys.mineAppliedDefault;
            },
            // 提交人选项
            submitterOptions() {
                let employeeList = this.employeeList || [];
                if (this.searchKeySubmitter) {
                    // 有关键词时，过滤一下
                    employeeList = tools.filterEmployeeBySearchKey(employeeList, this.searchKeySubmitter);
                }
                return employeeList.map((item) => ({
                    value: item.employeeId,
                    label: item.employeeName,
                }));
            },
            // 表格数据
            dataList() {
                return this.originData?.rows || [];
            },
            // 总条数
            count() {
                return this.originData?.total || 0;
            },
            renderConfig() {
                const tableConfig = new ApprovalTableConfig(this.currentClinic);
                return tableConfig.extendConfig({
                    status: {
                        customRender: (h, row) => {
                            const {
                                type, text, variant,
                            } = tools.createTagProps(row.status, false, true);
                            return (<abc-table-cell>
                                <abc-tag-v2 theme={type} variant={variant}>{text}</abc-tag-v2>
                            </abc-table-cell>);
                        },
                    },
                    candidateUsers: {
                        dataFormatter: (_,row) => (row.candidateUsers || []).map((item) => item.name).join('、'),
                    },
                    submitter: {
                        dataFormatter: (_,row) => row.submitter?.name || '',
                    },
                    submitTime: {
                        dataFormatter: (_,row) => tools.getDatetimeFormat(row.submitTime),
                    },
                });
            },
        },
        watch: {
            toolsParams: {
                handler() {
                    this.initPageIndex();
                    this.fetchDataList();
                },
                deep: true,
            },
        },
        async mounted() {
            this.setPageSizeWithTableHeight();
        },
        methods: {
            customTrKey(item) {
                return `approval_${item.approvalNo}`;
            },
            handleMounted(data) {
                this.pageParams.pageSize = data.paginationLimit;
                this.fetchDataList();
            },
            /**
             * 创建查询参数
             * <AUTHOR>
             * @date 2023-12-27
             * @returns {Object}
             */
            createParams() {
                const {
                    dateRange, // 搜索时间范围
                    keyword, // 搜索关键词
                    submitterId, // 提交人ID
                } = this.toolsParams;
                const {
                    pageIndex,
                    pageSize,
                } = this.pageParams;
                const params = {
                    submitterId, // 提交人ID
                    keyword, // 审批名称关键词
                    startTime: dateRange[0], // 开始时间
                    endTime: dateRange[1], // 结束时间
                    // 如果需要按状态返回，就将需要的状态添加到statusList里，否则返回待审批状态的内容
                    status: (() => {
                        const list = [];
                        if (this.isPending) {
                            // 我审核的 - 待处理
                            list.push(10);
                        } else if (this.isCompleted) {
                            // 我审批的 - 已处理
                            list.push(20, 30);
                        }
                        return list.join(',');
                    })(),
                    type: this.isMineApproved ? 0 : 1, // 查询的类型，0:我审批的，1：我发起的
                    offset: pageIndex * pageSize, // 分页
                    limit: pageSize, // 每页条数
                };
                return params;
            },
            /**
             * 拉取数据列表
             * <AUTHOR>
             * @date 2023-12-27
             */
            async fetchDataList() {
                this.loading = true;
                const params = this.createParams();
                const fetchResponse = await ApprovalAPI.fetchInstListMe(params);
                if (!isEqual(params, this.createParams())) {
                    return fetchResponse;
                }
                this.loading = false;
                if (fetchResponse.status === false) {
                    return fetchResponse;
                }
                this.originData = fetchResponse.data;
            },
            /**
             * 创建审批详情打开参数
             * <AUTHOR>
             * @date 2024-01-05
             * @param {Object} row
             * @returns {Object}
             */
            createOptions(row) {
                const {
                    businessId, // 业务id
                    businessType, // 业务类型
                    processInstId, // 审批流程id
                    version,
                } = row;
                const options = {
                    propsData: {},
                    component: null,
                };
                switch (businessType) {
                    case constants.businessTypeConst.goodsFirstOperation: // 药品首营申请
                    case constants.businessTypeConst.goodsMedicalEquipmentFirstOperation: // 医疗器械首营申请
                    case constants.businessTypeConst.goodsOtherGoodsFirstOperation: // 其他商品首营申请
                    case constants.businessTypeConst.goodsModify: // 药品首营修改
                    case constants.businessTypeConst.goodsMedicalEquipmentModify: // 医疗器械首营修改
                    case constants.businessTypeConst.goodsOtherGoodsModify: // 其他商品首营修改
                        Object.assign(options, {
                            propsData: {
                                goodsId: businessId, // 商品id
                                gspInstId: processInstId, // 审核任务id
                            },
                            component: DialogGoodsDetail,
                        });
                        break;
                    case constants.businessTypeConst.goodsSupplierFirstOperation: // 供应商首营申请
                    case constants.businessTypeConst.goodsSupplierModify: // 供应商修改申请
                        Object.assign(options, {
                            propsData: {
                                supplierId: businessId, // 供应商id
                                gspInstId: processInstId, // 审核任务id
                            },
                            component: DialogSuppliersDetail,
                        });
                        break;
                    case constants.businessTypeConst.marketingPriceAdjustment: // 调价申请
                        Object.assign(options, {
                            propsData: {
                                value: true,
                                orderId: businessId, // 调价单id
                                gspInstId: processInstId, // 审核任务id
                            },
                            component: DialogPriceAdjustmentDetail,
                        });
                        break;
                    // case constants.businessTypeConst.goodsReportingLosses: 'goodsReportingLosses', // 报损申请
                    // 商品采购单
                    case constants.businessTypeConst.goodsPurchaseOrder:
                    case constants.businessTypeConst.goodsClaimOrder:
                    case constants.businessTypeConst.goodsProcurementCollective:
                    case constants.businessTypeConst.goodsProcurementSelf:
                        Object.assign(options, {
                            propsData: {
                                value: true,
                                orderId: businessId,
                                gspInstId: processInstId, // 审核任务id
                            },
                            component: DialogRequireGoodsDetail,
                        });
                        break;

                    // 报损申请
                    case constants.businessTypeConst.goodsReportingLosses:
                        Object.assign(options, {
                            propsData: {
                                visible: true,
                                orderId: businessId,
                                title: '报损申请',
                                gspInstId: processInstId, // 审核任务id
                            },
                            component: DialogLossGoodsDetail,
                        });
                        break;

                    // 退货申请
                    case constants.businessTypeConst.goodsStockReturnOut:
                        Object.assign(options, {
                            propsData: {
                                value: true,
                                orderId: businessId,
                                title: '退货出库申请',
                                gspInstId: processInstId, // 审核任务id
                            },
                            component: DialogReturnGoodsDetail,
                        });
                        break;

                    // 采购结算单
                    case constants.businessTypeConst.goodsStockSettlementOrder:
                        Object.assign(options, {
                            propsData: {
                                value: true,
                                id: businessId,
                                gspInstId: processInstId, // 审核任务id
                            },
                            component: version === 1 ? DialogSettlementDetailV2 : DialogSettlementDetail,
                        });
                        break;

                    // 质量可疑上报/不合格品处理
                    case constants.businessTypeConst.goodsSuspiciousReport:
                        Object.assign(options, {
                            propsData: {
                                value: true,
                                id: businessId,
                                title: '不合格品上报申请',
                                gspInstId: processInstId, // 审核任务id
                            },
                            component: SuspiciousQualityDialog,
                        });
                        break;
                    // 销毁申请
                    case constants.businessTypeConst.goodsDestroy:
                        Object.assign(options, {
                            propsData: {
                                value: true,
                                id: businessId,
                                title: '销毁申请',
                                gspInstId: processInstId, // 审核任务id
                            },
                            component: DestroyApplyDialog,
                        });
                        break;

                    // 盘点结果审批
                    case constants.businessTypeConst.goodsStockCheck:
                        Object.assign(options, {
                            propsData: {
                                value: true,
                                orderId: businessId,
                                title: '盘点单',
                                gspInstId: processInstId, // 审核任务id
                                showReinitiateButton: false,
                            },
                            component: DialogGoodsCheckDetail,
                        });
                        break;
                    default:
                        break;
                }
                return options;
            },
            /**
             * 当点击一行触发
             * <AUTHOR>
             * @date 2023-12-25
             * @param {Object} row
             */
            async onClickRow(row) {
                if (this.loading) return;
                try {
                    // 结算单查详情判断版本（临时方案)
                    if (row.businessType === constants.businessTypeConst.goodsStockSettlementOrder) {
                        this.loading = true;
                        const { data } = await SettlementAPI.fetchSettlementDetail(row.businessId);
                        row.version = data?.version ?? 0;
                        this.loading = false;
                    }
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
                const options = this.createOptions(row);
                if (!options.component) {
                    // 没有找到对应审批配置
                    return;
                }
                const openResponse = await tools.openDialog(options);
                if (openResponse.status === false) {
                    return;
                }
                this.fetchDataList();
            },
            /**
             * 当切换页码时触发
             * <AUTHOR>
             * @date 2023-12-25
             * @param {Number} pageIndex
             */
            onChangePage(pageIndex) {
                this.pageParams.pageIndex = pageIndex - 1;
                this.fetchDataList();
            },
            /**
             * 初始化页码
             * <AUTHOR>
             * @date 2023-12-29
             */
            initPageIndex() {
                this.pageParams.pageIndex = 0;
            },
        },
    };
</script>

<style lang="scss">
    .abc-pharmacy-gsp-frames-first-battalion-goods-container {
        .abc-layout-section {
            flex: 1;

            thead > tr {
                background: #f6f8fa;
            }
        }
    }
</style>

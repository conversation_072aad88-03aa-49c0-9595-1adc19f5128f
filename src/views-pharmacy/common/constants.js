/*
 * <AUTHOR>
 * @DateTime 2023-12-29 10:33:51
 */
import {
    BaseDrug,
    OtcType,
    MaintainType,
    StorageType,
    SpecType,
} from 'views/common/inventory/constants';

// 首营审核状态
export const gspStatusConst = Object.freeze({
    AWAIT: 0, // 待首营
    DOING: 10, // 审核中
    REJECT: 20, // 已驳回
    FINISH: 30, // 已首营
    CANCEL: 50, // 已撤销
});

// 首营审核状态名称
export const gspStatusNameConst = Object.freeze({
    [gspStatusConst.AWAIT]: '待首营',
    [gspStatusConst.DOING]: '审批中',
    [gspStatusConst.REJECT]: '已驳回',
    [gspStatusConst.FINISH]: '已首营',
    [gspStatusConst.CANCEL]: '已撤销',
});

// 供应商类型
export const supplierTypeConst = Object.freeze({
    BUSINESS: 1, // 商业企业-药品批发企业
    MEDICINAL_MAKE: 10, // 药品生产企业-药品生产企业
    INSTRUMENT_MAKE: 20, // 器械生产企业-医疗器械生产企业
    INSTRUMENT_WHOLESALE: 30, // 医疗器械批发企业
    COSMETICS_MAKE: 40, // 化妆品生产企业
    OTHER: 1000, // 其他商业企业
});

// 养护类型
export const maintenanceTypeConst = Object.freeze({
    GENERAL: 0, // 一般养护
    KEYNOTE: 1, // 重点养护
});

// 包装状况
export const appearancePackageConditionConst = Object.freeze({
    GOOD: 0, // 良好
    WORN: 1, // 破损
});

// 质量状况
export const qualityConditionConst = Object.freeze({
    GOOD: 0, // 良好
    BAD: 1, // 变质
});

// 处理结果
export const resultConst = Object.freeze({
    NORMAL_SALES: 0, // 正常销售
    STOP_SALES: 1, // 停止销售
});

// 时间段
export const timePhaseConst = Object.freeze({
    AM: 0, // 上午
    PM: 1, // 下午
});

// 检查结果
export const checkResultConst = Object.freeze({
    PASS: 0, // 合格
    PASS_NO: 1, // 不合格
});

// 陈列检查类型
export const displayTypeConst = Object.freeze({
    ENV_OUT: 0, // 周边环境
    ENV_IN: 1, // 店内环境
    STORAGE: 2, // 存放条件
    HEALTH: 3, // 卫生情况
});

// 严重程度
export const severityConst = Object.freeze({
    SERIOUS: 0, // 严重
    GENERAL: 1, // 一般
});

// 解决结果
export const solveResultConst = Object.freeze({
    HEAL: 0, // 痊愈
    TO_GOOD: 1, // 好转
    NO_GOOD: 2, // 未好转
    SEQUELA: 3, // 存在后遗症
    DEATH: 4, // 死亡
});

// 处置措施类型
export const measureTypeConst = Object.freeze({
    BACK_CLINIC: 0, // 退回门店
    BACK_SUPPLIER: 1, // 退回供应商
    USELESS: 2, // 直接报损
    STORAGE: 3, // 封存
    OTHER: 4, // 其他
});

// 对原患疾病影响程度
export const effectLevelConst = Object.freeze({
    UNOBVIOUS: 0, // 不明显
    PROLONGED: 1, // 病程延长
    EXACERBATION: 2, // 病情加重
    SEQUELA: 3, // 导致后遗症
    DEATH: 4, // 导致死亡
});

// 评价
export const evaluateConst = Object.freeze({
    RELATED: 0, // 相关
    RELATED_POSSIBLE: 1, // 可能相关
    AWAIT_JUDGE: 2, // 待评价
    UNABLE_JUDGE: 3, // 无法判断
});

// 用药类型
export const useMedicinesTypeConst = Object.freeze({
    DOUBT_USED: 0, // 怀疑用药
    TOGETHER_USED: 1, // 并用药品
});

// 审批方式
export const approveTypeConst = Object.freeze({
    SOME: 0, // 任意一人通过
    EVERY: 1, // 全部通过
});

// 审批业务类型
export const businessTypeConst = Object.freeze({
    goodsFirstOperation: 'goodsFirstOperation', // 药品首营申请
    goodsSupplierFirstOperation: 'goodsSupplierFirstOperation', // 供应商首营申请
    goodsMedicalEquipmentFirstOperation: 'goodsMedicalEquipmentFirstOperation', // 医疗器械首营申请
    goodsOtherGoodsFirstOperation: 'goodsOtherGoodsFirstOperation', // 其他商品首营申请
    goodsReportingLosses: 'goodsReportingLosses', // 报损申请
    marketingPriceAdjustment: 'marketingPriceAdjustment', // 调价申请
    goodsPurchaseOrder: 'goodsPurchaseOrder', // 商品采购单
    goodsClaimOrder: 'goodsClaimOrder', // 商品要货单
    goodsModify: 'goodsModify', // 商品档案修改
    goodsMedicalEquipmentModify: 'goodsMedicalEquipmentModify', // 医疗器械档案修改
    goodsOtherGoodsModify: 'goodsOtherGoodsModify', // 其他商品档案修改
    goodsSupplierModify: 'goodsSupplierModify', // 供应商修改

    goodsProcurementCollective: 'goodsProcurementCollective', // 采购申请（总部集采/委托配送）
    goodsProcurementSelf: 'goodsProcurementSelf', // 采购申请（门店自采）
    goodsSuspiciousReport: 'goodsSuspiciousReport', // 不合格商品上报
    goodsDestroy: 'goodsDestroy', // 销毁申请
    goodsStockReturnOut: 'goodsStockReturnOut', // 采购退货单
    goodsStockSettlementOrder: 'goodsStockSettlementOrder', // 采购结算单

    goodsStockCheck: 'goodsStockTaking', // 盘点审批
});

// 是否造成质量事故
export const causeQualityAccidentConst = Object.freeze({
    FALSE: 0, // 否
    TRUE: 1, // 是
});

// 召回级别
export const recallLevelConst = Object.freeze({
    L1: 1, // 一级
    L2: 2, // 二级
    L3: 3, // 三级
});

// 召回方式
export const recallMethodConst = Object.freeze({
    STORE: 1, // 门店召回
    FACTORY: 2, // 厂家召回
});

// 召回状态
export const RecallStatus = Object.freeze({
    INIT: 0, // 初始状态
    RETURNING: 10, // 退货中
    RETURN_REJECT: 20, // 退货驳回
    RETURN_SUCCESS: 30, // 已退货
});

// 召回状态名称
export const RecallStatusName = Object.freeze({
    [RecallStatus.INIT]: '退货',
    [RecallStatus.RETURNING]: '退货中',
    [RecallStatus.RETURN_REJECT]: '退货驳回',
    [RecallStatus.RETURN_SUCCESS]: '已退货',
});

// 追回级别
export const recoverLevelConst = Object.freeze({
    L1: 1, // 一级
    L2: 2, // 二级
    L3: 3, // 三级
});

// 追回方式
export const recoverMethodConst = Object.freeze({
    STORE: 1, // 门店追回
    FACTORY: 2, // 厂家追回
});

// 有无健康证
export const healthCertificateConst = Object.freeze({
    NONE: 0, // 无
    HAVE: 1, // 有
});

// 性别
export const sexConst = Object.freeze({
    MALE: 0, // 男
    FEMALE: 1, // 女
});

// 培训方式
export const trainMethodConst = Object.freeze({
    ON_LINE: 0, // 线上课程
    OFF_LINE: 1, // 线下课程
});

// 考核结果
export const assessmentResultConst = Object.freeze({
    PASS: 0, // 合格
    PASS_NO: 1, // 不合格
});

// 考核结果
export const formItemTypeConst = Object.freeze({
    INPUT: 'INPUT',
    SELECT: 'SELECT',
    RADIO: 'RADIO',
    CHECKBOX: 'CHECKBOX',
    CHECKBOX_GROUP: 'CHECKBOX_GROUP',
    TEXTAREA: 'TEXTAREA',
    DATE_PICKER: 'DATE_PICKER',
    CUSTOM_HTML: 'CUSTOM_HTML',
    SELECT_GROUP: 'SELECT_GROUP',
    AUTO_COMPLETE: 'AUTO_COMPLETE',
    ADDRESS_SELECTOR: 'ADDRESS_SELECTOR',
    CASCADER: 'CASCADER',
});

// 基药
export const baseMedicineTypeConst = BaseDrug;

// 处方/OTC
export const otcTypeConst = OtcType;

// 养护类型
export const maintainTypeConst = MaintainType;

// 储存条件
export const storageTypeConst = StorageType;

// 剂量模式
export const specTypeConst = SpecType;

// 证照类型
export const certTypeConst = Object.freeze({
    WTS: 0, // 委托书
    YYZZ: 1, // 营业执照
    YPYY_XKZ: 2, // 药品经营许可证
    YPSC_XKZ: 3, // 药品生产许可证
    YLZY_XKZ: 4, // 医疗执业许可证
    GSP_ZS: 5, // GSP证书
    SX_XKZ: 6, // 卫生许可证
    QXSC_XKZ: 7, // 器械生产许可证
    ZLBZ_XY: 8, // 质量保证协议
    BJPWS_XKZ: 9, // 保健品卫生许可证
    SPJY_XKZ: 10, // 食品经营许可证，，
    YBZSP_JYBA: 11, // 预包装食品经营备案
    DELYLQX_JYBA: 12, // 第二类医疗器械经营备案
    OTHER: 99, // 其他
});

// 调控措施
export const wsMeasureConst = Object.freeze({
    WD_DOW: '1', // 降温
    WD_TOP: '2', // 升温
    SD_ADD: '3', // 加湿
    SD_RED: '4', // 除湿
});

// 药诊互通
export const bindRelationType = Object.freeze({
    CLINIC: 1,
    PHARMACY: 0,
});


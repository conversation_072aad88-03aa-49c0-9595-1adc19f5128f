
import * as tools from './tools';

// 处理table高度占满
export default {
    data() {
        return {
            tableMaxHeight: 290,
        };
    },
    computed: {
        // 表格最小高度
        tableMinHeight() {
            let minHeight = 0;
            if ((this.dataList || []).length === 0) {
                // 没数据时，loading位置有问题，处理下
                minHeight = 290;
            }
            return minHeight;
        },
    },
    methods: {
        /**
         * 设置分页大小和表格高度
         * <AUTHOR>
         * @date 2023-12-27
         */
        setPageSizeWithTableHeight() {
            const $abcSectionTable = this.$refs['abc-section-table'];
            if (!$abcSectionTable) {
                return;
            }
            const result = tools.calcPageSizeWithTableHeight($abcSectionTable.$el);
            this.pageParams.pageSize = result.pageSize;
            this.tableMaxHeight = result.tableHeight;
        },
    },
};
/*
 * <AUTHOR>
 * @DateTime 2023-12-27 14:33:54
 */
import Vue from 'vue';
import store from 'store';
import Response from 'utils/Response';
import fecha from 'utils/fecha';
import {
    DATE_FORMATE,
    DATE_TIME_MM_FORMATE,
} from 'assets/configure/constants';
import { formatAge } from 'utils/index';
import { isFunction } from 'utils/lodash';

import * as constants from './constants';
import * as options from './options';
import {
    StorageTypeArr, StorageTypeObj,
} from 'views/common/inventory/constants';

/**
 * 获取日期格式 YYYY-MM-DD
 * <AUTHOR>
 * @date 2024-01-08
 * @param {String} dateStr
 * @returns {String}
 */
export const getDateFormat = (dateStr) => {
    return fecha.format(dateStr || new Date(), DATE_FORMATE);
};

/**
 * 获取日期时间格式 YYYY-MM-DD HH:mm
 * <AUTHOR>
 * @date 2024-01-08
 * @param {String} dateStr
 * @returns {String}
 */
export const getDatetimeFormat = (dateStr) => {
    return fecha.format(dateStr || new Date(), DATE_TIME_MM_FORMATE);
};

/**
 * 显示目标类型
 * <AUTHOR>
 * @date 2020-05-26
 * @param {Array} options 选项类型
 * @param {String} value 值
 * @param {String} defaultValue 没有找到对应选项时，显示的默认值
 * @param {String} key
 * @returns {String} 对应label
 */
export const showTargetLabel = (options, value, defaultValue = '', key = 'label') => {
    const target = (options || []).find((item) => item.value === value);
    return target ? (target[key] || target.name) : defaultValue;
};

/**
 * 获取库存量描述
 * <AUTHOR>
 * @date 2020-05-26
 * @param {Object} goods
 * @returns {String}
 */
export const getInventoryCountWording = (goods) => {

    const {
        packageCount,
        packageUnit,
        pieceCount,
        pieceUnit,
    } = goods || {};
    let wording = '';
    if (packageCount && packageUnit) {
        wording += `${packageCount}${packageUnit}`;
    }
    if (pieceCount && pieceUnit) {
        wording += `${pieceCount}${pieceUnit}`;
    }
    if (!wording && packageUnit) {
        wording += `0${packageUnit}`;
    }
    if (!wording && pieceUnit) {
        wording += `0${pieceUnit}`;
    }
    return wording;
};

/**
 * 删除object对象上面的字段，字段来源于target
 * <AUTHOR>
 * @date 2024-01-22
 * @param {Object} object
 * @param {Object} target
 */
export const deleteKeyObject = (object, target) => {
    Object.keys(target).forEach((key) => {
        delete object[key];
    });
};

/**
 * 算分页每页条数
 * <AUTHOR>
 * @date 2023-12-27
 * @param {Element} node
 * @returns {Object}
 */
export const calcPageSizeWithTableHeight = (node) => {
    const colHeight = 40;
    const colCount = Math.floor((node.offsetHeight - 15) / 40); // 去掉tableHeader
    const result = {
        pageSize: colCount - 1,
        tableHeight: colCount * colHeight,
    };
    return result;
};

/**
 * 处理tableHeader的边框
 * <AUTHOR>
 * @date 2022-08-08
 * @param {Array} tableHeader
 * @returns {Array}
 */
export const handleTableHeaderBorder = (tableHeader) => {
    tableHeader.forEach((item) => {
        const textAlign = item.customAlign || 'left';
        item.thStyle = Object.assign({
            textAlign,
            padding: '0px 16px',
            borderRight: 'none',
        }, item.thStyle);
        item.tdStyle = Object.assign({
            textAlign,
            padding: '0px 6px',
            border: 'none',
        }, item.tdStyle);
    });
    return tableHeader;
};

/**
 * 渲染组件
 * <AUTHOR>
 * @date 2021-11-02
 * @param {Component} Component 组件对象
 * @param {Object} propsData props入参
 * @param {DOM} mountPoint 挂载节点
 * @param {Object} extendProps 函数式组件在extendProps中传入parent: this，就可以在组件内使用store/router
 * @returns {Object}
 */
export const renderComponent = (Component, propsData = {}, mountPoint, extendProps = {}) => {
    const CompConstructor = Vue.extend(Component);
    let instance = new CompConstructor({
        propsData,
        store,
        ...extendProps,
    });
    const container = document.createElement('div');
    mountPoint = mountPoint || document.body;
    mountPoint.append(container);

    instance.$mount();
    container.append(instance.$el);
    const distroyComponent = () => {
        container.remove();
        instance.$nextTick(() => {
            instance?.$destroy();
            instance = null;
        });
    };
    instance.$on('input', (value) => {
        if (!value) {
            distroyComponent();
        }
    });
    return {
        mountPoint,
        instance,
        container,
        distroyComponent,
    };
};

/**
 * 加载组件
 * <AUTHOR>
 * @date 2024-01-31
 * @param {Component|Function} component
 * @returns {Component}
 */
export const loadComponent = async (component) => {
    if (isFunction(component)) {
        const module = await component();
        return module.default;
    }
    return component;
};

/**
 * 打开弹窗
 * <AUTHOR>
 * @date 2023-12-28
 * @param {Object} options
 * @returns {Promise<Response>}
 */
export const openDialog = async (options) => {
    let openResponse = null;
    try {
        const {
            component,
            propsData,
            mountPoint,
            callback,
            extendProps = {},
        } = options;
        const Component = await loadComponent(component);
        const data = await new Promise((resolve, reject) => {
            const {
                instance,
                distroyComponent,
            } = renderComponent(Component, {
                dialogType: mountPoint ? 'tiny' : 'normal',
                ...(propsData || {}),
            }, mountPoint, extendProps);
            instance.$on('confirm', (data) => {
                distroyComponent();
                resolve(data);
            });
            instance.$on('cancel', () => {
                distroyComponent();
                reject();
            });
            instance.$on('input', (value) => {
                if (value === false) {
                    distroyComponent();
                    reject();
                }
            });
            callback && callback(instance, distroyComponent);
        });
        openResponse = Response.success(data);
    } catch (error) {
        openResponse = Response.error();
    }
    return openResponse;
};

/**
 * 是否空
 * <AUTHOR>
 * @date 2022-05-11
 * @param {any} value
 * @returns {Boolean}
 */
export const isEmpty = (value) => {
    return value === undefined || value === null || value === '';
};

/**
 * 创建标签props
 * <AUTHOR>
 * @date 2024-01-02
 * @param {Number} gspStatus
 * @param {Boolean} isSY
 * @returns {Object}
 */
export const createTagProps = (gspStatus, isSY = true, isLight = false) => {
    let type = '';
    let text = '';
    let variant = 'outline';
    switch (gspStatus) {
        case constants.gspStatusConst.AWAIT:
            type = 'primary';
            text = isSY ? '待首营' : '待审批';
            variant = isLight ? 'light-outline' : 'outline';
            break;
        case constants.gspStatusConst.DOING:
            type = 'primary';
            variant = isLight ? 'light-outline' : 'outline';
            text = '审批中';
            break;
        case constants.gspStatusConst.REJECT:
            type = 'danger';
            text = '已驳回';
            break;
        case constants.gspStatusConst.CANCEL:
            type = 'default';
            text = '已撤销';
            break;
        case constants.gspStatusConst.FINISH:
            type = 'success';
            text = isSY ? '已首营' : '已同意';
            break;
        default:
            break;
    }
    return {
        type, text, variant,
    };
};

/**
 * 创建信息列表，通过配置
 * <AUTHOR>
 * @date 2024-01-05
 * @param {Object} target
 * @param {Array} configList
 * @returns {Array}
 */
export const createInfoListByConfig = (target, configList) => {
    return configList.map((item) => {
        const itemInfo = {
            ...item,
            value: '',
        };
        itemInfo.value = target?.[item.key];
        if (item.format) {
            itemInfo.value = item.format(itemInfo.value, item, target);
        }
        if (isEmpty(itemInfo.value)) {
            itemInfo.value = item.defaultValue;
        }
        return itemInfo;
    });
};

/**
 * 获取供应商类型
 * <AUTHOR>
 * @date 2024-01-02
 * @param {Number} supplierType
 * @returns {String}
 */
export const getSupplierTypeWording = (supplierType) => showTargetLabel(options.supplierTypeOptions, supplierType);

/**
 * 获取养护类型
 * <AUTHOR>
 * @date 2024-01-02
 * @param {Number} maintenanceType
 * @returns {String}
 */
export const getMaintenanceTypeWording = (maintenanceType) => showTargetLabel(options.maintenanceTypeOptions, maintenanceType);

/**
 * 获取包装状况
 * <AUTHOR>
 * @date 2024-01-02
 * @param {Number} appearancePackageCondition
 * @returns {String}
 */
export const getAppearancePackageConditionWording = (appearancePackageCondition) => showTargetLabel(options.appearancePackageConditionOptions, appearancePackageCondition);

/**
 * 获取质量状况
 * <AUTHOR>
 * @date 2024-01-02
 * @param {Number} qualityCondition
 * @returns {String}
 */
export const getQualityConditionWording = (qualityCondition) => showTargetLabel(options.qualityConditionOptions, qualityCondition);

/**
 * 获取处理结果
 * <AUTHOR>
 * @date 2024-01-02
 * @param {Number} result
 * @returns {String}
 */
export const getResultWording = (result) => showTargetLabel(options.resultOptions, result);

/**
 * 获取养护措施默认值
 * <AUTHOR>
 * @date 2024-01-02
 * @returns {Array}
 */
export const getYhMeasureDefaultValue = () => options.yhMeasureOptions.map((item) => item.value);

/**
 * 调控措施 - stringify
 * <AUTHOR>
 * @date 2024-01-03
 * @param {Array} measure
 * @returns {String}
 */
export const measureStringify = (measure) => (measure || []).join('、');

/**
 * 调控措施 - parse
 * <AUTHOR>
 * @date 2024-01-03
 * @param {String} measure
 * @returns {Array}
 */
export const measureParse = (measure) => (measure || '').split('、').filter((item) => !!item);

/**
 * 获取调控措施
 * <AUTHOR>
 * @date 2024-01-03
 * @param {String} measure
 * @returns {Array}
 */
export const getMeasureWording = (measure) => showTargetLabel(options.wsMeasureOptions, measure);

/**
 * 经营范围
 * <AUTHOR>
 * @date 2024-01-03
 * @param {Array} businessScope
 * @returns {String}
 */
export const businessScopeStringify = (businessScope) => {
    return {
        businessScopeList: (businessScope?.businessScopeList || []).map((item) => ({ id: item })),
    };
};

/**
 * 经营范围
 * <AUTHOR>
 * @date 2024-01-03
 * @param {String} businessScope
 * @returns {Array}
 */
export const businessScopeParse = (businessScope) => {
    return {
        businessScopeList: (businessScope?.businessScopeList || []).map((item) => item.id),
    };
};

/**
 * 获取经营范围
 * <AUTHOR>
 * @date 2024-01-02
 * @param {String} businessScope
 * @returns {String}
 */
export const getBusinessScopeWording = (businessScope) => showTargetLabel(options.businessScopeOptions, businessScope);

/**
 * 获取经营范围
 * <AUTHOR>
 * @date 2024-01-02
 * @param {Object} businessScope
 * @returns {String}
 */
export const getBusinessScopeListWording = (businessScope) => {
    return (businessScope?.businessScopeList || [])
        .map((item) => getBusinessScopeWording(item.id))
        .join('、');
};

/**
 * 获取经营范围
 * @param {Object} businessScope
 * @param split 分隔符
 * @returns {String}
 */
export const getBusinessScopeName = (businessScope, split = '/') => {
    return (businessScope || [])
        .length === 0 ? '-' :
        businessScope
            .map(({ name }) => name)
            .join(split);
};

/**
 * 获取时间段
 * <AUTHOR>
 * @date 2024-01-02
 * @param {Number} timePhase
 * @returns {String}
 */
export const getTimePhaseWording = (timePhase) => showTargetLabel(options.timePhaseOptions, timePhase);

/**
 * 获取时间段根据监控时间
 * <AUTHOR>
 * @date 2024-01-02
 * @param {String} monitorTime
 * @returns {Number}
 */
export const getTimePhaseByMonitorTime = (monitorTime) => {
    const time = monitorTime.split(' ')?.[1] || 0;
    return parseInt(time) < 12 ? constants.timePhaseConst.AM : constants.timePhaseConst.PM;
};

/**
 * 过滤人员，通过搜索关键词
 * <AUTHOR>
 * @date 2024-01-02
 * @param {Array} employeeList
 * @param {String} searchKey
 * @returns {Number}
 */
export const filterEmployeeBySearchKey = (employeeList, searchKey) => {
    const searchKeyStr = searchKey.toLocaleLowerCase();
    return employeeList.filter((item) => (
        (item.employeeName || '').toLocaleLowerCase().indexOf(searchKeyStr) !== -1 ||
        (item.employeeNamePy || '').toLocaleLowerCase().indexOf(searchKeyStr) !== -1 ||
        (item.employeeNamePyFirst || '').toLocaleLowerCase().indexOf(searchKeyStr) !== -1
    ));
};

/**
 * 创建商品名展示html
 * <AUTHOR>
 * @date 2024-01-17
 * @param {Object} item
 * @returns {String}
 */
export const createGoodsInfoHtml = (item) => {
    const {
        displayName, // 商品名
        displaySpec, // 规格
        manufacturer, // 厂商
    } = item || {};
    const wording = [displaySpec, manufacturer].filter((one) => !!one).join('&nbsp;');
    return `
        <span>${displayName}</span>
        <span style="display: block; margin-top: 4px;">${wording}</span>
    `;
};

/**
 * 获取检查结果
 * <AUTHOR>
 * @date 2024-01-02
 * @param {Number} checkResult
 * @returns {String}
 */
export const getCheckResultWording = (checkResult) => showTargetLabel(options.checkResultOptions, checkResult);

/**
 * 获取陈列检查类型
 * <AUTHOR>
 * @date 2024-01-02
 * @param {Number} displayType
 * @returns {String}
 */
export const getDisplayTypeWording = (displayType) => showTargetLabel(options.displayTypeOptions, displayType);

/**
 * 获取严重程度
 * <AUTHOR>
 * @date 2024-01-02
 * @param {Number} severity
 * @returns {String}
 */
export const getSeverityWording = (severity) => showTargetLabel(options.severityOptions, severity);

/**
 * 获取解决结果
 * <AUTHOR>
 * @date 2024-01-02
 * @param {Number} solveResult
 * @returns {String}
 */
export const getSolveResultWording = (solveResult) => showTargetLabel(options.solveResultOptions, solveResult);

/**
 * 获取处置措施类型
 * <AUTHOR>
 * @date 2024-01-02
 * @param {Number} measureType
 * @returns {String}
 */
export const getMeasureTypeWording = (measureType) => showTargetLabel(options.measureTypeOptions, measureType);

/**
 * 获取处置措施类型 - 列表
 * <AUTHOR>
 * @date 2024-01-02
 * @param {Number} measureTypes
 * @returns {String}
 */
export const getMeasureTypesWording = (measureTypes) => (measureTypes || []).map((item) => getMeasureTypeWording(item)).join('、');

/**
 * 获取对原患疾病影响程度
 * <AUTHOR>
 * @date 2024-01-02
 * @param {Number} effectLevel
 * @returns {String}
 */
export const getEffectLevelWording = (effectLevel) => showTargetLabel(options.effectLevelOptions, effectLevel);

/**
 * 获取评价
 * <AUTHOR>
 * @date 2024-01-02
 * @param {Number} evaluate
 * @returns {String}
 */
export const getEvaluateWording = (evaluate) => showTargetLabel(options.evaluateOptions, evaluate);

/**
 * 用药类型
 * <AUTHOR>
 * @date 2024-01-02
 * @param {Number} useMedicinesType
 * @returns {String}
 */
export const getUseMedicinesTypeWording = (useMedicinesType) => showTargetLabel(options.useMedicinesTypeOptions, useMedicinesType);

/**
 * 审批方式
 * <AUTHOR>
 * @date 2024-01-02
 * @param {Number} approveType
 * @returns {String}
 */
export const getApproveTypeWording = (approveType) => showTargetLabel(options.approveTypeOptions, approveType);

/**
 * 审批业务类型
 * <AUTHOR>
 * @date 2024-01-02
 * @param {Number} businessType
 * @returns {String}
 */
export const getBusinessTypeWording = (businessType) => showTargetLabel(options.businessTypeOptions, businessType);

/**
 * 获取湿度展示
 * <AUTHOR>
 * @date 2024-01-02
 * @param {String} humidity
 * @returns {String}
 */
export const getHumidityWording = (humidity) => (isEmpty(humidity) ? '' : `${humidity}%`);

/**
 * 获取温度展示
 * <AUTHOR>
 * @date 2024-01-02
 * @param {String} temperature
 * @returns {String}
 */
export const getTemperatureWording = (temperature) => (isEmpty(temperature) ? '' : `${temperature}℃`);

/**
 * 获取是否造成质量事故
 * <AUTHOR>
 * @date 2024-01-02
 * @param {String} causeQualityAccident
 * @returns {String}
 */
export const getCauseQualityAccidentWording = (causeQualityAccident) => showTargetLabel(options.causeQualityAccidentOptions, causeQualityAccident);

/**
 * 获取召回级别
 * <AUTHOR>
 * @date 2024-01-02
 * @param {String} recallLevel
 * @returns {String}
 */
export const getRecallLevelWording = (recallLevel) => showTargetLabel(options.recallLevelOptions, recallLevel);

/**
 * 获取召回方式
 * <AUTHOR>
 * @date 2024-01-02
 * @param {String} recallMethod
 * @returns {String}
 */
export const getRecallMethodWording = (recallMethod) => showTargetLabel(options.recallMethodOptions, recallMethod);

/**
 * 获取追回级别
 * <AUTHOR>
 * @date 2024-01-02
 * @param {String} recoverLevel
 * @returns {String}
 */
export const getRecoverLevelWording = (recoverLevel) => showTargetLabel(options.recoverLevelOptions, recoverLevel);

/**
 * 获取追回方式
 * <AUTHOR>
 * @date 2024-01-02
 * @param {String} recoverMethod
 * @returns {String}
 */
export const getRecoverMethodWording = (recoverMethod) => showTargetLabel(options.recoverMethodOptions, recoverMethod);

/**
 * 获取有无健康证
 * <AUTHOR>
 * @date 2024-01-02
 * @param {String} healthCertificate
 * @returns {String}
 */
export const getHealthCertificateWording = (healthCertificate) => showTargetLabel(options.healthCertificateOptions, healthCertificate);

/**
 * 获取性别
 * <AUTHOR>
 * @date 2024-01-02
 * @param {String} sex
 * @returns {String}
 */
export const getSexWording = (sex) => showTargetLabel(options.sexOptions, sex);

/**
 * 获取年龄
 * <AUTHOR>
 * @date 2024-01-02
 * @param {Object} age
 * @returns {String}
 */
export const getAgeWording = (age) => formatAge(age);

/**
 * 获取培训方式
 * <AUTHOR>
 * @date 2024-01-02
 * @param {String} trainMethod
 * @returns {String}
 */
export const getTrainMethodWording = (trainMethod) => showTargetLabel(options.trainMethodOptions, trainMethod);

/**
 * 获取考核结果
 * <AUTHOR>
 * @date 2024-01-02
 * @param {String} assessmentResult
 * @returns {String}
 */
export const getAssessmentResultWording = (assessmentResult) => showTargetLabel(options.assessmentResultOptions, assessmentResult);

/**
 * 获取剂型
 * <AUTHOR>
 * @date 2024-01-02
 * @param {String} dosageFormType
 * @returns {String}
 */
export const getDosageFormTypeWording = (dosageFormType) => showTargetLabel(options.dosageFormTypeOptions, dosageFormType);

/**
 * 获取基药
 * <AUTHOR>
 * @date 2024-01-02
 * @param {String} baseMedicineType
 * @returns {String}
 */
export const getBaseMedicineTypeWording = (baseMedicineType) => showTargetLabel(options.baseMedicineTypeOptions, baseMedicineType);

/**
 * 获取处方/OTC
 * <AUTHOR>
 * @date 2024-01-02
 * @param {String} otcType
 * @returns {String}
 */
export const getOtcTypeWording = (otcType) => showTargetLabel(options.otcTypeOptions, otcType);

/**
 * 获取养护类型
 * <AUTHOR>
 * @date 2024-01-02
 * @param {String} maintainType
 * @returns {String}
 */
export const getMaintainTypeWording = (maintainType) => showTargetLabel(options.maintainTypeOptions, maintainType);

/**
 * 获取储存条件
 * <AUTHOR>
 * @date 2024-01-02
 * @param {String} storageType
 * @returns {String}
 */
export const getStorageTypeWording = (storageType) => StorageTypeArr.filter((item) => storageType & item).map((type) => StorageTypeObj[type]).join('/');


/**
 * 获取剂量模式
 * <AUTHOR>
 * @date 2024-01-02
 * @param {String} specType
 * @returns {String}
 */
export const getSpecTypeWording = (specType) => showTargetLabel(options.specTypeOptions, specType);

/**
 * 获取证照类型
 * <AUTHOR>
 * @date 2024-01-02
 * @param {String} certType
 * @returns {String}
 */
export const getCertTypeWording = (certType) => showTargetLabel(options.certTypeOptions, certType);

/**
 * 获取联系电话
 * @param contactPhone 联系电话1
 * @param backupContactPhone 联系电话2
 * @param split 分隔符
 * @returns {string|string}
 */
export const getContactPhoneWording = (contactPhone, backupContactPhone, split = ',') => {
    const res = [];
    if (contactPhone) {
        res.push(contactPhone);
    }
    if (backupContactPhone) {
        res.push(backupContactPhone);
    }
    return res.join(split);
};

<template>
    <abc-flex
        class="activity-list-item-wrapper"
        justify="space-between"
        align="center"
        @click="handleClick"
    >
        <abc-flex class="activity-title" vertical>
            <abc-flex class="ellipsis" align="center">
                <abc-tag-v2
                    variant="dark"
                    size="tiny"
                    :theme="isActive ? 'danger' : 'warning'"
                    shape="square"
                    style="flex-shrink: 0;"
                >
                    {{ info.type === 1 ? '会员日' : '促销' }}
                </abc-tag-v2>
                <span v-abc-title.ellipsis="info.name" class="name"></span>
                <abc-text theme="warning-light">
                    {{ isActiveWillStart ? '即将开始' : '' }}
                </abc-text>
            </abc-flex>
            <abc-flex class="activity-info" align="center" :title="describeStr">
                <span class="ellipsis">{{ describeStr }}</span>
            </abc-flex>
        </abc-flex>
        <abc-icon icon="n-right-line-medium"></abc-icon>
    </abc-flex>
</template>

<script>
    import {
        formatDate,
        nextDate,
    } from '@abc/utils-date';
    import {
        PromotionStatusEnum, SalesPromotionTypeEnum,
    } from 'views/marketing/sales-promotion/constants';
    import { formatPeriodRule } from 'views/marketing/util';

    export default {
        name: 'ActivityListItem',
        props: {
            info: {
                type: Object,
                required: true,
            },
        },
        computed: {
            memberStr() {
                const { memberList } = this.info;
                return memberList ? memberList.join('、') : '';
            },

            isActive() {
                const {
                    status,
                } = this.info;
                return status === 1;
            },
            isActiveWillStart() {
                if (this.info.status === PromotionStatusEnum.WILL_START && this.info.beginDate) {
                    const now = new Date();
                    const beginDate = new Date(this.info.beginDate);
                    const nextDateFor15 = nextDate(now,15);
                    if (beginDate < nextDateFor15) {
                        return true;
                    }
                }
                return false;
            },
            describeStr() {
                const _arr = [];
                if (this.info.type === SalesPromotionTypeEnum.memberDays) {
                    _arr.push(formatPeriodRule(this.info.periodRuleType, this.info.periodRules, this.info.isForever));
                } else {
                    if (this.info.isForever) {
                        _arr.push('永久有效');
                    } else {
                        const beginDate = formatDate(this.info.beginDate, 'YYYY-MM-DD');
                        const endDate = formatDate(this.info.endDate, 'YYYY-MM-DD');
                        if (beginDate === endDate) {
                            _arr.push(beginDate);
                        } else {
                            _arr.push(`${beginDate} ~ ${endDate}`);
                        }
                    }
                }

                if (this.info.isAllPatients) {
                    _arr.push('全部顾客');
                } else {
                    _arr.push(this.memberStr);
                }

                return _arr.join(' ');
            },
        },
        methods: {
            formatDate,
            handleClick() {
                this.$emit('activity-click');
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/theme';

    .activity-list-item-wrapper {
        padding: 12px 10px;
        cursor: pointer;
        border-radius: var(--abc-border-radius-small, 6px);

        & + .activity-list-item-wrapper {
            margin-top: 8px;
        }

        .activity-title {
            flex: 1;
            width: 0;
            margin-right: 4px;

            .name {
                margin-right: 8px;
                margin-left: 8px;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px; /* 157.143% */
                color: $T1;
            }
        }

        .iconfont {
            color: $P10;
        }

        &:hover {
            background: $P8;
        }

        .activity-info {
            padding: 0 2px;
            margin-top: 4px;
            font-size: 12px;
            line-height: 16px; /* 133.333% */
            color: $T2;

            > span {
                width: 100%;
            }
        }
    }
</style>

<template>
    <abc-dialog
        v-if="formDialogVisible"
        v-model="formDialogVisible"
        title="设置统计维度"
        size="small"
    >
        <abc-radio-group v-model="currentDimension">
            <abc-space direction="vertical" align="start">
                <abc-radio v-for="(item, index) in dimensionList" :key="index" :label="item.value">
                    {{ item.label }}
                </abc-radio>
            </abc-space>
        </abc-radio-group>
        <div slot="footer" class="dialog-footer">
            <div style="flex: 1;"></div>
            <abc-button @click="handleSave">
                确定
            </abc-button>
            <abc-button type="blank" @click="handleCancel">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import clone from 'utils/clone';

    export default {
        name: 'DialogSettingStatDimension',
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            dimension: {
                type: Number,
                default: 0,
            },
            dimensionList: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                currentDimension: clone(this.dimension),
            };
        },
        computed: {
            formDialogVisible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
        },
        methods: {
            handleSave() {
                this.$emit('change-dimension', this.currentDimension);
                this.handleCancel();
            },
            handleCancel() {
                this.formDialogVisible = false;
            },
        },
    };
</script>

<template>
    <abc-dialog
        v-if="activityPreviewDialog"
        ref="abcDialog"
        v-model="activityPreviewDialog"
        size="xlarge"
        title="活动详情"
        class="marketing_activity-preview-dialog"
        append-to-body
        :show-header-border-bottom="false"
        content-styles="padding:0 24px 24px 24px; max-height: 700px"
    >
        <div v-abc-loading="contentLoading" class="marketing_activity-preview-dialog--content">
            <abc-descriptions
                :column="1"
                :label-width="92"
                grid
                size="large"
                label-align="center"
            >
                <abc-descriptions-item
                    v-for="item in listData"
                    :key="item.label"
                    :label-style="{
                        color: '#7a8794',
                        display: 'flex',
                        alignItems: 'center'
                    }"
                    :label="item.label"
                >
                    <abc-table
                        v-if="item.type === 'rule'"
                        theme="white"
                        custom
                        auto-height
                        style=" margin: -9px -12px; border: 0;"
                        :render-config="{ hasInnerBorder: true }"
                        header-size="small"
                        :table-min-height="0"
                        :show-hover-tr-bg="false"
                    >
                        <abc-table-body v-if="item.value && item.value.length">
                            <abc-table-tr v-for="goodsItem in item.value" :key="goodsItem.id">
                                <abc-table-td :width="344">
                                    <abc-row justify="space-between" style="width: 100%;">
                                        <abc-col :span="8">
                                            <div v-abc-title.ellipsis="goodsItem.name"></div>
                                        </abc-col>
                                        <abc-col :span="8" style="display: flex; justify-content: flex-end; padding-right: 8px;">
                                            <abc-text
                                                v-if="goodsItem.displaySpec || goodsItem. manufacturer"
                                                v-abc-title.ellipsis="goodsItem.displaySpec || goodsItem. manufacturer"
                                                tag="div"
                                                theme="gray"
                                            >
                                                {{ goodsItem.displaySpec }}
                                                <span style="margin-left: 6px;">{{ goodsItem. manufacturer }}</span>
                                            </abc-text>
                                            <abc-popover
                                                v-if="(goodsItem.exceptItems && goodsItem.exceptItems.length)"
                                                trigger="hover"
                                                theme="yellow"
                                                class="except"
                                                :popper-options="{
                                                    boundariesElement: 'viewport'
                                                }"
                                                :disabled="!(goodsItem.exceptItems && goodsItem.exceptItems.length)"
                                                placement="bottom-start"
                                                popper-class="except-goods-list__popover"
                                                style="flex-shrink: 0;"
                                            >
                                                <div slot="reference">
                                                    {{ goodsItem.exceptText }}
                                                </div>
                                                <div class="except-goods-list">
                                                    <div v-for="it in goodsItem.exceptItems" :key="it.id" class="except-goods-item">
                                                        <span class="name ellipsis" :title="it.goods && it.goods.displayName">{{ it.goods && it.goods.displayName }}</span>
                                                        <span class="spec ellipsis" :title="it.goods && it.goods.displaySpec ">{{ it.goods && it.goods.displaySpec }}</span>
                                                        <span class="spec ellipsis" :title="it.goods && it.goods.manufacturer">{{ it.goods && it.goods.manufacturer }}</span>
                                                    </div>
                                                </div>
                                            </abc-popover>
                                        </abc-col>
                                        <abc-col :span="8" style="display: flex; justify-content: flex-end;">
                                            <abc-text
                                                v-abc-title.ellipsis="goodsItem?.goods?.manufacturerFull || ''"
                                                tag="div"
                                                theme="gray"
                                            >
                                            </abc-text>
                                        </abc-col>
                                    </abc-row>
                                </abc-table-td>
                                <abc-table-td class="ellipsis">
                                    <span :title="goodsItem.isNewRule ? goodsItem.promotionWays : goodsItem.discountText">
                                        {{ goodsItem.isNewRule ? goodsItem.promotionWays : goodsItem.discountText }}
                                    </span>
                                </abc-table-td>
                            </abc-table-tr>
                        </abc-table-body>
                        <abc-table-body v-else>
                            <abc-table-tr>
                                <abc-table-td>
                                    无
                                </abc-table-td>
                            </abc-table-tr>
                        </abc-table-body>
                    </abc-table>

                    <div v-else-if="item.type === 'gift'">
                        <template v-if="item.value && item.value.length">
                            <div
                                v-for="(discount, index) in item.value"
                                :key="index"
                                :style="{ marginTop: index ? '12px' : 0 }"
                            >
                                <abc-flex justify="space-between" flex="1" align="center">
                                    <abc-space
                                        :custom-style="{
                                            fontWeight: 'bold'
                                        }"
                                    >
                                        {{ discount.title }}
                                    </abc-space>
                                </abc-flex>

                                <template v-for="(detail, detailIndex) in discount.details">
                                    <abc-flex :key="detailIndex" align="flex-start">
                                        <abc-space
                                            :size="8"
                                            :custom-style="{
                                                marginLeft: detail.tag ? '0' : '120px',
                                                marginBottom: '8px',
                                                alignItems: 'flex-start'
                                            }"
                                        >
                                            <abc-tag-v2
                                                v-if="detail.tag"
                                                variant="outline"
                                                theme="danger"
                                                shape="square"
                                                size="mini"
                                            >
                                                {{ detail.tag }}
                                            </abc-tag-v2>
                                            <abc-text theme="gray">
                                                {{ detail.description }}
                                            </abc-text>
                                        </abc-space>
                                    </abc-flex>
                                </template>
                            </div>
                        </template>
                    </div>

                    <div v-else-if="item.type === 'promotion'" style="margin: -9px -12px;">
                        <template v-if="item.value.activityRangeList && item.value.activityRangeList.length">
                            <abc-flex
                                :gap="8"
                                align="flex-start"
                                justify="space-between"
                                style="height: 100%; padding: 8px 10px;"
                            >
                                <abc-flex :gap="8">
                                    <abc-text theme="gray-light">
                                        范围
                                    </abc-text>

                                    <div style="flex-shrink: 0;">
                                        <div style="width: 400px;">
                                            {{ getGoodsListRangeStr(item.value.activityRangeList).typeRangeStr }}
                                        </div>
                                        <div style="width: 400px;">
                                            {{ getGoodsListRangeStr(item.value.activityRangeList).goodsRangeStr }}
                                        </div>
                                    </div>
                                </abc-flex>

                                <div>
                                    <abc-popover
                                        v-if="getGoodsListExceptGoods(item.value.activityRangeList).length"
                                        trigger="hover"
                                        theme="yellow"
                                        class="except"
                                        :popper-options="{
                                            boundariesElement: 'viewport'
                                        }"
                                        placement="top-end"
                                        popper-class="except-goods-list__popover"
                                    >
                                        <div slot="reference">
                                            <abc-button size="small" variant="text" theme="default">
                                                {{ getGoodsListExceptGoods(item.value.activityRangeList).length > 0 ?
                                                    `例外${getGoodsListExceptGoods(item.value.activityRangeList).length}` : '' }}
                                            </abc-button>
                                        </div>
                                        <div class="except-goods-list">
                                            <div v-for="it in getGoodsListExceptGoods(item.value.activityRangeList)" :key="it.id" class="except-goods-item">
                                                <span class="name ellipsis" :title="it.goods && it.goods.displayName">{{ it.goods && it.goods.displayName }}</span>
                                                <span class="spec ellipsis" :title="it.goods && it.goods.displaySpec ">{{ it.goods && it.goods.displaySpec }}</span>
                                                <span class="spec ellipsis" :title="it.goods && it.goods.manufacturer">{{ it.goods && it.goods.manufacturer }}</span>
                                            </div>
                                        </div>
                                    </abc-popover>
                                </div>
                            </abc-flex>
                        </template>
                        <abc-divider
                            v-if="item.value.activityRangeList &&
                                item.value.activityRangeList.length &&
                                item.value.gifts &&
                                item.value.gifts.length"
                            margin="none"
                        ></abc-divider>
                        <template v-if="item.value.gifts && item.value.gifts.length">
                            <abc-flex :gap="8" style="padding: 8px 10px;">
                                <abc-text style="flex-shrink: 0;" theme="gray-light">
                                    优惠
                                </abc-text>
                                <abc-flex vertical>
                                    <div
                                        v-for="(discount, index) in item.value.gifts"
                                        :key="index"
                                        :style="{ marginTop: index ? '12px' : 0 }"
                                    >
                                        <abc-flex justify="space-between" flex="1" align="center">
                                            <abc-space>
                                                {{ discount.title }}
                                            </abc-space>
                                        </abc-flex>

                                        <template v-for="(detail, detailIndex) in discount.details">
                                            <abc-flex :key="detailIndex" align="flex-start">
                                                <abc-space
                                                    :size="8"
                                                    :custom-style="{
                                                        marginLeft: detail.tag ? '0' : '120px',
                                                        marginBottom: '8px',
                                                        alignItems: 'flex-start'
                                                    }"
                                                >
                                                    <abc-tag-v2
                                                        v-if="detail.tag"
                                                        variant="outline"
                                                        theme="danger"
                                                        shape="square"
                                                        size="mini"
                                                    >
                                                        {{ detail.tag }}
                                                    </abc-tag-v2>
                                                    <abc-text theme="gray">
                                                        {{ detail.description }}
                                                    </abc-text>
                                                </abc-space>
                                            </abc-flex>
                                        </template>
                                    </div>
                                </abc-flex>
                            </abc-flex>
                        </template>
                        <template v-if="!item.value?.activityRangeList?.length && !item.value?.activityRangeList?.length">
                            <abc-flex align="center" style="height: 40px; padding-left: 12px;">
                                无
                            </abc-flex>
                        </template>
                    </div>

                    <div v-else-if="item.type === 'multiPoint'" style="margin: -9px -12px;">
                        <template v-if="!item.value.pointRateScope">
                            <abc-flex align="center" style="height: 40px; padding-left: 12px;">
                                无
                            </abc-flex>
                        </template>
                        <template v-else>
                            <abc-flex
                                :gap="8"
                                align="flex-start"
                                justify="space-between"
                                style="height: 100%; padding: 8px 10px;"
                            >
                                <abc-flex :gap="8">
                                    <abc-text theme="gray-light">
                                        范围
                                    </abc-text>

                                    <div style="flex-shrink: 0;">
                                        <div style="width: 400px;">
                                            {{ item.value.typeStr }}
                                        </div>
                                        <div style="width: 400px;">
                                            {{ item.value.typeStr ? '以及' : '' }}{{ item.value.singleStr }}
                                        </div>
                                    </div>
                                </abc-flex>
                                <abc-popover
                                    v-if="item.value.exceptItems.length"
                                    trigger="hover"
                                    theme="yellow"
                                    class="except"
                                    :popper-options="{
                                        boundariesElement: 'viewport'
                                    }"
                                    placement="top-end"
                                    popper-class="except-goods-list__popover"
                                >
                                    <div slot="reference">
                                        <abc-button size="small" variant="text" theme="default">
                                            {{ item.value.exceptItems.length > 0 ?
                                                `例外${item.value.exceptItems.length}` : '' }}
                                        </abc-button>
                                    </div>
                                    <div class="except-goods-list">
                                        <div v-for="it in item.value.exceptItems" :key="it.id" class="except-goods-item">
                                            <span class="name ellipsis" :title="it.goods && it.goods.displayName">{{ it.goods && it.goods.displayName }}</span>
                                            <span class="spec ellipsis" :title="it.goods && it.goods.displaySpec ">{{ it.goods && it.goods.displaySpec }}</span>
                                            <span class="spec ellipsis" :title="it.goods && it.goods.manufacturer">{{ it.goods && it.goods.manufacturer }}</span>
                                        </div>
                                    </div>
                                </abc-popover>
                            </abc-flex>
                            <abc-divider margin="none"></abc-divider>
                            <abc-flex :gap="8" style="padding: 8px 10px;">
                                <abc-text style="flex-shrink: 0;" theme="gray-light">
                                    规则
                                </abc-text>
                                <abc-flex>
                                    <abc-space>
                                        {{ item.value.pointRateStr }}
                                        <abc-text theme="gray" size="small">
                                            {{ item.value.pointRateTipStr || '' }}
                                        </abc-text>
                                    </abc-space>
                                </abc-flex>
                            </abc-flex>
                        </template>
                    </div>

                    <span v-else> {{ item.value }}</span>
                </abc-descriptions-item>
            </abc-descriptions>
        </div>
    </abc-dialog>
</template>
<script>
    import MarketingAPI from 'api/marketing';
    import {
        goodsSpec, goodsFullName,
    } from '@/filters';
    import { ActivityTypeEnum } from 'views/marketing/constants';
    import RuleAdapter from 'views/marketing/sales-promotion/rule-adapter.js';
    import PromotionAdapter from 'views/marketing/sales-promotion/form/data/adapter.js';
    import IntegralAPI from 'api/marketing/integral';
    import { mapGetters } from 'vuex';
    export default {
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            activityType: {
                type: Number,
                default: -1,
            },
            activityId: {
                type: String,
                default: '',
            },
            activityDesc: {
                type: String,
                default: '',
            },
            activityName: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                contentLoading: false,
                activityData: null,
                ActivityTypeEnum,
                pointsConfig: {},
            };
        },
        computed: {
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            transGoodsClassificationName() {
                return this.viewDistributeConfig.transGoodsClassificationName;
            },
            activityPreviewDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },

            listData() {
                const {
                    name,
                    activityTimeStr,
                    memberTypesStr,
                    activityRangeList,
                    discountGoodsList,
                    discountGoodsGroupList,
                    discountCategoryList,
                    gifts,
                    pointRatePromotion,
                } = this.activityData || {};

                const {
                    typeStr,
                    exceptItems,
                    singleStr,
                } = PromotionAdapter.getMultiPointRangeStr({
                    pointRatePromotion,
                    pointsConfig: this.pointsConfig,
                });

                return [
                    {
                        label: '活动名称',
                        value: name,
                    },
                    {
                        label: '活动类型',
                        value: this.activityDesc,
                    },
                    {
                        label: '活动时间',
                        value: activityTimeStr,
                    },
                    {
                        label: '活动对象',
                        value: memberTypesStr,

                    },
                    {
                        label: '单品促销',
                        value: discountGoodsList,
                        type: 'rule',
                    },
                    {
                        label: '商品组促销',
                        value: discountGoodsGroupList,
                        type: 'rule',
                    },
                    {
                        label: '分类促销',
                        value: discountCategoryList,
                        type: 'rule',
                    },
                    {
                        label: '满减/赠',
                        value: {
                            activityRangeList,
                            gifts,
                        },
                        type: 'promotion',
                    },
                    {
                        label: '多倍积分',
                        value: {
                            typeStr,
                            exceptItems,
                            singleStr,
                            pointRateScope: pointRatePromotion?.pointRateScope,
                            pointRateStr: `按会员平时${pointRatePromotion?.pointRate || 1}倍结算积分`,
                            pointRateTipStr: `每消费${this.pointsConfig.amountAccumulateRat || 0}元，积${pointRatePromotion?.pointRate || 1}分`,
                        },
                        type: 'multiPoint',
                    },
                ];
            },
        },
        created() {
            this.initData();
        },
        methods: {
            async initData() {
                this.contentLoading = true;
                if (this.activityType === ActivityTypeEnum.MEMBER || this.activityType === ActivityTypeEnum.DISCOUNT) {
                    await this.getMemberActivityInfo();
                }

                if (this.activityType === ActivityTypeEnum.FULL_REDUCTION) {
                    await this.getFullReductionActivityInfo();
                }
                this.contentLoading = false;
            },
            async fetchPointsConfig() {
                try {
                    const { data } = await IntegralAPI.fetchPointsConfig();
                    data.applicationGoodsList = data.applicationGoodsList?.map((it) => {
                        return {
                            ...it,
                            exceptItems: it.exceptInfo ? it.exceptInfo.goodsItems : [],
                        };
                    });
                    this.pointsConfig = data;
                } catch (error) {
                    console.log('fetchPointsConfig error', error);
                }
            },
            getGoodsListRangeStr(goodsList) {
                return PromotionAdapter.getGoodsListRangeStr(goodsList);
            },
            getGoodsListExceptGoods(goodsList) {
                const sumExceptGoodsList = [];
                goodsList.forEach((item) => {
                    if (item.exceptItems && item.exceptItems.length) {
                        sumExceptGoodsList.push(...item.exceptItems);
                    }
                });
                return sumExceptGoodsList;
            },
            trans2promotionTypeStrList(data) {
                return RuleAdapter.trans2promotionTypeStrList(data,false);
            },
            tran2PromotionTypeStr(data) {
                const promotionTypeStrList = this.trans2promotionTypeStrList(data);
                if (!promotionTypeStrList.length) {
                    return '';
                }
                return `${promotionTypeStrList.join(' / ')}`;
            },
            transSaleLimitRuleStr(item) {
                const arr = PromotionAdapter.getSaleLimitRuleText(item);
                if (!arr.length) {
                    return '';
                }
                return arr.join('|');
            },
            goodsItemTrans(item) {
                const isNewRule = item.rule || item.saleLimitRule;
                const baseInfo = {
                    ...item,
                    name: this.transGoodsClassificationName(item.type === 2 ? goodsFullName(item.goods) : item.name || item.goodsCMSpec),
                    displaySpec: goodsSpec(item.goods),
                    manufacturer: item.type === 2 ? item.goods?.manufacturer : '',
                    discountText: this.getDiscountStr(item),
                    isNewRule,
                };

                return {
                    ...baseInfo,
                    promotionWays: this.tran2PromotionTypeStr(item),
                    saleLimitRuleStr: this.transSaleLimitRuleStr(item),
                    exceptText: item.type === 1 ? `${item?.exceptItems?.length ? `例外${item?.exceptItems?.length}` : ''}` : '',
                };
            },
            async getMemberActivityInfo() {
                try {
                    const data = await MarketingAPI.salesPromotion.getPromotionDetail(this.activityId);
                    const {
                        name,
                        isForever,
                        beginDate,
                        endDate,
                        discountGoodsList,
                        discountGoodsGroupList,
                        discountCategoryList,
                        giftPromotion,
                        isAllPatient,
                        memberTypes,
                        pointRatePromotion,
                    } = data;
                    this.activityData = {
                        name,
                        activityTimeStr: isForever ? '永久有效' : `${beginDate}~${endDate}`,
                        memberTypesStr: isAllPatient ? '全部顾客' : memberTypes.map((item) => item.name).join('、'),
                        discountGoodsList: discountGoodsList?.map((item) => {
                            return this.goodsItemTrans(item);
                        }) || [],
                        discountGoodsGroupList: discountGoodsGroupList?.map((item) => {
                            return this.goodsItemTrans(item);
                        }) || [],
                        discountCategoryList: discountCategoryList?.map((item) => {
                            return this.goodsItemTrans(item);
                        }) || [],
                        activityRangeList: giftPromotion?.goodsList?.map((item) => {
                            const baseInfo = {
                                ...item,
                                name: item.type === 2 ? goodsFullName(item.goods) : item.name || item.goodsCMSpec,
                                displaySpec: goodsSpec(item.goods),
                                manufacturer: item.type === 2 ? item.goods?.manufacturer : '',
                            };
                            return {
                                ...baseInfo,
                                exceptText: item.type === 1 ? `例外${item?.exceptItems?.length}` : '',
                                tagName: this.getPromotionTagStr(item.discountType),
                                originPriceText: item.type === 2 ? `原价：${this.$t('currencySymbol')}${this.getGoodsPackagePrice(item, true)}` : '',
                                discountText: this.getDiscountStr(item),
                            };
                        }),
                        gifts: PromotionAdapter.giftRuleDataToViewFormat(giftPromotion?.giftRules) || [],
                        pointRatePromotion,
                    };
                    if (pointRatePromotion?.pointRateScope) {
                        this.fetchPointsConfig();
                    }
                    this.updateDialogHeight();
                } catch (err) {
                    console.log(err);
                }
            },

            getPromotionTagStr(type) {
                if (type === 0) return '折扣';
                if (type === 1) return '特价';
                if (type === 2) return '满赠';
            },

            getDiscountStr(item) {
                if (item.discountType) return `${item.discount}元`;
                if (!item.discountType) return item.discount ? `${Number((item.discount) * 10).toFixed(2)}折` : '';
            },
            getGoodsPackagePrice(goodItem, needFormat = false) {
                let packagePrice = goodItem?.goods?.packagePrice || 0;
                if (needFormat) {
                    packagePrice = packagePrice.toFixed(2);
                }
                return packagePrice;
            },
            async getFullReductionActivityInfo() {
                try {
                    const { data } = await MarketingAPI.getPromotionDetail(this.activityId);
                    const {
                        name,
                        isForever,
                        beginDate,
                        endDate,
                        isAllPatient,
                        memberTypes,
                        goodsList,
                        onlyOriginalPrice = '',
                        giftRules,
                        pointRatePromotion,
                    } = data;
                    this.activityData = {
                        name,
                        activityTimeStr: isForever ? '永久有效' : `${beginDate}~${endDate}`,
                        memberTypesStr: isAllPatient ? '全部顾客' : memberTypes.map((item) => item.name).join('、'),
                        onlyOriginalPriceStr: onlyOriginalPrice ? '*仅原价购买时可用' : '',
                        activityRangeList: goodsList?.map((item) => {
                            const baseInfo = {
                                ...item,
                                name: item.type === 2 ? goodsFullName(item.goods) : item.name || item.goodsCMSpec,
                                displaySpec: goodsSpec(item.goods),
                                manufacturer: item.type === 2 ? item.goods?.manufacturer : '',
                            };
                            return {
                                ...baseInfo,
                                exceptText: item.type === 1 ? `例外${item?.exceptItems?.length}` : '',
                                tagName: this.getPromotionTagStr(item.discountType),
                                originPriceText: item.type === 2 ? `原价：${this.$t('currencySymbol')}${this.getGoodsPackagePrice(item, true)}` : '',
                                discountText: this.getDiscountStr(item),
                            };
                        }),
                        gifts: PromotionAdapter.giftRuleDataToViewFormat(giftRules) || [],
                        pointRatePromotion,
                    };
                    if (pointRatePromotion?.pointRateScope) {
                        this.fetchPointsConfig();
                    }
                    this.updateDialogHeight();
                } catch (err) {
                    console.log(err);
                }
            },

            updateDialogHeight() {
                this.$nextTick(() => {
                    const $abcDialog = this.$refs.abcDialog;
                    $abcDialog && $abcDialog.updateDialogHeight();
                });
            },

        },
    };
</script>

<style lang="scss">
@import '~styles/theme.scss';

.except-goods-list__popover {
    .except-goods-list {
        .except-goods-item {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-gap: 10px;

            .name {
                display: inline-block;
                min-width: 60px;
                max-width: 160px;
            }

            .spec {
                display: inline-block;
                min-width: 40px;
                max-width: 120px;
                margin: 0 10px;
                font-size: 12px;
                color: $T2;
            }
        }
    }
}

.marketing_activity-preview-dialog {
    &--content {
        .activity-list {
            &-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding-right: 14px;
                margin-bottom: 8px;

                &:last-child {
                    margin-bottom: 0;
                }

                &.origin {
                    color: $T2;
                }

                .activity-item-left {
                    .name {
                        max-width: 180px;
                    }

                    .spec {
                        font-size: 12px;
                        color: $T2;
                    }
                }

                .activity-item-right {
                    display: flex;
                    align-items: center;

                    .col {
                        display: inline-block;
                        line-height: 1;
                        text-align: right;

                        &.tag {
                            width: 60px;
                        }

                        .except {
                            color: $theme1;
                        }

                        .origin-price {
                            display: inline-block;
                            width: 110px;
                            color: $T2;
                        }
                    }

                    .count {
                        display: inline-block;
                        width: 60px;
                        text-align: right;
                    }
                }
            }
        }
    }
}
</style>

<template>
    <div
        class="adjustment-fee-tr discount-item"
        :class="{
            'is-disabled': disabledOperation,
        }"
    >
        <abc-tag-v2
            variant="outline"
            size="small"
            theme="danger"
            shape="round"
        >
            议价
        </abc-tag-v2>

        <div class="discount-info">
            <div v-if="summary.oddFee" class="discount-info-item">
                <div class="promotion-name">
                    <span>抹零/凑整</span>
                </div>
                <abc-flex class="price" align="center">
                    <span>{{ 0 > summary.oddFee ? '-' : '+' }}</span>
                    <abc-money :show-symbol="false" :value="Math.abs(summary.oddFee)"></abc-money>
                </abc-flex>
            </div>
            <div v-if="summary.draftAdjustmentFee" class="discount-info-item">
                <div class="promotion-name">
                    <span>整单议价</span>
                </div>
                <div v-if="!disabledOperation" class="opt-button-wrapper">
                    <abc-button variant="text" size="small" @click.stop="$emit('cancel-adjustment-fee')">
                        取消
                    </abc-button>
                </div>
                <abc-flex class="price" align="center">
                    <span>
                        {{ 0 > summary.draftAdjustmentFee ? '-' : '+' }}
                    </span>
                    <abc-money :show-symbol="false" :value="Math.abs(summary.draftAdjustmentFee)"></abc-money>
                </abc-flex>
            </div>
        </div>

        <div v-if="!disabledOperation" class="right-arrow-icon">
            <abc-icon icon="n-right-line-medium"></abc-icon>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    export default {
        name: 'AdjustmentFee',
        props: {
            disabledOperation: {
                type: Boolean,
                default: false,
            },
            summary: Object,
        },
        data() {
            return {

            };
        },
    };
</script>
<style lang="scss">
@import 'src/styles/theme.scss';

.adjustment-fee-tr.discount-tr {
    .promotion-name {
        min-height: 24px;
    }

    .opt-button-wrapper {
        display: none;
    }

    &:hover {
        .opt-button-wrapper {
            display: initial;
        }
    }
}
</style>

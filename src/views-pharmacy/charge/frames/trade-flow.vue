<template>
    <abc-card :border="false" radius-size="none" class="content-card-wrapper">
        <abc-layout style="height: 100%;">
            <abc-layout-content style="height: 100%;">
                <cashier-work-summary hidden-filter-type="中药颗粒" title-name="" wide></cashier-work-summary>
            </abc-layout-content>
        </abc-layout>
    </abc-card>
</template>

<script>
    import CashierWorkSummary from 'views/cashier/components/cashier-work-summary.vue';
    export default {
        name: 'TradeFlow',
        components: {
            CashierWorkSummary,
        },
        props: {
            pageLoading: {
                type: Boolean,
                default: false,
            },
        },
    };
</script>


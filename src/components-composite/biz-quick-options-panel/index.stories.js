import BizQuickOptionsPanel from './src/views/index.vue';
import BizQuickOptionsPanelRow from './src/views/row.vue';
import BizQuickOptionsPanelGroup from './src/views/group.vue';

export default {
    title: 'Components/布局容器/用药网格选择面板-BizQuickOptionsPanel',
    component: BizQuickOptionsPanel,
    subcomponents: {
        BizQuickOptionsPanelRow,
        BizQuickOptionsPanelGroup,
    },
};

export const base = {
    render: () => ({
        components: {
            BizQuickOptionsPanel,
        },

        data() {
            return {
                vertical: 0,
                contentList: [
                    {
                        'label': '',
                        'list': [
                            {
                                label: '抗卡积分和娜姐开发三级开发拿手机妇女节发几款三分看见啊是妇女节开发你撒娇咖啡撒放你家吧净空法师你分看见啊不能放假卡收费和娜姐说开发', value: '抗卡积分和娜姐开发三级开发拿手机妇女节发几款三分看见啊是妇女节开发你撒娇咖啡撒放你家吧净空法师你分看见啊不能放假卡收费和娜姐说开发',
                            },
                            {
                                label: '高血压', value: '高血压',
                            },
                            {
                                label: '高血脂', value: '高血脂',
                            },
                            {
                                label: '心脏病', value: '心脏病',
                            },
                            {
                                label: '糖尿病', value: '糖尿病',
                            },
                            {
                                label: '精神疾病', value: '精神疾病',
                            },
                            {
                                label: '脑梗死', value: '脑梗死',
                            },
                            {
                                label: '肝炎', value: '肝炎',
                            },
                            {
                                label: '胃炎', value: '胃炎',
                            },
                            {
                                label: '肺结核', value: '肺结核',
                            },
                            {
                                label: '哮喘', value: '哮喘',
                            },
                            {
                                label: '鼻炎', value: '鼻炎',
                            },
                            {
                                label: '甲亢', value: '甲亢',
                            },
                            {
                                label: '高血压', value: '高血压',
                            },
                            {
                                label: '高血脂', value: '高血脂',
                            },
                            {
                                label: '心脏病', value: '心脏病',
                            },
                            {
                                label: '糖尿病', value: '糖尿病',
                            },
                            {
                                label: '精神疾病', value: '精神疾病',
                            },
                            {
                                label: '脑梗死', value: '脑梗死',
                            },
                            {
                                label: '肝炎', value: '肝炎',
                            },
                            {
                                label: '胃炎', value: '胃炎',
                            },
                            {
                                label: '肺结核', value: '肺结核',
                            },
                            {
                                label: '哮喘', value: '哮喘',
                            },
                            {
                                label: '鼻炎', value: '鼻炎',
                            },
                            {
                                label: '甲亢', value: '甲亢',
                            },
                        ],
                    },
                ],
            };
        },

        methods: {
            handleClose() {
                console.log('closed');
            },
            handleSelect(val) {
                console.log('selected:', val);
            },
        },

        template: `
            <abc-flex :gap="8" vertical>
                <abc-flex>
                    <abc-text>内容方向：</abc-text>
                    <abc-radio-group v-model="vertical">
                        <abc-radio :label="0">横向</abc-radio>
                        <abc-radio :label="1">纵向</abc-radio>
                    </abc-radio-group>
                </abc-flex>

                <biz-quick-options-panel
                    :list="contentList"
                    :vertical="vertical"
                    @select="handleSelect"
                    @close="handleClose"
                >
                </biz-quick-options-panel>
            </abc-flex>
        `,
    }),

    name: '基础用法',
};

export const widthTitle = {
    render: () => ({
        components: {
            BizQuickOptionsPanel,
        },

        data() {
            return {
                labelPosition: 'top',
                contentList: [
                    {
                        'label': '既往史',
                        'list': [
                            {
                                label: '高血压', value: '高血压',
                            },
                            {
                                label: '高血脂', value: '高血脂',
                            },
                            {
                                label: '心脏病', value: '心脏病',
                            },
                            {
                                label: '糖尿病', value: '糖尿病',
                            },
                            {
                                label: '精神疾病', value: '精神疾病',
                            },
                            {
                                label: '脑梗死', value: '脑梗死',
                            },
                            {
                                label: '肝炎', value: '肝炎',
                            },
                            {
                                label: '胃炎', value: '胃炎',
                            },
                            {
                                label: '肺结核', value: '肺结核',
                            },
                            {
                                label: '哮喘', value: '哮喘',
                            },
                            {
                                label: '鼻炎', value: '鼻炎',
                            },
                            {
                                label: '甲亢', value: '甲亢',
                            },
                            {
                                label: '高血压', value: '高血压',
                            },
                            {
                                label: '高血脂', value: '高血脂',
                            },
                            {
                                label: '心脏病', value: '心脏病',
                            },
                            {
                                label: '糖尿病', value: '糖尿病',
                            },
                            {
                                label: '精神疾病', value: '精神疾病',
                            },
                            {
                                label: '脑梗死', value: '脑梗死',
                            },
                            {
                                label: '肝炎', value: '肝炎',
                            },
                            {
                                label: '胃炎', value: '胃炎',
                            },
                            {
                                label: '肺结核', value: '肺结核',
                            },
                            {
                                label: '哮喘', value: '哮喘',
                            },
                            {
                                label: '鼻炎', value: '鼻炎',
                            },
                            {
                                label: '甲亢', value: '甲亢',
                            },
                        ],
                    },
                ],
            };
        },

        methods: {
            handleClose() {
                console.log('closed');
            },
            handleSelect(val) {
                console.log('selected:', val);
            },
        },

        template: `
            <abc-flex :gap="8" vertical>
                <abc-flex>
                    <abc-text>标题方向：</abc-text>
                    <abc-radio-group v-model="labelPosition">
                        <abc-radio label="top">在上侧</abc-radio>
                        <abc-radio label="left">在左侧</abc-radio>
                    </abc-radio-group>
                </abc-flex>

                <biz-quick-options-panel
                    :list="contentList"
                    :label-position="labelPosition"
                    @select="handleSelect"
                    @close="handleClose"
                >
                </biz-quick-options-panel>
            </abc-flex>
        `,
    }),

    name: '带标题',
};

export const widthGroup = {
    render: () => ({
        components: {
            BizQuickOptionsPanel,
        },

        data() {
            return {
                vertical: 0,
                labelPosition: 'top',
                showSplitLine: true,
                showTitle: true,
                tabsValue: 'chineseExamination',
                contentListWithLabel: [
                    {
                        'label': '既往史',
                        'list': [
                            {
                                label: '高血压', value: '高血压',
                            },
                            {
                                label: '高血脂', value: '高血脂',
                            },
                            {
                                label: '心脏病', value: '心脏病',
                            },
                            {
                                label: '糖尿病', value: '糖尿病',
                            },
                            {
                                label: '精神疾病', value: '精神疾病',
                            },
                            {
                                label: '脑梗死', value: '脑梗死',
                            },
                            {
                                label: '肝炎', value: '肝炎',
                            },
                            {
                                label: '胃炎', value: '胃炎',
                            },
                            {
                                label: '肺结核', value: '肺结核',
                            },
                            {
                                label: '哮喘', value: '哮喘',
                            },
                            {
                                label: '鼻炎', value: '鼻炎',
                            },
                            {
                                label: '甲亢', value: '甲亢',
                            },
                            {
                                label: '高血压', value: '高血压',
                            },
                            {
                                label: '高血脂', value: '高血脂',
                            },
                            {
                                label: '心脏病', value: '心脏病',
                            },
                            {
                                label: '糖尿病', value: '糖尿病',
                            },
                            {
                                label: '精神疾病', value: '精神疾病',
                            },
                            {
                                label: '脑梗死', value: '脑梗死',
                            },
                            {
                                label: '肝炎', value: '肝炎',
                            },
                            {
                                label: '胃炎', value: '胃炎',
                            },
                            {
                                label: '肺结核', value: '肺结核',
                            },
                            {
                                label: '哮喘', value: '哮喘',
                            },
                            {
                                label: '鼻炎', value: '鼻炎',
                            },
                            {
                                label: '甲亢', value: '甲亢',
                            },
                        ],
                    },
                    {
                        'label': '个人史',
                        'list': [
                            {
                                label: '吸烟', value: '吸烟',
                            },
                            {
                                label: '偶尔吸烟', value: '偶尔吸烟',
                            },
                            {
                                label: '长期吸烟', value: '长期吸烟',
                            },
                            {
                                label: '不饮酒', value: '不饮酒',
                            },
                            {
                                label: '偶尔饮酒', value: '偶尔饮酒',
                            },
                            {
                                label: '长期饮酒', value: '长期饮酒',
                            },
                            {
                                label: '未婚', value: '未婚',
                            },
                            {
                                label: '已婚', value: '已婚',
                            },
                            {
                                label: '未孕', value: '未孕',
                            },
                            {
                                label: '备孕', value: '备孕',
                            },
                            {
                                label: '怀孕', value: '怀孕',
                            },
                            {
                                label: '闭经', value: '闭经',
                            },
                            {
                                label: '有早产史', value: '有早产史',
                            },
                            {
                                label: '有流产史', value: '有流产史',
                            },
                        ],
                    },
                ],
                contentListWithoutLabel: [
                    {
                        'label': '',
                        'list': [
                            {
                                label: '高血压', value: '高血压',
                            },
                            {
                                label: '高血脂', value: '高血脂',
                            },
                            {
                                label: '心脏病', value: '心脏病',
                            },
                            {
                                label: '糖尿病', value: '糖尿病',
                            },
                            {
                                label: '精神疾病', value: '精神疾病',
                            },
                            {
                                label: '脑梗死', value: '脑梗死',
                            },
                            {
                                label: '肝炎', value: '肝炎',
                            },
                            {
                                label: '胃炎', value: '胃炎',
                            },
                            {
                                label: '肺结核', value: '肺结核',
                            },
                            {
                                label: '哮喘', value: '哮喘',
                            },
                            {
                                label: '鼻炎', value: '鼻炎',
                            },
                            {
                                label: '甲亢', value: '甲亢',
                            },
                            {
                                label: '高血压', value: '高血压',
                            },
                            {
                                label: '高血脂', value: '高血脂',
                            },
                            {
                                label: '心脏病', value: '心脏病',
                            },
                            {
                                label: '糖尿病', value: '糖尿病',
                            },
                            {
                                label: '精神疾病', value: '精神疾病',
                            },
                            {
                                label: '脑梗死', value: '脑梗死',
                            },
                            {
                                label: '肝炎', value: '肝炎',
                            },
                            {
                                label: '胃炎', value: '胃炎',
                            },
                            {
                                label: '肺结核', value: '肺结核',
                            },
                            {
                                label: '哮喘', value: '哮喘',
                            },
                            {
                                label: '鼻炎', value: '鼻炎',
                            },
                            {
                                label: '甲亢', value: '甲亢',
                            },
                        ],
                    },
                    {
                        'label': '',
                        'list': [
                            {
                                label: '吸烟', value: '吸烟',
                            },
                            {
                                label: '偶尔吸烟', value: '偶尔吸烟',
                            },
                            {
                                label: '长期吸烟', value: '长期吸烟',
                            },
                            {
                                label: '不饮酒', value: '不饮酒',
                            },
                            {
                                label: '偶尔饮酒', value: '偶尔饮酒',
                            },
                            {
                                label: '长期饮酒', value: '长期饮酒',
                            },
                            {
                                label: '未婚', value: '未婚',
                            },
                            {
                                label: '已婚', value: '已婚',
                            },
                            {
                                label: '未孕', value: '未孕',
                            },
                            {
                                label: '备孕', value: '备孕',
                            },
                            {
                                label: '怀孕', value: '怀孕',
                            },
                            {
                                label: '闭经', value: '闭经',
                            },
                            {
                                label: '有早产史', value: '有早产史',
                            },
                            {
                                label: '有流产史', value: '有流产史',
                            },
                        ],
                    },
                ],
            };
        },

        methods: {
            handleClose() {
                console.log('closed');
            },
            handleSelect(val) {
                console.log('selected:', val);
            },
        },

        computed: {
            contentList() {
                return this.showTitle ? this.contentListWithLabel : this.contentListWithoutLabel;
            },
        },

        template: `
            <abc-flex :gap="8" vertical>
                <abc-flex>
                    <abc-text>内容方向：</abc-text>
                    <abc-radio-group v-model="vertical">
                        <abc-radio :label="0">横向</abc-radio>
                        <abc-radio :label="1">纵向</abc-radio>
                    </abc-radio-group>
                </abc-flex>

                <abc-flex>
                    <abc-text>标题方向：</abc-text>
                    <abc-radio-group v-model="labelPosition">
                        <abc-radio label="top">在上侧</abc-radio>
                        <abc-radio label="left">在左侧</abc-radio>
                    </abc-radio-group>
                </abc-flex>

                <abc-checkbox v-model="showTitle">展示标题</abc-checkbox>

                <abc-checkbox v-model="showSplitLine">展示分割线</abc-checkbox>

                <biz-quick-options-panel
                    :list="contentList"
                    :vertical="vertical"
                    :label-position="labelPosition"
                    :show-split-line="showSplitLine"
                    @select="handleSelect"
                    @close="handleClose"
                >
                </biz-quick-options-panel>
            </abc-flex>
        `,
    }),

    name: '分组',
};

export const widthLineBreakAndSplit = {
    render: () => ({
        components: {
            BizQuickOptionsPanel,
        },

        data() {
            return {
                vertical: 0,
                labelPosition: 'top',
                showSplitLine: true,
                showTitle: true,
                tabsValue: 'chineseExamination',
                contentList: [
                    {
                        'label': '既往史',
                        'list': [
                            {
                                label: '高血压', value: '高血压',
                            },
                            {
                                label: '高血脂', value: '高血脂',
                            },
                            {
                                label: '心脏病', value: '心脏病',
                            },
                            {
                                label: '糖尿病', value: '糖尿病',
                            },
                            {
                                label: '精神疾病', value: '精神疾病',
                            },
                            {
                                label: '脑梗死', value: '脑梗死',
                            },
                            {
                                label: '肝炎', value: '肝炎',
                            },
                            {
                                label: '胃炎', value: '胃炎', isBreak: true,
                            },
                            {
                                label: '肺结核', value: '肺结核',
                            },
                            {
                                label: '哮喘', value: '哮喘',
                            },
                            {
                                label: '鼻炎', value: '鼻炎',
                            },
                            {
                                label: '甲亢', value: '甲亢', isSplit: true,
                            },
                            {
                                label: '高血压', value: '高血压',
                            },
                            {
                                label: '高血脂', value: '高血脂',
                            },
                            {
                                label: '心脏病', value: '心脏病',
                            },
                            {
                                label: '糖尿病', value: '糖尿病',
                            },
                            {
                                label: '精神疾病', value: '精神疾病',
                            },
                            {
                                label: '脑梗死', value: '脑梗死',
                            },
                            {
                                label: '肝炎', value: '肝炎',
                            },
                            {
                                label: '胃炎', value: '胃炎',
                            },
                            {
                                label: '肺结核', value: '肺结核',
                            },
                            {
                                label: '哮喘', value: '哮喘',
                            },
                            {
                                label: '鼻炎', value: '鼻炎',
                            },
                            {
                                label: '甲亢', value: '甲亢',
                            },
                        ],
                    },
                    {
                        'label': '个人史',
                        'list': [
                            {
                                label: '吸烟', value: '吸烟',
                            },
                            {
                                label: '偶尔吸烟', value: '偶尔吸烟',
                            },
                            {
                                label: '长期吸烟', value: '长期吸烟', isSplit: true,
                            },
                            {
                                label: '不饮酒', value: '不饮酒',
                            },
                            {
                                label: '偶尔饮酒', value: '偶尔饮酒',
                            },
                            {
                                label: '长期饮酒', value: '长期饮酒', isBreak: true,
                            },
                            {
                                label: '未婚', value: '未婚',
                            },
                            {
                                label: '已婚', value: '已婚',
                            },
                            {
                                label: '未孕', value: '未孕',
                            },
                            {
                                label: '备孕', value: '备孕',
                            },
                            {
                                label: '怀孕', value: '怀孕',
                            },
                            {
                                label: '闭经', value: '闭经', isBreak: true,
                            },
                            {
                                label: '有早产史', value: '有早产史',
                            },
                            {
                                label: '有流产史', value: '有流产史',
                            },
                        ],
                    },
                ],
            };
        },

        methods: {
            handleClose() {
                console.log('closed');
            },
            handleSelect(val) {
                console.log('selected:', val);
            },
        },

        template: `
            <abc-flex :gap="8" vertical>
                <biz-quick-options-panel
                    :list="contentList"
                    @select="handleSelect"
                    @close="handleClose"
                >
                </biz-quick-options-panel>
            </abc-flex>
        `,
    }),

    name: '指定换行和指定分割线',
};

export const widthHeader = {
    render: () => ({
        components: {
            BizQuickOptionsPanel,
        },

        data() {
            return {
                vertical: 0,
                labelPosition: 'top',
                showSplitLine: true,
                showTitle: true,
                tabsValue: 'chineseExamination',
                contentList: [
                    {
                        'label': '既往史',
                        'list': [
                            {
                                label: '高血压', value: '高血压',
                            },
                            {
                                label: '高血脂', value: '高血脂',
                            },
                            {
                                label: '心脏病', value: '心脏病',
                            },
                            {
                                label: '糖尿病', value: '糖尿病',
                            },
                            {
                                label: '精神疾病', value: '精神疾病',
                            },
                            {
                                label: '脑梗死', value: '脑梗死',
                            },
                            {
                                label: '肝炎', value: '肝炎',
                            },
                            {
                                label: '胃炎', value: '胃炎',
                            },
                            {
                                label: '肺结核', value: '肺结核',
                            },
                            {
                                label: '哮喘', value: '哮喘',
                            },
                            {
                                label: '鼻炎', value: '鼻炎',
                            },
                            {
                                label: '甲亢', value: '甲亢',
                            },
                            {
                                label: '高血压', value: '高血压',
                            },
                            {
                                label: '高血脂', value: '高血脂',
                            },
                            {
                                label: '心脏病', value: '心脏病',
                            },
                            {
                                label: '糖尿病', value: '糖尿病',
                            },
                            {
                                label: '精神疾病', value: '精神疾病',
                            },
                            {
                                label: '脑梗死', value: '脑梗死',
                            },
                            {
                                label: '肝炎', value: '肝炎',
                            },
                            {
                                label: '胃炎', value: '胃炎',
                            },
                            {
                                label: '肺结核', value: '肺结核',
                            },
                            {
                                label: '哮喘', value: '哮喘',
                            },
                            {
                                label: '鼻炎', value: '鼻炎',
                            },
                            {
                                label: '甲亢', value: '甲亢',
                            },
                        ],
                    },
                    {
                        'label': '个人史',
                        'list': [
                            {
                                label: '吸烟', value: '吸烟',
                            },
                            {
                                label: '偶尔吸烟', value: '偶尔吸烟',
                            },
                            {
                                label: '长期吸烟', value: '长期吸烟',
                            },
                            {
                                label: '不饮酒', value: '不饮酒',
                            },
                            {
                                label: '偶尔饮酒', value: '偶尔饮酒',
                            },
                            {
                                label: '长期饮酒', value: '长期饮酒',
                            },
                            {
                                label: '未婚', value: '未婚',
                            },
                            {
                                label: '已婚', value: '已婚',
                            },
                            {
                                label: '未孕', value: '未孕',
                            },
                            {
                                label: '备孕', value: '备孕',
                            },
                            {
                                label: '怀孕', value: '怀孕',
                            },
                            {
                                label: '闭经', value: '闭经',
                            },
                            {
                                label: '有早产史', value: '有早产史',
                            },
                            {
                                label: '有流产史', value: '有流产史',
                            },
                        ],
                    },
                ],
            };
        },

        methods: {
            handleClose() {
                console.log('closed');
            },
            handleSelect(val) {
                console.log('selected:', val);
            },
        },

        template: `
            <abc-flex :gap="8" vertical>
                <biz-quick-options-panel
                    :list="contentList"
                    @select="handleSelect"
                    @close="handleClose"
                >
                    <div slot="header" style="line-height: 26px;">
                        这是插槽自定义的头部
                    </div>
                </biz-quick-options-panel>
            </abc-flex>
        `,
    }),

    name: '带自定义头部',
};

export const widthTabs = {
    render: () => ({
        components: {
            BizQuickOptionsPanel,
        },

        data() {
            return {
                labelPosition: 'top',
                showSplitLine: true,
                showTitle: true,
                tabsValue: 'chineseExamination',
                tabsOptions: [
                    {
                        label: '望闻切诊',
                        value: 'chineseExamination',
                    },
                    {
                        label: '体温',
                        value: 'temperature',
                    },
                    {
                        label: '脉搏',
                        value: 'heartRate',
                    },
                    {
                        label: '呼吸',
                        value: 'breathe',
                    },
                ],
                contentList1: [
                    {
                        'label': '既往史',
                        'list': [
                            {
                                label: '高血压', value: '高血压',
                            },
                            {
                                label: '高血脂', value: '高血脂',
                            },
                            {
                                label: '心脏病', value: '心脏病',
                            },
                            {
                                label: '糖尿病', value: '糖尿病',
                            },
                            {
                                label: '精神疾病', value: '精神疾病',
                            },
                            {
                                label: '脑梗死', value: '脑梗死',
                            },
                            {
                                label: '肝炎', value: '肝炎',
                            },
                            {
                                label: '胃炎', value: '胃炎',
                            },
                            {
                                label: '肺结核', value: '肺结核',
                            },
                            {
                                label: '哮喘', value: '哮喘',
                            },
                            {
                                label: '鼻炎', value: '鼻炎',
                            },
                            {
                                label: '甲亢', value: '甲亢',
                            },
                            {
                                label: '高血压', value: '高血压',
                            },
                            {
                                label: '高血脂', value: '高血脂',
                            },
                            {
                                label: '心脏病', value: '心脏病',
                            },
                            {
                                label: '糖尿病', value: '糖尿病',
                            },
                            {
                                label: '精神疾病', value: '精神疾病',
                            },
                            {
                                label: '脑梗死', value: '脑梗死',
                            },
                            {
                                label: '肝炎', value: '肝炎',
                            },
                            {
                                label: '胃炎', value: '胃炎',
                            },
                            {
                                label: '肺结核', value: '肺结核',
                            },
                            {
                                label: '哮喘', value: '哮喘',
                            },
                            {
                                label: '鼻炎', value: '鼻炎',
                            },
                            {
                                label: '甲亢', value: '甲亢',
                            },
                        ],
                    },
                    {
                        'label': '个人史',
                        'list': [
                            {
                                label: '吸烟', value: '吸烟',
                            },
                            {
                                label: '偶尔吸烟', value: '偶尔吸烟',
                            },
                            {
                                label: '长期吸烟', value: '长期吸烟',
                            },
                            {
                                label: '不饮酒', value: '不饮酒',
                            },
                            {
                                label: '偶尔饮酒', value: '偶尔饮酒',
                            },
                            {
                                label: '长期饮酒', value: '长期饮酒',
                            },
                            {
                                label: '未婚', value: '未婚',
                            },
                            {
                                label: '已婚', value: '已婚',
                            },
                            {
                                label: '未孕', value: '未孕',
                            },
                            {
                                label: '备孕', value: '备孕',
                            },
                            {
                                label: '怀孕', value: '怀孕',
                            },
                            {
                                label: '闭经', value: '闭经',
                            },
                            {
                                label: '有早产史', value: '有早产史',
                            },
                            {
                                label: '有流产史', value: '有流产史',
                            },
                        ],
                    },
                ],
                contentList2: [
                    {
                        'label': '收缩压',
                        'list': [
                            {
                                label: 70, value: 70,
                            },
                            {
                                label: 75, value: 75,
                            },
                            {
                                label: 80, value: 80,
                            },
                            {
                                label: 85, value: 85,
                            },
                            {
                                label: 90, value: 90,
                            },
                            {
                                label: 95, value: 95,
                            },
                            {
                                label: 100, value: 100,
                            },
                            {
                                label: 105, value: 105,
                            },
                            {
                                label: 110, value: 110,
                            },
                            {
                                label: 115, value: 115,
                            },
                            {
                                label: 120, value: 120,
                            },
                            {
                                label: 125, value: 125,
                            },
                            {
                                label: 130, value: 130,
                            },
                            {
                                label: 135, value: 135,
                            },
                            {
                                label: 140, value: 140,
                            },
                            {
                                label: 145, value: 145,
                            },
                            {
                                label: 150, value: 150,
                            },
                            {
                                label: 155, value: 155,
                            },
                            {
                                label: 160, value: 160,
                            },
                            {
                                label: 165, value: 165,
                            },
                            {
                                label: 170, value: 170,
                            },
                        ],
                    },
                    {
                        'label': '个人史',
                        'list': [
                            {
                                label: 80, value: 80,
                            },
                            {
                                label: 83, value: 83,
                            },
                            {
                                label: 86, value: 86,
                            },
                            {
                                label: 89, value: 89,
                            },
                            {
                                label: 92, value: 92,
                            },
                            {
                                label: 95, value: 95,
                            },
                            {
                                label: 98, value: 98,
                            },
                            {
                                label: 101, value: 101,
                            },
                            {
                                label: 104, value: 104,
                            },
                            {
                                label: 107, value: 107,
                            },
                            {
                                label: 110, value: 110,
                            },
                            {
                                label: 113, value: 113,
                            },
                            {
                                label: 116, value: 116,
                            },
                            {
                                label: 119, value: 119,
                            },
                            {
                                label: 122, value: 122,
                            },
                            {
                                label: 125, value: 125,
                            },
                            {
                                label: 128, value: 128,
                            },
                            {
                                label: 131, value: 131,
                            },
                            {
                                label: 134, value: 134,
                            },
                            {
                                label: 137, value: 137,
                            },
                            {
                                label: 140, value: 140,
                            },
                            {
                                label: 143, value: 143,
                            },
                            {
                                label: 146, value: 146,
                            },
                            {
                                label: 149, value: 149,
                            },
                            {
                                label: 152, value: 152,
                            },
                            {
                                label: 155, value: 155,
                            },
                            {
                                label: 158, value: 158,
                            },
                            {
                                label: 161, value: 161,
                            },
                            {
                                label: 164, value: 164,
                            },
                            {
                                label: 167, value: 167,
                            },
                            {
                                label: 170, value: 170,
                            },
                            {
                                label: 173, value: 173,
                            },
                            {
                                label: 176, value: 176,
                            },
                            {
                                label: 179, value: 179,
                            },
                        ],
                    },
                ],
            };
        },

        computed: {
            contentList() {
                switch (this.tabsValue) {
                    case 'chineseExamination':
                        return this.contentList1;
                    case 'temperature':
                        return this.contentList2;
                    case 'heartRate':
                        return this.contentList1;
                    case 'breathe':
                        return [];
                    default:
                        return [];
                }
            },
        },

        methods: {
            handleClose() {
                console.log('closed');
            },
            handleSelect(val) {
                console.log('selected:', val);
            },
            changeTabs(val) {
                console.log('changeTabs', val);
                this.tabsValue = val;
            },
        },

        template: `
            <abc-flex :gap="8" vertical>
                <biz-quick-options-panel
                    :list="contentList"
                    :tabs-value="tabsValue"
                    :tabs-options="tabsOptions"
                    @select="handleSelect"
                    @close="handleClose"
                    @changeTabs="changeTabs"
                >
                </biz-quick-options-panel>
            </abc-flex>
        `,
    }),

    name: '带Tabs',
};

export const widthSettingSlot = {
    render: () => ({
        components: {
            BizQuickOptionsPanel,
        },

        data() {
            return {
                vertical: 0,
                contentList: [
                    {
                        'label': '',
                        'list': [
                            {
                                label: '抗卡积分和娜姐开发三级开发拿手机妇女节发几款三分看见啊是妇女节开发你撒娇咖啡撒放你家吧净空法师你分看见啊不能放假卡收费和娜姐说开发健身房八九分不开', value: '抗卡积分和娜姐开发三级开发拿手机妇女节发几款三分看见啊是妇女节开发你撒娇咖啡撒放你家吧净空法师你分看见啊不能放假卡收费和娜姐说开发健身房八九分不开',
                            },
                            {
                                label: '高血压', value: '高血压',
                            },
                            {
                                label: '高血脂', value: '高血脂',
                            },
                            {
                                label: '心脏病', value: '心脏病',
                            },
                            {
                                label: '糖尿病', value: '糖尿病',
                            },
                            {
                                label: '精神疾病', value: '精神疾病',
                            },
                            {
                                label: '脑梗死', value: '脑梗死',
                            },
                            {
                                label: '肝炎', value: '肝炎',
                            },
                            {
                                label: '胃炎', value: '胃炎',
                            },
                            {
                                label: '肺结核', value: '肺结核',
                            },
                            {
                                label: '哮喘', value: '哮喘',
                            },
                            {
                                label: '鼻炎', value: '鼻炎',
                            },
                            {
                                label: '甲亢', value: '甲亢',
                            },
                            {
                                label: '高血压', value: '高血压',
                            },
                            {
                                label: '高血脂', value: '高血脂',
                            },
                            {
                                label: '心脏病', value: '心脏病',
                            },
                            {
                                label: '糖尿病', value: '糖尿病',
                            },
                            {
                                label: '精神疾病', value: '精神疾病',
                            },
                            {
                                label: '脑梗死', value: '脑梗死',
                            },
                            {
                                label: '肝炎', value: '肝炎',
                            },
                            {
                                label: '胃炎', value: '胃炎',
                            },
                            {
                                label: '肺结核', value: '肺结核',
                            },
                            {
                                label: '哮喘', value: '哮喘',
                            },
                            {
                                label: '鼻炎', value: '鼻炎',
                            },
                            {
                                label: '甲亢', value: '甲亢',
                            },
                        ],
                    },
                ],
            };
        },

        methods: {
            handleClose() {
                console.log('closed');
            },
            handleSelect(val) {
                console.log('selected:', val);
            },
            onSettingClick(event) {
                console.log('onSettingClick', event);
            },
        },

        template: `
            <abc-flex :gap="8" vertical>
                <abc-flex>
                    <abc-text>内容方向：</abc-text>
                    <abc-radio-group v-model="vertical">
                        <abc-radio :label="0">横向</abc-radio>
                        <abc-radio :label="1">纵向</abc-radio>
                    </abc-radio-group>
                </abc-flex>

                <biz-quick-options-panel
                    :list="contentList"
                    :vertical="vertical"
                    show-setting
                    @select="handleSelect"
                    @close="handleClose"
                    @onSettingClick="onSettingClick"
                >
                    <template #extra>
                        <abc-button
                            variant="text"
                            theme="default"
                        >
                            这是extra插槽按钮
                        </abc-button>
                    </template>
                </biz-quick-options-panel>
            </abc-flex>
        `,
    }),

    name: '带setting和extra插槽',
};

import BizValueAddedCard from './src/views/index.vue';

export default {
    title: 'Components/卡片组件/开通卡片-BizValueAddedCard',
    component: BizValueAddedCard,
};

export const base = {
    render: () => ({
        components: {
            BizValueAddedCard,
        },

        template: `
          <biz-value-added-card
              :openStatus="1"
              title="云煎药服务对接"
              content="对接代煎代配云平台，开立中药处方后自动上传，数据无缝衔接流转"
              btn-text="立即开通"
              plainBtnText="产品介绍"
              theme="blue"
              icon="s-DaiJian-logo"
          >
          </biz-value-added-card>
        `,
    }),

    name: '基础用法',
};

import {Meta, <PERSON>vas, Controls, Story,} from '@storybook/blocks';
import * as Stories from './index.stories';

import PatientCard from './src/views/index.vue';
import img1 from "./src/images/img.png";
import img2 from "./src/images/img2.png";

<Meta of={Stories} />

# BizPatientCard

## 基础用法

<Canvas>
    <Story of={Stories.base} />
</Canvas>


## 指南
使用场景 用于各个业务模块的biz-patient-section组件中，一般不独立使用
<img src={img1} alt="患者标签选择器" width="400" />
<img src={img2} alt="患者标签选择器" width="700" />
## 属性详情
<Controls />

<template>
    <div ref="view-social" class="package-social">
        <div class="package-social-btn">
            <slot></slot>
        </div>
    </div>
</template>

<script>
    export default {
        props: {
            // 社保卡信息
            shebaoCardInfo: {
                type: Object,
                default: null,
            },
            // 弹窗定位
            placement: {
                type: String,
                default: 'bottom-end',
            },
            value: {
                type: Boolean,
                required: true,
            },
            abcSocialSecurity: {
                type: Object,
                default: null,
            },
        },
        watch: {
            async value(newValue) {
                if (newValue) {
                    await this.abcSocialSecurity.showCardInfo({
                        cardInfo: {
                            ...this.shebaoCardInfo,
                        },
                        showFooter: false,
                    });
                    this.$emit('input', false);
                }
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/abc-common.scss';

.crm-module__package-social__card-box {
    z-index: 2001;

    .crm-module__package-social__view-card-info {
        position: relative;
        top: 4px;
        padding: 24px;
        overflow: hidden;
        background-color: #ffffff;
        border: 1px solid #b7b9c2;
        border-radius: var(--abc-border-radius-small);
        box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);
    }
}
</style>

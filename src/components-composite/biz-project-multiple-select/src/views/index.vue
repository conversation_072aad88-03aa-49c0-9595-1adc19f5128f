<template>
    <abc-select-input
        :value="selectedNames"
        :visible-popper.sync="visiblePanel"
        clearable
        placeholder="请选择项目"
        adaptive-width
        plain-popper
        multiple
        @change="handleSelectChange"
    >
        <template #prepend>
            <abc-icon icon="filter"></abc-icon>
        </template>
        <abc-suggestions-panel
            :key="visiblePanel"
            :loading="loading"
            searchable
            multiple-select
            @search="handleSearch"
        >
            <template #header>
                <abc-suggestions-header>
                    <abc-suggestions-cell width="140">
                        项目
                    </abc-suggestions-cell>
                    <abc-suggestions-cell width="70">
                        规格
                    </abc-suggestions-cell>
                    <abc-suggestions-cell align="right" width="80">
                        库存
                    </abc-suggestions-cell>
                    <abc-suggestions-cell align="right" width="80">
                        价格
                    </abc-suggestions-cell>
                    <abc-suggestions-cell width="100" align="center">
                        医保
                    </abc-suggestions-cell>
                    <abc-suggestions-cell width="100">
                        厂家
                    </abc-suggestions-cell>
                </abc-suggestions-header>
            </template>
            <abc-suggestions-item
                v-for="item in renderList"
                :key="item.id"
                :selected="item.checked"
                style="gap: 8px;"
                @click="handleSelectItem(item)"
            >
                <abc-suggestions-cell width="140">
                    <abc-flex align="center" :gap="4">
                        <abc-text style="max-width: 76px;" class="ellipsis">
                            {{ item.name }}
                        </abc-text>

                        <biz-exam-business-tag
                            is-out-sourcing-tag
                            :coop-flag="item.coopFlag"
                            :type="item.type"
                            :sub-type="item.subType"
                        ></biz-exam-business-tag>
                    </abc-flex>
                </abc-suggestions-cell>
                <abc-suggestions-cell width="70">
                    {{ item.displaySpec || '' }}
                </abc-suggestions-cell>
                <abc-suggestions-cell align="right" width="80">
                </abc-suggestions-cell>
                <abc-suggestions-cell align="right" width="80">
                    {{ $t('currencySymbol') }}{{ item.packagePrice | formatMoney(false) }}
                </abc-suggestions-cell>
                <abc-suggestions-cell width="100" align="center">
                    {{ item | medicalFeeGradeFormatStr({ shebaoCardInfo: null }) }}
                </abc-suggestions-cell>
                <abc-suggestions-cell width="100">
                    {{ item.manufacturer || '' }}
                </abc-suggestions-cell>
            </abc-suggestions-item>
        </abc-suggestions-panel>
    </abc-select-input>
</template>

<script>
    import {
        clone, debounce,
    } from '@abc/utils';
    import BizExamBusinessTag from '@/components-composite/biz-exam-business-tag/src/views/index.vue';

    export default {
        name: 'BizProjectMultipleSelect',
        components: { BizExamBusinessTag },
        props: {
            // 选中的项目
            value: {
                type: Array,
                default: () => [],
            },
            /**
             * 获取推荐项目的方法
             * @return {Promise<{list: any[]}>}
             */
            fetchFn: {
                required: true,
                type: Function,
                default: () => {},
            },
        },
        data() {
            return {
                visiblePanel: false,
                selectedNames: this.value.map((item) => item.name),
                selectedItems: clone(this.value),
                loading: false,
                keyword: '',
                recommendList: [],
            };
        },
        computed: {
            renderList() {
                return this.recommendList.map((item) => {
                    return {
                        ...item,
                        checked: this.selectedItems.some((selectedItem) => selectedItem.id === item.id),
                    };
                });
            },
        },
        watch: {
            value: {
                handler(newValue) {
                    this.selectedItems = clone(newValue);
                    this.selectedNames = newValue.map((item) => item.name);
                },
                immediate: true,
            },
            visiblePanel: {
                handler(visible) {
                    if (visible) {
                        this.recommendList = [];
                        this.loadData();
                    } else {
                        this.keyword = '';
                    }
                },
            },
        },
        created() {
            this._debounceSearch = debounce(this.loadData, 250, true);
        },
        methods: {
            async loadData() {
                try {
                    this.loading = true;

                    const { keyword } = this;

                    const { list } = await this.fetchFn({
                        keyword,
                    });

                    if (keyword !== this.keyword) {
                        return;
                    }

                    this.recommendList = list;
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
            },
            handleSelectChange(names) {
                this.selectedNames = names;
                const newSelectedItems = this.selectedItems.filter((item) => {
                    return names.includes(item.name);
                });
                this.selectedItems = newSelectedItems;
                this.$emit('input', clone(newSelectedItems));
            },
            handleSelectItem(item) {
                const index = this.selectedItems.findIndex((selectedItem) => selectedItem.id === item.id);
                if (index === -1) {
                    this.selectedItems.push(item);
                } else {
                    this.selectedItems.splice(index, 1);
                }
                this.selectedNames = this.selectedItems.map((selectedItem) => selectedItem.name);
                this.$emit('input', clone(this.selectedItems));
            },
            handleSearch(keyword) {
                this.keyword = keyword;
                this._debounceSearch();
            },
        },
    };
</script>

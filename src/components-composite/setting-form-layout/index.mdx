import { Meta, Story, Canvas,ArgsTable } from '@storybook/blocks';
import * as Stories from './index.stories.js';
import BizSettingLayout from './src/views/index.vue';

<Meta of={Stories} />

# BizSettingLayout

设置页面的标准布局组件，提供了侧边栏、内容区、底部等布局结构。

<div style={{ height: '24px' }}></div>

## 组合组件列表

- `BizSettingLayout` - 设置布局容器
- `BizSettingContent` - 布局内容区
- `BizSettingFooter` - 布局底部
- `BizSettingSidebar` - 布局侧边栏
- `BizFillRemainHeight` - 自动填充剩余高度的容器

<div style={{ height: '24px' }}></div>

## 概览

<Canvas>
  <Story of={Stories.Basic} />
</Canvas>

<div style={{ height: '24px' }}></div>

## 属性详情
<ArgsTable of={BizSettingLayout} />

<template>
    <div class="track-card-model" :class="[customClass]">
        <div class="left">
            <abc-flex align="center" justify="flex-start" class="head">
                <slot name="img">
                </slot>
                <abc-text class="title">
                    <slot name="title">
                    </slot>
                </abc-text>
                <abc-text class="dates">
                    <slot name="dates">
                    </slot>
                </abc-text>
                <abc-text class="track">
                    <slot name="track">
                    </slot>
                </abc-text>
            </abc-flex>
            <div class="section">
                <slot name="section">
                </slot>
            </div>
        </div>
        <div class="right">
            <abc-icon icon="Arrow_Rgiht" size="16" color="#d9dbe3"></abc-icon>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'TrackCardModel',
        props: {
            customClass: {
                type: String,
                default: '',
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/abc-common.scss";

.track-card-model {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 10px 10px 16px;
    cursor: pointer;

    &:hover {
        background-color: $P4;
    }

    .left {
        > .head {
            height: 16px;
            font-size: 14px;
            color: var(--abc-color-T1);

            img {
                width: 16px;
                height: 16px;
            }

            .title {
                margin: 0 12px 0 8px;
            }
        }

        > .section {
            padding: 4px 0 0;
            font-size: 13px;
            line-height: 16px;
            color: var(--abc-color-T2);

            .item {
                @include flex(row, flex-start, flex-start);

                margin-top: 6px;

                &:first-child {
                    margin-top: 8px !important;
                }

                > span:first-child {
                    flex-shrink: 0;
                }

                > span:last-child {
                    flex: 1;

                    @include ellipsis(2);
                }
            }
        }
    }
}
</style>

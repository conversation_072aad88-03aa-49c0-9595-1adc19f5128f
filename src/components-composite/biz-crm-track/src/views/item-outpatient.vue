<template>
    <track-card-model custom-class="crm-module__package-track__item-outpatient" @click.native="$emit('look-detail', info.actionAbstract.outpatientId)">
        <template slot="img">
            <abc-icon size="20" icon="s-outpatient-color"></abc-icon>
        </template>
        <template slot="title">
            门诊
        </template>
        <template slot="dates">
            {{ info.recordHappened | formatCacheTime }}
        </template>
        <template slot="section">
            <div class="item">
                <span>门店：</span>
                <span>{{ info.actionAbstract.clinicName }}</span>
            </div>
            <div class="item">
                <span>医生：</span>
                <span>{{ info.actionAbstract.doctorName }}</span>
            </div>
            <div class="item">
                <span>诊断：</span>
                <span>{{ info.actionAbstract.diagnosis | parseDiagnosis }}</span>
            </div>
            <div class="item">
                <span>付费：</span>
                <span v-if="info.actionAbstract.receivedFee"><abc-money :value="info.actionAbstract.receivedFee" :is-show-space="true"></abc-money></span>
                <span v-else><abc-money :value="0" :is-show-space="true"></abc-money></span>
            </div>
        </template>
    </track-card-model>
</template>

<script>
    import trackCardModel from './track-card-model';
    export default {
        name: 'ItemOutpatient',
        components: {
            trackCardModel,
        },
        props: {
            info: {
                type: Object,
                default() {
                    return {};
                },
            },
        },
    };
</script>


@import "src/styles/mixin.scss";
@import "src/styles/abc-common.scss";

.composite-week-schedule {
    .h_100 {
        height: 100%;
    }

    &.has-border {
        border: 1px solid var(--abc-color-P8);
    }

    &--fill {
        .composite-week-schedule__header {
            // background-color: var(--abc-color-cp-grey3);
            height: 48px !important;
        }

        .composite-week-schedule__header-prepend {
            border-right-color: transparent !important;
        }

        .composite-week-schedule__header-item {
            border-right-color: transparent !important;
        }

        .composite-week-schedule__body {
            .composite-week-schedule__body-row {
                .composite-week-schedule__body-col {
                    .composite-week-schedule-cell {
                        padding: 0 var(--abc-paddingLR-xs);
                    }
                }
            }
        }
    }

    .composite-week-schedule__header {
        display: flex;
        height: 40px;
        border-bottom: 1px solid var(--abc-color-P8);

        &.has-scrollbar {
            .composite-week-schedule__header-item {
                &:nth-last-child(2) {
                    border-right: 0;
                }
            }
        }

        .composite-week-schedule__header-prepend {
            flex-shrink: 0;
            height: 100%;
            border-right: 1px solid var(--abc-color-P8);
        }

        .composite-week-schedule__header-item {
            flex: 1;
            height: 100%;

            &:not(:last-child) {
                border-right: 1px solid var(--abc-color-P8);
            }
        }

        .composite-week-schedule__header-append {
            flex-shrink: 0;
            height: 100%;
        }

        .composite-week-schedule__header-scroll-bar {
            flex-shrink: 0;
            width: 10px;
            height: 100%;
        }
    }

    .composite-week-schedule__body {
        overflow-y: auto;

        @include scrollBar();

        &.persistent-scrollbar {
            overflow-y: scroll;

            @include scrollBar(true);
        }

        .composite-week-schedule__body-row + .composite-week-schedule__body-row {
            border-top: 1px solid var(--abc-color-P8);

            .composite-week-schedule__body-append {
                position: relative;

                &::after {
                    position: absolute;
                    top: -1px;
                    right: 0;
                    left: 0;
                    z-index: 1;
                    height: 1px;
                    content: '';
                    background-color: #ffffff;
                }
            }
        }

        .composite-week-schedule__body-row {
            display: flex;
            min-height: 48px;

            .composite-week-schedule__body-col {
                flex: 1;
                align-self: stretch;

                &:not(:last-child) {
                    border-right: 1px solid var(--abc-color-P8);
                }

                .composite-week-schedule-cell {
                    cursor: pointer;

                    &:hover {
                        background-color: var(--abc-color-cp-grey2);
                        border-color: var(--abc-color-B3);
                    }

                    &.is-disabled {
                        color: var(--abc-color-T3);
                        cursor: default;

                        &:hover {
                            background-color: transparent;
                            border-color: transparent;
                        }
                    }

                    &.has-background {
                        background-color: var(--abc-color-cp-grey2);
                    }

                    &.is-expired {
                        cursor: not-allowed;

                        &:hover {
                            border-color: transparent;
                        }
                    }

                    &.is-expired:not(.has-background) {
                        background: url('../assets/image/shedule-cell-bg.svg');
                    }
                }
            }

            .composite-week-schedule__body-prepend {
                align-self: stretch;
                border-right: 1px solid var(--abc-color-P8);

                .composite-week-schedule-cell {
                    padding: var(--abc-paddingTB-m) var(--abc-paddingLR-m);
                }
            }

            .composite-week-schedule__body-append {
                align-self: stretch;

                .composite-week-schedule-cell {
                    padding: var(--abc-paddingTB-m) var(--abc-paddingLR-m);
                }
            }
        }
    }
}

.composite-week-schedule-cell {
    position: relative;
    width: 100%;
    height: 100%;
    padding: 0 var(--abc-paddingLR-m);
    color: var(--abc-color-T1);
    border: 1px solid transparent;

    &.is-excel {
        padding: 0;
        border: 0;

        .abc-form-item,
        .abc-form-item-content,
        .abc-input-wrapper,
        .abc-date-picker,
        .abc-date-time-picker,
        .abc-time-picker,
        .abc-select-wrapper {
            height: 100%;
        }

        .abc-date-time-picker .abc-date-picker {
            margin-right: 0;
        }

        .abc-input__inner {
            height: 100%;
            background-color: transparent;
            border-color: transparent;
            border-radius: 0;
        }
    }
}

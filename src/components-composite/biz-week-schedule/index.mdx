import {Meta, Story, Controls, Canvas} from '@storybook/blocks';
import * as BizWeekSchedule from './index.stories.js';
import imgSrc1 from "./src/assets/image/example1.png";
import imgSrc2 from "./src/assets/image/example2.png";

<Meta of={BizWeekSchedule} />

# BizWeekSchedule

## 基本用法 variant（outline）

<Canvas>
    <Story of={BizWeekSchedule.Base} />
</Canvas>

## variant（fill）

<Canvas>
    <Story of={BizWeekSchedule.Fill} />
</Canvas>

## 支持分页, 仅fill 支持分页

<Canvas>
    <Story of={BizWeekSchedule.Paginate} />
</Canvas>

## 指南
- outline
<img src={imgSrc1} alt="" />
- fill
<img src={imgSrc2} alt="" />

## 属性详情


<Controls />

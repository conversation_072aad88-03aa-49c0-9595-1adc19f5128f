import { Meta } from '@storybook/blocks';
import img1 from './src/assets/file-upload.png'
import img2 from './src/assets/file-input-upload.png'
import img3 from './src/assets/image-upload.png'

<Meta title="Components/AbcFileUploader" />

# AbcFileUploader

文件上传组件，基于`abc-upload`，把文件上传后的预览删除都封装到了内部，使用者只需要关注抛出的文件列表结果

## 代码示例

### 基础用法

```js
export default {
    components: {
        AbcFileUploader
    },
    data() {
        return {
          fileList: [],
          patient: { name:'xxx', age: {}, sex: '女' }
        }
    },
    template: `
        <abc-file-uploader
            v-model="fileList"
            :max-upload-count="10"
            :trigger="{
                variant: 'image',
                size: 'small',
            }"
            :pc-upload="{
                accept: 'image/*',
                ossFilePath: 'examination',
            }"
            :mobile-upload="{
                businessType: BusinessTypeEnum.EXAMINATION,
                businessId,
                businessDesc: '检验',
                patientInfo: patient,
                uploadDescription: '检验附件支持图片、PDF格式',
            }"
            :layout-config={
                vertical: true,
            }"
        ></abc-file-uploader>
    `
}
```

### 使用效果

<img src={img1} />
<img src={img2} />
<img src={img3} />

## 属性详情

### 主要属性说明

<table>
    <thead>
    <tr>
        <th>属性名</th>
        <th>类型</th>
        <th>默认值</th>
        <th>说明</th>
    </tr>
    </thead>
    <tbody>
    <tr>
        <td>value/v-model</td>
        <td>Array</td>
        <td>`[]`</td>
        <td>上传的文件列表，支持双向绑定</td>
    </tr>
    <tr>
        <td>disabled</td>
        <td>`Boolean`</td>
        <td>`false`</td>
        <td>是否禁用</td>
    </tr>
    <tr>
        <td>maxUploadCount</td>
        <td>Number</td>
        <td>`1`</td>
        <td>最大上传文件数量</td>
    </tr>
    <tr>
        <td>trigger</td>
        <td>Object</td>
        <td>{ `{variant: 'file', triggerUploadText: '上传文件', ...}` }</td>
        <td>触发器配置，详见下方<a href="#触发器配置-trigger" target="_self">说明</a></td>
    </tr>
    <tr>
        <td>fileViewConfig</td>
        <td>Object</td>
        <td>{`{}`}</td>
        <td>文件预览配置</td>
    </tr>
    <tr>
        <td>layoutConfig</td>
        <td>Object</td>
        <td>{`{}`}</td>
        <td>文件布局配置，加在最外层的abc-flex上，需要传 abc-flex 的参数</td>
    </tr>
    <tr>
        <td>pcUpload</td>
        <td>Object</td>
        <td>{ `{accept: 'image/*', multiple: false, ...}` }</td>
        <td>PC端上传配置，详见下方<a href="#pc端上传配置-pcupload" target="_self">说明</a></td>
    </tr>
    <tr>
        <td>mobileUpload</td>
        <td>Object</td>
        <td>{ `{businessType: -1, businessId: '', ...}` }</td>
        <td>移动端上传配置，详见下方<a href="#移动端上传配置-mobileupload" target="_self">说明</a></td>
    </tr>
    </tbody>
</table>

### 触发器配置 (trigger)

<table>
    <thead>
    <tr>
        <th>属性名</th>
        <th>类型</th>
        <th>默认值</th>
        <th>说明</th>
    </tr>
    </thead>
    <tbody>
    <tr>
        <td>variant</td>
        <td>String</td>
        <td>'file'</td>
        <td>触发器类型，可选值：`file`、`file-input`、`image`</td>
    </tr>
    <tr>
        <td>triggerUploadText</td>
        <td>String</td>
        <td>`上传文件`</td>
        <td>触发器文本</td>
    </tr>
    <tr>
        <td>triggerButtonProps</td>
        <td>Object</td>
        <td>{}</td>
        <td>按钮属性，当variant为file时生效</td>
    </tr>
    <tr>
        <td>size</td>
        <td>String</td>
        <td>`small`</td>
        <td>触发器大小，可选值：`mini`、`small`、`normal`</td>
    </tr>
    <tr>
        <td>width</td>
        <td>String/Number</td>
        <td>`''`</td>
        <td>触发器宽度，优先级高于size</td>
    </tr>
    <tr>
        <td>height</td>
        <td>String/Number</td>
        <td>`''`</td>
        <td>触发器高度，优先级高于size</td>
    </tr>
    </tbody>
</table>

### PC端上传配置 (pcUpload)

<table>
    <thead>
    <tr>
        <th>属性名</th>
        <th>类型</th>
        <th>默认值</th>
        <th>说明</th>
    </tr>
    </thead>
    <tbody>
    <tr>
        <td>ossFilePath</td>
        <td>String</td>
        <td>`''`</td>
        <td>（必填）OSS上传文件路径</td>
    </tr>
    <tr>
        <td>accept</td>
        <td>String</td>
        <td>`image/*`</td>
        <td>可选文件类型</td>
    </tr>
    <tr>
        <td>multiple</td>
        <td>Boolean</td>
        <td>`false`</td>
        <td>是否支持文件多选</td>
    </tr>
    <tr>
        <td>maxFileSize</td>
        <td>Number</td>
        <td>`100 * 1024 * 1024`</td>
        <td>文件最大尺寸限制，默认100MB</td>
    </tr>
    <tr>
        <td>beforeFileUpload</td>
        <td>Function</td>
        <td>`() => true`</td>
        <td>上传文件前的钩子函数，返回false拦截文件上传</td>
    </tr>
    </tbody>
</table>

### 移动端上传配置 (mobileUpload)

<table>
    <thead>
    <tr>
        <th>属性名</th>
        <th>类型</th>
        <th>默认值</th>
        <th>说明</th>
    </tr>
    </thead>
    <tbody>
    <tr>
        <td>businessType</td>
        <td>Number</td>
        <td>`-1`</td>
        <td>（必填）业务类型，必须是BusinessTypeEnum中定义的值</td>
    </tr>
    <tr>
        <td>businessId</td>
        <td>String</td>
        <td>`''`</td>
        <td>业务ID</td>
    </tr>
    <tr>
        <td>businessDesc</td>
        <td>String</td>
        <td>`上传文件`</td>
        <td>业务描述</td>
    </tr>
    <tr>
        <td>patientInfo</td>
        <td>Object</td>
        <td>{}</td>
        <td>患者信息</td>
    </tr>
    <tr>
        <td>uploadDescription</td>
        <td>String</td>
        <td>`''`</td>
        <td>上传描述</td>
    </tr>
    <tr>
        <td>socketKey</td>
        <td>String</td>
        <td>`short-url.upload_attachment`</td>
        <td>Socket消息的key</td>
    </tr>
    </tbody>
</table>

### 事件

<table>
    <thead>
    <tr>
        <th>事件名</th>
        <th>说明</th>
        <th>回调参数</th>
    </tr>
    </thead>
    <tbody>
    <tr>
        <td>input</td>
        <td>文件列表变化时触发</td>
        <td>(fileList: Array) 更新后的文件列表</td>
    </tr>
    </tbody>
</table>

<template>
    <component
        :is="itemView"
        :key="item.id"
        class="goods-list-item"
        :style="{ width: itemWidth }"
        :class="{
            'active': selectedGoodsIds.includes(item.id),
            'disabled': hasNoStock(item),
        }"
        :size="size"
        :item="item"
        :fee-type-name="feeTypeName"
        :patient="patient"
        @click.native="handleClickItem(item)"
    ></component>
</template>

<script>
    import {
        GoodsSubTypeEnumStr,
        GoodsTypeEnum,
    } from '@abc/constants';
    import ItemNormal from './item-normal';
    import ItemChinese from './item-chinese';
    import ItemWestern from './item-western';
    import ItemEyeglasses from './item-eyeglasses';

    export default {
        props: {
            item: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            selectedGoodsIds: {
                type: Array,
                default: () => [],
            },
            size: {
                type: String,
                default: 'small',
            },
            itemAlone: Boolean, // item 独占一行
            itemMode: String, // item 内容的显示模式，中西药有自定义显示内容
            feeTypeName: String, // 费别
            patient: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            featureSupportFilterEyeGlasses: Boolean,
            disableNoStockGoods: Boolean,
        },
        data() {
            return {
                GoodsSubTypeEnumStr,
            };
        },
        computed: {
            boxStyle() {
                return {
                    padding: this.size === 'small' ? '8px 6px 8px 16px' : '16px 6px 16px 16px',
                    height: this.size === 'small' ?
                        'calc(100% - 44px)' : this.featureSupportFilterEyeGlasses ?
                            'calc(100% - 39px)' : '100%',
                };
            },
            itemWidth() {
                if (this.itemAlone) {
                    return '100%';
                }
                return this.size === 'small' ? 'calc((100% - 25px)/ 2)' : 'calc((100% - 36px)/ 2)';
            },
            itemView() {
                if (this.itemMode === 'medicine_western') {
                    return ItemWestern;
                }
                if (this.itemMode === 'medicine_chinese') {
                    return ItemChinese;
                }
                if (this.itemMode === 'eyeglasses') {
                    return ItemEyeglasses;
                }
                return ItemNormal;
            },
        },
        methods: {
            needStock(item) {
                return (
                    item.type === GoodsTypeEnum.MEDICINE ||
                    item.type === GoodsTypeEnum.MATERIAL ||
                    item.type === GoodsTypeEnum.GOODS
                );
            },
            /**
             * @desc dialog 上显示的是默认药房库存，只要有药房列表就都能选，业务方可以去切换药房
             * <AUTHOR>
             * @date 2023/09/06 14:22:13
             * @return {boolean}
             */
            hasNoStock(item) {
                if (this.size === 'big') {
                    const {
                        pharmacyGoodsStockList,
                    } = item;
                    if (this.disableNoStockGoods) {
                        if (this.needStock(item)) {
                            return !pharmacyGoodsStockList || pharmacyGoodsStockList?.length === 0;
                        }
                        return false;
                    }
                    return false;
                }
                if (this.needStock(item)) {
                    const {
                        pharmacyList,
                    } = item;
                    return !pharmacyList || pharmacyList.length === 0;
                }
                return false;
            },
            handleClickItem(item) {
                if (this.hasNoStock(item)) return;
                this.$emit('select-item', item);
            },
        },
    };
</script>


import { FunctionalDialog } from '@/views/common/functional-dialog.js';
import component from '@/components-composite/biz-goods-select-dialog/src/views/index.vue';

component.install = function (Vue) {
    Vue.component(component.name, component);
};

class BizGoodsSelectDialog extends FunctionalDialog {
    constructor(props, id = 'goods-select-dialog') {
        super(props, component, id);
    }
}

export {
    BizGoodsSelectDialog,
};

export default component;

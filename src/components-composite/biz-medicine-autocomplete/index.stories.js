import './src/medicine-autocomplete.scss';
// eslint-disable-next-line no-unused-vars
// import CrmAPI from 'src/api/crm';
export default {
    title: 'Components/选择器组件/药品选择器-BizMedicineAutocomplete',
};

export const base = {
    render: () => ({
        data() {
            return {
                westernMedicineCache: this.placeholder || '',
            };
        },

        methods: {
            queryWesternMedicineAsync(queryString, callback) {
                callback([
                    {
                        displayName: '阿莫西林颗粒(小儿阿莫西林颗粒)',
                        displaySpec: '1袋/盒',
                        price: '12.00',
                        stock: '88923盒',
                        manufacturer: '广州白云山',
                        minExpiryDate: '2027-12-31',
                        feeGrade: '甲',
                        remark: '',
                    },
                ]);
            },
        },

        template: `
            <abc-autocomplete
                v-model="westernMedicineCache"
                placeholder="输入药名或拼音码"
                size="medium"
                class="medicine-autocomplete"
                custom-class="wm-autocomplete-suggestion"
                :delay-time="10"
                :fetch-suggestions="queryWesternMedicineAsync"
                :async-fetch="true"
                :focus-show="true"
                :popper-options="{
            boundariesElement: 'body',
        }"
                :data-cy="$attrs['data-cy'] || 'medicine-autocomplete'"
            >
                <template slot="suggestion-header">
                    <div class="biz-medicine-autocomplete-title">
                        <div class="biz-medicine-autocomplete-name">
                            药名
                        </div>
                        <div class="biz-medicine-autocomplete-spec">
                            规格
                        </div>
                        <div class="biz-medicine-autocomplete-stock">
                            库存
                        </div>
                        <div class="biz-medicine-autocomplete-price">
                            价格
                        </div>
                        <div class="biz-medicine-autocomplete-manufacturer">
                            厂家
                        </div>
                        <div class="biz-medicine-autocomplete-expired">
                            最近效期
                        </div>
                        <div class="biz-medicine-autocomplete-medical-fee-grade">
                            医保
                        </div>
                        <div class="biz-medicine-autocomplete-remark">
                            备注
                        </div>
                    </div>
                </template>

                <template slot="suggestions" slot-scope="props">
                    <dt
                        slot="reference"
                        class="suggestions-item"
                        data-cy="medicine-suggestions-item"
                    >
                        <div class="biz-medicine-autocomplete-name">
                            {{ props.suggestion.displayName || '' }}
                        </div>
                        <div class="biz-medicine-autocomplete-spec">
                            {{ props.suggestion.displaySpec || '' }}
                        </div>
                        <div  class="biz-medicine-autocomplete-stock">
                            {{ props.suggestion.stock || '' }}
                        </div>
                        <div class="biz-medicine-autocomplete-price">
                            {{ props.suggestion.price || '' }}
                        </div>
                        <div class="biz-medicine-autocomplete-manufacturer">
                            {{ props.suggestion.manufacturer }}
                        </div>
                        <div class="biz-medicine-autocomplete-expired warn">
                            {{ props.suggestion.minExpiryDate || '' }}
                        </div>
                        <div class="biz-medicine-autocomplete-medical-fee-grade">
                            {{ props.suggestion.feeGrade || '' }}
                        </div>
                        <div class="biz-medicine-autocomplete-remark">
                            {{ props.suggestion.remark || '' }}
                        </div>
                    </dt>
                </template>

                <template #prepend>
                    <abc-icon icon="s-add-line-medium" color="var(--abc-color-T3)"></abc-icon>
                </template>
            </abc-autocomplete>
        `,
    }),

    name: '基础用法',
};

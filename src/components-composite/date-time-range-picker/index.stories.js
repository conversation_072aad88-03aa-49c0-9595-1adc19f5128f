import DateTimeRangePicker from './src/views/index.vue';

export default {
    title: 'Components/表单控件/时间范围选择器-DateTimeRangePicker',
    component: DateTimeRangePicker,
};

export const base = {
    render: () => ({
        components: {
            DateTimeRangePicker,
        },

        data() {
            return {
                params: {
                    dateFilter$: {
                        begin: '2020-01-01 00:00',
                        end: '2020-01-02 00:00',
                    },
                },
            };
        },

        methods: {
            handleDateChange(dateFilter) {
                console.log(dateFilter);
            },
        },

        template: `
            <date-time-range-picker
                v-model="params.dateFilter$"
                :label="{
                    begin: '开始时间', end: '结束时间'
                }"
                @change="handleDateChange"
            >
                <template #separator>
                    <abc-text theme="gray">
                        至
                    </abc-text>
                </template>
            </date-time-range-picker>
        `,
    }),

    name: '基础用法',
};

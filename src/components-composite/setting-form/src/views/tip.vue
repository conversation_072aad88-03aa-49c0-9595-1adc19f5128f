<template>
    <div class="setting-form-item_tip" style="width: 100%;">
        <div :style="{ 'margin-bottom': tip || $slots.tip ? '4px' : 0, }">
            <slot></slot>
        </div>

        <!--@slot 提示插槽，样式需要自行调整-->
        <slot name="tip">
            <abc-text
                v-if="tip"
                :theme=" isWarn ? 'warning-light' : 'gray'"
                size="mini"
                style="font-size: 12px;"
            >
                {{ tip }}
            </abc-text>
        </slot>
    </div>
</template>

<script>
    export default {
        name: 'BizSettingFormItemTip',

        props: {
            /**
             * 提示内容
             */
            tip: {
                type: String,
                default: '',
            },

            /**
             * 是否为警告主题
             */
            isWarn: Boolean,
        },
    };
</script>

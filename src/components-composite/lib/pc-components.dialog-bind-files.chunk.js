import{_ as e,a as i,n as a,c as r}from"./pc-components.index.chunk.js";import{isEqual as t}from"@abc/utils";import"vue";import"vue-i18n";import"jquery";import"vuedraggable";import"@abc/constants";import"@abc/ui-pc/src/mixins/src/form";import"@abc/ui-pc/src/mixins/src/input-size";import"@abc/ui-pc/src/mixins/src/cypress";import"@abc/utils-date";var o=a({render:function(){var e=this,i=e.$createElement,a=e._self._c||i;return e.value?a("div",{staticClass:"crm-module__package-card__dialog-bind-files"},[a("div",{staticClass:"bind-file-wrapper"},[a("div",{staticClass:"header-line"},[a("div",{staticClass:"name"},[e._v("\n                "+e._s("添加健康档案")+"\n            ")]),e._v(" "),a("div",{staticClass:"del-btn"},[a("abc-delete-icon",{attrs:{size:"large"},on:{delete:function(i){return e.$emit("input",!1)}}})],1)]),e._v(" "),a("div",{staticClass:"file-info-wrapper"},[a("div",{staticClass:"checkbox-group"},[a("abc-checkbox-group",{model:{value:e.healthFiles,callback:function(i){e.healthFiles=i},expression:"healthFiles"}},[a("abc-checkbox",{attrs:{label:"child",disabled:e.disabledChildHealth}},[a("div",{staticClass:"item-checkbox"},[a("span",[e._v("儿童健康档案")]),e._v(" "),a("span",[e._v("发育评测、生长看护记录")])])])],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("abc-button",{attrs:{disabled:e.disabledBtn,loading:e.loadingBtn},on:{click:e.onClickConfirm}},[e._v("\n                    确认\n                ")]),e._v(" "),a("abc-button",{attrs:{type:"blank"},on:{click:function(i){return e.$emit("input",!1)}}},[e._v("\n                    取消\n                ")])],1)])])]):e._e()},staticRenderFns:[]},(function(e){e&&e("data-v-314d7194_0",{source:'@charset "UTF-8";.base-font-style{font-style:normal;-webkit-font-smoothing:antialiased;-webkit-text-stroke-width:.2px;-moz-osx-font-smoothing:grayscale}.abc-font-color-primary{color:var(--abc-color-B1)}.abc-font-color-success{color:var(--abc-color-G1)}.abc-font-color-warn{color:var(--abc-color-Y2)}.abc-font-color-danger{color:var(--abc-color-R2)}.abc-font-color-tips{color:var(--abc-color-T2)}.crm-module__package-card__dialog-bind-files .bind-file-wrapper .header-line{display:flex;align-items:center;justify-content:space-between;height:40px;padding:0 8px 0 12px}.crm-module__package-card__dialog-bind-files .bind-file-wrapper .header-line .name{font-size:16px;font-weight:700;color:var(--abc-color-S1)}.crm-module__package-card__dialog-bind-files .bind-file-wrapper .file-info-wrapper{padding:0 12px 12px}.crm-module__package-card__dialog-bind-files .bind-file-wrapper .file-info-wrapper .checkbox-group{display:flex;align-items:center;width:100%;height:68px;border-top:1px dashed var(--abc-color-P6);border-bottom:1px dashed var(--abc-color-P6)}.crm-module__package-card__dialog-bind-files .bind-file-wrapper .file-info-wrapper .checkbox-group .abc-checkbox-wrapper{align-items:flex-start}.crm-module__package-card__dialog-bind-files .bind-file-wrapper .file-info-wrapper .checkbox-group .abc-checkbox-wrapper>.abc-checkbox__input{margin-top:2px}.crm-module__package-card__dialog-bind-files .bind-file-wrapper .file-info-wrapper .checkbox-group .abc-checkbox-wrapper .item-checkbox{display:flex;flex-direction:column;align-items:flex-start;justify-content:space-between;height:40px}.crm-module__package-card__dialog-bind-files .bind-file-wrapper .file-info-wrapper .checkbox-group .abc-checkbox-wrapper .item-checkbox>span:first-child{font-size:14px;line-height:20px;color:var(--abc-color-T1)}.crm-module__package-card__dialog-bind-files .bind-file-wrapper .file-info-wrapper .checkbox-group .abc-checkbox-wrapper .item-checkbox>span:last-child{margin-top:4px;font-size:12px;line-height:16px;color:var(--abc-color-T3)}.crm-module__package-card__dialog-bind-files .bind-file-wrapper .file-info-wrapper .dialog-footer{margin-top:12px;text-align:right}.crm-module__package-card__dialog-bind-files .bind-file-wrapper .file-info-wrapper .dialog-footer .abc-button{min-width:48px;height:26px;padding:0 9px}.crm-module__package-card__dialog-bind-files .bind-file-wrapper .file-info-wrapper .dialog-footer .abc-button+.abc-button{margin-left:4px}',map:void 0,media:void 0})}),{name:"DialogBindFiles",props:{value:{type:Boolean,default:!1},patientInfo:{type:Object,required:!0},crmAPI:{type:Object,default:null}},data:function(){return{healthFiles:[],loadingBtn:!1,disabledChildHealth:!1}},computed:{disabledBtn:function(){return t(this.healthFiles,this.getHealthFiles())||this.loadingBtn}},created:function(){this.healthFiles=this.getHealthFiles()},methods:{onClickConfirm:function(){var a=this;return e(i().mark((function e(){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!a.healthFiles.includes("child")||!1!==a.disabledChildHealth){e.next=13;break}return a.loadingBtn=!0,e.prev=2,e.next=5,a.crmAPI.updatePatientBindChildHealthFile(a.patientInfo.id);case 5:a.$Toast({type:"success",message:"添加成功"}),a.$emit("success"),e.next=12;break;case 9:e.prev=9,e.t0=e.catch(2),console.log("onClickConfirm error",e.t0);case 12:a.loadingBtn=!1;case 13:case"end":return e.stop()}}),e,null,[[2,9]])})))()},getHealthFiles:function(){var e=[];return 1===(this.patientInfo||{}).childCareArchives&&(e.push("child"),this.disabledChildHealth=!0),e}}},undefined,false,undefined,!1,r,void 0,void 0);export default o;

import{b as t,h as n,_ as i,a,n as o,c as e}from"./pc-components.index.chunk.js";import"vue";import"vue-i18n";import"jquery";import"vuedraggable";import"@abc/constants";import"@abc/ui-pc/src/mixins/src/form";import"@abc/ui-pc/src/mixins/src/input-size";import"@abc/ui-pc/src/mixins/src/cypress";import"@abc/utils-date";import"@abc/utils";var r=0,c=1,s=2,l=3,d=4,f=5,p={name:"DialogFamilyDoctor",props:{value:{type:Boolean,default:!1},patientInfo:{required:!0,validator:function(n){return"object"===t(n)||null===n}},crmAPI:{type:Object,default:null},familyDoctor:{type:Object,default:null}},data:function(){return{familyDoctorInfo:null,loadingSigning:!1,loadingCancel:!1,loadingEdit:!1,loadingUnbind:!1}},computed:{patientId:function(){return this.patientInfo&&this.patientInfo.id||null},familyDoctorInfoId:function(){return this.familyDoctorInfo&&this.familyDoctorInfo.id||null},familyDoctorConfig:function(){var t=(this.familyDoctorInfo||{}).familyDoctorConfig;return t},doctorTeamName:function(){if(!this.familyDoctorInfo)return"";var t=this.familyDoctorInfo.doctorTeam;return(t=t||[]).map((function(t){return t.doctorName})).join("、")},serviceUnit:function(){var t=(this.familyDoctorConfig||{}).serviceType;return 0===t?"年":1===t?"季度":2===t?"月":""},servicePackId:function(){var t=(this.familyDoctorInfo||{}).servicePackId;return t||""},isSigning:function(){return(this.familyDoctorInfo||{}).state===r},isCharging:function(){return(this.familyDoctorInfo||{}).state===c},isEffecting:function(){return(this.familyDoctorInfo||{}).state===s},isUnbind:function(){return(this.familyDoctorInfo||{}).state===l},isExpired:function(){var t=(this.familyDoctorInfo||{}).state;return t===d||t===f},awaitTipsWording:function(){if(this.isSigning)return"等待患者签约";if(this.isCharging)return"等待患者缴费";if(this.isEffecting){var t=(this.familyDoctorInfo||{}).overdueDays;if(t)return"还有".concat(t,"天到期")}return this.isExpired?"服务到期，请重新签约":this.isUnbind?"服务已解约，请重新签约":""},serviceDateStr:function(){var t=this.familyDoctorInfo||{},i=t.beginTime,a=t.endTime;return i&&a?"".concat(n(i,"y-m-d"),"~").concat(n(a,"y-m-d")):i?n(i,"y-m-d"):a?n(a,"y-m-d"):"-"}},watch:{patientId:{handler:function(){this.familyDoctorInfo=null,this.fetchFamilyDoctorInfoByPatientId()},immediate:!0}},methods:{fetchFamilyDoctorInfoByPatientId:function(){var t=this;return i(a().mark((function n(){var i,o;return a().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(t.patientId){n.next=2;break}return n.abrupt("return");case 2:return n.prev=2,t.loading=!0,n.next=6,t.crmAPI.fetchFamilyDoctorInfoByPatientId(t.patientId);case 6:i=n.sent,o=i.data,t.familyDoctorInfo=o,n.next=14;break;case 11:n.prev=11,n.t0=n.catch(2),console.error(n.t0);case 14:return n.prev=14,t.loading=!1,n.finish(14);case 17:case"end":return n.stop()}}),n,null,[[2,11,14,17]])})))()},handleClickSignIn:function(){var t=this,n={patientId:this.patientId},i={success:function(){t.fetchFamilyDoctorInfoByPatientId(),t.$emit("success")}};this.familyDoctor.handleSignIn(n,i)},handleTodoSign:function(){var t=this;return i(a().mark((function n(){var i,o;return a().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return t.loadingSigning=!0,i={familyDoctorId:t.familyDoctorInfoId},o={success:function(){t.fetchFamilyDoctorInfoByPatientId(),t.$emit("success")}},n.next=5,t.familyDoctor.handleSigningPanel(i,o);case 5:t.loadingSigning=!1;case 6:case"end":return n.stop()}}),n)})))()},handleCancelSign:function(){var t=this;return i(a().mark((function n(){return a().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(!t.loadingCancel&&t.familyDoctorInfoId){n.next=2;break}return n.abrupt("return");case 2:return n.prev=2,t.loadingCancel=!0,n.next=6,t.familyDoctor.handleCancel(t.familyDoctorInfoId);case 6:n.next=11;break;case 8:n.prev=8,n.t0=n.catch(2),console.error(n.t0);case 11:return n.prev=11,t.loadingCancel=!1,n.next=15,t.fetchFamilyDoctorInfoByPatientId();case 15:return n.finish(11);case 16:case"end":return n.stop()}}),n,null,[[2,8,11,16]])})))()},handleEdit:function(){var t=this;return i(a().mark((function n(){var i,o;return a().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return t.loadingEdit=!0,i={patientId:t.patientId,isEditSign:!0,familyDoctorInfo:t.familyDoctorInfo},o={success:function(){t.fetchFamilyDoctorInfoByPatientId()}},n.next=5,t.familyDoctor.handleSignIn(i,o);case 5:t.loadingEdit=!1;case 6:case"end":return n.stop()}}),n)})))()},handleUnbindSign:function(){var t=this;return i(a().mark((function n(){return a().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(!t.loadingUnbind&&t.familyDoctorInfoId){n.next=2;break}return n.abrupt("return");case 2:return n.prev=2,t.loadingUnbind=!0,n.next=6,t.familyDoctor.handleUnbind(t.familyDoctorInfoId);case 6:t.$emit("unbind-success"),n.next=12;break;case 9:n.prev=9,n.t0=n.catch(2),console.error(n.t0);case 12:return n.prev=12,t.loadingUnbind=!1,n.next=16,t.fetchFamilyDoctorInfoByPatientId();case 16:return n.finish(12);case 17:case"end":return n.stop()}}),n,null,[[2,9,12,17]])})))()},handleRenewalSign:function(){var t=this;return i(a().mark((function n(){var i,o;return a().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:i={patientId:t.patientId,isRenewalSign:!0,defaultServicePackId:t.servicePackId},o={success:function(){t.fetchFamilyDoctorInfoByPatientId(),t.$emit("success")}},t.familyDoctor.handleSignIn(i,o);case 3:case"end":return n.stop()}}),n)})))()},handleAgainSign:function(){var t=this;return i(a().mark((function n(){var i,o;return a().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:i={patientId:t.patientId,defaultServicePackId:t.servicePackId},o={success:function(){t.fetchFamilyDoctorInfoByPatientId(),t.$emit("success")}},t.familyDoctor.handleSignIn(i,o);case 3:case"end":return n.stop()}}),n)})))()},handlePreviewProtocol:function(){this.familyDoctorConfig&&this.familyDoctorInfo&&this.familyDoctor.handlePreviewProtocolByInfo(this.familyDoctorConfig,this.familyDoctorInfo)}}},u=o({render:function(){var t=this,n=t.$createElement,i=t._self._c||n;return t.value?i("div",{directives:[{name:"abc-loading",rawName:"v-abc-loading",value:t.loading,expression:"loading"}],staticClass:"crm-module__package-card__family__doctor__dialog"},[i("div",{staticClass:"family-doctor-wrapper"},[i("div",{staticClass:"header-line"},[i("div",{staticClass:"name"},[t._v("\n                家庭医生\n            ")]),t._v(" "),i("div",{staticClass:"del-btn"},[i("abc-delete-icon",{attrs:{size:"large"},on:{delete:function(n){return t.$emit("input",!1)}}})],1)]),t._v(" "),i("div",{staticClass:"doctor-info-wrapper"},[t.familyDoctorInfoId?i("div",{staticClass:"family-doctor-sign-content"},[t.awaitTipsWording?i("div",{staticClass:"row-line warn"},[i("abc-icon",{attrs:{icon:"Attention"}}),t._v("\n                    "+t._s(t.awaitTipsWording)+"\n                ")],1):t._e(),t._v(" "),t.familyDoctorConfig?i("div",{staticClass:"row-line"},[i("div",{staticClass:"describe"},[t._v("\n                        服务包\n                    ")]),t._v(" "),i("div",{staticClass:"value"},[t._v("\n                        "+t._s(t.familyDoctorConfig.servicePackName)+"\n                    ")])]):t._e(),t._v(" "),t.familyDoctorConfig?i("div",{staticClass:"row-line"},[i("div",{staticClass:"describe"},[t._v("\n                        服务费用\n                    ")]),t._v(" "),i("div",{staticClass:"value"},[t._v("\n                        "+t._s(t._f("formatMoney")(t.familyDoctorConfig.unitPrice))+"/"+t._s(t.serviceUnit)+"\n                    ")])]):t._e(),t._v(" "),i("div",{staticClass:"row-line"},[i("div",{staticClass:"describe"},[t._v("\n                        服务时间\n                    ")]),t._v(" "),i("div",{staticClass:"value"},[t._v("\n                        "+t._s(t.serviceDateStr)+"\n                    ")])]),t._v(" "),i("div",{staticClass:"row-line"},[i("div",{staticClass:"describe"},[t._v("\n                        签约门店\n                    ")]),t._v(" "),i("div",{staticClass:"value"},[t._v("\n                        "+t._s(t.familyDoctorInfo.clinicName)+"\n                    ")])]),t._v(" "),i("div",{staticClass:"row-line"},[i("div",{staticClass:"describe"},[t._v("\n                        家庭医生\n                    ")]),t._v(" "),i("div",{staticClass:"value"},[t._v("\n                        "+t._s(t.familyDoctorInfo.doctorName)+"\n                    ")])]),t._v(" "),t.doctorTeamName?i("div",{staticClass:"row-line"},[i("div",{staticClass:"describe"},[t._v("\n                        团队成员\n                    ")]),t._v(" "),i("div",{staticClass:"value"},[t._v("\n                        "+t._s(t.doctorTeamName)+"\n                    ")])]):t._e(),t._v(" "),t.familyDoctorConfig?i("div",{staticClass:"row-line"},[i("div",{staticClass:"describe"},[t._v("\n                        包含权益\n                    ")]),t._v(" "),t.familyDoctorConfig.servicePackDetails?i("div",{staticClass:"value"},t._l(t.familyDoctorConfig.servicePackDetails,(function(n,a){return i("p",{key:a},[t._v("\n                            "+t._s(a+1)+"."+t._s(n)+"\n                        ")])})),0):t._e()]):t._e(),t._v(" "),i("div",{staticClass:"btn-group"},[t.isSigning?[i("abc-button",{attrs:{type:"blank",loading:t.loadingSigning},on:{click:t.handleTodoSign}},[t._v("\n                            发起签约\n                        ")]),t._v(" "),i("abc-button",{attrs:{type:"blank",loading:t.loadingCancel},on:{click:t.handleCancelSign}},[t._v("\n                            取消\n                        ")])]:t._e(),t._v(" "),t.isEffecting?i("abc-button",{attrs:{type:"blank",loading:t.loadingEdit},on:{click:t.handleEdit}},[t._v("\n                        编辑\n                    ")]):t._e(),t._v(" "),t.isEffecting?i("abc-button",{attrs:{type:"blank"},on:{click:t.handleRenewalSign}},[t._v("\n                        续约\n                    ")]):t._e(),t._v(" "),t.isExpired||t.isUnbind?i("abc-button",{attrs:{type:"blank"},on:{click:t.handleAgainSign}},[t._v("\n                        重新签约\n                    ")]):t._e(),t._v(" "),i("abc-button",{attrs:{type:"blank"},on:{click:t.handlePreviewProtocol}},[t._v("\n                        协议\n                    ")]),t._v(" "),t.isCharging||t.isEffecting?i("abc-button",{staticClass:"unbind-sign",attrs:{type:"danger",loading:t.loadingUnbind},on:{click:t.handleUnbindSign}},[t._v("\n                        解约\n                    ")]):t._e()],2)]):i("div",{staticClass:"no-sign-wrapper"},[i("img",{attrs:{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHwAAACECAMAAABlAb/4AAABIFBMVEUAAAD////f4eHf4eHh4+P09PTf4eHf4eHf4eHg4uL5+fnq6urt7e3u7u7l5eXn6+v39/ff4eH4+Pjf4eHf4eHf4eHf4eH4+Pj4+Pj4+Pj4+Pjf4eHf4eHf4uLt7e3t7e3y8vLp6ens7Oz4+Pj39/f4+Pj5+fn5+fn4+Pjk5ube4eHf4eHe4ODe4ODf4eHf4eHf4ODt7e339/f4+Pj4+Pjf4ODg4eHf4ODf4eHf4eH4+Pjg4uL19fXg4uL4+Pje4eH09PTg4uLf4uLe4eHh4eH4+Pjt7e3u7u7t7e34+Pj4+Pj39/f5+fn4+Pj5+fne4OD39/ft7e3s7e3r6+vh4uLf4eHn5+fl5ubi4+Py8vLw8PDo6enj5eXq6ur09PTv7++hO6oKAAAAT3RSTlMABffUMhPZmYVIQiMOLCkY7N7dycO9t7CXkn9+bE8/NhsL+ffm2HhSSvrz7ero5eTPyca8taWinZGLioJ0c2pmZGJZVSL58tzZ0MzAno1acyS0PQAAA3lJREFUeNrt21lb2kAYhuGPAIKCa9XWatVqtfu+7/u+vCYQCCL2//+LotJrwiTCJG8wV2vuM44ekvlmhhPEQOHJHQuRWHeeFCQJxYeI5WFRaB/GENPYByFVg23zelU4JRBKQqmAUhHGMijLwpgHZV4Y10G5Lgxw3NHFnZ0uB4Nk8SyexYm4c6R1EG85R04svhMii//f8bCBOx1bLYtn8VMXP5LFs3gWz+L/aHxmh7IpjK9cfEsYZ7n4fWGc4+I/hLHNxc8L4xcXfyOMVS4+IYx1Ll4RyiYVzwlli2nPCecBt805S0z8nHBeMPFt4Vxh4peFs87tNNKl+O0ZIbD32gNhbTPXCmsifvyKsIrx40WhbRGHK+1Z3PiS8FbZyzyNn88zOUnAEnGr0CbjxSclEd/jtO9JMi4TNxrvXvT2N0nKRAr7jLjazkpyihFv9UvTkqAJ4j7jnScu8hO9X35K4paIc5X3atMk/fGVjMTa3PD23JqMyIvWsHbrjYzI5IZttwambfvTlIxE8bN9YHc/vLy/ax/4UpRRuGv/tdva18KtbrnnriRvanHD7vd799Bvu9/GYtJvfroEXLWNXAVKSR7tF5evocsxaddcANeWL0pC3t7CoYZJvIlDt95KEsbz6KmbxOvoyY/ze3sRStv/en1spQ1lcZKbs8fw21MRx0NXcDk8+D2ejj9nzy30q/nqdfQ0/e8D/aznMSfv9U34BDt1/XV07UF38zUzZ351O/CUnu3nQok9eYUF6IIjF/w+DYRaKIi59UcuwnnBkhPYZ0Huo3UxUy1bOFYtsL5tbZ+Fs8pVg3Ru5QYGaAYesxkct1A3VnIyxIUxDOSqJQ+shIPBxi4MnrN5DNMODJejjvVh5o+fvFwZw3mBd9xW+2y4ck5CFfIw0dEnu6FehYF8IXS1LRhp6gepp451E1bIyq/AhFrjtj6DHZhaEc1LGGvo09VRM2DmpfR578KYpx9mDbXPzLjvxWfNQgQdreWpcTNkral2dRZR7Oknqdpnpmarcf+y4eoHSk19GVMl6VmFYjpyde2zh4hWe/EziKhuO9o61BDVGTk0jsg6HW0dmohsXHtwY3tarIYAs0efQgx1/WMMU914GSkpd+OzSMmsSAWpqcg7pOadPEVqnsoCUrMgeaQmL7eRmttiITWWIEVZ3EQWz+JZPDbJpegPIxbW8AYB8V4AAAAASUVORK5CYII=",alt:""}}),t._v(" "),i("div",{staticClass:"tips"},[t._v("\n                    还未签约家庭医生\n                ")]),t._v(" "),i("abc-button",{staticClass:"sign-button",on:{click:t.handleClickSignIn}},[t._v("\n                    签约\n                ")])],1)])])]):t._e()},staticRenderFns:[]},(function(t){t&&t("data-v-6d143e8e_0",{source:'@charset "UTF-8";.base-font-style{font-style:normal;-webkit-font-smoothing:antialiased;-webkit-text-stroke-width:.2px;-moz-osx-font-smoothing:grayscale}.abc-font-color-primary{color:var(--abc-color-B1)}.abc-font-color-success{color:var(--abc-color-G1)}.abc-font-color-warn{color:var(--abc-color-Y2)}.abc-font-color-danger{color:var(--abc-color-R2)}.abc-font-color-tips{color:var(--abc-color-T2)}.crm-module__package-card__family__doctor__dialog .family-doctor-wrapper .header-line{display:flex;align-items:center;justify-content:space-between;height:40px;padding:0 8px 0 12px}.crm-module__package-card__family__doctor__dialog .family-doctor-wrapper .header-line .name{font-size:16px;font-weight:700;color:var(--abc-color-S1)}.crm-module__package-card__family__doctor__dialog .family-doctor-wrapper .doctor-info-wrapper{padding:12px}.crm-module__package-card__family__doctor__dialog .family-doctor-wrapper .doctor-info-wrapper .no-sign-wrapper{text-align:center}.crm-module__package-card__family__doctor__dialog .family-doctor-wrapper .doctor-info-wrapper .no-sign-wrapper img{height:66px}.crm-module__package-card__family__doctor__dialog .family-doctor-wrapper .doctor-info-wrapper .no-sign-wrapper .tips{margin-top:10px;font-size:14px;font-weight:400;line-height:20px;color:var(--abc-color-T3)}.crm-module__package-card__family__doctor__dialog .family-doctor-wrapper .doctor-info-wrapper .no-sign-wrapper .sign-button{width:180px;margin-top:24px}.crm-module__package-card__family__doctor__dialog .family-doctor-wrapper .doctor-info-wrapper .family-doctor-sign-content .row-line{display:flex;line-height:20px}.crm-module__package-card__family__doctor__dialog .family-doctor-wrapper .doctor-info-wrapper .family-doctor-sign-content .row-line:not(:first-child){margin-top:6px}.crm-module__package-card__family__doctor__dialog .family-doctor-wrapper .doctor-info-wrapper .family-doctor-sign-content .row-line .describe{width:68px;color:var(--abc-color-T2)}.crm-module__package-card__family__doctor__dialog .family-doctor-wrapper .doctor-info-wrapper .family-doctor-sign-content .row-line .value{flex:1;width:0}.crm-module__package-card__family__doctor__dialog .family-doctor-wrapper .doctor-info-wrapper .family-doctor-sign-content .row-line.warn{display:inline-flex;align-items:center;justify-content:center;width:100%;height:38px;color:var(--abc-color-Y2);background:var(--abc-color-Y4);border-radius:var(--abc-border-radius-small)}.crm-module__package-card__family__doctor__dialog .family-doctor-wrapper .doctor-info-wrapper .family-doctor-sign-content .row-line .iconfont{margin-right:4px;font-size:14px;color:var(--abc-color-Y2)}.crm-module__package-card__family__doctor__dialog .family-doctor-wrapper .doctor-info-wrapper .btn-group{display:flex;align-items:center;justify-content:flex-end;margin-top:12px;text-align:right}.crm-module__package-card__family__doctor__dialog .family-doctor-wrapper .doctor-info-wrapper .btn-group .abc-button{min-width:48px;height:26px;padding:0 9px}.crm-module__package-card__family__doctor__dialog .family-doctor-wrapper .doctor-info-wrapper .btn-group .abc-button+.abc-button{margin-left:4px}.crm-module__package-card__family__doctor__dialog .family-doctor-wrapper .doctor-info-wrapper .btn-group .unbind-sign{position:absolute;left:8px}',map:void 0,media:void 0})}),p,undefined,false,undefined,!1,e,void 0,void 0);export default u;

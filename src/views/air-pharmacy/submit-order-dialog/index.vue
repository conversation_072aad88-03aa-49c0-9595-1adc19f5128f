<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        content-styles="width: 390px;padding: 24px;"
        title="提交订单-空中药房"
        custom-class="air-pharmacy-submit-order-dialog"
    >
        <div class="dialog-content clearfix">
            <div class="total-price" style="margin-bottom: 14px;">
                <abc-money :value="totalFee" is-show-space value-tag="b"></abc-money>
                <abc-button
                    v-if="canViewDetail"
                    type="ghost"
                    style="color: #7a8794;"
                    @click="viewOrderDetailHandle"
                >
                    查看详情
                </abc-button>
            </div>
            <div class="tips">
                请及时付款，空中药房收到付款后进行发货配送
            </div>

            <div class="pay-mode">
                <div class="tabs">
                    <div
                        v-for="item in payModes"
                        :key="item.value"
                        :class="{
                            'is-selected': item.value === payMode,
                        }"
                        class="tab-item"
                        @click="changePayMode(item.value)"
                    >
                        <i :class="['iconfont', item.icon]"></i>
                        {{ item.label }}
                    </div>
                </div>

                <div class="tab-content">
                    <template v-if="payMode === 1">
                        <div v-abc-loading="loadingBalance">
                            <b class="balance"><span>预付款余额</span>  <abc-money :value="availableBalance" class="money-value" is-show-space></abc-money>
                                <abc-button type="text" style="margin-left: 8px;" @click="recharge">
                                    去充值
                                </abc-button>
                            </b>
                        </div>
                    </template>
                    <template v-else-if="payMode === 2 || payMode === 3">
                        <div v-abc-loading="loadingQrCode" class="qr-code">
                            <img v-if="qrCode" :src="qrCode" />

                            <template v-if="qrCode && needRefresh">
                                <div class="qr-code-cover"></div>
                                <div class="tips" @click="payOrder">
                                    <i class="icon iconfont cis-icon-shuaxin"></i>
                                    <h5>二维码失效</h5>
                                    <p>点击刷新</p>
                                </div>
                            </template>
                        </div>
                    </template>
                </div>
            </div>
        </div>

        <div
            v-if="showOrderDetail"
            slot="left-extend"
            v-abc-loading="orderDetailLoading"
            :class="{
                'charge-dialog-left-extend': true,
            }"
        >
            <div v-for="(order, index) in orders" :key="order.orderId" class="order-wrapper">
                <h5>订单{{ index + 1 }} {{ order.vendorName || '' }}</h5>
                <div class="price-info">
                    <div>
                        <label>药房零售价</label>
                        <p><abc-money :value="order.sourceGoodsTotalPrice"></abc-money></p>
                    </div>
                    <div>
                        <label>平台折扣金额</label>
                        <p class="total-price">
                            -<abc-money :value="order.profitsPrice"></abc-money>
                        </p>
                    </div>
                    <div v-if="order.isDelivery">
                        <label>配送费</label>
                        <p><abc-money :value="order.clinicDeliveryPrice"></abc-money></p>
                    </div>
                    <div v-if="order.isProcess">
                        <label>加工费</label>
                        <p><abc-money :value="order.clinicProcessPrice"></abc-money></p>
                    </div>
                    <div v-if="order.clinicIngredientPrice">
                        <label>辅料费</label>
                        <p><abc-money :value="order.clinicIngredientPrice"></abc-money></p>
                    </div>
                    <div>
                        <label>合计</label>
                        <p class="total-price">
                            <abc-money :value="order.clinicTotalPrice"></abc-money>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <div slot="footer" class="dialog-footer">
            <abc-button :loading="saveLoading" :disabled="disabledConfirm" @click="ok">
                付款
            </abc-button>
            <abc-button :disabled="saveLoading" type="blank" @click="no">
                关闭
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import ChargeAPI from 'src/api/charge';
    import WalletAPI from 'src/api/wallet';
    import rechargeAirPharmacy from 'src/views/wallet/recharge-dialog';
    import Store from 'utils/localStorage-handler';
    import QRCode from 'qrcode';

    export default {
        data() {
            return {
                visible: false,
                closed: false,
                saveLoading: false,
                showOrderDetail: false,
                orderDetailLoading: false,

                orderIds: [],
                isAllPaid: 0,
                systemConfig: {},

                clinicName: '',
                balanceId: null,
                totalFee: 0,
                availableBalance: 0,
                payMode: 1,
                wechatQrCode: '',
                needWechatRefresh: false,
                alipayQrCode: '',
                needAlipayRefresh: false,
                loadingQrCode: false,
                loadingBalance: false,

                canViewDetail: true,

                payModes: [
                    {
                        label: '余额',
                        value: 1,
                        icon: 'cis-icon-xianjin',
                    },
                    {
                        label: '微信',
                        value: 2,
                        icon: 'cis-icon-wechat1',
                    },
                    {
                        label: '支付宝',
                        value: 3,
                        icon: 'cis-icon-alipay',
                    },
                ],
                orders: [],
            };
        },

        computed: {
            qrCode() {
                if (this.payMode === 2) {
                    return this.wechatQrCode;
                } if (this.payMode === 3) {
                    return this.alipayQrCode;
                }
                return '';

            },
            needRefresh() {
                if (this.payMode === 2) {
                    return this.needWechatRefresh;
                } if (this.payMode === 3) {
                    return this.needAlipayRefresh;
                }
                return false;

            },
            disabledConfirm() {
                if (this.payMode === 1) {
                    return !this.availableBalance || +this.totalFee > +this.availableBalance;
                }
                return true;
            },
        },

        watch: {
            closed(newVal) {
                if (newVal) {
                    if (this._timer) {
                        clearInterval(this._timer);
                    }
                    this.visible = false;
                    this.destroyElement();
                }
            },
            visible() {
                if (this._timer) {
                    clearInterval(this._timer);
                }
            },
        },
        mounted() {
            this.payAirPharmacyOrder();
            this.fetchWalletBalance();
        },
        beforeDestroy() {
            if (this._timer) {
                clearInterval(this._timer);
            }
        },
        methods: {
            async fetchWalletBalance() {
                this.loadingBalance = true;
                const { data } = await WalletAPI.fetchWalletBalance();
                this.availableBalance = data.availableBalance;
                this.loadingBalance = false;
            },
            changePayMode(payMode) {
                clearInterval(this._timer);
                this.payMode = payMode;
                if (payMode === 2 || payMode === 3) {
                    if (this.qrCode) {
                        this.queryPayStatus();
                    } else {
                        this.payOrder();
                    }
                }
            },

            async payAirPharmacyOrder() {
                try {
                    const { data } = await ChargeAPI.payAirPharmacyOrder({
                        orderIds: this.orderIds,
                        isAllPaid: this.isAllPaid,
                        isAllMergeOrderPaid: 1,
                    });
                    this.balanceId = data.id;
                    this.totalFee = data.totalFee;
                } catch (e) {
                    this.saveLoading = false;
                    // 库存不足不会生成订单，此时查不了详情
                    this.canViewDetail = false;
                    console.error(e);
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '订单状态已发生变化，请刷新重试',
                        onClose: () => {
                            if (typeof this.payError === 'function') {
                                this.payError(e);
                            }
                            this.close();
                        },
                    });
                }
            },

            async payOrder() {
                if (!this.balanceId) return false;
                this.loadingQrCode = true;

                try {
                    const { data } = await WalletAPI.payOrder(this.balanceId, {
                        payMode: this.payMode,
                    });

                    if (this.payMode === 1) {
                        if (typeof this.paySuccess === 'function') {
                            this.paySuccess(data);
                        }
                        this.$Toast({
                            message: '支付成功',
                            type: 'success',
                        });
                        this.close();
                    } else {
                        QRCode.toDataURL(data.thirdPartyPayInfo.codeUrl)
                            .then((url) => {
                                if (this.payMode === 2) {
                                    this.wechatQrCode = url;
                                    this.needWechatRefresh = false;

                                    const timer = setTimeout(() => {
                                        this.needWechatRefresh = true;
                                        clearTimeout(timer);
                                    }, 3 * 60 * 1000);
                                } else {
                                    this.alipayQrCode = url;
                                    this.needAlipayRefresh = false;

                                    const timer = setTimeout(() => {
                                        this.needAlipayRefresh = true;
                                        clearTimeout(timer);
                                    }, 3 * 60 * 1000);
                                }
                            })
                            .catch((err) => {
                                console.error(err);
                            });
                        clearInterval(this._timer);
                        this.queryPayStatus();
                    }
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loadingQrCode = false;
                }
            },

            queryPayStatus() {
                this._timer = setInterval(async () => {
                    const { data } = await WalletAPI.queryPayStatus(this.balanceId);
                    if (data.status === 10) {
                        clearInterval(this._timer);
                        this.$Toast({
                            message: '支付成功',
                            type: 'success',
                        });
                        if (typeof this.paySuccess === 'function') {
                            this.paySuccess(data);
                        }
                        this.close();
                    }
                    if (this.payMode === 2 && this.needWechatRefresh === true) {
                        clearInterval(this._timer);
                    } else if (this.payMode === 3 && this.needAlipayRefresh === true) {
                        clearInterval(this._timer);
                    }
                }, 1000);
            },

            async ok() {
                try {
                    if (this.payMode === 1) {
                        this.saveLoading = true;
                        await this.payOrder();
                    }
                    this.close();
                } catch (e) {
                    this.saveLoading = false;
                    console.error(e);
                } finally {
                    this.saveLoading = false;
                }
            },

            async no() {
                if (typeof this.cancel === 'function') {
                    this.cancel();
                }
                this.close();
            },

            close() {
                this.closed = true;
                if (typeof this.onClose === 'function') {
                    this.onClose(this);
                }
                if (this._timer) {
                    clearInterval(this._timer);
                }
            },

            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode.removeChild(this.$el);
            },

            async viewOrderDetailHandle() {
                this.showOrderDetail = !this.showOrderDetail;
                if (this.showOrderDetail) {
                    this.orderDetailLoading = true;
                    const { data } = await ChargeAPI.fetchWaitPayOrders(this.balanceId);
                    this.orders = data.orders;
                    this.orderDetailLoading = false;
                }
            },

            recharge() {
                const currentClinic = Store.get('_current_clinic_', true) || null;
                rechargeAirPharmacy({
                    clinicName: currentClinic ? currentClinic.clinicName : '',
                    onClose: this.fetchWalletBalance,
                    allowSetPaymentByInput: true,
                    parent: this,
                });
            },
        },
    };
</script>

<style lang="scss">
    @import '../../../styles/theme';

    .air-pharmacy-submit-order-dialog {
        .total-price {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: $Y2;

            > span {
                margin-right: 4px;
            }

            b {
                font-size: 24px;
            }

            button {
                position: absolute;
                top: 0;
                right: 0;
                margin-left: auto;
            }
        }

        .tips {
            margin-top: 16px;
            color: $T2;
            text-align: center;
        }

        .balance-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 80px;
            margin-top: 24px;
            border-top: 1px dashed $P6;

            p {
                height: 20px;
                text-align: center;
            }

            .balance {
                display: flex;
                align-items: center;
                height: 20px;
                font-size: 16px;
                font-weight: normal;
            }

            .shortage {
                font-size: 12px;
                color: $Y2;
            }

            span {
                margin-right: 12px;
                font-size: 14px;
                font-weight: normal;
                color: $T2;
            }
        }

        .pay-mode {
            width: 342px;
            height: 260px;
            margin-top: 16px;
            border: 1px solid $P6;
            border-radius: var(--abc-border-radius-small);

            .tabs {
                display: flex;
                align-items: center;
                height: 48px;
            }

            .tab-item {
                display: flex;
                flex: 1;
                align-items: center;
                justify-content: center;
                height: 48px;
                cursor: pointer;
                background-color: $P5;
                border-bottom: 1px solid $P6;

                &.is-selected {
                    background-color: #ffffff;
                    border-bottom-color: #ffffff;
                }

                &:first-child {
                    border-top-left-radius: var(--abc-border-radius-small);
                }

                &:last-child {
                    border-top-right-radius: var(--abc-border-radius-small);
                }
            }

            .iconfont {
                margin-right: 8px;
            }

            .cis-icon-xianjin {
                color: #f69534;
            }

            .cis-icon-wechat1 {
                color: #17ae3c;
            }

            .cis-icon-alipay {
                color: #0d92f6;
            }

            .tab-content {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;

                > div {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    width: 100%;
                    height: 212px;

                    img {
                        width: 160px;
                        height: 160px;
                    }

                    p {
                        text-align: center;
                    }

                    .balance {
                        display: flex;
                        align-items: center;
                        height: 20px;
                        font-size: 16px;
                        font-weight: normal;
                    }

                    .shortage {
                        font-size: 12px;
                        color: $Y2;
                    }

                    span {
                        margin-right: 12px;
                        font-size: 14px;
                        font-weight: normal;
                        color: $T2;

                        &.money-value {
                            display: flex;
                            align-items: center;
                            height: 20px;
                            margin-right: 0;

                            > span {
                                margin-right: 0;
                                font-size: 16px;
                                color: $S1;
                            }
                        }
                    }
                }

                .qr-code {
                    position: relative;

                    .qr-code-cover {
                        position: absolute;
                        top: 0;
                        left: 0;
                        z-index: 1;
                        width: 100%;
                        height: 100%;
                        background-color: rgba(0, 0, 0, 0.7);
                    }

                    .tips {
                        position: absolute;
                        top: 45px;
                        left: 0;
                        z-index: 2;
                        width: 100%;
                        color: #ffffff;
                        text-align: center;
                        cursor: pointer;

                        i {
                            font-size: 24px;
                        }

                        h5 {
                            margin-top: 5px;
                            font-size: 16px;
                            font-weight: normal;
                            color: #ffffff;
                        }

                        p {
                            font-size: 14px;
                        }
                    }
                }
            }
        }

        .charge-dialog-left-extend {
            position: absolute;
            top: 0;
            right: -364px;
            width: 360px;
            min-height: 160px;
            max-height: 590px;
            padding: 12px 16px;
            overflow-y: auto;
            overflow-y: overlay;
            background-color: #ffffff;
            border-radius: var(--abc-dialog-border-radius);

            .order-wrapper {
                &:not(&:last-child) {
                    margin-bottom: 12px;
                }

                h5 {
                    margin-bottom: 8px;
                    font-weight: 500;
                    line-height: 20px;
                    color: $T1;
                }
            }

            .price-info {
                > div {
                    display: flex;
                    align-items: center;

                    &:not(:last-child) {
                        margin-bottom: 4px;
                    }
                }

                label {
                    color: $T2;
                }

                p {
                    margin-left: auto;
                    text-align: right;
                }

                .total-price {
                    color: $Y2;
                }

                & + .price-info {
                    margin-top: 12px;
                }
            }
        }

        .abc-dialog-footer {
            padding-left: 16px;

            .abc-checkbox-wrapper {
                flex: 1;
                color: $T2;
            }
        }
    }
</style>

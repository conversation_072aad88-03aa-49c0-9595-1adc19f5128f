<template>
    <div v-abc-loading:page="loading">
        <abc-container-center-top-head>
            <h2>体检收费</h2>
            <div class="buttons-wrapper">
                <template v-if="isCharged">
                    <div class="amount">
                        <abc-money :symbol-icon-size="16" :value="receivedFee"></abc-money>
                    </div>
                    <abc-button
                        v-if="needPayFee < 0"
                        type="blank"
                        @click="confirmRefund"
                    >
                        退费
                    </abc-button>

                    <abc-button type="blank" @click="openInvoiceDialog">
                        开票
                    </abc-button>
                </template>
                <template v-else>
                    <div class="amount">
                        <abc-money :symbol-icon-size="16" :value="needPayFee"></abc-money>
                    </div>
                    <abc-button v-if="needPayFee >= 0" @click="handleCharge">
                        {{ isPartCharged ? '继续收费' : '收费' }}
                    </abc-button>
                    <abc-button
                        v-if="needPayFee < 0"
                        type="blank"
                        @click="confirmRefund"
                    >
                        退费
                    </abc-button>
                </template>

                <print-popper
                    size="small"
                    :width="64"
                    :style="{
                        'margin-left': '4px'
                    }"
                    :box-style="{
                        width: '104px'
                    }"
                    placement="bottom"
                    :options="printOptions"
                    @print="printHandler"
                    @select-print-setting="openPrintConfigSettingDialog"
                >
                </print-popper>
            </div>
        </abc-container-center-top-head>

        <abc-container-center-main-content>
            <abc-layout>
                <abc-section>
                    <div class="organ-name-section" :title="organName">
                        <abc-icon
                            icon="institution_fill"
                            size="12"
                            :color="themeStyle.theme2"
                            style="margin-right: 8px;"
                        ></abc-icon>
                        {{ organName }}
                    </div>
                </abc-section>
                <abc-section>
                    <abc-descriptions
                        :column="3"
                        :label-width="108"
                        grid
                    >
                        <abc-descriptions-item
                            v-for="item in peGroupOrderInfoList"
                            :key="item.label"
                            :label="item.label"
                            label-class-name="order-info-label"
                            content-class-name="ellipsis"
                        >
                            <span :title="item.value">{{ item.value }}</span>
                        </abc-descriptions-item>
                    </abc-descriptions>
                </abc-section>

                <abc-section>
                    <p-e-table-team :settle-items="settleItems" :refund-receivable-fee="chargeSheetSummary.refundReceivableFee"></p-e-table-team>
                </abc-section>
            </abc-layout>
        </abc-container-center-main-content>

        <sidebar
            :order-type="PEOrderTypeEnum.GROUP"
            :charge-forms="settleItems"
            :invoice-list="invoiceList"
            :pe-order-info="groupOrderInfo"
            :charge-sheet-summary="chargeSheetSummary"
            :transactions="chargeSheetSummary.transactions"
        ></sidebar>
    </div>
</template>

<script type="text/ecmascript-6">
    import PETableTeam from '../components/pe-table-team.vue';
    import {
        ReportGetWayLabel,
        OrderStatusLabelEnum,
        ChargeFormTypeEnum,
        PEOrderTypeEnum,
        PEPayStatusEnum,
    } from 'views/physical-examination/constants.js';
    import { address2Str } from 'src/filters/index';
    import { formatDate } from '@abc/utils-date';
    import AbcCommonChargeDialog from 'components/common-charge-dialog/index.js';
    import AbcCommonRefundDialog from '@/components/common-refund-dialog/index.js';
    import {
        navigateToAggregatePaymentContentSetting, navigateToInvoiceConfig,
    } from '@/core/navigate-helper.js';
    import { mapGetters } from 'vuex';
    import PEChargeAPI from 'api/physical-examination/pe-charge.js';
    import { PayModeEnum } from '@/service/charge/constants.js';
    import themeStyle from '@/styles/theme.module.scss';
    import InvoiceDialog from 'views/cashier/invoice';
    import {
        InvoiceBusinessScene, InvoiceCategory,
    } from 'views/cashier/invoice/constants';
    import InvoiceService from 'views/cashier/invoice/write-invoice-core-v2/invoice-service';

    import PrintPopper from 'views/print/popper';
    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');
    import { PhysicalExaminationApi } from '@/printer/print-api/pe';

    export default {
        name: 'PhysicalExaminationCashierMain',
        components: {
            Sidebar: () => import('views/physical-examination/cashier/components/sidebar.vue'),
            PETableTeam,
            PrintPopper,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        data() {
            return {
                themeStyle,
                loading: false,
                ChargeFormTypeEnum,
                PEOrderTypeEnum,
                groupOrderInfo: {},
                patientInfo: {},
                chargeSheetSummary: {},
                settleItems: [],
                invoiceList: [],
            };
        },
        computed: {
            ...mapGetters('viewDistribute',[
                'viewDistributeConfig',
            ]),
            ...mapGetters([
                'currentClinic',
                'printBillConfig',
                'printMedicalListConfig',
                'userInfo',
            ]),
            ...mapGetters('invoice', ['isOpenInvoice', 'isOpenMedicalInvoice', 'medicalElectronicAPIConfig', 'writeInvoiceConfig', 'invoiceConfigList']),
            isCharged() {
                const {
                    chargeStatus,
                } = this.chargeSheetSummary;
                return PEPayStatusEnum.CHARGED === chargeStatus;
            },
            printOptions() {
                return [
                    {
                        value: this.viewDistributeConfig.Print.printOptions.PE_CHARGE_FEE_LIST.label,
                        disabled: this.chargeSheetSummary.chargeStatus !== PEPayStatusEnum.CHARGED,
                    },
                ];
            },
            isPartCharged() {
                const {
                    chargeStatus,
                } = this.chargeSheetSummary;
                return PEPayStatusEnum.PART_CHARGED === chargeStatus;
            },
            canRefund() {
                return this.needPayFee < 0;
            },

            receivedFee() {
                const {
                    receivedFee,
                } = this.chargeSheetSummary;
                return receivedFee || 0;
            },

            /**
             * @desc 小于0代表有退项，需要展示退费按钮
             * <AUTHOR>
             * @date 2023-11-27 14:32:27
             */
            needPayFee() {
                const {
                    needPayFee,
                } = this.chargeSheetSummary;
                return needPayFee || 0;
            },

            currentQuickItem() {
                return this.$abcPage.$store.selectedQuickItem;
            },

            currentPatient() {
                return this.currentQuickItem.patientInfo || {};
            },
            organName() {
                const {
                    peOrganInfoVO,
                } = this.groupOrderInfo;
                const {
                    name,
                } = peOrganInfoVO || {};
                return name || '';
            },
            peGroupOrderInfoList() {
                const {
                    orderNo,
                    beginDate,
                    endDate,
                    orderName,
                    peOrganInfoVO,
                    peOrganContactName,
                    peOrganContactMobile,
                    reportGetWay,
                    salesEmployeeName,
                    refusedSettlementRule,
                    status,
                } = this.groupOrderInfo;
                return [
                    {
                        label: '订单编号',
                        value: orderNo || '',
                    },
                    {
                        label: '企业单位',
                        value: peOrganInfoVO?.name || '',
                    },
                    {
                        label: '体检周期',
                        value: `${formatDate(beginDate, 'YYYY-MM-DD')} ~ ${formatDate(endDate, 'YYYY-MM-DD')}`,
                    },
                    {
                        label: '订单名称',
                        value: orderName || '',
                    },
                    {
                        label: '订单联系人',
                        value: peOrganContactName || '',
                    },
                    {
                        label: '联系电话',
                        value: peOrganContactMobile || '',
                    },
                    {
                        label: '订单状态',
                        value: OrderStatusLabelEnum[status],
                    },
                    {
                        label: '弃检计费方式',
                        value: refusedSettlementRule === 1 ? '不从结算中扣除' : '从结算中扣除',
                    },
                    {
                        label: '销售人',
                        value: salesEmployeeName || '',
                    },
                    {
                        label: '取报告方式',
                        value: ReportGetWayLabel[reportGetWay],
                    },
                    {
                        label: '客户地址',
                        value: address2Str(peOrganInfoVO) || '',
                    },
                ];
            },
        },
        watch: {
            '$route.params.id': {
                handler(val) {
                    this.chargeSettleId = val;
                    this.fetchDetail(true);
                },
                immediate: true,
            },
        },
        created() {
            this.$store.dispatch('invoice/initInvoiceConfig');

            // 监听收费后自动开票的消息,如果开票成功,则重新拉取发票列表
            this.$abcEventBus.$on('refresh-pe-charge-invoice-list', () => {
                this.fetchInvoiceList();
            }, this);
        },
        methods: {
            async openPrintConfigSettingDialog() {
                const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                new PrintConfigDialog({ scene: 'pe-charge-fee-list' }).generateDialogAsync({ parent: this });
            },

            async fetchDetail(refreshInvoice) {
                this.loading = true;
                const { data } = await PEChargeAPI.fetchGroupSettleDetail(this.chargeSettleId);
                this.chargeSettleId = data.id;
                this.groupOrderInfo = data.groupOrderInfo || {};
                this.settleItems = data.settleItems || [];
                this.chargeSheetSummary = {
                    chargeStatus: data.status,
                    actualReceivableFee: data.actualReceivableFee, // 排除套餐中退项金额的应收
                    receivableFee: data.receivableFee, // 应收
                    needPayFee: data.needPayFee,
                    payTransactions: data.payTransactions,
                    receivedFee: data.receivedFee,
                    refundableFee: data.refundableFee,
                    refundedFee: data.refundedFee,
                    totalFee: data.totalFee, // 订单原价
                    transactions: data.transactions,
                    firstChargedTime: data.firstChargedTime,
                    lastChargedTime: data.lastChargedTime,
                    salesEmployeeId: data.salesEmployeeId,
                    salesEmployeeName: data.salesEmployeeName,
                    refundReceivableFee: data.refundReceivableFee,
                    lastChargedEmployeeName: data.lastChargedEmployeeName,
                };
                this.loading = false;

                if (refreshInvoice) {
                    this.$nextTick(() => {
                        if (this.isCharged) {
                            this.fetchInvoiceList();
                        }
                    });
                }
            },

            handleCharge() {
                this.payHandler();
            },
            payHandler() {
                this._chargeDialog = new AbcCommonChargeDialog({
                    dialogTitle: '收费',
                    hiddenPayModeList: [
                        PayModeEnum.SOCIAL_CARD,
                        PayModeEnum.ARREARS,
                        PayModeEnum.MEMBER_CARD,
                    ],
                    receivableFee: this.chargeSheetSummary.needPayFee,
                    onAbcPayOpenCallback: () => {
                        navigateToAggregatePaymentContentSetting(this.currentClinic);
                    },
                    submit: this.submitHandler,
                    onPartChargeSuccess: this.fetchDetail,
                    onChargeSuccess: this.fetchDetail,
                    onChargeError: this.fetchDetail,
                    onClose: this.onClose,
                });
                this._chargeDialog.generateDialog({ parent: this });
            },
            printHandler() {
                PhysicalExaminationApi.printPEGroupFeeList({
                    id: this.chargeSettleId,
                    clinicName: this.currentClinic?.name || '',
                });
            },
            async submitHandler(chargeData) {
                return PEChargeAPI.settlePayOrder(this.chargeSettleId, {
                    ...chargeData,
                });
            },
            onClose() {
                if (this._chargeDialog) {
                    this._chargeDialog.destroyDialog();
                    this._chargeDialog = null;
                }
            },

            confirmRefund() {
                this._refundDialog = new AbcCommonRefundDialog({
                    hiddenPayModeList: [
                        PayModeEnum.SOCIAL_CARD,
                        PayModeEnum.ARREARS,
                        PayModeEnum.MEMBER_CARD,
                        PayModeEnum.PATIENT_CARD,
                    ],
                    refundFee: Math.abs(this.chargeSheetSummary.needPayFee),
                    payTransactions: this.chargeSheetSummary.payTransactions,
                    updatePayTransactions: async () => {
                        const { data } = await PEChargeAPI.fetchGroupSettleDetail(this.chargeSettleId);
                        return data?.payTransactions || [];
                    },
                    submit: (chargeData) => {
                        return PEChargeAPI.settleRefundOrder(this.chargeSettleId, chargeData);
                    },
                    onPartSuccess: this.fetchDetail,
                    onSuccess: this.onRefundSuccess,
                });
                this._refundDialog.generateDialog({ parent: this });
            },

            onRefundSuccess() {
                this.fetchDetail();
                if (this._refundDialog) {
                    this._refundDialog.destroyDialog();
                    this._refundDialog = null;
                }
            },
            async openInvoiceDialog() {
                await new InvoiceDialog({
                    chargeSheetId: this.chargeSettleId,
                    chargeStatus: this.chargeSheetSummary.chargeStatus,
                    patientInfo: {
                        buyerPhone: this.groupOrderInfo.peOrganContactMobile,
                        buyerName: this.groupOrderInfo.peOrganInfoVO?.name,
                    },
                    printMedicalListConfig: this.printMedicalListConfig,
                    medicalElectronicAPIConfig: this.medicalElectronicAPIConfig,
                    writeInvoiceConfig: this.writeInvoiceConfig,
                    isOpenMedicalInvoice: this.isOpenMedicalInvoice,
                    printBillConfig: this.printBillConfig,
                    invoiceConfigList: this.invoiceConfigList,
                    isOpenInvoice: this.isOpenInvoice,
                    userInfo: this.userInfo,
                    businessType: InvoiceBusinessScene.PE_CHARGE_GROUP,
                    toBillPrintSetting: () => {
                        navigateToInvoiceConfig(this.currentClinic);
                    },
                    updateInvoice: () => {
                        // 发票更新,刷新QL列表
                        this.$abcEventBus.$emit('open-invoice-pe-charge-quick-list');
                        // 发票更新,重新拉取发票列表
                        this.fetchInvoiceList();
                    },
                }).generateDialog({ parent: this });
            },
            /**
             * 拉取发票列表
             */
            async fetchInvoiceList() {
                if (this.isCharged) {
                    try {
                        const invoiceService = new InvoiceService();
                        const invoiceListResp = await invoiceService.fetchCashierSideBarInvoiceList(this.chargeSettleId,
                                                                                                    InvoiceBusinessScene.PE_CHARGE_GROUP,
                                                                                                    InvoiceCategory.ELECTRONIC,
                                                                                                    0);
                        this.invoiceList = invoiceListResp.rows || [];
                    } catch (e) {
                        this.invoiceList = [];
                        console.warn('获取发票列表失败\n', e);
                    }
                } else {
                    this.invoiceList = [];
                }
            },
        },
    };
</script>



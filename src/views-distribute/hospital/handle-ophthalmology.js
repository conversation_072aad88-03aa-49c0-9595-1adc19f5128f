import { getBusiness } from '@/views-distribute/utils.js';
import { EXAMPLE_DATA_EYE } from 'views/settings/print-config/medical-documents/medical/constants-eye';

export function handleOphthalmology(options) {
    const {
        busSupportFlag,
    } = options || {};
    const { hasOphthalmology } = getBusiness(busSupportFlag);
    if (!hasOphthalmology) return;
    console.log('%c view分发：处理医院眼科业务配置', 'background: #5199f8; padding: 4px; font-weight: bold; color: white'); // eslint-disable-next-line no-console

    this.viewFeature.supportGlassesPrescription = true; // 配镜处方
    this.viewFeature.supportFilterEyeGlasses = true; // 搜索-过滤眼镜
    this.viewDistributeConfig.Inventory.hasEyeglasses = true; // 库存-眼镜

    // 打印
    this.viewDistributeConfig.PrintConfig.medicalRecord.style.isSupportShowThemeColor = true;
    this.viewDistributeConfig.PrintConfig.medicalRecord.content.isSupportShowInspectResult = true;
    this.viewDistributeConfig.Print.exampleData = EXAMPLE_DATA_EYE;
}

<template>
    <abc-modal
        v-if="visible"
        v-model="visible"
        size="huge"
        preset="confirm"
        type="warn"
        disabled-keyboard
        :dialog-content-styles="computedDialogContentStyles"
        :show-icon="false"
        :data-cy="dataCy"
        :need-high-level="false"
        class="goods-stock-in-dialog"
        :close-after-confirm="false"
        :on-confirm="handleConfirm"
    >
        <template v-if="sourceType === 1" #top-extend>
            <abc-tips-card-v2 theme="primary">
                <div style="width: 100%;">
                    <abc-flex justify="space-between" align="center">
                        <p>用于历史已购进库存补录，如：「系统迁移」或「库存盘点时批次不存在」</p>
                    </abc-flex>
                </div>
            </abc-tips-card-v2>
        </template>
        <abc-form
            v-if="goodsInfo"
            ref="goodsStockInTableRef"
            item-no-margin
            class="goods-stock-in-content"
        >
            <!-- 商品标题信息 -->
            <abc-flex
                vertical
                justify="flex-start"
                align="flex-start"
                gap="small"
                style="width: 100%;"
            >
                <abc-flex
                    justify="flex-start"
                    align="center"
                    gap="large"
                    style="width: 100%;"
                >
                    <abc-text
                        bold
                        size="large"
                        theme="black"
                    >
                        {{ goodsInfo.displayName || '' }}
                    </abc-text>
                    <abc-text
                        bold
                        size="large"
                        theme="black"
                    >
                        {{ $t('currencySymbol') }} {{ formatMoney(goodsInfo.packagePrice) || 0 }}/{{ goodsInfo.packageUnit || goodsInfo.pieceUnit }}
                    </abc-text>
                </abc-flex>
                <abc-space
                    direction="horizontal"
                    size="small"
                >
                    <abc-text
                        size="normal"
                        theme="gray"
                    >
                        {{ goodsInfo.displaySpec || '' }}
                    </abc-text>
                    <abc-text
                        size="normal"
                        theme="gray"
                    >
                        {{ goodsInfo.manufacturer || '' }}
                    </abc-text>
                    <abc-text
                        size="normal"
                        theme="gray"
                    >
                        {{ goodsInfo.cMSpec || '' }}
                    </abc-text>
                </abc-space>
            </abc-flex>

            <template v-if="sourceType === 1">
                <!-- 分隔线 -->
                <abc-divider
                    size="normal"
                    theme="light"
                    margin="large"
                    variant="solid"
                    layout="horizontal"
                ></abc-divider>

                <!-- 新增批次按钮 -->
                <abc-button
                    variant="fill"
                    theme="success"
                    shape="square"
                    icon="s-b-add-line-medium"
                    icon-position="left"
                    @click="addNewBatch"
                >
                    新增批次
                </abc-button>
            </template>


            <abc-table
                type="excel"
                :render-config="renderConfig"
                :data-list="stockInList"
                empty-size="small"
                :support-delete-tr="sourceType === 1"
                :style="tableStyles"
                :show-content-empty="sourceType === 1"
                @delete-tr="handleDeleteTr"
            >
                <!-- 生产批号 -->
                <template
                    #batchNo="{
                        trData: row, index
                    }"
                >
                    <abc-form-item required>
                        <abc-input
                            v-model="row.batchNo"
                            :max-length="20"
                            :input-custom-style="{
                                borderBottomLeftRadius: index === stockInList.length - 1 ? 'var(--abc-border-radius-small)' : '0',
                                paddingLeft: '12px'
                            }"
                            @enter="enterEvent"
                        ></abc-input>
                    </abc-form-item>
                </template>

                <!-- 入库数量 -->
                <template
                    #useCount="{
                        trData: row
                    }"
                >
                    <abc-form-item required>
                        <abc-input
                            v-model.number="row.useCount"
                            type="number"
                            :config="{
                                max: 9999999,
                                supportZero: false,
                                formatLength: getFormatLength(row.goods, 'receiveUnit')
                            }"
                            class="focus-input"
                            @enter="enterEvent"
                            @change="calTotalPrice(row)"
                        ></abc-input>
                    </abc-form-item>
                </template>

                <!-- 入库单位 -->
                <template #useUnit="{ trData: row }">
                    <abc-form-item required>
                        <abc-select
                            :key="row.keyId"
                            v-model="row.useUnit"
                            :inner-width="56"
                            adaptive-width
                            :input-style="{
                                'text-align': 'center'
                            }"
                            @enter="enterEvent"
                        >
                            <abc-option
                                v-for="unit in getUnits(row.goods)"
                                :key="unit"
                                :label="unit"
                                :value="unit"
                                center
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>
                </template>

                <!-- 进价 -->
                <template #useUnitCostPrice="{ trData: row }">
                    <abc-form-item required>
                        <price-fluctuation-popover
                            :show-history-cost-price="false"
                            :use-goods-config="true"
                            :use-cost-price="row.useUnitCostPrice"
                            :last-price="row.goods.lastPackageCostPrice"
                            :goods="row.goods"
                            :use-unit="row.useUnit"
                            :clinic-id="clinicId"
                            :update-price-source-type="PriceModifySourceType.QUICK_MODIFY_PURCHASE"
                            @changePrice="changePrice($event, row)"
                        >
                            <template
                                #default="{
                                    isRise, showIcon
                                }"
                            >
                                <abc-input
                                    v-model="row.useUnitCostPrice"
                                    type="money"
                                    :config="getCostConfig(row.goods)"
                                    :input-custom-style="{ textAlign: 'right' }"
                                    @enter="enterEvent"
                                    @change="calTotalPrice(row)"
                                >
                                    <span v-if="showIcon" slot="prepend">
                                        <abc-icon v-if="isRise" icon="arrow_up" color="var(--abc-color-R2)"></abc-icon>
                                        <abc-icon v-else icon="arrow_down" color="var(--abc-color-G2)"></abc-icon>
                                    </span>
                                </abc-input>
                            </template>
                        </price-fluctuation-popover>
                    </abc-form-item>
                </template>

                <!-- 金额 -->
                <template #useTotalCostPrice="{ trData: row }">
                    <abc-form-item required>
                        <abc-input
                            v-model="row.useTotalCostPrice"
                            type="money"
                            :config="{
                                formatLength: 2, max: 10000000, supportZero: true
                            }"
                            :input-custom-style="{ textAlign: 'right' }"
                            @enter="enterEvent"
                            @change="calCostPrice(row)"
                        ></abc-input>
                    </abc-form-item>
                </template>

                <template #productionDate="{ trData: row }">
                    <abc-form-item
                        :validate-event="validateProductionDate"
                    >
                        <abc-tooltip style="height: 100%;" :disabled="!getProductDateTooltip(row)" content="生产日期不可选择未来时间">
                            <abc-date-picker
                                v-model="row.productionDate"
                                :width="100"
                                type="datequick"
                                :show-icon="false"
                                :class="{ 'tool-tip-price': getProductDateTooltip(row) }"
                                :prevent-direction-navigation="false"
                                editable
                                placeholder=""
                                @enter="enterEvent"
                            >
                            </abc-date-picker>
                        </abc-tooltip>
                    </abc-form-item>
                </template>

                <template #expiryDate="{ trData: row }">
                    <abc-form-item
                        :validate-event="validateExpiryDate"
                        :required="row.goods && !isChineseMedicine(row.goods)"
                    >
                        <abc-tooltip style="height: 100%;" :disabled="!getExpiryDateTooltip(row)" :content="getExpiryDateTooltip(row)">
                            <abc-date-picker
                                v-model="row.expiryDate"
                                :width="100"
                                type="datequick"
                                :show-icon="false"
                                :class="{ 'tool-tip-price': getExpiryDateTooltip(row) }"
                                :prevent-direction-navigation="false"
                                editable
                                placeholder=""
                                @enter="enterEvent"
                            >
                            </abc-date-picker>
                        </abc-tooltip>
                    </abc-form-item>
                </template>

                <!-- 供应商 -->
                <template
                    #supplier="{
                        trData: row, index 
                    }"
                >
                    <abc-form-item>
                        <abc-select
                            :ref="`supplierSelect_${row.keyId}`"
                            v-model="row.supplierId"
                            custom-class="supplierWrapper"
                            with-search
                            :width="210"
                            clearable
                            placement="bottom-end"
                            size="large"
                            :input-style="supplierExpired(row.supplierId) ? {
                                color: '#D72E22 !important',
                            } : {}"
                            :fetch-suggestions="fetchSuggestions"
                            @open="openSupplierSelectPanel"
                            @change="(value) => handleSupplierSelect(index, value)"
                        >
                            <abc-option
                                v-for="it in supplierOptionsNormal"
                                :key="`${it.id }`"
                                :value="it.id"
                                :label="it.name"
                            >
                                <abc-text tag="div" :title="it.name" style="overflow: hidden; white-space: nowrap;">
                                    {{ it.name }}
                                </abc-text>
                            </abc-option>
                            <abc-option
                                v-if="supplierOptionsExpired.length"
                                style="align-items: flex-end; height: 24px; min-height: 24px;"
                                value="-2"
                                disabled
                            >
                                资质过期
                            </abc-option>
                            <abc-option
                                v-for="it in supplierOptionsExpired"
                                :key="`${it.id }`"
                                :value="it.id"
                                :label="it.name"
                            >
                                <abc-text
                                    tag="div"
                                    theme="danger"
                                    :title="it.name"
                                    style="overflow: hidden; white-space: nowrap;"
                                >
                                    {{ it.name }}
                                </abc-text>
                            </abc-option>
                            <template v-if="canCreateSupplier" #bottom-fixed>
                                <abc-flex class="supplier-select-bottom-fixed">
                                    <abc-button
                                        icon="n-add-line-medium"
                                        variant="text"
                                        size="small"
                                        @click="openSupplierDialog(row.keyId)"
                                    >
                                        新增供应商
                                    </abc-button>
                                </abc-flex>
                            </template>
                        </abc-select>
                    </abc-form-item>
                </template>
            </abc-table>
        </abc-form>

        <pharmacy-supplier-dialog
            v-if="showSupplierDialog"
            v-model="showSupplierDialog"
        ></pharmacy-supplier-dialog>
    </abc-modal>
</template>

<script>
    import {
        checkExpiryDate, showExpiryDateTip, showOverdueTip, showProductionDateTip, validateExpirationTime,
    } from 'views/inventory/goods-in/common';
    import {
        parseTime, createGUID, isNull, isNotNull,
    } from '@/utils';
    import {
        formatMoney, isChineseMedicine,
    } from '@/filters';
    import {
        unitEqual,
    } from 'views/inventory/goods-utils';
    import {
        calCostPriceSingle,
        calCostPriceTotal,
        getCostConfig,
        getFormatLength,
    } from '@/views-pharmacy/inventory/utils';
    import { GoodsTypeEnum } from '@abc/constants';
    import { PriceModifySourceType } from 'views/common/inventory/constants';
    import { EntrustDeliveryType } from '@/views-pharmacy/inventory/constant';
    import useSearchSupplier from 'views/inventory/hooks/useSearchSupplier';
    const PriceFluctuationPopover = () => import('@/views-pharmacy/inventory/frames/components/price-fluctuation-popover.vue');
    const PharmacySupplierDialog = () => import('@/views-pharmacy/inventory/frames/supplier/dialog.vue');
    import EnterEvent from 'views/common/enter-event';
    import GoodsAPI from 'api/goods';
    import BusinessGoods from '@/views-pharmacy/inventory/core/goods';
    import clone from 'utils/clone';
    import { mapGetters } from 'vuex';

    export default {
        name: 'GoodsStockInDialog',
        components: {
            PharmacySupplierDialog,
            PriceFluctuationPopover,
        },
        mixins: [EnterEvent],
        props: {
            goodsId: {
                type: String,
                default: '',
            },
            sourceType: {
                type: Number,
                default: 0, // 0-零售 1-档案
            },
            successCallback: {
                type: Function,
                default: () => {},
            },
            onCancel: {
                type: Function,
                default: () => {},
            },
            dataCy: {
                type: String,
                default: 'goods-stock-in-dialog',
            },
        },
        setup() {
            const {
                currentSupplierList,
                fetchSuggestions,
                findSupplier,
                initSupplierList,
            } = useSearchSupplier({
                status: 1,
                excludeInitSupplier: true,
            });

            return {
                currentSupplierList,
                initSupplierList,
                fetchSuggestions,
                findSupplier,
            };
        },
        data() {
            return {
                visible: false,
                loading: false,
                goodsInfo: null,
                // 入库表格数据列表
                stockInList: [],
                GoodsTypeEnum,
                PriceModifySourceType,
                showSupplierDialog: false,
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'isChainSubStore',
                'modulePermission',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            // 是否有供应商模块权限
            hasPharmacySupplierModule() {
                return !!this.modulePermission?.hasPharmacySupplierModule;
            },
            canCreateSupplier() {
                if (this.sourceType === 1) {
                    return !this.isChainSubStore && this.hasPharmacySupplierModule;
                }
                return this.viewDistributeConfig.Inventory.isSupportCreateSupplier;
            },
            computedDialogContentStyles() {
                if (this.sourceType === 1) {
                    return 'padding: 0; min-height: 554px;; max-height: 554px;';
                }
                return 'padding: 0; min-height: 198px;';
            },
            tableStyles() {
                if (this.sourceType === 1) {
                    return 'max-height: 366px; margin-top: 8px;';
                }
                return 'height: 77px; margin-top: 24px;';
            },
            // 表格配置
            renderConfig() {
                return {
                    list: [
                        {
                            label: '生产批号',
                            key: 'batchNo',
                            style: {
                                width: '100px',
                                minWidth: '100px',
                                textAlign: 'left',
                            },
                            // headerAppendRender: () => {
                            //     return <span style="color: var(--abc-color-Y2); font-weight: bold; margin-left: 4px;">*</span>;
                            // },
                        },
                        {
                            label: '入库数量',
                            key: 'useCount',
                            style: {
                                width: '100px',
                                minWidth: '100px',
                                textAlign: 'right',
                            },
                            // headerAppendRender: () => {
                            //     return <span style="color: var(--abc-color-Y2); font-weight: bold; margin-left: 4px;">*</span>;
                            // },
                        },
                        {
                            label: '单位',
                            key: 'useUnit',
                            slot: true,
                            style: {
                                width: '56px',
                                minWidth: '56px',
                                maxWidth: '56px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'useUnitCostPrice',
                            label: '进价',
                            slot: true,
                            style: {
                                width: '100px',
                                minWidth: '100px',
                                maxWidth: '100px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'right',
                            },
                            // headerAppendRender: () => {
                            //     return <span style="color: var(--abc-color-Y2); font-weight: bold; margin-left: 4px;">*</span>;
                            // },
                        },
                        {
                            key: 'useTotalCostPrice',
                            label: '金额',
                            slot: true,
                            style: {
                                width: '100px',
                                minWidth: '100px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'right',
                            },
                            // headerAppendRender: () => {
                            //     return <span style="color: var(--abc-color-Y2); font-weight: bold; margin-left: 4px;">*</span>;
                            // },
                        },
                        {
                            key: 'productionDate',
                            label: '生产日期',
                            slot: true,
                            style: {
                                width: '100px',
                                minWidth: '100px',
                                maxWidth: '100px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'expiryDate',
                            label: '有效日期',
                            slot: true,
                            style: {
                                width: '100px',
                                minWidth: '100px',
                                maxWidth: '100px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                            // headerAppendRender: () => {
                            //     if (this.goodsInfo && !isChineseMedicine(this.goodsInfo)) {
                            //         return <span style="color: var(--abc-color-Y2); font-weight: bold; margin-left: 4px;">*</span>;
                            //     }
                            //     return null;
                            // },
                        },
                        {
                            label: '供应商',
                            key: 'supplier',
                            width: '210',
                            style: {
                                width: '210px',
                                minWidth: '210px',
                                maxWidth: '210px',
                                padding: '0px',
                            },
                        },
                    ],
                };
            },
            supplierOptions() {
                return this.currentSupplierList.map((item) => {
                    const handleItem = this.getGualificationStatus(item);
                    return {
                        ...item,
                        isExpired: handleItem.isExpired,
                        expiredList: handleItem.expiredList,
                    };
                }) || [];
            },
            supplierOptionsNormal() {
                return this.supplierOptions?.filter((item) => {
                    if (this.isChainSubStore) {
                        return !item.isExpired && !(item.isEntrustDelivery && item.entrustDeliveryType === EntrustDeliveryType.SELF_DELIVERY);
                    }
                    return !item.isExpired;
                });
            },
            supplierOptionsExpired() {
                return this.supplierOptions?.filter((item) => {
                    if (this.isChainSubStore) {
                        return item.isExpired && !(item.isEntrustDelivery && item.entrustDeliveryType === EntrustDeliveryType.SELF_DELIVERY);
                    }
                    return item.isExpired;
                });
            },
            clinicId() {
                return this.currentClinic.id;
            },
        },
        watch: {
            visible(val) {
                if (!val) {
                    this.destroyElement();
                } else {
                    this.fetchGoodsInfo();
                }
            },
        },
        methods: {
            formatMoney,
            unitEqual,
            isChineseMedicine,
            calCostPriceTotal,
            calCostPriceSingle,
            getCostConfig,
            getFormatLength,
            getUnits(goods) {
                const businessGoods = new BusinessGoods(goods);
                return businessGoods.getAvailableUnits(true);
            },
            gualificationList(id) {
                return this.supplierOptionsExpired?.find((item) => {
                    return item.id === id;
                })?.expiredList || [];
            },
            supplierExpired(id) {
                return this.supplierOptionsExpired?.find((item) => {
                    return item.id === id;
                });
            },
            // 获取经营范围
            getGualificationStatus(item) {
                const { extendInfo = {} } = item;
                const certificationInfos = extendInfo?.certificationInfos || [];
                if (!certificationInfos?.length) {
                    return {
                        isExpired: false,
                        expiredList: [],
                    };
                }
                const expiredItem = certificationInfos?.find((it) => {
                    const currentTime = this.getCurrentTime(it);
                    return this.isExpired(currentTime);
                });
                if (expiredItem) {
                    return {
                        isExpired: true,
                        expiredList: certificationInfos?.filter((it) => {
                            const currentTime = this.getCurrentTime(it);
                            return this.isExpired(currentTime);
                        }) || [],
                    };
                }
                return {
                    isExpired: false,
                    expiredList: [],
                };
            },
            // 是否已到期
            isExpired(currentTime) {
                const date1 = new Date(currentTime);
                const date2 = new Date();
                return date1.getTime() - date2.getTime() < 0;
            },
            getCurrentTime(item) {
                if (!item?.validTo) {
                    return '';
                }
                const validToDate = new Date(item.validTo);
                // 格式化为 YYYY-MM-DD 23:59:59
                const year = validToDate.getFullYear();
                const month = String(validToDate.getMonth() + 1).padStart(2, '0');
                const day = String(validToDate.getDate()).padStart(2, '0');
                return `${year}-${month}-${day} 23:59:59`;
            },
            openSupplierSelectPanel() {
                if (!this.currentSupplierList.length) {
                    this.initSupplierList(true);
                }
            },
            openSupplierDialog(keyId) {
                if (this.$refs[`supplierSelect_${keyId}`]) {
                    this.$refs[`supplierSelect_${keyId}`].showPopper = false;
                }
                this.showSupplierDialog = true;
            },
            getProductDateTooltip(item) {
                if (!item.productionDate || !checkExpiryDate(item.productionDate)) return '';
                return showProductionDateTip(item.productionDate);
            },
            getExpiryDateTooltip(item) {
                if (!item.expiryDate || !checkExpiryDate(item.expiryDate)) return '';
                let diffTime;
                const expiredWarnMonths = item.goods?.expiredWarnMonths;
                if (expiredWarnMonths) {
                    diffTime = expiredWarnMonths * 30 * 24 * 3600 * 1000;
                }

                const today = parseTime((new Date()), 'y-m-d', true);
                if (showOverdueTip(item.expiryDate, today)) {
                    return '该药品已过期';
                }
                if (showExpiryDateTip(item.productionDate, item.expiryDate)) {
                    return '效期不可小于或等于生产日期';
                }
                if (validateExpirationTime(today, item.expiryDate, diffTime)) {
                    if (expiredWarnMonths) {
                        return `该药品将在${expiredWarnMonths}个月之内过期`;
                    }
                    return '该药品将在一年半之内过期';
                }
                return '';
            },
            validateProductionDate(value, callback) {
                value = value.trim();
                if (value) {
                    if (checkExpiryDate(value)) {
                        callback({ validate: true });
                    } else {
                        callback({
                            validate: false, message: '格式错误',
                        });
                    }
                }
            },
            validateExpiryDate(value, callback) {
                value = value.trim();
                if (value) {
                    if (checkExpiryDate(value)) {
                        callback({ validate: true });
                    } else {
                        callback({
                            validate: false, message: '格式错误',
                        });
                    }
                }
            },
            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
            async fetchGoodsInfo() {
                if (!this.goodsId) return;

                this.loading = true;
                try {
                    const { data } = await GoodsAPI.fetchGoods(this.goodsId);
                    if (data) {
                        this.goodsInfo = {
                            ...data,
                            dismounting: true,
                        };
                        this.addNewBatch();
                    }
                } catch (error) {
                    console.error('获取商品详情失败', error);
                    this.$Toast.error('获取商品详情失败');
                } finally {
                    this.loading = false;
                }
            },
            changePrice(priceInfo, row) {
                // 更新价格信息
                Object.assign(row.goods, priceInfo);
            },
            calCostPrice(item) {
                const {
                    useTotalCostPrice, useCount,
                } = item;

                if (isNotNull(useCount) && isNotNull(useTotalCostPrice)) {
                    const price = calCostPriceSingle({
                        useCount,
                        useTotalCostPrice,
                    });

                    this.$set(item, 'useUnitCostPrice', price);
                }
            },

            async calTotalPrice(item) {
                const {
                    useUnitCostPrice, useCount,
                } = item;

                console.log(useCount, useUnitCostPrice);

                if (isNull(useCount) || isNull(useUnitCostPrice)) {
                    this.$set(item, 'useTotalCostPrice', '');
                } else {
                    const totalCost = calCostPriceTotal({
                        useCount,
                        useUnitCostPrice,
                    });

                    console.log(totalCost);

                    this.$set(item, 'useTotalCostPrice', totalCost);
                }
            },
            async handleConfirm() {
                if (this.stockInList.length === 0) {
                    this.$Toast.error('请至少添加一行数据');
                    return;
                }
                this.$refs.goodsStockInTableRef?.validate(async (valid) => {
                    if (!valid) return;

                    const params = {
                        sourceType: this.sourceType,
                        list: this.stockInList.map((item) => ({
                            useUnit: item.useUnit,
                            useCount: item.useCount,
                            useUnitCostPrice: item.useUnitCostPrice,
                            useTotalCostPrice: item.useTotalCostPrice,
                            batchNo: item.batchNo,
                            expiryDate: item.expiryDate,
                            productionDate: item.productionDate,
                            supplierId: item.supplierId,
                        })),
                    };
                    await GoodsAPI.quickStockIn(this.goodsId, params);
                    this.$Toast.success('初始入库完成');
                    if (this.successCallback) {
                        this.successCallback();
                    }
                    this.visible = false;
                });
            },
            handleCancel() {
                this.onCancel && this.onCancel();
                this.visible = false;
            },
            // 添加新批次
            addNewBatch() {
                this.stockInList.push({
                    keyId: createGUID(),
                    supplierId: '',
                    useUnit: this.goodsInfo.packageUnit || this.goodsInfo.pieceUnit || '',
                    useUnitCostPrice: '',
                    useTotalCostPrice: '',
                    batchNo: '',
                    productionDate: '',
                    expiryDate: '',
                    goods: clone(this.goodsInfo),
                });
                // 添加后聚焦到最后一行的第一个输入框（生产批号）
                this.$nextTick(() => {
                    const inputs = document.querySelectorAll('.goods-stock-in-dialog .abc-table-body .abc-table-tr:last-child .abc-input-wrapper input');
                    if (inputs && inputs.length > 0) {
                        inputs[0].focus();
                    }
                });
            },
            // 删除批次行
            handleDeleteTr(index) {
                this.stockInList.splice(index, 1);
            },
            // 选择供应商后聚焦下一行的第一个输入框
            handleSupplierSelect(index, value) {
                if (!value) {
                    return;
                }
                this.$nextTick(() => {
                    // 检查是否有下一行
                    if (index < this.stockInList.length - 1) {
                        // 如果不是最后一行，聚焦到下一行的第一个输入框
                        const nextIndex = index + 1;
                        const nextRowInputs = document.querySelectorAll(`.goods-stock-in-dialog .abc-table-body .abc-table-tr:nth-child(${nextIndex + 1}) .abc-input-wrapper input`);
                        if (nextRowInputs && nextRowInputs.length > 0) {
                            nextRowInputs[0].focus();
                        }
                    }
                });
            },
        },
    };
</script>

<style lang="scss" scoped>
.supplier-select-bottom-fixed {
    padding: 7px 4px 7px 8px;
    background-color: var(--abc-color-cp-grey2);
    border-top: 1px solid var(--abc-color-P8);
    border-radius: var(--abc-border-radius-small);
    border-top-left-radius: unset;
    border-top-right-radius: unset;
}
</style>

<template>
    <div class="debug-container">
        <h1>FileUploaderWithPopover 调试页面</h1>
        
        <div class="debug-section">
            <h2>基础测试 - maxDisplayCount = 4</h2>
            <p>当前文件数: {{ fileList.length }}</p>
            <p>显示文件数: {{ displayFileCount }}</p>
            <p>是否有溢出: {{ hasOverflow ? '是' : '否' }}</p>
            <p>剩余文件数: {{ remainingCount }}</p>
            
            <div class="test-area">
                <file-uploader-with-popover
                    v-model="fileList"
                    :max-upload-count="20"
                    :max-display-count="4"
                    :trigger="{
                        variant: 'image',
                        size: 'small',
                    }"
                    :pc-upload="{
                        accept: 'image/*',
                        multiple: true,
                    }"
                    :mobile-upload="{
                        businessType: 1001,
                        businessDesc: '测试上传',
                    }"
                ></file-uploader-with-popover>
            </div>
        </div>

        <div class="debug-section">
            <h2>不同 maxDisplayCount 测试</h2>
            
            <div class="test-grid">
                <div class="test-item">
                    <h3>maxDisplayCount = 2</h3>
                    <p>文件数: {{ fileList2.length }}</p>
                    <file-uploader-with-popover
                        v-model="fileList2"
                        :max-upload-count="10"
                        :max-display-count="2"
                        :trigger="{ variant: 'image', size: 'small' }"
                        :pc-upload="{ accept: 'image/*', multiple: true }"
                        :mobile-upload="{ businessType: 1001, businessDesc: '测试' }"
                    ></file-uploader-with-popover>
                </div>

                <div class="test-item">
                    <h3>maxDisplayCount = 3</h3>
                    <p>文件数: {{ fileList3.length }}</p>
                    <file-uploader-with-popover
                        v-model="fileList3"
                        :max-upload-count="10"
                        :max-display-count="3"
                        :trigger="{ variant: 'image', size: 'small' }"
                        :pc-upload="{ accept: 'image/*', multiple: true }"
                        :mobile-upload="{ businessType: 1001, businessDesc: '测试' }"
                    ></file-uploader-with-popover>
                </div>

                <div class="test-item">
                    <h3>maxDisplayCount = 5</h3>
                    <p>文件数: {{ fileList5.length }}</p>
                    <file-uploader-with-popover
                        v-model="fileList5"
                        :max-upload-count="15"
                        :max-display-count="5"
                        :trigger="{ variant: 'image', size: 'small' }"
                        :pc-upload="{ accept: 'image/*', multiple: true }"
                        :mobile-upload="{ businessType: 1001, businessDesc: '测试' }"
                    ></file-uploader-with-popover>
                </div>
            </div>
        </div>

        <div class="controls">
            <abc-button type="primary" @click="addFiles">添加文件</abc-button>
            <abc-button @click="removeFile">删除一个文件</abc-button>
            <abc-button @click="clearFiles">清空文件</abc-button>
            <abc-button @click="setTestFiles">设置测试文件</abc-button>
        </div>

        <div class="debug-info">
            <h3>调试信息</h3>
            <pre>{{ debugInfo }}</pre>
        </div>
    </div>
</template>

<script>
    import FileUploaderWithPopover from './index.vue';

    export default {
        name: 'FileUploaderDebug',
        components: {
            FileUploaderWithPopover,
        },

        data() {
            return {
                fileList: [],
                fileList2: [],
                fileList3: [],
                fileList5: [],
            };
        },

        computed: {
            displayFileCount() {
                const maxDisplay = 4;
                if (this.fileList.length > maxDisplay) {
                    return maxDisplay - 1; // 为 "+N" 按钮留位置
                }
                return this.fileList.length;
            },

            hasOverflow() {
                return this.fileList.length > 4;
            },

            remainingCount() {
                return Math.max(0, this.fileList.length - 4);
            },

            debugInfo() {
                return {
                    fileList: {
                        total: this.fileList.length,
                        display: this.displayFileCount,
                        overflow: this.hasOverflow,
                        remaining: this.remainingCount,
                    },
                    fileList2: {
                        total: this.fileList2.length,
                        display: this.fileList2.length > 2 ? 1 : this.fileList2.length,
                        overflow: this.fileList2.length > 2,
                    },
                    fileList3: {
                        total: this.fileList3.length,
                        display: this.fileList3.length > 3 ? 2 : this.fileList3.length,
                        overflow: this.fileList3.length > 3,
                    },
                    fileList5: {
                        total: this.fileList5.length,
                        display: this.fileList5.length > 5 ? 4 : this.fileList5.length,
                        overflow: this.fileList5.length > 5,
                    },
                };
            },
        },

        mounted() {
            this.setTestFiles();
        },

        methods: {
            generateMockFile(index) {
                const colors = ['FF0000', '00FF00', '0000FF', 'FFFF00', 'FF00FF', '00FFFF', 'FFA500', '800080'];
                return {
                    url: `https://via.placeholder.com/150/${colors[index % colors.length]}/FFFFFF?text=File${index + 1}`,
                    fileName: `测试文件${index + 1}.jpg`,
                    fileSize: 1024 * (100 + Math.random() * 100),
                    id: `test-file-${index + 1}`,
                };
            },

            addFiles() {
                const newFile = this.generateMockFile(this.fileList.length);
                this.fileList.push(newFile);

                // 为其他列表也添加文件
                if (this.fileList2.length < 6) {
                    this.fileList2.push(this.generateMockFile(this.fileList2.length));
                }
                if (this.fileList3.length < 7) {
                    this.fileList3.push(this.generateMockFile(this.fileList3.length));
                }
                if (this.fileList5.length < 10) {
                    this.fileList5.push(this.generateMockFile(this.fileList5.length));
                }
            },

            removeFile() {
                if (this.fileList.length > 0) {
                    this.fileList.pop();
                }
                if (this.fileList2.length > 0) {
                    this.fileList2.pop();
                }
                if (this.fileList3.length > 0) {
                    this.fileList3.pop();
                }
                if (this.fileList5.length > 0) {
                    this.fileList5.pop();
                }
            },

            clearFiles() {
                this.fileList = [];
                this.fileList2 = [];
                this.fileList3 = [];
                this.fileList5 = [];
            },

            setTestFiles() {
                // 设置不同数量的测试文件
                this.fileList = Array.from({ length: 8 }, (_, i) => this.generateMockFile(i));
                this.fileList2 = Array.from({ length: 5 }, (_, i) => this.generateMockFile(i));
                this.fileList3 = Array.from({ length: 6 }, (_, i) => this.generateMockFile(i));
                this.fileList5 = Array.from({ length: 9 }, (_, i) => this.generateMockFile(i));
            },
        },
    };
</script>

<style lang="scss" scoped>
.debug-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;

    h1 {
        color: #333;
        margin-bottom: 30px;
        text-align: center;
    }

    .debug-section {
        margin-bottom: 40px;
        padding: 20px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        background: #fafafa;

        h2 {
            color: #555;
            margin-bottom: 15px;
        }

        p {
            margin: 5px 0;
            font-size: 14px;
            color: #666;
        }

        .test-area {
            margin-top: 15px;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border: 1px solid #ddd;
        }
    }

    .test-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
        margin-top: 15px;

        .test-item {
            padding: 15px;
            background: white;
            border-radius: 6px;
            border: 1px solid #ddd;

            h3 {
                margin-bottom: 10px;
                color: #666;
                font-size: 14px;
            }

            p {
                margin-bottom: 10px;
                font-size: 12px;
                color: #888;
            }
        }
    }

    .controls {
        margin: 30px 0;
        display: flex;
        gap: 12px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .debug-info {
        margin-top: 30px;
        padding: 20px;
        background: #f5f5f5;
        border-radius: 8px;
        border: 1px solid #ddd;

        h3 {
            margin-bottom: 15px;
            color: #333;
        }

        pre {
            background: white;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
            overflow-x: auto;
            font-size: 12px;
            line-height: 1.4;
        }
    }
}
</style>

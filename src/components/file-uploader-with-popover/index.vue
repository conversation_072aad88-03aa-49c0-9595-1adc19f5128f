<template>
    <div
        class="file-uploader-with-popover"
        :class="{ 'has-overflow': hasMoreFiles }"
        :style="{
            '--max-display': maxDisplayCount,
            '--overflow-count': `'+${remainingFilesCount}'`
        }"
        @click="handleClick"
    >
        <!-- 只需要一个abc-file-uploader，用CSS控制显示 -->
        <abc-file-uploader
            ref="uploader"
            v-model="currentValue"
            v-bind="$attrs"
        ></abc-file-uploader>

        <!-- 简单的弹窗显示所有文件 -->
        <abc-popover
            v-model="showPopover"
            placement="bottom-start"
            trigger="manual"
            theme="white"
            width="auto"
            :popper-style="{
                padding: '12px', maxWidth: '400px'
            }"
        >
            <div slot="reference" style="display: none;"></div>

            <div class="all-files-content">
                <abc-file-uploader
                    :value="currentValue"
                    :disabled="true"
                    :file-view-config="{ showDeleteIcon: false }"
                    :trigger="{
                        variant: 'image', size: 'small'
                    }"
                    :layout-config="{ wrap: 'wrap' }"
                ></abc-file-uploader>
            </div>
        </abc-popover>
    </div>
</template>

<script>
    import AbcFileUploader from 'components/abc-file-uploader/index.vue';

    export default {
        name: 'FileUploaderWithPopover',
        components: {
            AbcFileUploader,
        },

        props: {
            value: {
                type: Array,
                default: () => [],
            },
            maxDisplayCount: {
                type: Number,
                default: 4,
            },
        },

        data() {
            return {
                showPopover: false,
            };
        },

        computed: {
            currentValue: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },

            hasMoreFiles() {
                return this.value.length > this.maxDisplayCount;
            },

            remainingFilesCount() {
                return this.value.length - this.maxDisplayCount;
            },
        },

        methods: {
            handleClick(e) {
                // 点击"+N"区域时显示所有文件
                if (this.hasMoreFiles) {
                    // 检查是否点击在伪元素覆盖层区域
                    const rect = e.currentTarget.querySelector('.abc-flex').getBoundingClientRect();
                    const clickX = e.clientX - rect.left;
                    const expectedX = (this.maxDisplayCount - 1) * 56; // 最后一个位置

                    if (clickX >= expectedX && clickX <= expectedX + 48) {
                        this.showPopover = true;
                    }
                }
            },
        },
    };
</script>

<style lang="scss" scoped>
.file-uploader-with-popover {
    position: relative;

    // 使用更简单的flex布局，避免CSS变量兼容性问题
    :deep(.abc-flex) {
        display: flex !important;
        flex-wrap: wrap;
        gap: 8px;
    }

    // 默认情况下，隐藏超出maxDisplayCount的文件
    &[style*="--max-display: 1"] :deep(.abc-flex > *:nth-child(n + 2)) { display: none; }
    &[style*="--max-display: 2"] :deep(.abc-flex > *:nth-child(n + 3)) { display: none; }
    &[style*="--max-display: 3"] :deep(.abc-flex > *:nth-child(n + 4)) { display: none; }
    &[style*="--max-display: 4"] :deep(.abc-flex > *:nth-child(n + 5)) { display: none; }
    &[style*="--max-display: 5"] :deep(.abc-flex > *:nth-child(n + 6)) { display: none; }
    &[style*="--max-display: 6"] :deep(.abc-flex > *:nth-child(n + 7)) { display: none; }

    // 有溢出时显示"+N"按钮
    &.has-overflow {
        // 在最后一个显示位置添加"+N"按钮
        :deep(.abc-flex) {
            position: relative;

            &::after {
                content: var(--overflow-count);
                position: absolute;
                top: 0;
                width: 48px;
                height: 48px;
                border: 1px dashed var(--abc-color-P1);
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                background: var(--abc-color-N1);
                font-size: 12px;
                color: var(--abc-color-theme1);
                font-weight: 500;
                transition: all 0.2s;
                z-index: 1;

                &:hover {
                    border-color: var(--abc-color-theme1);
                    background: var(--abc-color-theme1-light);
                }
            }
        }

        // 根据maxDisplayCount设置"+N"按钮的位置，并隐藏最后一个文件
        &[style*="--max-display: 1"] {
            :deep(.abc-flex::after) { left: 0; }
            :deep(.abc-flex > *:nth-child(1)) { display: none !important; }
        }
        &[style*="--max-display: 2"] {
            :deep(.abc-flex::after) { left: 56px; }
            :deep(.abc-flex > *:nth-child(2)) { display: none !important; }
        }
        &[style*="--max-display: 3"] {
            :deep(.abc-flex::after) { left: 112px; }
            :deep(.abc-flex > *:nth-child(3)) { display: none !important; }
        }
        &[style*="--max-display: 4"] {
            :deep(.abc-flex::after) { left: 168px; }
            :deep(.abc-flex > *:nth-child(4)) { display: none !important; }
        }
        &[style*="--max-display: 5"] {
            :deep(.abc-flex::after) { left: 224px; }
            :deep(.abc-flex > *:nth-child(5)) { display: none !important; }
        }
        &[style*="--max-display: 6"] {
            :deep(.abc-flex::after) { left: 280px; }
            :deep(.abc-flex > *:nth-child(6)) { display: none !important; }
        }
    }

    .all-files-content {
        max-width: 400px;
    }
}
</style>

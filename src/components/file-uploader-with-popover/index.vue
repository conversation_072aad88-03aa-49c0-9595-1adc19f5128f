<template>
    <div
        class="file-uploader-with-popover"
        :class="{ 'has-overflow': hasMoreFiles }"
        :style="{
            '--max-display': maxDisplayCount,
            '--overflow-count': `'+${remainingFilesCount}'`
        }"
    >
        <!-- 主要的文件上传组件 -->
        <abc-file-uploader
            ref="uploader"
            v-model="currentValue"
            v-bind="$attrs"
        ></abc-file-uploader>

        <!-- 当有溢出文件时，显示"+N"按钮的popover -->
        <abc-popover
            v-if="hasMoreFiles"
            v-model="showPopover"
            placement="bottom-start"
            trigger="hover"
            theme="white"
            width="auto"
            :popper-style="{
                padding: '12px', maxWidth: '400px'
            }"
        >
            <!-- 触发器："+N"按钮 -->
            <div
                slot="reference"
                class="overflow-trigger"
                :style="{
                    left: `${(maxDisplayCount - 1) * 56}px`
                }"
            >
                +{{ remainingFilesCount }}
            </div>

            <!-- 弹窗内容：显示所有文件 -->
            <div class="all-files-content">
                <abc-file-uploader
                    :value="currentValue"
                    :disabled="true"
                    :file-view-config="{ showDeleteIcon: false }"
                    :trigger="{
                        variant: 'image',
                        size: 'small'
                    }"
                    :layout-config="{ wrap: 'wrap' }"
                ></abc-file-uploader>
            </div>
        </abc-popover>
    </div>
</template>

<script>
    import AbcFileUploader from 'components/abc-file-uploader/index.vue';

    export default {
        name: 'FileUploaderWithPopover',
        components: {
            AbcFileUploader,
        },

        props: {
            value: {
                type: Array,
                default: () => [],
            },
            maxDisplayCount: {
                type: Number,
                default: 4,
            },
        },

        data() {
            return {
                showPopover: false,
            };
        },

        computed: {
            currentValue: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },

            hasMoreFiles() {
                return this.value.length > this.maxDisplayCount;
            },

            remainingFilesCount() {
                return this.value.length - this.maxDisplayCount;
            },
        },
    };
</script>

<style lang="scss" scoped>
.file-uploader-with-popover {
    position: relative;

    // 控制文件显示数量
    :deep(.abc-flex) {
        display: flex !important;
        flex-wrap: wrap;
        gap: 8px;
    }

    // 根据maxDisplayCount隐藏超出的文件
    &[style*="--max-display: 1"] :deep(.abc-flex > *:nth-child(n + 2)) {
        display: none;
    }
    &[style*="--max-display: 2"] :deep(.abc-flex > *:nth-child(n + 3)) {
        display: none;
    }
    &[style*="--max-display: 3"] :deep(.abc-flex > *:nth-child(n + 4)) {
        display: none;
    }
    &[style*="--max-display: 4"] :deep(.abc-flex > *:nth-child(n + 5)) {
        display: none;
    }
    &[style*="--max-display: 5"] :deep(.abc-flex > *:nth-child(n + 6)) {
        display: none;
    }
    &[style*="--max-display: 6"] :deep(.abc-flex > *:nth-child(n + 7)) {
        display: none;
    }

    // 有溢出时，隐藏最后一个显示位置的文件，为"+N"按钮让位
    &.has-overflow {
        &[style*="--max-display: 1"] :deep(.abc-flex > *:nth-child(1)) {
            display: none !important;
        }
        &[style*="--max-display: 2"] :deep(.abc-flex > *:nth-child(2)) {
            display: none !important;
        }
        &[style*="--max-display: 3"] :deep(.abc-flex > *:nth-child(3)) {
            display: none !important;
        }
        &[style*="--max-display: 4"] :deep(.abc-flex > *:nth-child(4)) {
            display: none !important;
        }
        &[style*="--max-display: 5"] :deep(.abc-flex > *:nth-child(5)) {
            display: none !important;
        }
        &[style*="--max-display: 6"] :deep(.abc-flex > *:nth-child(6)) {
            display: none !important;
        }
    }

    // "+N"按钮样式
    .overflow-trigger {
        position: absolute;
        top: 0;
        width: 48px;
        height: 48px;
        border: 1px dashed var(--abc-color-P1);
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        background: var(--abc-color-N1);
        font-size: 12px;
        color: var(--abc-color-theme1);
        font-weight: 500;
        transition: all 0.2s;
        z-index: 1;

        &:hover {
            border-color: var(--abc-color-theme1);
            background: var(--abc-color-theme1-light);
        }
    }

    // 弹窗内容样式
    .all-files-content {
        max-width: 400px;
    }
}
</style>

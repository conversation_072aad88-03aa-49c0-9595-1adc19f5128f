<template>
    <div class="file-uploader-with-popover">
        <!-- 显示的文件列表 -->
        <div class="visible-files">
            <abc-file-uploader
                ref="uploader"
                :value="displayFiles"
                :disabled="disabled"
                :file-view-config="fileViewConfig"
                :trigger="triggerConfig"
                :pc-upload="pcUpload"
                :mobile-upload="mobileUpload"
                :layout-config="layoutConfig"
                :max-upload-count="maxUploadCount"
                @input="handleFileChange"
            ></abc-file-uploader>
        </div>

        <!-- 当有溢出文件时，显示"+N"按钮的popover -->
        <abc-popover
            v-if="hasMoreFiles"
            v-model="showPopover"
            placement="bottom-start"
            trigger="hover"
            theme="white"
            width="auto"
            :popper-style="{
                padding: '12px', maxWidth: '400px'
            }"
        >
            <!-- 触发器："+N"按钮 -->
            <div
                slot="reference"
                class="overflow-trigger"
                :style="{
                    left: `${(maxDisplayCount - 1) * 56}px`
                }"
            >
                +{{ remainingFilesCount }}
            </div>

            <!-- 弹窗内容：显示所有文件 -->
            <div class="all-files-content">
                <abc-file-uploader
                    :value="currentValue"
                    :disabled="true"
                    :file-view-config="{ showDeleteIcon: false }"
                    :trigger="{
                        variant: 'image',
                        size: 'small'
                    }"
                    :layout-config="{ wrap: 'wrap' }"
                ></abc-file-uploader>
            </div>
        </abc-popover>
    </div>
</template>

<script>
    import AbcFileUploader from 'components/abc-file-uploader/index.vue';

    export default {
        name: 'FileUploaderWithPopover',
        components: {
            AbcFileUploader,
        },

        props: {
            value: {
                type: Array,
                default: () => [],
            },
            maxDisplayCount: {
                type: Number,
                default: 4,
            },
            disabled: Boolean,
            fileViewConfig: {
                type: Object,
                default: () => ({}),
            },
            layoutConfig: {
                type: Object,
                default: () => ({}),
            },
            trigger: {
                type: Object,
                default: () => ({
                    variant: 'image',
                    size: 'small',
                }),
            },
            pcUpload: {
                type: Object,
                default: () => ({}),
            },
            mobileUpload: {
                type: Object,
                default: () => ({}),
            },
            maxUploadCount: {
                type: Number,
                default: 20,
            },
        },

        data() {
            return {
                showPopover: false,
            };
        },

        computed: {
            currentValue: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },

            // 显示的文件列表（限制数量）
            displayFiles() {
                if (this.hasMoreFiles) {
                    // 如果有溢出，显示 maxDisplayCount - 1 个文件，为 "+N" 按钮留位置
                    return this.currentValue.slice(0, this.maxDisplayCount - 1);
                }
                return this.currentValue;
            },

            hasMoreFiles() {
                return this.currentValue.length > this.maxDisplayCount;
            },

            remainingFilesCount() {
                return this.currentValue.length - this.maxDisplayCount;
            },

            triggerConfig() {
                return {
                    variant: 'image',
                    size: 'small',
                    ...this.trigger,
                };
            },
        },

        methods: {
            handleFileChange(newFiles) {
                this.currentValue = newFiles;
            },
        },
    };
</script>

<style lang="scss" scoped>
.file-uploader-with-popover {
    position: relative;

    .visible-files {
        position: relative;
    }

    // "+N"按钮样式
    .overflow-trigger {
        position: absolute;
        top: 0;
        width: 48px;
        height: 48px;
        border: 1px dashed var(--abc-color-P1);
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        background: var(--abc-color-N1);
        font-size: 12px;
        color: var(--abc-color-theme1);
        font-weight: 500;
        transition: all 0.2s;
        z-index: 1;

        &:hover {
            border-color: var(--abc-color-theme1);
            background: var(--abc-color-theme1-light);
        }
    }

    // 弹窗内容样式
    .all-files-content {
        max-width: 400px;
    }
}</style>

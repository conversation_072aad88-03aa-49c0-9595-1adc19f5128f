# FileUploaderWithPopover 使用指南

## 快速开始

这个组件已经完全实现了你需要的功能：当 `abc-descriptions-item` 放不下 `abc-file-uploader` 后，使用 `abc-popover` 来展示剩余的上传附件组件。

### 核心功能

✅ **自动隐藏超出文件**: 使用 CSS 将超出 `maxDisplayCount` 的元素隐藏  
✅ **"+N" 蒙层显示**: 在最后一个元素上面加个蒙层显示剩余数量  
✅ **Hover 弹出面板**: hover 蒙层可以弹出 popover 面板展示剩余文件  
✅ **完全兼容**: 支持 `abc-file-uploader` 的所有功能和属性  

## 基础使用

### 1. 在 abc-descriptions-item 中使用

```vue
<abc-descriptions-item
    label="附件"
    :content-style="{
        display: 'flex',
        alignItems: 'center',
        padding: '2px 8px'
    }"
>
    <file-uploader-with-popover
        v-model="fileList"
        :max-upload-count="20"
        :max-display-count="4"
        :disabled="!canEdit"
        :file-view-config="{
            showDeleteIcon: canEdit
        }"
        :trigger="{
            variant: 'image',
            size: 'small',
        }"
        :pc-upload="{
            accept: 'image/*',
            multiple: true,
            businessModule: OssBusinessModuleEnum.GOODS,
            businessScene: OssBusinessSceneEnum.EXAMPLE_ATTACHMENTS,
        }"
        :mobile-upload="{
            businessType: BusinessTypeEnum.EXAMPLE_ATTACHMENTS,
            businessDesc: '附件上传',
            uploadDescription: '支持图片、PDF格式',
        }"
    ></file-uploader-with-popover>
</abc-descriptions-item>
```

### 2. 独立使用

```vue
<file-uploader-with-popover
    v-model="fileList"
    :max-upload-count="10"
    :max-display-count="3"
    :trigger="{ variant: 'image', size: 'small' }"
    :pc-upload="{ accept: 'image/*', multiple: true }"
    :mobile-upload="{ businessType: 1001, businessDesc: '上传文件' }"
></file-uploader-with-popover>
```

## 关键属性

### maxDisplayCount
- **类型**: `Number`
- **默认值**: `4`
- **说明**: 最多显示的文件数量，超出部分会被隐藏并显示 "+N" 按钮

### 其他属性
完全继承 `abc-file-uploader` 的所有属性，无需额外学习。

## 效果展示

### 文件数量 ≤ maxDisplayCount
```
[文件1] [文件2] [文件3] [+]
```

### 文件数量 > maxDisplayCount (例如 maxDisplayCount=4，实际有8个文件)
```
[文件1] [文件2] [文件3] [+5] [+]
                        ↑
                    hover显示popover
                    包含所有8个文件
```

## 实现原理

1. **JavaScript 控制显示**: 通过 `displayFiles` 计算属性控制显示的文件数量，而不是使用 CSS 隐藏
2. **智能文件切片**: 当文件数量超过 `maxDisplayCount` 时，只显示前 `maxDisplayCount - 1` 个文件，为 "+N" 按钮留位置
3. **"+N" 按钮定位**: 通过绝对定位将按钮放置在最后一个显示位置
4. **Popover 触发**: 使用 `abc-popover` 组件的 hover 触发方式显示所有文件的只读列表

## 样式自定义

### 修改 "+N" 按钮样式

```scss
.file-uploader-with-popover {
    .overflow-trigger {
        border-color: #your-color;
        background: #your-background;
        color: #your-text-color;
        
        &:hover {
            border-color: #your-hover-color;
            background: #your-hover-background;
        }
    }
}
```

### 修改 Popover 内容样式

```scss
.file-uploader-with-popover {
    .all-files-content {
        max-width: 500px; // 自定义最大宽度
        // 其他自定义样式
    }
}
```

## 注意事项

1. **文件尺寸**: 组件假设每个文件的宽度为 48px，间距为 8px
2. **无限制支持**: 新实现支持任意数量的 maxDisplayCount，不再有 1-6 的限制
3. **只读 Popover**: Popover 中的文件列表是只读的，不支持删除操作
4. **组件依赖**: 确保项目中已正确引入 `abc-file-uploader` 和 `abc-popover`
5. **文件操作**: 所有文件的增删操作都通过主要的 abc-file-uploader 组件进行

## 测试

运行测试页面查看效果：
```vue
import TestPage from 'components/file-uploader-with-popover/test.vue';
```

## 已有项目集成

该组件已经在以下文件中使用：
- `src/views-pharmacy/inventory/frames/purchase/take-delivery/order-dialog.vue`
- `src/views/inventory/goods-in/form.vue`

可以参考这些文件的使用方式。

<template>
    <div class="test-container">
        <h1>FileUploaderWithPopover 测试页面</h1>
        
        <!-- 测试不同的显示数量 -->
        <div class="test-section">
            <h2>测试不同的 maxDisplayCount 设置</h2>
            
            <div class="test-grid">
                <div class="test-item">
                    <h3>maxDisplayCount = 2</h3>
                    <p>当前文件数: {{ fileList2.length }}</p>
                    <file-uploader-with-popover
                        v-model="fileList2"
                        :max-upload-count="10"
                        :max-display-count="2"
                        :trigger="{
                            variant: 'image',
                            size: 'small',
                        }"
                        :pc-upload="{
                            accept: 'image/*',
                            multiple: true,
                        }"
                        :mobile-upload="{
                            businessType: 1001,
                            businessDesc: '测试上传',
                        }"
                    ></file-uploader-with-popover>
                </div>

                <div class="test-item">
                    <h3>maxDisplayCount = 3</h3>
                    <p>当前文件数: {{ fileList3.length }}</p>
                    <file-uploader-with-popover
                        v-model="fileList3"
                        :max-upload-count="10"
                        :max-display-count="3"
                        :trigger="{
                            variant: 'image',
                            size: 'small',
                        }"
                        :pc-upload="{
                            accept: 'image/*',
                            multiple: true,
                        }"
                        :mobile-upload="{
                            businessType: 1001,
                            businessDesc: '测试上传',
                        }"
                    ></file-uploader-with-popover>
                </div>

                <div class="test-item">
                    <h3>maxDisplayCount = 4 (默认)</h3>
                    <p>当前文件数: {{ fileList4.length }}</p>
                    <file-uploader-with-popover
                        v-model="fileList4"
                        :max-upload-count="15"
                        :max-display-count="4"
                        :trigger="{
                            variant: 'image',
                            size: 'small',
                        }"
                        :pc-upload="{
                            accept: 'image/*',
                            multiple: true,
                        }"
                        :mobile-upload="{
                            businessType: 1001,
                            businessDesc: '测试上传',
                        }"
                    ></file-uploader-with-popover>
                </div>

                <div class="test-item">
                    <h3>maxDisplayCount = 5</h3>
                    <p>当前文件数: {{ fileList5.length }}</p>
                    <file-uploader-with-popover
                        v-model="fileList5"
                        :max-upload-count="20"
                        :max-display-count="5"
                        :trigger="{
                            variant: 'image',
                            size: 'small',
                        }"
                        :pc-upload="{
                            accept: 'image/*',
                            multiple: true,
                        }"
                        :mobile-upload="{
                            businessType: 1001,
                            businessDesc: '测试上传',
                        }"
                    ></file-uploader-with-popover>
                </div>
            </div>
        </div>

        <!-- 在 abc-descriptions-item 中的测试 -->
        <div class="test-section">
            <h2>在 abc-descriptions-item 中的使用测试</h2>
            
            <abc-descriptions :column="1" :label-width="100" grid size="large">
                <abc-descriptions-item
                    label="订单附件"
                    :content-style="{
                        display: 'flex',
                        alignItems: 'center',
                        padding: '2px 8px'
                    }"
                >
                    <file-uploader-with-popover
                        v-model="orderFiles"
                        :max-upload-count="20"
                        :max-display-count="4"
                        :disabled="false"
                        :file-view-config="{
                            showDeleteIcon: true
                        }"
                        :trigger="{
                            variant: 'image',
                            size: 'small',
                        }"
                        :pc-upload="{
                            accept: 'image/*',
                            multiple: true,
                        }"
                        :mobile-upload="{
                            businessType: 1001,
                            businessDesc: '订单附件',
                            uploadDescription: '支持图片、PDF格式',
                        }"
                    ></file-uploader-with-popover>
                </abc-descriptions-item>

                <abc-descriptions-item
                    label="产品图片"
                    :content-style="{
                        display: 'flex',
                        alignItems: 'center',
                        padding: '2px 8px'
                    }"
                >
                    <file-uploader-with-popover
                        v-model="productImages"
                        :max-upload-count="10"
                        :max-display-count="3"
                        :disabled="false"
                        :file-view-config="{
                            showDeleteIcon: true
                        }"
                        :trigger="{
                            variant: 'image',
                            size: 'small',
                        }"
                        :pc-upload="{
                            accept: 'image/*',
                            multiple: true,
                        }"
                        :mobile-upload="{
                            businessType: 1001,
                            businessDesc: '产品图片',
                        }"
                    ></file-uploader-with-popover>
                </abc-descriptions-item>
            </abc-descriptions>
        </div>

        <!-- 控制按钮 -->
        <div class="test-controls">
            <abc-button type="primary" @click="addTestFiles">添加测试文件</abc-button>
            <abc-button @click="clearAllFiles">清空所有文件</abc-button>
            <abc-button @click="showDebugInfo = !showDebugInfo">
                {{ showDebugInfo ? '隐藏' : '显示' }}调试信息
            </abc-button>
        </div>

        <!-- 调试信息 -->
        <div v-if="showDebugInfo" class="debug-info">
            <h3>调试信息</h3>
            <div class="debug-item">
                <h4>fileList2 ({{ fileList2.length }} 个文件):</h4>
                <pre>{{ JSON.stringify(fileList2.map(f => ({ fileName: f.fileName, url: f.url })), null, 2) }}</pre>
            </div>
            <div class="debug-item">
                <h4>fileList3 ({{ fileList3.length }} 个文件):</h4>
                <pre>{{ JSON.stringify(fileList3.map(f => ({ fileName: f.fileName, url: f.url })), null, 2) }}</pre>
            </div>
            <div class="debug-item">
                <h4>orderFiles ({{ orderFiles.length }} 个文件):</h4>
                <pre>{{ JSON.stringify(orderFiles.map(f => ({ fileName: f.fileName, url: f.url })), null, 2) }}</pre>
            </div>
        </div>
    </div>
</template>

<script>
    import FileUploaderWithPopover from './index.vue';

    export default {
        name: 'FileUploaderWithPopoverTest',
        components: {
            FileUploaderWithPopover,
        },

        data() {
            return {
                fileList2: [],
                fileList3: [],
                fileList4: [],
                fileList5: [],
                orderFiles: [],
                productImages: [],
                showDebugInfo: false,
            };
        },

        mounted() {
            // 自动添加一些测试文件
            this.addTestFiles();
        },

        methods: {
            addTestFiles() {
                const testFiles = this.generateTestFiles(8);
                
                this.fileList2 = [...testFiles.slice(0, 4)];
                this.fileList3 = [...testFiles.slice(0, 5)];
                this.fileList4 = [...testFiles.slice(0, 6)];
                this.fileList5 = [...testFiles];
                this.orderFiles = [...testFiles.slice(0, 7)];
                this.productImages = [...testFiles.slice(0, 5)];
            },

            clearAllFiles() {
                this.fileList2 = [];
                this.fileList3 = [];
                this.fileList4 = [];
                this.fileList5 = [];
                this.orderFiles = [];
                this.productImages = [];
            },

            generateTestFiles(count) {
                const colors = ['FF0000', '00FF00', '0000FF', 'FFFF00', 'FF00FF', '00FFFF', 'FFA500', '800080'];
                const files = [];
                
                for (let i = 0; i < count; i++) {
                    files.push({
                        url: `https://via.placeholder.com/150/${colors[i % colors.length]}/FFFFFF?text=File${i + 1}`,
                        fileName: `测试文件${i + 1}.jpg`,
                        fileSize: 1024 * (100 + Math.random() * 100),
                        id: `test-file-${i + 1}`,
                    });
                }
                
                return files;
            },
        },
    };
</script>

<style lang="scss" scoped>
.test-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;

    h1 {
        color: #333;
        margin-bottom: 30px;
        text-align: center;
    }

    .test-section {
        margin-bottom: 40px;
        padding: 20px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        background: #fafafa;

        h2 {
            color: #555;
            margin-bottom: 20px;
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
        }
    }

    .test-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;

        .test-item {
            padding: 16px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;

            h3 {
                margin-bottom: 8px;
                color: #666;
                font-size: 14px;
            }

            p {
                margin-bottom: 12px;
                font-size: 12px;
                color: #888;
            }
        }
    }

    .test-controls {
        margin: 30px 0;
        display: flex;
        gap: 12px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .debug-info {
        margin-top: 30px;
        padding: 20px;
        background: #f5f5f5;
        border-radius: 8px;
        border: 1px solid #ddd;

        h3 {
            margin-bottom: 20px;
            color: #333;
        }

        .debug-item {
            margin-bottom: 20px;

            h4 {
                margin-bottom: 8px;
                color: #555;
                font-size: 14px;
            }

            pre {
                background: white;
                padding: 12px;
                border-radius: 4px;
                border: 1px solid #e0e0e0;
                overflow-x: auto;
                font-size: 11px;
                line-height: 1.4;
                max-height: 200px;
                overflow-y: auto;
            }
        }
    }
}
</style>

# FileUploaderWithPopover 组件

一个带有溢出显示功能的文件上传组件，当文件数量超过指定的显示数量时，会在最后一个位置显示 "+N" 按钮，hover 时弹出 popover 显示所有文件。

## 功能特性

- ✅ 基于 `abc-file-uploader` 组件封装
- ✅ 支持设置最大显示文件数量
- ✅ 超出文件自动隐藏，显示 "+N" 按钮
- ✅ Hover "+N" 按钮弹出 popover 显示所有文件
- ✅ 完全兼容 `abc-file-uploader` 的所有属性
- ✅ 响应式设计，支持不同的显示数量配置

## 使用方法

### 基础用法

```vue
<template>
    <file-uploader-with-popover
        v-model="fileList"
        :max-upload-count="20"
        :max-display-count="4"
        :trigger="{
            variant: 'image',
            size: 'small',
        }"
        :pc-upload="{
            accept: 'image/*',
            multiple: true,
        }"
        :mobile-upload="{
            businessType: 1001,
            businessDesc: '上传文件',
        }"
    ></file-uploader-with-popover>
</template>

<script>
import FileUploaderWithPopover from 'components/file-uploader-with-popover/index.vue';

export default {
    components: {
        FileUploaderWithPopover,
    },
    data() {
        return {
            fileList: [],
        };
    },
};
</script>
```

### 在 abc-descriptions-item 中使用

```vue
<template>
    <abc-descriptions :column="1" :label-width="80" grid size="large">
        <abc-descriptions-item
            label="附件"
            :content-style="{
                display: 'flex',
                alignItems: 'center',
                padding: '2px 8px'
            }"
        >
            <file-uploader-with-popover
                v-model="fileList"
                :max-upload-count="20"
                :max-display-count="4"
                :disabled="!canEdit"
                :file-view-config="{
                    showDeleteIcon: canEdit
                }"
                :trigger="{
                    variant: 'image',
                    size: 'small',
                }"
                :pc-upload="{
                    accept: 'image/*',
                    multiple: true,
                    businessModule: OssBusinessModuleEnum.GOODS,
                    businessScene: OssBusinessSceneEnum.EXAMPLE_ATTACHMENTS,
                }"
                :mobile-upload="{
                    businessType: BusinessTypeEnum.EXAMPLE_ATTACHMENTS,
                    businessDesc: '附件上传',
                    uploadDescription: '支持图片、PDF格式',
                }"
            ></file-uploader-with-popover>
        </abc-descriptions-item>
    </abc-descriptions>
</template>
```

## 属性说明

### 新增属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| maxDisplayCount | Number | 4 | 最大显示文件数量，超出部分会被隐藏并显示 "+N" 按钮 |

### 继承属性

该组件完全继承 `abc-file-uploader` 的所有属性，包括：

- `value` / `v-model`: 文件列表
- `maxUploadCount`: 最大上传数量
- `disabled`: 是否禁用
- `trigger`: 触发器配置
- `fileViewConfig`: 文件预览配置
- `layoutConfig`: 布局配置
- `pcUpload`: PC端上传配置
- `mobileUpload`: 移动端上传配置

详细属性说明请参考 `abc-file-uploader` 组件文档。

## 样式说明

### CSS 变量

组件使用以下 CSS 变量来控制样式：

- `--abc-color-P1`: 边框颜色
- `--abc-color-N1`: 背景颜色
- `--abc-color-theme1`: 主题色
- `--abc-color-theme1-light`: 主题色浅色版本

### 自定义样式

如果需要自定义 "+N" 按钮的样式，可以通过以下方式：

```scss
.file-uploader-with-popover {
    .overflow-trigger {
        // 自定义样式
        border-color: #your-color;
        background: #your-background;
        
        &:hover {
            border-color: #your-hover-color;
            background: #your-hover-background;
        }
    }
}
```

## 工作原理

1. **文件显示控制**: 使用 CSS 的 `:nth-child` 选择器根据 `maxDisplayCount` 隐藏超出的文件
2. **"+N" 按钮定位**: 通过绝对定位将按钮放置在最后一个显示位置
3. **Popover 触发**: 使用 `abc-popover` 组件的 hover 触发方式显示所有文件
4. **响应式支持**: 支持 1-6 个文件的显示数量配置

## 注意事项

1. 确保项目中已正确引入 `abc-file-uploader` 和 `abc-popover` 组件
2. 文件的宽度固定为 48px，间距为 8px，这影响 "+N" 按钮的定位计算
3. 当文件数量小于等于 `maxDisplayCount` 时，不会显示 "+N" 按钮
4. Popover 中的文件列表是只读的，不支持删除操作

## 示例

查看 `example.vue` 文件获取完整的使用示例。

<template>
    <div class="example-container">
        <h2>文件上传组件示例</h2>
        
        <!-- 基础示例 -->
        <abc-descriptions :column="1" :label-width="80" grid size="large">
            <abc-descriptions-item
                label="附件"
                :content-style="{
                    display: 'flex',
                    alignItems: 'center',
                    padding: '2px 8px'
                }"
            >
                <file-uploader-with-popover
                    v-model="fileList"
                    :max-upload-count="20"
                    :max-display-count="4"
                    :disabled="false"
                    :file-view-config="{
                        showDeleteIcon: true
                    }"
                    :trigger="{
                        variant: 'image',
                        size: 'small',
                    }"
                    :pc-upload="{
                        accept: 'image/*',
                        multiple: true,
                        businessModule: 'GOODS',
                        businessScene: 'EXAMPLE_ATTACHMENTS',
                    }"
                    :mobile-upload="{
                        businessType: 1001,
                        businessDesc: '示例上传',
                        uploadDescription: '支持图片、PDF格式',
                    }"
                ></file-uploader-with-popover>
            </abc-descriptions-item>
        </abc-descriptions>

        <!-- 不同显示数量的示例 -->
        <div class="examples-grid">
            <div class="example-item">
                <h3>最多显示2个文件</h3>
                <file-uploader-with-popover
                    v-model="fileList2"
                    :max-upload-count="10"
                    :max-display-count="2"
                    :trigger="{
                        variant: 'image',
                        size: 'small',
                    }"
                    :pc-upload="{
                        accept: 'image/*',
                        multiple: true,
                    }"
                    :mobile-upload="{
                        businessType: 1001,
                        businessDesc: '示例上传',
                    }"
                ></file-uploader-with-popover>
            </div>

            <div class="example-item">
                <h3>最多显示3个文件</h3>
                <file-uploader-with-popover
                    v-model="fileList3"
                    :max-upload-count="10"
                    :max-display-count="3"
                    :trigger="{
                        variant: 'image',
                        size: 'small',
                    }"
                    :pc-upload="{
                        accept: 'image/*',
                        multiple: true,
                    }"
                    :mobile-upload="{
                        businessType: 1001,
                        businessDesc: '示例上传',
                    }"
                ></file-uploader-with-popover>
            </div>

            <div class="example-item">
                <h3>最多显示5个文件</h3>
                <file-uploader-with-popover
                    v-model="fileList5"
                    :max-upload-count="15"
                    :max-display-count="5"
                    :trigger="{
                        variant: 'image',
                        size: 'small',
                    }"
                    :pc-upload="{
                        accept: 'image/*',
                        multiple: true,
                    }"
                    :mobile-upload="{
                        businessType: 1001,
                        businessDesc: '示例上传',
                    }"
                ></file-uploader-with-popover>
            </div>
        </div>

        <!-- 控制按钮 -->
        <div class="controls">
            <abc-button @click="addMockFiles">添加模拟文件</abc-button>
            <abc-button @click="clearFiles">清空文件</abc-button>
            <abc-button @click="showFileInfo">显示文件信息</abc-button>
        </div>

        <!-- 文件信息显示 -->
        <div v-if="showInfo" class="file-info">
            <h3>当前文件列表：</h3>
            <pre>{{ JSON.stringify(fileList, null, 2) }}</pre>
        </div>
    </div>
</template>

<script>
    import FileUploaderWithPopover from './index.vue';

    export default {
        name: 'FileUploaderExample',
        components: {
            FileUploaderWithPopover,
        },

        data() {
            return {
                fileList: [],
                fileList2: [],
                fileList3: [],
                fileList5: [],
                showInfo: false,
            };
        },

        mounted() {
            // 添加一些模拟文件用于演示
            this.addMockFiles();
        },

        methods: {
            addMockFiles() {
                const mockFiles = [
                    {
                        url: 'https://via.placeholder.com/150/0000FF/FFFFFF?text=File1',
                        fileName: '示例文件1.jpg',
                        fileSize: 1024 * 100,
                    },
                    {
                        url: 'https://via.placeholder.com/150/FF0000/FFFFFF?text=File2',
                        fileName: '示例文件2.jpg',
                        fileSize: 1024 * 200,
                    },
                    {
                        url: 'https://via.placeholder.com/150/00FF00/FFFFFF?text=File3',
                        fileName: '示例文件3.jpg',
                        fileSize: 1024 * 150,
                    },
                    {
                        url: 'https://via.placeholder.com/150/FFFF00/000000?text=File4',
                        fileName: '示例文件4.jpg',
                        fileSize: 1024 * 180,
                    },
                    {
                        url: 'https://via.placeholder.com/150/FF00FF/FFFFFF?text=File5',
                        fileName: '示例文件5.jpg',
                        fileSize: 1024 * 120,
                    },
                    {
                        url: 'https://via.placeholder.com/150/00FFFF/000000?text=File6',
                        fileName: '示例文件6.jpg',
                        fileSize: 1024 * 160,
                    },
                    {
                        url: 'https://via.placeholder.com/150/808080/FFFFFF?text=File7',
                        fileName: '示例文件7.jpg',
                        fileSize: 1024 * 140,
                    },
                    {
                        url: 'https://via.placeholder.com/150/800080/FFFFFF?text=File8',
                        fileName: '示例文件8.jpg',
                        fileSize: 1024 * 190,
                    },
                ];

                this.fileList = [...mockFiles];
                this.fileList2 = [...mockFiles.slice(0, 5)];
                this.fileList3 = [...mockFiles.slice(0, 6)];
                this.fileList5 = [...mockFiles];
            },

            clearFiles() {
                this.fileList = [];
                this.fileList2 = [];
                this.fileList3 = [];
                this.fileList5 = [];
            },

            showFileInfo() {
                this.showInfo = !this.showInfo;
            },
        },
    };
</script>

<style lang="scss" scoped>
.example-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;

    h2 {
        margin-bottom: 20px;
        color: #333;
    }

    .examples-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin: 20px 0;

        .example-item {
            padding: 16px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f9f9f9;

            h3 {
                margin-bottom: 12px;
                font-size: 14px;
                color: #666;
            }
        }
    }

    .controls {
        margin: 20px 0;
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
    }

    .file-info {
        margin-top: 20px;
        padding: 16px;
        background: #f5f5f5;
        border-radius: 8px;
        border: 1px solid #ddd;

        h3 {
            margin-bottom: 12px;
            color: #333;
        }

        pre {
            background: white;
            padding: 12px;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
            overflow-x: auto;
            font-size: 12px;
            line-height: 1.4;
        }
    }
}
</style>

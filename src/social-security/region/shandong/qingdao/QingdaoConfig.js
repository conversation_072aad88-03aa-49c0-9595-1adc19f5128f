/*
 * <AUTHOR>
 * @DateTime 2023-04-10 15:51:34
 */
import ShandongConfig from '../ShandongConfig';

export default class QingdaoConfig extends ShandongConfig {
    isEnableReimbursement = true; //是否展示运营报表的收费日月报的收费方式的外诊报销 - 青岛
    isShandongQingdao = true; //是否是山东青岛
    isNotIncludeRefund = true; // 是否在对总账的时候，不需要包含退费交易
    isCheckingExcludeNoFundPay = true; // 是否总额对账排除入账为0的数据，江西地区默认true
    exportPathByPsnType = '/api/v2/shebao-stat/qingdao/approve/export'; // 按人员类别导出接口
    isEnableOperateReportBudgetTarget = true; // 是否展示运营报表的收费日月报的预算指标模块
}

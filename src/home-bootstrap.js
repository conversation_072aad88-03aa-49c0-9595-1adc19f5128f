import Vue from 'vue';
import './abc-ui-regist';
import components from './components/';
import * as directives from './directive'; // 全局vue directives
import * as filters from './filters'; // 全局vue filter
import Home from './Home.vue';
import router from './router/home';
import store from './store';
import { watchUserClinicChange } from './views/common/login-optimize';
import 'core-js/stable'; // polyfill
import 'regenerator-runtime/runtime'; // polyfill
import lifecycle from '@/lifecycle';
import { BaseApp } from '@/core/index.js';

console.info('构建时间：', process.env.buildInfo && process.env.buildInfo.BUILD_TIME);


class HomeApp extends BaseApp {
    onInit() {
        lifecycle.beforeCreate();
        watchUserClinicChange();
        // 注册全局 filter
        Object.keys(filters).forEach((key) => {
            Vue.filter(key, filters[ key ]);
        });

        // 注册全局指令
        Object.keys(directives).forEach((key) => {
            Vue.directive(key, directives[ key ]);
        });

        // 全局组件注册
        Object.keys(components).forEach((key) => {
            Vue.component(key,components[key]);
        });

        Vue.cancel = [];
        router.beforeEach((to, from, next) => {
            while (Vue.cancel.length > 0) {
                Vue.cancel.pop()('cancel');
            }
            next();
        });

        //是否客户端环境判断，注册window.ipcRendererInit方法，在客户端环境完成初始化后，会调用
        window.ipcRendererInit = () => {
            store.commit('SET_ELECTRON', true);
        };
        if (window.electronFlag) {
            window.ipcRendererInit();
        }
    }
    onBoot() {
        this.store = store;
        this.router = router;
        this.vm = window._vue = new Vue({
            router,
            store,
            render: (h) => h(Home),
        });
        this.vm.$mount('#app');
    }
}
new HomeApp(Vue, {
    enableOrderCloudDaemon: false,
}).boot();


[TOC]

## 安装

> yarn add abc-micro-frontend



## 使用

### 主工程

```javascript
import { ABCPlatform } from "abc-micro-frontend";

const options = {Vue, VueRouter, store, fetch, moduleConfig};

// 调用之后，将会挂载 Vue.prototype.$abcPlatoform；此后可在 Vue 组件中通过 this.$abcPlatform 访问，同时 options 中的内容将会挂载在 this.$abcPlatform.context 中
Vue.use(ABCPlatform, options);

const vm = new Vue({...});

// 注册全局组件，提供给子工程使用
vm.$abcPlatform.registerGlobalComponents('Schedules', Schedules);
// 注册基础服务
vm.$abcPlatform.registerService(Service);
// 启动主工程
vm.$boot(vm);
```

AbcPlatform 提供了插件安装形式，调用 `Vue.use` 进行安装即可，安装后，有两种方式可以访问到 platform 实例：

- Vue 组件中：this.$abcPlatform
- 全局：window.$platform

安装时，传入的 options，除了框架初始化外，会挂载到 platform.context 中，可以按需灵活使用

### 子工程

```javascript
import { ABCModule } from 'abc-micro-frontend';
import provide from './provide';
new AbcModule({
  domain: 'mall', // 子工程唯一 domain
  provide, // 将该子工程下的功能提供给主工程/其他子工程调用
  store, // 子工程的 Vuex，会动态注册到主工程的 Vuex 中，命名空间为：mall
  cssNamespace: 'b2b-mall-module', // 挂载子工程后，根节点使用此 css
  
  create({Vue, VueRouter, components, store}) {
    	return new Vue({
        router: router,
        store: store,
        render: (h) => h(App),
      });
  },
  
  mount(vm, container) {
    vm.$mount(container);
  },
  
  unmount(vm) {},
  
  destroy(vm) {},
})
```



## 最佳实践

### 子模块提供功能给主工程/其他子工程

```javascript
// 子工程提供功能，通过 provide 属性

// provide/index.js
import Message from '@module/components/message';
export default {
    service: {
        message(options) {
            return Message(options);
        },
    },
};

// app.js
import provide from 'provide/index';
new AbcModule({
  domain: 'mall',
  provide,
  ...
})

```



```javascript
// 主工程 or 其他子工程调用
async showMallMessage() {
    // 获取 module，返回的一个 Promise，因为可能该 Module 还没有加载，需要异步加载，交互上可以加一个 Loading 用于更好的用户体验
    const mall = await this.$abcPlatform.module.mall;

    // 返回的 mall 对象即为 new AbcModule 时传入的 provide 对象
    const message = mall.service.message({
        messageType: 'success',
        title: '嘻嘻嘻',
        onClose: () => {
            this.$Toast({
                message: '哈哈哈'
            })
        }
    });
  
    setTimeout(() => {
        message.close();
    }, 3000);
}
```





## API



### registerService

注册 Service，用于提供基础能力。注册后，子模块可通过 `this.$abcPlatform.service.xxx 进行访问`，例如：

```javascript
const serviceMap = {
	wallet: {
		payOrder(id, data) {}
	}
}
// 注册服务
this.$abcPlatform.registerService(serviceMap);

// 调用服务
this.$abcPlatform.service.wallet.payOrder(id, data);
```

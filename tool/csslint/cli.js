const fs = require('fs');

const chalk = require('chalk');

const path = require('path');
const _ = require('lodash');
const { parse } = require('@vue/compiler-sfc');
const scssParse = require('postcss-scss/lib/scss-parse');

const Err = chalk.bold.hex('#F7FDB6');
const Path = chalk.underline.bold;
const Warning = chalk.hex('#FFA500');
const Tip = chalk.hex('#737677');

const map = new Map([
    ['#005ed9', '$theme1/$B1'],
    ['#0090ff', '$theme2'],
    ['#459eff', '$theme3'],
    ['#c6e2ff', '$theme4'],
    ['#a2cfff', '$theme5'],
    ['#80bdff', '$theme6'],

    ['#2680f7', '$B2/$S3'],
    ['#5199f8', '$B3'],
    ['#e9f2fe', '$B4'],
    ['#d4e6fd', '$B5'],
    ['#85baff', '$B6'],

    ['#000000', '$S1/$T1'],
    ['#ffffff', '$S2'],
    ['#385068', '$S4'],

    ['#7a8794', '$T2'],
    ['#aab4bf', '$T3'],

    ['#d9dbe3', '$P1/$P1'],
    ['#dadbe0', '$P3'],
    ['#eff3f6', '$P4'],
    ['#f5f7fb', '$P5'],
    ['#e6eaee', '$P6'],

    ['#e52d5b', '$R1'],
    ['#f36', '$R2'],
    ['#ff5b84', '$R3'],
    ['#ffeaef', '$R4'],
    ['#ffd6e0', '$R5'],

    ['#e5892d', '$Y1'],
    ['#f93', '$Y2'],
    ['#ffad5b', '$Y3'],
    ['#fff4ea', '$Y4'],
    ['#ffebd6', '$Y5'],

    ['#08a446', '$G1'],
    ['#1EC761', '$G2'],
    ['#23cf67', '$G3'],
    ['#e3fced', '$G4'],
    ['#bbf2d1', '$G5'],

    ['#e65f20', '$01'],
    ['#ff793b', '$02'],
    ['#ff9563', '$03'],

    ['#fd9800', '$C1'],
    ['#0a8cea', '$C2'],
    ['#67ce0e', '$C3'],
    ['#ff6464', '$C4'],
    ['#fec166', '$C5'],
    ['#6cbaf2', '$C6'],
]);


const cli = {
    async execute(args) {
        console.log('execute!!!!!')

        let error = [];
        // 分组css 文件 和 vue 文件
        const fileKeys = args;
        const groupFileKeys = _.groupBy(fileKeys, (key) => {
            return _.split(key, '.')[1];
        });

        const scssFiles = _.reduce(groupFileKeys.scss, (res, cur) => {
            // 处理scss文件
            res[cur] = scssParse(fs.readFileSync(cur, 'utf-8'));
            return res;
        }, {});

        const vueFiles = _.reduce(groupFileKeys.vue, (res, cur) => {
            // 处理vue 文件的style； 转buffer
            const { styles } = parse(
                fs.readFileSync(path.resolve(cur), 'utf-8'),
            ).descriptor;

            const buffers = styles.map((o) => {
                return scssParse(o.content);
                // return Buffer.from(o.content, 'utf-8')
            });

            res[cur] = buffers;

            return res;
        }, {});

        Object.keys(scssFiles).forEach((key) => {
            scssFiles[key].walkDecls((decl) => {
                for (const k of map.keys()) {
                    if (decl.value && decl.value.includes(k)) {
                        error.push(
                            `${Path(key)} \n ${Warning('warning')}     ${decl.parent.selector} - ${decl.prop}:     the value ${Tip(decl.value)} in theme - ${Tip(map.get(k))} \n\n`);
                    }
                }
            });
        });

        Object.keys(vueFiles).forEach((key) => {
            vueFiles[key].forEach((b) => {
                b.walkDecls((decl) => {
                    for (const k of map.keys()) {
                        if (decl.value && decl.value.includes(k)) {
                            error.push(
                                `${Path(key)} \n ${Warning('warning')}     ${decl.parent.selector} - ${decl.prop}:     the value ${Tip(decl.value)} in theme - ${Tip(map.get(k))} \n\n`);
                        }
                    }
                });

            });
        });


        if (error.length > 0) {
            error.forEach((err) => {
                console.log(err);
            });

            console.log(Err(`👉   ${error.length}  problem  (请根据提示修改)`));
        }

        return 0
    }
}

module.exports = cli

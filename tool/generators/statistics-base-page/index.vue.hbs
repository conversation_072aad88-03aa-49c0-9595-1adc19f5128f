<template>
    <div class="common-padding-container">
        <stat-toolbar
            ref="statToolbarRef"
            :clinic-id-filter.sync="params.clinicId"
            :enable-features="toolbarFeatures"
            :patient-width="130"
            :date-filter.sync="params.dateFilter$"
            :handle-export="handleExport"
            :export-task-type="exportTaskType"
            @change-date="handleDateChange"
             @change-clinic="handleClinicChange">
            <filter-select
                v-model="params.departmentId"
                clearable
                placeholder=""
                :width="120"
                :options="departmentsOptions"
                @change="getTableData">
            </filter-select>
            <stat-dimension-picker
                slot="right"
                v-model="params.dimension"
                :options="dimensionOptions"
                @change="handleDimensionChange"></stat-dimension-picker>
        </stat-toolbar>
        <pro-statistics-table
            :loading="loading"
            :data="tableData"
            :max-height="calcStatTableInfo && calcStatTableInfo.maxHeight"
            :min-height="calcStatTableInfo && calcStatTableInfo.minHeight"
            :header="tableRenderHeader"
            :pagination-params="params"
            :total-count="totalCount"
            @current-change="handlePageIndexChange"></pro-statistics-table>
    </div>
</template>

<script>
    import ClinicTypeJudger from 'views/statistics/statUtils/clinicTypeJudger';
    import PickerOptions from 'views/common/pickerOptions';
    import StatToolbar from 'views/statistics/common/stat-toolbar/stat-toolbar';
    import DateParamsMixins from 'views/statistics/mixins/date-params-mixin';
    import StatDimensionPicker from 'views/statistics/common/stat-dimension-picker/stat-dimension-picker';
    import {
        mapGetters,
    } from 'vuex';
    import ExportService from 'views/statistics/core/services/export/export-service';
    import {
        calcStatTablePageSize, HeaderHeightEnum,
    } from 'utils/statistic.js';
    import FilterSelect from 'views/layout/filter-select/index.vue';
    import TableUtilsMixin from 'views/statistics/mixins/table-utils-mixin';
    import RevenueAPI from 'views/statistics/core/api/revenue.js';
    import ProStatisticsTable from 'views/statistics/common/pro-statistics-table/index.vue';
    import { isEqual } from 'utils/lodash';

    export default {
        components: {
            StatToolbar,
            StatDimensionPicker,
            FilterSelect,
            ProStatisticsTable,
        },
        mixins: [ClinicTypeJudger, PickerOptions, DateParamsMixins, TableUtilsMixin],
        data() {
            return {
                params: {
                    dimension: 'employee',
                    clinicId: '',
                    departmentId: '',
                    employeeId: '',
                    pageIndex: 0,
                    pageSize: 12,
                },
                dimensionOptions: [
                    {
                        label: 'employee', name: '人员',
                    },
                    {
                        label: 'product', name: '项目',
                    },
                ],
                loading: false,
                tableHeader: [],
                tableData: [],
                totalCount: 0,
                exportTaskType: '',
                calcStatTableInfo: {},
                totalInfo: '',
                departmentsOptions: [],
            };
        },

        computed: {
            ...mapGetters(['isSingleStore']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            toolbarFeatures() {
                const features = [StatToolbar.Feature.DATE, StatToolbar.Feature.EXPORT, StatToolbar.Feature.CLINIC];
                return features;
            },
            tableRenderHeader() {
                return this.resolveHeader(this.tableHeader, {});
            },
            queryClinicId() {
                if (this.isChainAdmin) {
                    return this.params.clinicId;
                }
                return this.isSingleStore ? '' : this.currentClinic?.clinicId;
            },
        },

        created() {
            this.exportService = new ExportService();
            this.getTableData();
        },
        methods: {
            handlePageIndexChange(index) {
                this.params.pageIndex = index - 1;
                this.getTableData(false);
            },

            handleDateChange() {
                this.getTableData();
            },

            getTableParams() {
                const {
                    dateFilter$: {
                        begin: beginDate, end: endDate,
                    },
                    pageIndex,
                    pageSize,
                    employeeId,
                    departmentId,
                } = this.params;
                const offset = pageIndex * pageSize;
                const baseParams = {
                    beginDate,
                    clinicId: this.queryClinicId,
                    endDate,
                    offset,
                    limit: pageSize,
                    departmentId,
                    employeeId,
                };
                return baseParams;
            },

            handleClinicChange() {
                this.getTableData();
            },
            async handleExport() {
                const {
                    patientId, dateFilter$: {
                        begin: beginDate, end: endDate,
                    },
                } = this.params;

                try {
                    await this.exportService.startExport(this.exportTaskType, {
                        patientId,
                        beginDate,
                        clinicId: this.queryClinicId,
                        endDate,
                    });
                } catch (e) {
                    console.error(e);
                    return false;
                }
                return true;
            },

            handleDimensionChange() {
                this.fetchClinicDepartments();
                this.getTableData();
            },

            async getTableData(resetPageParams = true) {
                await this.$nextTick();
                const defaultHeaderHeight = HeaderHeightEnum.ONE_ROW;
                this.calcStatTableInfo = calcStatTablePageSize(defaultHeaderHeight);

                this.params.pageSize = this.calcStatTableInfo.pageSize;

                if (resetPageParams) {
                    this.params.pageIndex = 0;
                    this.params.pageSize = this.calcStatTableInfo.pageSize;
                }

                const params = this.getTableParams();


                this.loading = true;
                if (this.showEmployeeTab) {
                    try {
                        const {
                            data,
                        } = await RevenueAPI.hospitalRevenue.employee({
                            ...params,
                        });
                       if (isEqual(params, this.getTableParams())) {
                            this.setTableData(false, data, resetPageParams);
                        }
                    } catch (err) {
                        this.loading = false;
                        console.log(err);
                        this.setTableData(true);
                    } finally {
                        this.loading = false;
                    }
                } else {
                    try {
                        const {
                            data,
                        } = await RevenueAPI.hospitalRevenue.product({
                            ...params,
                        });
                        this.setTableData(false, data, resetPageParams);
                    } catch (err) {
                        this.setTableData(true);
                        console.log(err);
                    } finally {
                        this.loading = false;
                    }
                }
            },
        },
    };
</script>

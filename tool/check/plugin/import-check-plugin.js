const { declare } = require('@babel/helper-plugin-utils');
const chalk = require('chalk');
const loggerError = chalk.bold.hex('#F56C6C');
const path = require('path');

const normalizePath = (filename, importValue) => {
    return path.resolve(filename.split('/').slice(0, -1).join('/'), importValue);
};

const ImportCheckPlugin = declare((api, options) => {
    api.assertVersion(7);

    return {
        pre(file) {
            file.set('errors', []);
        },

        visitor: {
            ImportDeclaration(p, state) {
                const errors = state.file.get('errors');
                const { dependencies, alias, filename, CHECK_DIR } = options;
                const { node } = p;
                const importValue = node.source.value;

                // 允许引入第三方包
                if (dependencies.some(dep => importValue.startsWith(dep))) {
                    return;
                }

                const isAliasATImport = alias.some(alias => importValue.startsWith('@/'));
                const isAliasImport = alias.some(alias => importValue.startsWith(alias));
                const isRelativeImport = importValue.startsWith('.');
                const newImportValue = isRelativeImport ? normalizePath(filename, importValue) : importValue;

                // 如果是别名引入 - 非common目录需要加入错误
                if ((isAliasATImport && !importValue.startsWith('@/common')) ||
                    (isAliasImport && !isAliasATImport && !importValue.includes(CHECK_DIR)) ||
                    // 如果是相对路径引入 - 非common目录需要加入错误
                    (isRelativeImport && !newImportValue.includes(CHECK_DIR))) {
                    errors.push({
                        message: `Importing from 【${importValue}】 is not allowed`,
                        line: node.loc.start.line,
                        filename
                    });
                }
            }
        },

        post(file) {
            const errors = file.get('errors');

            if (errors.length) {
                errors.forEach(error => {
                    console.log(loggerError(`👉👉👉  ${error.message}
                        at line ${error.line}
                        in ${error.filename}`));
                });

                throw new Error('Import check failed');
            }
        }
    };
});

module.exports = {
    ImportCheckPlugin
};

#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const { transformFromAstSync } = require('@babel/core');
const parser = require('@babel/parser');

const { ImportCheckPlugin } = require('./plugin/import-check-plugin');
const { getStagedFiles } = require('../csslint/help');

const pkg = fs.readFileSync(path.resolve(__dirname, '../../package.json'), 'utf-8');
const dependencies = Object.keys(JSON.parse(pkg).dependencies);
const alias = [
    '@/',
    'src',
    'assets',
    'components',
    'views',
    'styles',
    'theme',
    'api',
    'utils',
    'store',
    'router',
    'mock',
    'static',
    '@ohif-core',
    '@modules',
    '@module',
    '@social',
    '@social-mock',
];

function onFatalError(e) {
    console.error(e.message);
    process.exitCode = 2;
}

const CHECK_DIR = 'src/common/';

(async function main() {
    const list = await getStagedFiles();

    const needCheckFiles = list.filter((file) => {
        return file.indexOf(CHECK_DIR) > -1 && file.endsWith('.js');
    });

    if (!needCheckFiles.length) {
        console.log('no need check files');
        return;
    }

    for (const file of needCheckFiles) {
        const sourceCode = fs.readFileSync(file, 'utf-8');

        const ast = parser.parse(sourceCode, {
            sourceType: 'unambiguous',
        });

        try {
            transformFromAstSync(ast, sourceCode, {
                plugins: [[ImportCheckPlugin, {
                    dependencies,
                    alias,
                    filename: file,
                    CHECK_DIR,
                }]],
            });
        } catch (e) {
            onFatalError(e);
        }
    }
}()).catch(onFatalError);

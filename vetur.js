const fs = require('fs');
const path = require('path');
const { parseSource } = require('vue-docgen-api');
// const { convert } = require('./vscode-vetur/index.js');


const resolve = (p) => {
    return path.resolve(__dirname, p);
};
function createJsonFile(tags, dist) {
    const json = {
        $schema: 'https://json.schemastore.org/web-types',
        framework: 'vue',
        contributions: {
            html: {
                'types-syntax': 'typescript',
                tags,
            },
        },
    };

    fs.writeFileSync(dist, JSON.stringify(json, null, 4));
}
const components = require(path.join(__dirname, 'src/components-composite/components.json'));
const base = 'src/components-composite';
const sourceRoot = '@/components-composite';
function resolveSourcePath(componentPath) {
    return componentPath.replace(base, sourceRoot);
}

const customComponents = [
    'biz-setting-form',
    'biz-setting-form-layout',
];

const customMap = {
    'src/components-composite/setting-form/src/views/index.vue': 'biz-setting-form',
    'src/components-composite/setting-form/src/views/group.vue': 'biz-setting-form-group',
    'src/components-composite/setting-form/src/views/header.vue': 'biz-setting-form-header',
    'src/components-composite/setting-form/src/views/indent.vue': 'biz-setting-form-item-indent',
    'src/components-composite/setting-form/src/views/item.vue': 'biz-setting-form-item',
    'src/components-composite/setting-form/src/views/tip.vue': 'biz-setting-form-item-tip',

    'src/components-composite/setting-form-layout/src/views/index.vue': 'biz-setting-form-layout',
    'src/components-composite/setting-form-layout/src/views/content.vue': 'biz-setting-form-content',
    'src/components-composite/setting-form-layout/src/views/fill-remain-height.vue': 'biz-fill-remain-height',
    'src/components-composite/setting-form-layout/src/views/footer.vue': 'biz-setting-form-footer',
    'src/components-composite/setting-form-layout/src/views/sidebar.vue': 'biz-setting-form-sidebar',
};

const dirs = Object.keys(components).map((key) => {
    return {
        dir: key,
    };
});


function readFileList(dir, fileList = []) {
    const files = fs.readdirSync(dir);
    files.forEach((item) => {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        if (stat.isDirectory()) {
            readFileList(path.join(dir, item), fileList);
        } else if (fullPath.endsWith('.vue')) {
            fileList.push({
                fullPath,
                content: fs.readFileSync(fullPath, 'utf-8'),
            });
        }
    });
    return fileList;
}

async function start() {
    const files = dirs.reduce((res, cur) => {
        if (customComponents.includes(cur.dir)) {
            if (cur.dir === 'biz-setting-form') {
                const settingFormFiles = readFileList(path.join(__dirname, `${base}/setting-form/src/views`));
                res = [...res, ...settingFormFiles];
            } else if (cur.dir === 'biz-setting-form-layout') {
                const settingFormLayoutFiles = readFileList(path.join(__dirname, `${base}/setting-form-layout/src/views`));
                res = [...res, ...settingFormLayoutFiles];
            } else {
                const extraFiles = readFileList(path.join(__dirname, `${base}/${cur.dir}/src`));
                res = [...res, ...extraFiles];
            }

        } else {
            const fullPath = `${base}/${cur.dir}/src/views/index.vue`;

            res.push({
                name: `${cur.dir}`,
                content: fs.readFileSync(resolve(fullPath), 'utf-8'),
                fullPath,
            });
        }

        return res;
    }, []);

    const tagPromises = files
        .filter((file) => file.name || customMap[file.fullPath])
        .map(async (file) => {

            const doc = await parseSource(file.content, file.fullPath, {
                alias: {
                    packages: resolve('./packages'),
                    style: resolve('./style'),
                    theme: resolve('./packages/theme'),
                },
            });

            return {
                name: file.name || customMap[file.fullPath],
                description: doc && doc.description,
                attributes: doc.props?.map((prop) => ({
                    name: prop.name,
                    required: prop.required,
                    description: prop.description,
                    value: {
                        kind: 'expression',
                        type: prop.type?.name ?? 'any',
                    },
                    values: prop.values,
                    default: prop.defaultValue?.value,
                })),
                events: doc.events?.map((event) => ({
                    name: event.name,
                    description: event.description,
                })),
                slots: doc.slots?.map((slot) => ({
                    name: slot.name,
                    description: slot.description,
                })),
                source: {
                    module: resolveSourcePath(file.fullPath),
                    symbol: doc.exportName,
                },
            };
        });

    const tags = await Promise.all(tagPromises);

    createJsonFile(tags, path.join(__dirname, `${base}/web-types.json`));

    console.log('生成types 成功！！！');
}


(async function () {
    try {
        await start();
        // await convert();
    } catch (e) {
        console.error(e);
    }
}());





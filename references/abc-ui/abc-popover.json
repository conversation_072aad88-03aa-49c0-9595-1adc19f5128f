{"name": "AbcPopover", "description": "弹出框组件，用于在鼠标悬停或点击时显示隐藏的内容。", "usage": "<script>\nimport { AbcPopover } from '@abc/ui-pc'\n\nexport default {\n  components: {\n    AbcPopover\n  }\n}\n</script>\n\n<template>\n  <abc-popover width=\"348px\" placement=\"top-start\" trigger=\"hover\" theme=\"white\">\n    <span slot=\"reference\">\n      hover\n    </span>\n    <div>\n      该成员已接受你的邀请，需对他的信息进行确认并完善后，他才能正式加入门店\n    </div>\n  </abc-popover>\n</template>\n"}
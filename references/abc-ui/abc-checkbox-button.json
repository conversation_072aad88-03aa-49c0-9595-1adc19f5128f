{"name": "AbcCheckboxButton", "description": "一个复选按钮组件，用于多选操作。", "usage": "<script>\nimport { AbcCheckboxButton, AbcSpace, AbcDescriptions, AbcDescriptionsItem } from '@abc/ui-pc'\n\nexport default {\n  components: {\n    AbcCheckboxButton,\n    AbcSpace,\n    AbcDescriptions,\n    AbcDescriptionsItem\n  },\n  data() {\n    return {\n      variantValue: 0,\n      variantValue2: 0,\n      themeValue: 0,\n      themeValue2: 0\n    };\n  }\n}\n</script>\n\n<template>\n  <AbcDescriptions :column=\"1\">\n    <AbcDescriptionsItem label=\"variant\">\n      <AbcSpace>\n        <AbcCheckboxButton v-model=\"variantValue\" type=\"number\">\n          默认\n        </AbcCheckboxButton>\n        <AbcCheckboxButton v-model=\"variantValue\" type=\"number\" statistics-number=\"2\">\n          默认\n        </AbcCheckboxButton>\n        <AbcCheckboxButton v-model=\"variantValue\" type=\"number\" statistics-number=\"0\">\n          默认\n        </AbcCheckboxButton>\n        <AbcCheckboxButton v-model=\"variantValue2\" type=\"number\" variant=\"plain\">\n          加工\n        </AbcCheckboxButton>\n      </AbcSpace>\n    </AbcDescriptionsItem>\n\n    <AbcDescriptionsItem label=\"theme\">\n      <AbcSpace>\n        <AbcCheckboxButton v-model=\"themeValue\" type=\"number\">\n          默认\n        </AbcCheckboxButton>\n        <AbcCheckboxButton v-model=\"themeValue\" type=\"number\" statistics-number=\"2\">\n          默认\n        </AbcCheckboxButton>\n        <AbcCheckboxButton theme=\"dark\" v-model=\"themeValue2\" type=\"number\" statistics-number=\"2\">\n          dark\n        </AbcCheckboxButton>\n      </AbcSpace>\n    </AbcDescriptionsItem>\n  </AbcDescriptions>\n</template>\n"}
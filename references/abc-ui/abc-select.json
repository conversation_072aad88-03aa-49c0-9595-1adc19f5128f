{"name": "AbcSelect", "description": "下拉选择器，用于从下拉列表中选择一个或多个选项。", "usage": "<script>\nimport { AbcSelect, AbcOption } from '@abc/ui-pc'\n\nexport default {\n  components: {\n    AbcSelect,\n    AbcOption\n  },\n  data() {\n    return {\n      patientSex: ''\n    };\n  }\n}\n</script>\n\n<template>\n  <abc-select v-model=\"patientSex\" placeholder=\"请选择性别\" size=\"large\">\n    <abc-option value=\"男\" label=\"男\"></abc-option>\n    <abc-option value=\"女\" label=\"女\"></abc-option>\n  </abc-select>\n</template>"}
{"name": "AbcEditDiv", "description": "一个可编辑的文本区域组件，支持多种配置选项。", "usage": "<script>\nimport { AbcEditDiv } from '@abc/ui-pc'\n\nexport default {\n  components: {\n    AbcEditDiv\n  },\n  data() {\n    return {\n      comment: '',\n      size: '',\n      isDisabled: false,\n      responsive: false,\n      isAutoHeight: false,\n      isOrder: false\n    };\n  }\n}\n</script>\n\n<template>\n  <abc-edit-div v-model=\"comment\" :responsive=\"responsive\" :auto-height=\"isAutoHeight\" :size=\"size\" :disabled=\"isDisabled\" :is-order=\"isOrder\" :maxlength=\"100\" placeholder=\"请输入取消入库的原因\" />\n</template>\n"}
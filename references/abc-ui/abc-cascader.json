{"name": "AbcCascader", "description": "一个级联选择器组件，用于选择多级数据。", "usage": "<script>\nimport { AbcCascader } from '@abc/ui-pc'\n\nexport default {\n  components: {\n    AbcCascader\n  },\n  data() {\n    return {\n      options: [],\n      value: []\n    };\n  }\n}\n</script>\n\n<template>\n  <AbcCascader\n    v-model=\"value\"\n    :options=\"options\"\n    :props=\"{\n      children: 'childs',\n      label: 'name',\n      value: 'id'\n    }\"\n    placeholder=\"请输入\"\n    :width=\"200\"\n  />\n</template>\n"}
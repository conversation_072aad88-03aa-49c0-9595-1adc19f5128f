{"name": "AbcDialog", "description": "一个对话框组件，用于展示弹出式内容。", "usage": "<script>\nimport { AbcDialog, AbcButton, AbcSelect, AbcOption } from '@abc/ui-pc'\n\nexport default {\n  components: {\n    AbcDialog,\n    AbcButton,\n    AbcSelect,\n    AbcOption\n  },\n  data() {\n    return {\n      showDialog: false,\n      dialogSize: 'default',\n      dialogSizeOptions: ['default', 'small', 'middle', 'large', 'huge', 'hugely']\n    };\n  },\n  methods: {\n    handleClose() {\n      this.showDialog = false;\n    }\n  }\n}\n</script>\n\n<template>\n  <div style=\"min-height: 400px\">\n    <abc-select :width=\"120\" v-model=\"dialogSize\">\n      <abc-option v-for=\"it in dialogSizeOptions\" :label=\"it\" :value=\"it\"></abc-option>\n    </abc-select>\n    <button @click=\"showDialog = true\">打开弹窗</button>\n    <AbcDialog v-model=\"showDialog\" title=\"费用预览\" append-to-body :auto-focus=\"false\" tabindex=\"-1\" responsive size=\"hugely\" content-styles=\"padding: 0\" @close-dialog=\"handleClose\">\n      <div>this is content</div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <abc-button>确定</abc-button>\n        <abc-button type=\"blank\" @click=\"showDialog = false\">取消</abc-button>\n      </div>\n    </AbcDialog>\n  </div>\n</template>\n"}
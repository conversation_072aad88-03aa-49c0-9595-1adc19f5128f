{"name": "AbcCard", "description": "一个卡片组件，用于展示内容和操作。", "usage": "<script>\nimport { AbcCard, AbcLayout, AbcLayoutContent, AbcSection, AbcTitle, AbcP } from '@abc/ui-pc'\n\nexport default {\n  components: {\n    AbcCard,\n    AbcLayout,\n    AbcLayoutContent,\n    AbcSection,\n    AbcTitle,\n    AbcP\n  }\n}\n</script>\n\n<template>\n  <AbcCard>\n    <AbcLayout>\n      <AbcLayoutContent>\n        <AbcSection>\n          <AbcTitle level=\"1\">优惠券功能介绍</AbcTitle>\n          <AbcTitle :bold=\"false\">为什么使用优惠券？</AbcTitle>\n          <AbcP gray>发放优惠券，可提升顾客复购率</AbcP>\n          <AbcP gray>设置免费领取的优惠券，可吸引新顾客</AbcP>\n          <AbcP gray>设置优惠券使用门槛，可提升客单价</AbcP>\n        </AbcSection>\n        <AbcSection>\n          <AbcTitle>优惠券如何发放？</AbcTitle>\n          <AbcP>设置为可免费领取的优惠券，顾客可在 [微诊所] - [我的优惠券] 中自行领取</AbcP>\n          <AbcP>可结合满赠活动，消费满足一定金额赠送优惠券，提升顾客复购率</AbcP>\n          <AbcP>可结合满赠活动，消费满足一定金额赠送优惠券，提升顾客复购率</AbcP>\n        </AbcSection>\n        <AbcSection>\n          <AbcTitle level=\"1\">为什么使用优惠券？</AbcTitle>\n          <AbcP>发放优惠券，可提升顾客复购率</AbcP>\n        </AbcSection>\n        <AbcSection>\n          <AbcTitle level=\"3\">为什么使用优惠券？</AbcTitle>\n          <AbcP gray small>发放优惠券，可提升顾客复购率</AbcP>\n        </AbcSection>\n      </AbcLayoutContent>\n    </AbcLayout>\n  </AbcCard>\n</template>\n"}
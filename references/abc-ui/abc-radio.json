{"name": "AbcRadio", "description": "单选框组件，用于在多个选项中选择一个。", "usage": "<script>\nimport { AbcRadio, AbcRadioGroup } from '@abc/ui-pc'\n\nexport default {\n  components: {\n    AbcRadio,\n    AbcRadioGroup\n  },\n  data() {\n    return {\n      value: 0\n    };\n  }\n}\n</script>\n\n<template>\n  <abc-radio-group v-model=\"value\">\n    <abc-radio :label=\"0\">男</abc-radio>\n    <abc-radio :label=\"1\">女</abc-radio>\n  </abc-radio-group>\n</template>"}
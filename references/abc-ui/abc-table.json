{"name": "AbcTable", "description": "用于展示数据列表的表格组件。当表格带筛选栏时，需要配合 AbcLayout 使用，并将 AbcTable 放在 AbcLayoutContent 中，将筛选控件放在 AbcLayoutHeader 中", "usage": "<script>\nimport { AbcTable } from '@abc/ui-pc'\n\nexport default {\n  components: {\n    AbcTable\n  },\n  data() {\n    return {\n      renderConfig: {\n        list: [\n          { key: 'name', label: '诊疗项目' },\n          { key: 'describe', label: '介绍' }\n        ]\n      },\n      dataList: [\n        {\n          keyId: '1',\n          name: '自动计费项目',\n          describe: 'success'\n        },\n        {\n          keyId: '2',\n          name: '诊疗项目',\n          describe: 'warning'\n        }\n      ]\n    };\n  }\n}\n</script>\n\n<template>\n  <abc-table :render-config=\"renderConfig\" :data-list=\"dataList\"></abc-table>\n</template>"}
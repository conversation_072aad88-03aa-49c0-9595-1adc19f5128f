{"name": "AbcForm", "description": "一个表单组件，支持表单验证和提交。", "usage": "<script>\nimport { AbcForm, AbcFormItem, AbcInput, AbcButton, AbcTextarea } from '@abc/ui-pc'\n\nexport default {\n  components: {\n    AbcForm,\n    AbcFormItem,\n    AbcInput,\n    AbcButton,\n    AbcTextarea\n  },\n  data() {\n    return {\n      postData: {\n        taxNum: '',\n        taxName: '',\n        address: '',\n        telephone: '',\n        bankAgent: '',\n        bankAccount: '',\n        invoiceChecker: ''\n      },\n      itemNoMargin: false,\n      marginSize: 'default',\n      labelPosition: 'left-top'\n    };\n  },\n  methods: {\n    validateMobile(value, callback) {\n      if (!value) {\n        callback({\n          validate: true\n        });\n        return;\n      }\n      if (value.length !== 11 && value.length !== 8 && !((value.length === 9 || value.length === 12) && value.includes('-')))\n        callback({\n          validate: false,\n          message: '输入11位的手机号或正确格式的座机号'\n        });\n      else if (!/^1[3|4|5|6|7|8|9]\\d{9}$/.test(value) && !/^(?:0[1-9][0-9]{1,2}-?)?[2-8][0-9]{6,7}$/.test(value))\n        callback({\n          validate: false,\n          message: '手机/座机号格式不正确'\n        });\n      else\n        callback({\n          validate: true\n        });\n    },\n    submit() {\n      this.$refs.form.validate(valid => {\n        if (valid)\n          console.log('校验成功');\n        else\n          console.log('校验失败');\n      });\n    }\n  }\n}\n</script>\n\n<template>\n  <abc-form ref=\"form\" :label-position=\"labelPosition\" :label-width=\"110\" :item-no-margin=\"itemNoMargin\" :margin-size=\"marginSize\">\n    <abc-form-item label=\"纳税人识别号\" help=\"这里是help信息\">\n      <template slot=\"labelTips\">\n        <div>提示信息1</div>\n        <div>提示信息2</div>\n        <div>提示信息3</div>\n        <div>提示信息4</div>\n      </template>\n      <abc-input v-model=\"postData.taxNum\" :width=\"300\" readonly disabled></abc-input>\n    </abc-form-item>\n    <abc-form-item required label=\"企业名称\">\n      <template slot=\"labelTips\">\n        <div>提示信息</div>\n      </template>\n      <abc-input v-model=\"postData.taxName\" :width=\"300\"></abc-input>\n    </abc-form-item>\n    <abc-form-item required label=\"地址\" error-theme=\"inner\">\n      <abc-input v-model=\"postData.address\" :width=\"300\" placeholder=\"当设置error-theme为inner时，错误标记只是占位文字变黄\"></abc-input>\n    </abc-form-item>\n    <abc-form-item required label=\"电话\" :validate-event=\"validateMobile\">\n      <abc-input v-model=\"postData.telephone\" :width=\"300\"></abc-input>\n    </abc-form-item>\n    <abc-form-item label=\"开户行名称\">\n      <abc-input v-model=\"postData.bankAgent\" :width=\"300\"></abc-input>\n    </abc-form-item>\n    <abc-form-item label=\"开户行账号\">\n      <abc-input v-model=\"postData.bankAccount\" :width=\"300\"></abc-input>\n    </abc-form-item>\n    <abc-form-item required label=\"复核人\">\n      <abc-textarea v-model=\"postData.invoiceChecker\" :rows=\"3\" :width=\"300\"></abc-textarea>\n    </abc-form-item>\n    <abc-button class=\"confirm-btn\" @click=\"submit\">确定</abc-button>\n  </abc-form>\n</template>\n"}
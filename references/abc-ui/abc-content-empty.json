{"name": "AbcContentEmpty", "description": "一个内容为空的占位组件，用于在数据为空时展示。", "usage": "<script>\nimport { AbcContentEmpty } from '@abc/ui-pc'\n\nexport default {\n  components: {\n    AbcContentEmpty\n  }\n}\n</script>\n\n<template>\n  <div style=\"margin: 40px auto; display: flex\">\n    <div style=\"flex: 1\">\n      <AbcContentEmpty size=\"small\" value=\"这是small\"></AbcContentEmpty>\n    </div>\n    <div style=\"flex: 1\">\n      <AbcContentEmpty value=\"暂无数据\"></AbcContentEmpty>\n    </div>\n    <div style=\"flex: 1\">\n      <AbcContentEmpty size=\"large\" value=\"这是large\"></AbcContentEmpty>\n    </div>\n  </div>\n</template>\n"}
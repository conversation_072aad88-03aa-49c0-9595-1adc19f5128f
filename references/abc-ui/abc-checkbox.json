{"name": "AbcCheckbox", "description": "一个复选框组件，用于多选操作。", "usage": "<script>\nimport { AbcCheckbox, AbcCheckboxGroup, AbcSpace, AbcFlex } from '@abc/ui-pc'\n\nexport default {\n  components: {\n    AbcCheckbox,\n    AbcCheckboxGroup,\n    AbcSpace,\n    AbcFlex\n  },\n  data() {\n    return {\n      value: 1,\n      value2: false,\n      value3: true,\n      value4: false,\n      checked: 1\n    };\n  }\n}\n</script>\n\n<template>\n  <div>\n    <AbcSpace>\n      <AbcCheckbox v-model=\"value\" type=\"number\">\n        checkbox组件类型为number\n      </AbcCheckbox>\n      <AbcCheckbox v-model=\"value2\">\n        checkbox组件\n      </AbcCheckbox>\n      <AbcCheckbox v-model=\"value3\" shape=\"round\">\n        checkbox圆形组件\n      </AbcCheckbox>\n      <AbcCheckbox v-model=\"value4\" :no-border=\"true\">\n        checkbox圆形组件\n      </AbcCheckbox>\n    </AbcSpace>\n  </div>\n</template>\n"}
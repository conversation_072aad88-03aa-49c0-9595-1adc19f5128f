{"name": "AbcDropdown", "description": "一个下拉菜单组件，用于展示可选择的选项列表。", "usage": "<script>\nimport { AbcDropdown, AbcDropdownItem, AbcButton } from '@abc/ui-pc'\n\nexport default {\n  components: {\n    AbcDropdown,\n    AbcDropdownItem,\n    AbcButton\n  },\n  data() {\n    return {\n      curVal: 'outpatient'\n    };\n  },\n  methods: {\n    handleChange(val) {\n      this.curVal = val;\n    }\n  }\n}\n</script>\n\n<template>\n  <abc-dropdown @change=\"handleChange\">\n    <div slot=\"reference\">\n      <abc-button variant=\"ghost\">\n        {{ curVal }}\n      </abc-button>\n    </div>\n    <abc-dropdown-item label=\"门诊单\" value=\"outpatient\"></abc-dropdown-item>\n    <abc-dropdown-item label=\"收费单\" value=\"cashier\"></abc-dropdown-item>\n    <abc-dropdown-item label=\"发药单\" value=\"dispense\"></abc-dropdown-item>\n  </abc-dropdown>\n</template>\n"}
{"name": "AbcAutocomplete", "description": "一个自动完成组件，支持异步数据获取和自定义建议列表。", "usage": "<script>\nimport { AbcAutocomplete } from '@abc/ui-pc'\n\nexport default {\n  components: {\n    AbcAutocomplete\n  },\n  data() {\n    return {\n      value: '',\n      options: [\n        { name: 'bubble', age: 10 },\n        { name: '刘喜', age: 12 },\n        { name: '王富民', age: 13, disabled: true },\n        { name: '王二小', age: 14 },\n        { name: 'a', age: 15 },\n        { name: 'b', age: 16 },\n        { name: 'c', age: 16 },\n        { name: 'd', age: 22, disabled: true }\n      ]\n    };\n  },\n  methods: {\n    fetchData(key, callback) {\n      return callback(this.options.filter(item => item.name.includes(key)));\n    },\n    handleSelect(data) {\n      this.value = data.name;\n    }\n  }\n}\n</script>\n\n<template>\n  <AbcAutocomplete\n    v-model.trim=\"value\"\n    :fetch-suggestions=\"fetchData\"\n    @enterEvent=\"handleSelect\"\n  />\n</template>\n"}
{"name": "AbcGrid", "description": "栅格系统基于 24 栅格，将页面水平等分为 24 格，使用 AbcRow 和 AbcCol 结合，可以有效的保证页面的一致性、逻辑性。", "usage": "<script>\nimport { AbcRow, AbcCol } from '@abc/ui-pc'\n\nexport default {\n  components: {\n    AbcRow,\n    AbcCol\n  }\n}\n</script>\n\n<template>\n  <abc-row class=\"abc-grid-wrapper\" :gutter=\"[0, 'large']\" :wrap=\"true\">\n    <abc-col :span=\"24\">\n      <abc-row gutter=\"middle\">\n        <abc-col :span=\"24\">\n          <div class=\"abc-col-cell b1\">col-24</div>\n        </abc-col>\n      </abc-row>\n    </abc-col>\n    <abc-col :span=\"24\">\n      <abc-row gutter=\"middle\">\n        <abc-col :span=\"8\">\n          <div class=\"abc-col-cell b1\">col-8</div>\n        </abc-col>\n        <abc-col :span=\"8\">\n          <div class=\"abc-col-cell b2\">col-8</div>\n        </abc-col>\n        <abc-col :span=\"8\">\n          <div class=\"abc-col-cell b1\">col-8</div>\n        </abc-col>\n      </abc-row>\n    </abc-col>\n  </abc-row>\n</template>\n"}
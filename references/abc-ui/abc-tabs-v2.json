{"name": "AbcTabsV2", "description": "标签页组件，用于在一个容器中展示多个内容面板，每个面板对应一个标签。", "usage": "<script>\nimport { AbcTabsV2 } from '@abc/ui-pc'\n\nexport default {\n  components: {\n    AbcTabsV2\n  },\n  data() {\n    return {\n      currentTab: 2,\n      options: [\n        {\n          label: '标签1',\n          value: 1\n        },\n        {\n          label: '标签2',\n          value: 2\n        },\n        {\n          label: '标签3',\n          value: 3\n        }\n      ]\n    };\n  }\n}\n</script>\n\n<template>\n  <abc-tabs-v2 v-model=\"currentTab\" :option=\"options\"></abc-tabs-v2>\n</template>"}
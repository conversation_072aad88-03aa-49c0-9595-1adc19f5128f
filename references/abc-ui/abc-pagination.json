{"name": "AbcPagination", "description": "分页组件，用于将大量数据进行分页展示。", "usage": "<script>\nimport { AbcPagination } from '@abc/ui-pc'\n\nexport default {\n  components: {\n    AbcPagination\n  },\n  data() {\n    return {\n      pageParams: {\n        pageIndex: 0,\n        pageSize: 10,\n        count: 100\n      }\n    };\n  },\n  methods: {\n    handlePageChange(val) {\n      console.log('val', val);\n    }\n  }\n}\n</script>\n\n<template>\n  <abc-pagination :show-total-page=\"true\" :pagination-params=\"pageParams\" :count=\"pageParams.count\" @current-change=\"handlePageChange\">\n  </abc-pagination>\n</template>\n"}
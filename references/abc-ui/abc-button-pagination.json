{"name": "AbcButtonPagination", "description": "一个按钮分页组件，用于分页显示数据。", "usage": "<script>\nimport { AbcButtonPagination } from '@abc/ui-pc'\n\nexport default {\n  components: {\n    AbcButtonPagination\n  },\n  data() {\n    return {\n      paginationParams: {\n        totalCount: 100,\n        pageSize: 10,\n        pageIndex: 0\n      }\n    };\n  },\n  methods: {\n    handlePageChange(pageIndex) {\n      this.paginationParams.pageIndex = pageIndex;\n    }\n  }\n}\n</script>\n\n<template>\n  <AbcButtonPagination\n    :total-count=\"paginationParams.totalCount\"\n    :page-size=\"paginationParams.pageSize\"\n    :current-page=\"paginationParams.pageIndex\"\n    @changePage=\"handlePageChange\"\n  />\n</template>\n"}
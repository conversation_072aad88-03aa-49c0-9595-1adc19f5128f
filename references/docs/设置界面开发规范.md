<h2 id="iDNij">设置布局规范</h2>
<h3 id="iVTgI">规范描述</h3>
**双栏布局：**主要内容 + 侧边栏

> **注意：**以上实现基于父级高度固定的情况，父级高度固定才能让内部元素实现滚动，目前使用 `fill-remain-height`组件通过`flex`属性实现指定元素占据剩余高度的效果
>



![画板](https://cdn.nlark.com/yuque/0/2025/jpeg/13012400/1737022815039-fee4b123-e375-4c7c-af26-1f6d0aa701c0.jpeg)

<h3 id="CAgkv">相关组件概述</h3>
+ `fill-remain-height`: 占据剩余高度的布局组件，用于帮助某些容器占据父级剩余的高度而无需使用 css 的`calc` 属性计算高度
+ `setting-layout`: 设置布局容器，包裹内容（setting-content）和侧边栏（setting-sidebar），撑起高度
+ `setting-content`: 设置内容区域，控制内容边距、内容高度超出最大高度时在内部出现滚动条，包裹底部操作栏（setting-footer）
    - `setting-footer`: 叫 setting-content-footer 更贴切，内容区域的底部操作栏
+ `setting-sidebar`: 设置侧边栏，一般渲染需要预览的内容，滚动条在内部出现



最后，代码看起来像这样：

```vue
<template>
    <biz-fill-remain-height>
        <template #header>
            <abc-manage-tabs :option="tabsOption" @change="handleTabsChange">
            </abc-manage-tabs>
        </template>
        
        <router-view></router-view>
    </biz-fill-remain-height>
<template>

<script type="text/ecmascript-6">
    import { AbcManageTabs } from 
        '@/views/settings/components/abc-manage/index';
    import BizFillRemainHeight from 
        '@/components-composite/setting-form-layout/src/views/fill-remain-height.vue';
    
    export default {
        components: {
            BizFillRemainHeight,
            AbcManageTabs,
        },
    };
</script>
```

```vue
<template>
    <biz-setting-layout>
        <biz-setting-content>
            <div>
                内容
            </div>
            
            <template #footer>
                <biz-setting-footer>
                    <abc-button>
                        确定
                    </abc-button>
                </biz-setting-footer>
            </template>
        </biz-setting-content>

        <biz-setting-sidebar>
            侧边栏
        </biz-setting-sidebar>
    </biz-setting-layout>
</template>

<script type="text/ecmascript-6">
    import {
        BizSettingLayout,
        BizSettingContent,
        BizSettingFooter,
        BizSettingSidebar,
    } from '@/components-composite/setting-form-layout/index.js';

    export default {
        components: {
            BizSettingLayout,
            BizSettingContent,
            BizSettingFooter,
            BizSettingSidebar,
        },
    }
</script>
```

<h2 id="ro8RL">设置表单规范</h2>
<h3 id="CvoAd">规范描述</h3>
**<font>表单（setting-form）：</font>**<font>一般情况下</font><font>内容区域定宽（768px），特殊场景下可设置特殊宽度</font>

**<font>表单组（setting-form-group）</font>**<font>：由标题（title）、多个表单项（setting-form-item）、分割线组成，组与组之间间距为 24px，最后一组无分割线</font>

**<font>表单项（setting-form-item）</font>**<font>：由标签（label）、自定义表单组件、可选的分割线（hasDivider）构成，表单项之间间距为 24px</font>

**<font>标签（label）</font>**<font>：顶部对齐，行高（label-line-height-size）根据表单项中的内容确定，一般尽量居中对齐</font>

**<font>提示文字（setting-form-item-tip）：</font>**<font>跟随表单组件，换行展示</font>

**<font>操作按钮：</font>**<font>跟随在表单选项后的操作按钮为文字按钮，</font>`<font>size</font>`<font>一般为</font>`<font>small</font>`

**<font>表单内容：</font>**<font>单选框、多选框根据业务需求同行或换行排列，垂直方向上的元素间距 12px </font>

<h3 id="lZIqs">相关组件概述</h3>
+ `setting-form`: 设置表单容器，控制表单组（setting-form-group）的间距，统一设置子级的`labelWidth``contentWidth`等属性
+ `setting-form-group`: 设置表单组，控制设置表单项之间（setting-form-item）的间距
+ `setting-form-item`: 设置表单项，`item`根据参数`hasDivider`控制分割线的展示，`item`的`label`需要对齐内容区域的某一个元素，所以需要指定某个元素的行高作为`label`的行高，给对应元素设置`data-type='label-align'`控制`label`的行高；内部覆写了 `radio``checkbox`的间距，使用`useInnerLayout`取消该覆写；`verticle`控制内容选项的排列方向，默认为`true`即纵向排列内容选项
+ `setting-form-tip`: 设置提示，包裹需要加提示的表单，换行展示提示，支持 `tip`参数或者`tip`插槽；
+ `setting-form-indent`: 设置二级缩进，包裹需要加缩进的内容，会在左侧加边距控制缩进，提供插槽`content`存放缩进内容



最后的代码可能像这样：

```vue
<template>
  <biz-setting-form  :label-width="120">
      <biz-setting-form-group title="预约模式">
          <biz-setting-form-item label="预约人员" label-line-height-size="medium">
              <abc-radio-group>
                  <biz-setting-form-item-tip>
                      <abc-radio :label="1">
                          按号源精确时间预约
                      </abc-radio>
  
                      <template #tip>
                          <abc-text theme="gray" size="mini">
                              适用于为每个患者保证充足服务时间
                          </abc-text>
                      </template>
                  </biz-setting-form-item-tip>
  
                  <biz-setting-form-item-indent >
                       <abc-radio :label="0">
                          按设定时段预约，每个时段可预约多个号源
                      </abc-radio>
  
                      <template #content>
                          <div>
                              缩进内容
                          </div>
                      </template>
                  </biz-setting-form-item-indent>
              </abc-radio-group>
          </biz-setting-form-item>
      </biz-setting-form-group>
  </biz-setting-form>
</template>

<script>
  import {
      BizSettingForm,
      BizSettingFormGroup,
      BizSettingFormItem,
      BizSettingFormItemTip,
      BizSettingFormHeader,
      BizSettingFormItemIndent,
  } from '@/components-composite/setting-form/index.js';

  export default {
      components: {
        BizSettingForm,
        BizSettingFormGroup,
        BizSettingFormItem,
        BizSettingFormItemTip,
        BizSettingFormItemIndent,
      }
  }
</script>
```

<h2 id="saCy1">设置表格</h2>
<h3 id="njDS6">规范描述</h3>
**表格布局**

+ 使用`abc-layout`布局，`preset`预设为`setting-table`

**<font>分页</font>**

+ <font>单表格带分页器：分页条数根据页面高度计算</font>
+ <font>表单中的表格带分页器：分页器包裹在表格中，分页条数默认10</font>
+ <font>没有分页器无尽表格，不处理</font>

**<font>对齐</font>**

+ <font>数字右对齐</font>
+ <font>状态固定字数居中对齐，其他左对齐</font>
+ <font>性别居中对齐</font>
+ <font>其他左对齐</font>

**<font>文字高亮</font>**

+ <font>有二级弹窗，第一行蓝色高亮（primary）</font>
+ <font>禁用文字置灰 theme: gray-light</font>
+ <font>警告 theme: warning-light</font>

**<font>列宽</font>**

+ <font>定宽，出滚动条</font>
+ <font>最小宽度加自适应</font>

**<font>文字超出</font>**

+ <font>... 展示，hover 有系统提示的 title 展示</font>

**<font>hover 提示</font>**

+ <font>需要带 tooltip 的图标</font>

<h3 id="tsbsp">相关组件概述</h3>
直接看代码

```vue
<template>
    <abc-layout preset="setting-table">
        <abc-layout-header>
           <!-- 搜索  -->
        </abc-layout-header>

        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                type="pro"
                class="diagnosis-treatment-table-view"
                :render-config="viewTableConfig"
                :data-list="panelData.rows"
                :sort-config.sync="sortConfig"
                :loading="loading"
                :empty-opt="{ label: '暂无其他费用项' }"
                :custom-tr-class="handleSetTableTrClass"
                @handleClickTr="clickEvent"
                @sortChange="changeSortHandler"
            >
            </abc-table>
        </abc-layout-content>

        <abc-layout-footer>
            <abc-pagination
                :show-total-page="false"
                :pagination-params="paginationParams"
                :count="totalCount"
                @current-change="changePageIndex"
            >
                <ul v-if="totalCount" slot="tipsContent" class="total-page">
                    <li>共 <span>{{ totalCount }}</span> 条数据</li>
                </ul>
            </abc-pagination>
        </abc-layout-footer>
    </abc-layout>
</template>
```




server {
    listen 9090;
    root /workspace/www;

    access_log /workspace/log/access.log;
    error_log   /workspace/log/error.log;

    location ~ \.(jpg|png|jpeg|gif)$ {
        expires 30d;
    }

    location ~ \.(js|css)$ {
        expires 1d;
    }

    location ~ ^/(\d+\.txt)$ {
        alias /workspace/oss/$1;
    }

    location ~ ^/(MP_verify_\w+\.txt)$ {
        alias /workspace/oss/$1;
    }

    location /BKFPJHkefe.txt {
        alias /workspace/oss/BKFPJHkefe.txt;
    }

    location /VeWRBfuNfO.txt {
        alias /workspace/oss/VeWRBfuNfO.txt;
    }

    location /RUuXMAGGUW.txt {
        alias /workspace/oss/RUuXMAGGUW.txt;
    }

    location /zdQ2WSagIJ.txt {
        alias /workspace/oss/zdQ2WSagIJ.txt;
    }

    location /PN7aaAJZBz.txt {
        alias /workspace/oss/PN7aaAJZBz.txt;
    }

    # /auth-callback 路径处理
    location /auth-callback {
        set $new_uri "";

        # 根据 target 参数决定重写路径
        if ($arg_target = "pharmacy") {
            set $new_uri "/biz-pharmacy/region-auth";
        }

        if ($arg_target = "hospital") {
            set $new_uri "/hospital/region-auth";
        }

        if ($arg_target = "chain") {
            set $new_uri "/chain/region-auth";
        }

        if ($arg_target = "clinic") {
            set $new_uri "/region-auth";
        }

        # 如果 new_uri 不为空，则重写为对应路径，并让请求继续处理
        if ($new_uri != "") {
            rewrite ^ $new_uri?$args last;
        }

        # 否则，返回 /home.html
        rewrite ^ /home.html break;
    }

    location / {
        if (-f $request_filename){
            break;
        }

        add_header Cache-Control no-store;

        rewrite ^/$ /static/app.html break;

        rewrite ^/external/* /external-app.html break;

        rewrite ^/hospital/* /hospital-app.html break;

        rewrite ^/biz-pharmacy/* /pharmacy-app.html break;

	    rewrite ^/air-pharmacy-introduce$ /home.html break;
	    rewrite ^/auth-callback/* /home.html break;
        rewrite ^/medical-development/* /home.html break;
        rewrite ^/examination-equipment-sale-activity/* /home.html break;
        rewrite ^/medical-device-promotion/* /home.html break;
        rewrite ^/record-guidelines/* /home.html break;

        rewrite ^/chain/* /chain.html break;
        rewrite /* /index.html break;
    }

    gzip on;
    gzip_comp_level 5;
    gzip_min_length 1k;
    gzip_buffers 4 16k;
    gzip_proxied any;
    gzip_vary on;
    gzip_types text/plain application/javascript application/x-javascript text/css application/xml;
    gzip_static on;
    gzip_disable msie6;
}

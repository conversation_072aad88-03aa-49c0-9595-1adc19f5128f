## 工程介绍
基于 Vue 2.7 开发的医疗 HIS 系统，分为诊所管家、口腔管家、眼视光管家、药店管家、医院管家产品线。不同产品线都包含连锁总部、连锁子店、单店等维度

- 组件库基于私有组件库 ABC UI，在 references/abc-ui/ 目录下有对应组件的用法
- npm包管理器是 yarn
- 本地开发使用 yarn q 快速启动工程
- ESlint + Stylelint 控制代码规范
- 基于 webpack 的多入口配置，实现不同产品线的打包

## 文件结构
- references: 存放的参考内容，可以从里面查找私有组件库的使用方法，不要修改这里面的文件，仅做用法参考
- build: webpack 的构建脚本
- build-v2: rspack 的构建脚本，本地开发主要用 rspack
- src: 项目源码
- src/api: 对后台接口的封装
- src/assets: 用到的静态文件，例如图片等资源
- src/common: 公共目录，放 filters、utils、constants 等，目录内可以相互引用，但是不允许引用其他目录的文件，保证绝对的干净
- src/components: 通用组件
- src/components-composite: 业务组件，保持绝对干净，对于公共能力需要从 src/common 公共目录进行引用
- src/core: 实现了核心的 Page 架构，后续新增的模块，都基于 Page 架构
- src/directive: vue指令
- src/filters: vue过滤器
- src/hooks: 封装的 hooks
- src/i18n: 国际化相关
- src/modules: 通过自研的微前端框架，接入了商城和医保两个自工程，放在这里面，通过 git submodule 独立管理
- src/router: vue-router 路由相关配置
- src/service: 共用的服务，通过微前端框架暴露给全局，可以通过 this.$abcPlatform.service.xxx 进行调用，参考文档 `ABC微前端使用文档.md`
- src/store: vuex 相关配置
- src/styles: 样式文件
- src/utils: 工具封装
- src/views/layout: 通用的布局文件，由于历史原因放在了这里，理论上应该提到 src/layout 中
- src/views: 诊所管家的模块，子目录以模块名命名，例如 src/views/cashier 为收费模块
- src/views-dentistry: 口腔管家特有的模块，其他与诊所管家共用的模块会在路由配置时指向 src/views 中的代码，完成模块复用
- src/views-hospital: 医院管家特有的模块，其他与诊所管家共用的模块会在路由配置时指向 src/views 中的代码，完成模块复用，医院管家中的模块大部分采用了 Page 架构
- src/views-ophthalmology: 眼视光管家特有的模块，其他与诊所管家共用的模块会在路由配置时指向 src/views 中的代码，完成模块复用
- src/views-pharmacy: 药店管家特有的模块，其他与诊所管家共用的模块会在路由配置时指向 src/views 中的代码，完成模块复用
- src/views-distribute: 根据产品线进行不同的配置，使得能够针对不同产品线进行功能特性，视图等的分发。具体的，src/views-distribute/base-config.js 是基础配置，clinic-config-normal.js 继承自 base-config，同时能够针对业务情况做覆盖
- src/app.js: 诊所管家、口腔管家、眼视光管家的入口文件，单店和连锁子店
- src/chain.js: 诊所管家、口腔管家、眼视光管家连锁总部的入口文件
- src/pharmacy.js: 药店管家的入口文件
- src/hospital.js: 医院管家的入口文件
- template-dev.html: 开发环境的 vue html 模板文件
- template.html: 生成环境的 vue html 模板文件
- template-pahrmacy.html: 生产环境的药店管家的 vue html 模板文件

## 组件库

### ABC UI
ABC UI 是公司内部封装的组件库，对应的 npm 包：@abc/ui-pc，具体用法在 references/abc-ui/ 中参考，例如 references/abc-ui/abc-table.json 是 abc-table 的用法，提供组件如下：

abc-ui
├── abc-autocomplete.json
├── abc-badge.json
├── abc-button-group.json
├── abc-button-pagination.json
├── abc-button.json
├── abc-card.json
├── abc-cascader.json
├── abc-checkbox-button.json
├── abc-checkbox.json
├── abc-content-empty.json
├── abc-date-picker.json
├── abc-date-time-picker.json
├── abc-descriptions.json
├── abc-dialog.json
├── abc-divider.json
├── abc-dropdown.json
├── abc-edit-div.json
├── abc-flex.json
├── abc-form.json
├── abc-grid.json
├── abc-icon.json
├── abc-image.json
├── abc-input-mobile.json
├── abc-input-number.json
├── abc-input.json
├── abc-layout.json
├── abc-link.json
├── abc-list.json
├── abc-loading.json
├── abc-modal.json
├── abc-pagination.json
├── abc-popover.json
├── abc-radio-button.json
├── abc-radio.json
├── abc-select.json
├── abc-space.json
├── abc-statistic.json
├── abc-steps.json
├── abc-switch.json
├── abc-table.json
├── abc-tabs-v2.json
├── abc-tag-v2.json
├── abc-text.json
├── abc-textarea.json
├── abc-time-picker.json
├── abc-time-range-picker.json
├── abc-tips-card-v2.json
├── abc-tips.json
├── abc-tooltip-info.json
├── abc-tooltip.json
├── abc-transfer-v2.json
├── abc-tree-v2.json
└── abc-week-pagination.json

#### 重要说明
始终使用 ABC UI 组件库中提供的组件完成代码编写，当你不知道组件用法时，索引references/abc-ui/中的文档，或者其他文件中的类似用法，禁止使用开源组件库例如 element-ui 等！

对应的模块有各自的开发规范，具体如下：
### 设置模块
当开发设置页面时，参考文档 [[references/docs/设置界面开发规范.md]]
### 证件组件替换
当替换证件组件时，参考文档 [[src/views/crm/common/package-info/证件组件替换开发规范.md]]

## 编码风格及规范

- [强制] css 类取名原则上要包含 业务属性 || 自身特色属性，例如门诊西药处方： outpatient-western-prescription-[warpper|container|table]，弹窗类挂载到body上的组件，一定要定义唯一的class名字，防止全局污染
- [强制] 文件名使用 kebab-case 命名，template 中使用 vue 组件，也使用 kebab-case 命名，例如: <abc-button></abc-button>
- [强制] get请求不能使用参数拼接的形式，需要序列化参数
- [强制] 变量 使用 Camel命名法。（驼峰）
- [强制] 常量 使用 全部字母大写，单词间下划线分隔 的命名方式。
- [强制] 函数 使用 Camel命名法。
- [强制] 函数的 参数 使用 Camel命名法。
- [强制] 类 使用 Pascal命名法。（首字母大写）
- [强制] 类的 方法 / 属性 使用 Camel命名法。
- [强制] 枚举变量 使用 Pascal命名法，枚举的属性 使用 全部字母大写，单词间下划线分隔 的命名方式。
- [强制] 由多个单词组成的缩写词，在命名中，根据当前命名法和出现的位置，所有字母的大小写与首字母的大小写保持一致。
- [强制] 类名 使用 名词。
- [建议] 函数名 使用 动宾短语。
- [建议] boolean 类型的变量使用 is 或 has 开头。

## OSS 上传
因为需要计算门店存储空间用量，上传资源都必须调用 this.$abcPlatform.service.oss.clinicUsageUpload，此时会上传到 clinic-usage/${clinic}/${filePath} 下，filePath 根据业务场景进行指定。

## Git仓库管理
基于 git-flow 衍生出 abc-git-flow，分支分为长期分支及临时分支

## vue hooks
创建新hook时，始终使用脚本命令 'yarn create:hooks'，进行创建并基于创建出来的文件修改，实现要求的功能，和完善使用文档。

### 分支模型
#### 长期分支
- master 只能用来包括稳定的代码
- gray 只能用来包括稳定的灰度环境代码
- rc 预发布环境的分支。用于上线前测试，完成后合入 gray 分支
- develop 进行任何新需求开发的基础分支。当开始一个新需求开发时，从该分支拉取。另外，该分支也汇集所有已经完成的功能，并等待被合并到 rc 分支中

#### 临时分支
- feature 开发新功能时，从develop拉取的功能分支，在该分支上开发，测试通过后合入develop
- hotfix 修复线上紧急问题。从master拉出，修改提测后，合入master、gray、rc和develop
- hotfix-g 修复灰度环境的紧急问题。从 gray 拉出，修改提测后，合入gray、rc和develop

## 约束
- 始终使用 ABC UI 组件库中提供的组件完成代码编写，当你不知道组件用法时，索引其他文件中的类似用法，禁止使用开源组件库例如 element-ui 等！
- 需要使用色值时，优先使用 src/styles/var.scss 以及 @abc/ui-pc/theme/common/var.scss 中定义的 css 变量
- 始终用中文回答

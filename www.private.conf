server {
    listen 80;
    root /workspace/www;

    access_log /workspace/log/access.log;
    error_log   /workspace/log/error.log;

    location ~ \.(jpg|png|jpeg|gif)$ {
        expires 30d;
    }

    location ~ \.(js|css)$ {
        expires 1d;
    }

    location ~ ^/(\d+\.txt)$ {
        alias /workspace/oss/$1;
    }

    location ~ ^/(MP_verify_\w+\.txt)$ {
        alias /workspace/oss/$1;
    }

    location /BKFPJHkefe.txt {
        alias /workspace/oss/BKFPJHkefe.txt;
    }

    location /VeWRBfuNfO.txt {
        alias /workspace/oss/VeWRBfuNfO.txt;
    }

    location / {
        if (-f $request_filename){
          break;
        }

        add_header Cache-Control no-store;

        rewrite ^/$ /static/app.html break;

        rewrite ^/login /home.html break;
	    rewrite ^/apply$ /home.html break;
	    rewrite ^/air-pharmacy-introduce$ /home.html break;
	    rewrite ^/price$ /home.html break;
        rewrite ^/about$ /home.html break;
        rewrite ^/login/* /home.html break;
        rewrite ^/reset-pw/* /home.html break;
        rewrite ^/auth-callback/* /home.html break;
        rewrite ^/auth/* /home.html break;
        rewrite ^/oauth /home.html break;

        rewrite ^/chain/* /chain.html break;
        rewrite /* /index.html break;
    }
}

{"compilerOptions": {"target": "es2017", "allowSyntheticDefaultImports": false, "baseUrl": "./", "paths": {"@/*": ["src/*"], "components/*": ["src/components/*"], "views/*": ["src/views/*"], "styles/*": ["src/styles/*"], "theme/*": ["src/theme/*"], "api/*": ["src/api/*"], "utils/*": ["src/utils/*"], "store/*": ["src/store/*"], "router/*": ["src/router/*"], "mock/*": ["src/mock/*"], "static/*": ["../static/*"], "@modules/*": ["src/modules/*"], "@module/*": ["src/modules/b2b-mall/src/*"], "@social/*": ["src/modules/social-security-lite/src/*"], "@ohif-core/*": ["src/medical-imaging-viewer/core/*"]}}, "vueCompilerOptions": {"target": 2.7}, "exclude": ["node_modules", "dist"], "include": ["src"]}
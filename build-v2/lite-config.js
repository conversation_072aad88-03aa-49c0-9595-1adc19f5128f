const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');



function resolveApp(relativePath) {
    return path.resolve(relativePath);
}


const baseEntry = {
    home: path.resolve(__dirname, '../src/home.js'),
    // 'external-app': path.resolve(__dirname, '../src/external-app/index.js'),
};

const chainEntry = {
    chain: path.resolve(__dirname, '../src/chain.js'),
};

const hospitalEntry = {
    'hospital-app': path.resolve(__dirname, '../src/hospital.js'),
};

const pharmacyEntry = {
    'pharmacy-app': path.resolve(__dirname, '../src/pharmacy.js'),
};

const appEntry = {
    app: path.resolve(__dirname, '../src/app.js'),
};

const baseTemplate = [
    new HtmlWebpackPlugin({
        filename: 'home.html',
        template: 'build-v2/template.html',
        favicon: resolveApp('favicon.ico'),
        chunks: ['manifest', 'vendor', 'home-vendor', 'home'],
        inject: true,
    }),
    // new HtmlWebpackPlugin({
    //     filename: 'external-app.html',
    //     template: 'build-v2/template.html',
    //     favicon: resolveApp('favicon.ico'),
    //     chunks: ['external-app'],
    //     inject: true,
    // }),
];

const chainTemplate = [
    new HtmlWebpackPlugin({
        filename: 'chain.html',
        template: 'build-v2/template.html',
        favicon: resolveApp('favicon.ico'),
        chunks: ['manifest', 'vendor', 'chain-vendor', 'chain'],
        inject: true,
    }),
];


const hospitalTemplate = [
    new HtmlWebpackPlugin({
        filename: 'hospital-app.html',
        template: 'build-v2/template.html',
        favicon: resolveApp('favicon.ico'),
        chunks: ['manifest', 'vendor', 'app-vendor', 'hospital-app'],
        inject: true,
        scriptLoading: 'defer',
    }),
];


const pharmacyTemplate = [
    new HtmlWebpackPlugin({
        filename: 'pharmacy-app.html',
        template: 'build-v2/template.html',
        favicon: resolveApp('favicon.ico'),
        chunks: ['manifest', 'vendor', 'app-vendor', 'pharmacy-app'],
        inject: true,
        scriptLoading: 'defer',
    }),
];

const appTemplate = [
    new HtmlWebpackPlugin({
        filename: 'app.html',
        template: 'build-v2/template.html',
        favicon: resolveApp('favicon.ico'),
        chunks: ['manifest', 'vendor', 'app-vendor', 'app'],
        inject: true,
        scriptLoading: 'defer',
    }),
];

const getEntry = () => {
    const { ABC_LITE_ENTRY } = process.env;
    if (ABC_LITE_ENTRY === 'all') {
        return {
            ...baseEntry,
            ...appEntry,
            ...chainEntry,
            ...hospitalEntry,
            ...pharmacyEntry,
        };
    }

    if (ABC_LITE_ENTRY === 'chain') {
        return {
            ...baseEntry,
            ...chainEntry,
        };
    }

    if (ABC_LITE_ENTRY === 'hospital') {
        return {
            ...baseEntry,
            ...hospitalEntry,
        };
    }

    if (ABC_LITE_ENTRY === 'pharmacy') {
        return {
            ...baseEntry,
            ...pharmacyEntry,
        };
    }

    if (ABC_LITE_ENTRY === 'app') {
        return {
            ...appEntry,
            ...baseEntry,
        };
    }
};

const getTemplate = () => {
    const { ABC_LITE_ENTRY } = process.env;
    if (ABC_LITE_ENTRY === 'all') {
        return [
            ...appTemplate,
            ...baseTemplate,
            ...chainTemplate,
            ...hospitalTemplate,
            ...pharmacyTemplate,
        ];
    }

    if (ABC_LITE_ENTRY === 'chain') {
        return [
            ...chainTemplate,
            ...baseTemplate,
        ];
    }

    if (ABC_LITE_ENTRY === 'hospital') {
        return [
            ...hospitalTemplate,
            ...baseTemplate,
        ];
    }

    if (ABC_LITE_ENTRY === 'pharmacy') {
        return [
            ...pharmacyTemplate,
            ...baseTemplate,
        ];
    }

    if (ABC_LITE_ENTRY === 'app') {
        return [
            ...appTemplate,
            ...baseTemplate,
        ];
    }
};

module.exports = {
    getEntry,
    getTemplate,
};

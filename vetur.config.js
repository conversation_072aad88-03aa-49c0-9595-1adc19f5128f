// vetur.config.js
const fs = require('fs');
const path = require('path');

function parseComponents(filePath) {
    const content = fs.readFileSync(filePath, 'utf-8');
    
    // 匹配两种导入格式：
    // 1. import ComponentName from 'path'
    // 2. import { Component1, Component2 } from 'path'
    const importRegexes = [
        /import\s+(\w+)\s+from\s+['"]([^'"]+)['"]/g, // 单个导入
        /import\s+{([^}]+)}\s+from\s+['"]([^'"]+)['"]/g, // 多个导入
    ];
    
    const components = [];
    
    importRegexes.forEach((regex) => {
        let match;
        while ((match = regex.exec(content)) !== null) {
            if (regex.source.includes('{')) {
                // 处理 { Component1, Component2 } 格式
                const names = match[1]
                    .split(',')
                    .map((n) => n.trim())
                    .filter((n) => n);
                const importPath = match[2];
                
                names.forEach((name) => {
                    if (/^[A-Z]/.test(name)) {
                        components.push({
                            name,
                            path: importPath.replace(/^@\//, 'src/'),
                        });
                    }
                });
            } else {
                // 处理 ComponentName 格式
                const name = match[1];
                const importPath = match[2];
                
                if (/^[A-Z]/.test(name)) {
                    components.push({
                        name,
                        path: importPath.replace(/^@\//, 'src/'),
                    });
                }
            }
        }
    });
    
    return components;
}


/** @type {import('vls').VeturConfig} */
module.exports = {
    projects: [
        {
            root: './',
            // **optional** default: `[]`
            // Register globally Vue component glob.
            // If you set it, you can get completion by that components.
            // It is relative to root property.
            // Notice: It won't actually do it. You need to use `require.context` or `Vue.component`
            globalComponents: [
                ...parseComponents('./src/components/index.js').map((component) => ({
                    name: component.name,
                    path: path.resolve(__dirname, component.path),
                })),
                
            ],
        },
    ],
};
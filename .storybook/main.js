import {join, dirname, resolve} from "path";
import webpack from "webpack";

/**
 * This function is used to resolve the absolute path of a package.
 * It is needed in projects that use Yarn PnP or are set up within a monorepo.
 */
function getAbsolutePath(value) {
    return dirname(require.resolve(join(value, "package.json")));
}

const config = {
    stories: [
        "../src/components-composite/*.mdx",
        "../src/components-composite/**/*.mdx",
        "../src/components-composite/**/*.stories.@(js|jsx|ts|tsx)",
        "../src/hooks/*.mdx",
        "../src/hooks/**/*.mdx",
        "../src/hooks/**/*.stories.@(js|jsx|ts|tsx)",
    ],
    addons: [
        "@storybook/addon-links",
        "@storybook/addon-essentials",
        "@storybook/addon-interactions",
        {
            name: '@storybook/addon-styling',
            options: {
                sass: {
                    // Require your Sass preprocessor here
                    implementation: require('sass'),
                },
            },
        },
    ],
    framework: {
        name: '@storybook/vue-webpack5',
        options: {},
    },
    babel: async (options) => {
        return {
            ...options,
            presets: [
                '@vue/babel-preset-jsx',
            ],
        };
    },
    webpackFinal: async (config, {
        configType
    }) => {
        // `configType` has a value of 'DEVELOPMENT' or 'PRODUCTION'
        // You can change the configuration based on that.
        // 'PRODUCTION' is used when building the static version of storybook.

        // Make whatever fine-grained changes you need
        config.resolve = {
            ...config.resolve,
            alias: {
                ...config.resolve?.alias,
                packages: resolve(__dirname, "../packages"),
                style: resolve(__dirname, "../style"),
                theme: resolve(__dirname, "../packages/theme"),
                '@': resolve(__dirname, "../src"),
                '~': resolve(__dirname, "../src/"),
                'assets': resolve(__dirname, "../src/assets"),
            }
        };

        config.plugins.push(
            new webpack.ProvidePlugin({
                $: 'jquery',
                'jQuery': 'jquery',
                Popper: ['popper.js', 'default']
            }),
        );

        // Return the altered config
        return config;
    },
    docs: {
        autodocs: "tag",
    },
};
export default config;

import { decorators } from './injectPcContext';
import { formatCode } from '../tool/storybook/format-code';
import './nav-bar.scss'

const preview = {
    parameters: {
        actions: { argTypesRegex: "^on[A-Z].*" },
        controls: {
            matchers: {
                color: /(background|color)$/i,
                date: /Date$/,
            },
        },
        backgrounds: {
            default: 'gray',
            values: [
                { name: 'gray', value: '#f5f5f5' },
                { name: 'white', value: '#fff' },
            ],
        },
        docs: {
            source: {
                transform: (code) => {
                    return formatCode(code)
                },
                format: 'dedent',
                language: 'jsx',
            },
        },
    },
    globalTypes: {
        theme: {
            description: 'Global theme for components',
            defaultValue: 'light',
            toolbar: {
                title: 'Theme',
                icon: 'circlehollow',
                items: ['default', 'pharmacy'],
                dynamicTitle: true,
            }
        }
    },
    decorators
};

export default preview;

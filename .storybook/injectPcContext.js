import Vue from 'vue';
import '../src/abc-ui-regist';
import { setAbcTheme } from '@abc/ui-pc';
import '../src/styles/var.scss';
import i18n from '../src/i18n/index.js';
import AbcMoney from '../src/components/abc-money';
// https://github.com/storybookjs/storybook/issues/1573
Vue.prototype.$t = function (...args) {
    return i18n.t(...args);
};
// 注册全局指令
import AbcScrollLoader from '../src/directive/scroll-loader';
import AbcFocusSelected from '../src/directive/focus-select';
import * as filters from '../src/common/filters'; // 全局vue directives
Vue.directive('AbcScrollLoader', AbcScrollLoader);
Vue.directive('AbcFocusSelected', AbcFocusSelected);
Vue.component(AbcMoney.name, AbcMoney);

// 注册全局 filter
Object.keys(filters).forEach((key) => {
    Vue.filter(key, filters[key]);
});

const decorators = [
    // 提供主体
    (story, context) => {
        const theme = context.globals.theme || 'default';
        return {
            components: { story },
            mounted() {
                setAbcTheme(theme);
            },
            template: `
                <div>
                    <story/>
                </div>
            `,
        };
    },
];
export {
    decorators
};

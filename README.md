# ABC诊所管家 #

## 开发前
请阅读开发规范 https://abcyun.yuque.com/abc-home/ywhr14/wr7rtz

## 开发
```bash
    # 安装依赖
    yarn install

    # 本地开发 开启服务，后端代理到 dev.abczs.cn
    yarn dev
```

## 注意事项
2. 项目采用了NPM私有库，安装依赖前需要进行NPM私有库登陆
3. NPM私有库登陆流程地址，请参考：https://abcyun.yuque.com/abc-home/ywhr14/mcp10p
4. node-saas版本最多兼容至Node.js版本V.14.x

## Cypress E2E 测试
Cypress 工程作为 `git submodule` 集成到 PC 工程中，需要先初始化 cypress 工程

```shell
git submodule init cypress && git submodule update cypress
cd cypress && git checkout master

# 也可以执行
yarn setup:cypress
```
Cypress 工程也通过 `abc-git-flow` 管理分支，例如灰度用例执行失败，则需要拉 hotfix-g 进行调整

！！！！！注意看：
当修改了 Cypress 工程里的文件，需要提交到 Cypress 工程的仓库

PC 中提示 `cypress` 文件夹有修改，***不用理会***，无需 add，也无需 commit，只需要提交 Cypress 工程的仓库即可

不明白的可以了解下 [git submodule](https://git-scm.com/book/zh/v2/Git-%E5%B7%A5%E5%85%B7-%E5%AD%90%E6%A8%A1%E5%9D%97) 机制

Webstorm 支持多仓库管理，按照图中添加 cypress 路径即可：
![Webstorm 设置](tool/docs/version-control.png)

## 代码格式化
Eslint + EditorConfig + Stylelint

## 权限说明
moduleId 代表模块，session storage中的`__user_info__`保存的moduleIds代表拥有的权限
- `0` 工作台，表示所有权限
- `1` 挂号
- `2` 门诊
- `3` 收费
- `4` 药房
- `5` 患者
- `6` 统计
- `7` 管理设置
- `8` 库存
- `9` 检验
- `10` 护士站 改名为 "治疗理疗"
- `11` 会员
- `12` 门诊代录
- `13` 营销
- `14` 微诊所
- `15` 儿保

## 角色说明
roles
- `1` 医生
- `2` 护士
- `3` 检验师
- `4` 理疗师
- `5` 医助
- `6` 其他
- `9` 验光师


### 武汉健康码接入说明
以`挂号`举例，参考 views/registration/table.vue

1. 引入 mixin
    ```javascript
    import HealthyCardHandler from 'views/common/healthy-card-handler';
    ```

2. mounted 中调用 `createHealthyCardGlobalDetector` 创建健康码探测器
    ```javascript
    this.createHealthyCardGlobalDetector( {
        // 新增按钮文案
        addBtnText: '新增挂号',
       
        // 查询按钮文案
        searchBtnText: '查询挂号',
        
        // 新增按钮点击回调
        addCallback: ( patient ) => {
                       // do sth...
                   },
        // 查询按钮点击回调
        searchCallback: null 
    } );
    ```
3. mixin 中在 beforeDestroy 中调用了 `destroyHealthyCardGlobalDetector` 进行销毁，若其他业务场景需要，可以直接调用 `destroyHealthyCardGlobalDetector`，
销毁后将停止监听

### oss 上传
用户的上传资源都调用 this.$abcPlatform.service.oss.clinicUsageUpload，此时会上传到 clinic-usage/${clinic}/${filePath} 下，filePath需自己指定
目前已经指定的filePath：
- chronic-care // 慢病相关
- doctor // 医生相关 ex头像 执业证照
- call-number // 叫号屏幕
- self-service // 自助服务机
- examination // 检查模块
- medical-record // 门诊病历模块
- inspect-report // 检查报告同步
- physical-examination // 体检相关

### 项目中使用主题色
所有颜色都使用css变量，文件地址：@abc/ui-pc/theme/common/var.scss，用法如下：

1. 在scss文件中或者vue的style指定了lang="scss"可以直接使用css变量：
```scss
 // demo.scss
 .demo-class {
     background: var(--abc-color-T1);
 }
    
<style lang="scss">
    .class-name {
        color: var(--abc-color-T1);
    }
</style>
```
2. 在vue模板中使用
```vue
<template>
   <div style="color: var(--abc-color-T1)">使用引入的变量</div>
</template>
```
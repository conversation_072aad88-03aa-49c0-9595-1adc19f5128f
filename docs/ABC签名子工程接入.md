# ABC签名子工程接入
## 简介
为了提高整个ABC工程的兼容性，PC以及其子工程已经引入了wasm。相对于javascript，wasm天然可以隐藏代码逻辑，提高安全性。另外wasm相对于javascript有更好的性能表现。因此，ABC签名子工程也引入了wasm。本文档主要介绍ABC签名子工程如何接入ABC工程。

## 接入示例
子工程接入也非常简单，签名其实就是要在请求上面加上sign字段 下面是一个接入参考

### 接入代码
axios代码示例, 如果是原生fetch可以沟通具体的接入方案
```javascript
// request拦截器
service.interceptors.request.use(
    async (config) => {
        // ....
        await addSignToRequest(config, new Date().getTime().toString());
        return config;
    },
    (error) => {
        Promise.reject(error);
    }
);
```

addSignToRequest方法
```javascript
const addSignToRequest = async (config, timestamp) => {
    // 构建的tag信息
    config.headers["Abc-Client-Info"] = `${buildInfo.BUILD_TAG};`;
    config.headers["X-ABC-Ts"] = timestamp;
    // 这里需要根据实际情况获取cookie中的_abcyun_token_
    // 也可以通过url传递
    const abcSecret = await getCookieByName('_abcyun_token_');
    config.headers["X-ABC-Sign"] = await SignService.generateSignature({
        config,
        timestamp,
        abcSecret,
    });
};
```

sign-service.js
```javascript
import Qs from "qs";
function isNumber(a) {
  return parseFloat(a).toString() !== "NaN";
}
export const HEADER_ABC_TS = "__abc-ts__";
export const REQUEST_PATH = "__abc-path__";
export const REQUEST_TOKEN = "__abc-secret__";
export default class SignService {
  static async generateSignature({ config, timestamp, abcSecret }) {
    const { url, params, paramsSerializer } = config;
    let reqUrl = url;
    if (params) {
      if (paramsSerializer) {
        reqUrl += `&${paramsSerializer(params)}`;
      } else {
        reqUrl += `&${Qs.stringify(params)}`;
      }
    }
    //解析url
    const uri = new URL(reqUrl);
    const { pathname, searchParams } = uri;
    //构建签名参数map
    const signParams = new Map();
    signParams.set(HEADER_ABC_TS, timestamp);
    signParams.set(REQUEST_TOKEN, abcSecret);
    signParams.set(REQUEST_PATH, pathname);
    // 遍历查询参数
    if (searchParams && searchParams.toString()) {
      for (const [key, value] of searchParams.entries()) {
        if (isNumber(value) || value) {
          signParams.set(key, value);
        }
      }
    }
    const AbcSafePkg = await import("abc-safe-pkg");
    return AbcSafePkg.sign(signParams, config.paramsSerializer);
  }
}
```

## 接入框架
### webpack
webpack配置文件中开启实验特性
```javascript
experiments: {
    asyncWebAssembly: true
}
```

### vite
vite
```javascript
import wasm from "vite-plugin-wasm";
import topLevelAwait from "vite-plugin-top-level-await";
// vite要加这两个插件
plugins: [
    topLevelAwait(),
    wasm(),
    // ....
]
```

